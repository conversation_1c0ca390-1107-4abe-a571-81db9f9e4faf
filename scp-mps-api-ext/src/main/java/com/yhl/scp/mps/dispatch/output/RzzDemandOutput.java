package com.yhl.scp.mps.dispatch.output;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName RzzDemandOutput
 * @Description TODO
 * @Date 2024-12-07 13:51:31
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Data
@ApiModel("需求输出结果")
public class RzzDemandOutput {

    @ApiModelProperty("需求id")
    private String demandId;

    @ApiModelProperty("需求订单id")
    private String demandOrderId;

    @ApiModelProperty("算法自建需求id")
    private String selfDemandId;

    @ApiModelProperty("算法自建计划量id")
    private String selfPlannedId;

    @ApiModelProperty("算法自建计划量输入id")
    private String selfPlannedInputId;

    @ApiModelProperty("需求类型")
    private String demandType;

    @ApiModelProperty("库存点物品id")
    private String productStockPointId;

    @ApiModelProperty("数量")
    private BigDecimal qty;

    @ApiModelProperty("未满足数量")
    private BigDecimal unfulfilledQty;

    @ApiModelProperty("需求时间")
    private String demandTime;

    @ApiModelProperty("原始需求时间")
    private String originalTime;

}
