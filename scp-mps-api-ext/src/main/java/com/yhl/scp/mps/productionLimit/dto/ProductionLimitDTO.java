package com.yhl.scp.mps.productionLimit.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ProductionLimitDTO</code>
 * <p>
 * 特殊工艺产能约束DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-28 15:46:28
 */
@ApiModel(value = "特殊工艺产能约束DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductionLimitDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -46684018890450424L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    private String companyCode;

    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;
    /**
     * 要素值
     */
    @ApiModelProperty(value = "要素值")
    private String factorValue;
    /**
     * 天数
     */
    @ApiModelProperty(value = "天数")
    private Integer days;
    /**
     * 主工序产能上限
     */
    @ApiModelProperty(value = "主工序产能上限")
    private Long mainOperationProductionCapacity;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "限制开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "限制结束时间")
    private Date endTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;

}
