package com.yhl.scp.mps.operationPublished.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.ddd.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>DemandPublishedDTO</code>
 * <p>
 * 需求发布信息表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 10:00:20
 */
@ApiModel(value = "需求发布信息表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DemandPublishedDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 582909527667002302L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String newId;
    
    /**
     * 源数据id
     */
    @ApiModelProperty(value = "源数据id")
    private String id;
    
    @ApiModelProperty(value = "${column.comment}")
    private String planVersionId;
    /**
     * 需求代码
     */
    @ApiModelProperty(value = "需求代码")
    private String demandCode;
    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    private Date demandTime;
    /**
     * 库存点物品ID
     */
    @ApiModelProperty(value = "库存点物品ID")
    private String productStockPointId;
    /**
     * 物品ID
     */
    @ApiModelProperty(value = "物品ID")
    private String productId;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 未分配数量
     */
    @ApiModelProperty(value = "未分配数量")
    private BigDecimal unfulfilledQuantity;
    /**
     * 客户ID
     */
    @ApiModelProperty(value = "客户ID")
    private String customerId;
    /**
     * 产生需求的订单ID
     */
    @ApiModelProperty(value = "产生需求的订单ID")
    private String demandOrderId;
    /**
     * 工序输入ID
     */
    @ApiModelProperty(value = "工序输入ID")
    private String operationInputId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String demandType;
    /**
     * 分配状态
     */
    @ApiModelProperty(value = "分配状态")
    private String fulfillmentStatus;
    /**
     * 计数单位ID
     */
    @ApiModelProperty(value = "计数单位ID")
    private String countingUnitId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 成品物品代码
     */
    @ApiModelProperty(value = "成品物品代码")
    private String finishedProductCode;
    
    /**
     * 主生产计划发布日志id
     */
    @ApiModelProperty(value = "主生产计划发布日志id")
    private String publishedLogId;

}
