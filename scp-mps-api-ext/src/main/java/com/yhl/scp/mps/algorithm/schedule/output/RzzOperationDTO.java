package com.yhl.scp.mps.algorithm.schedule.output;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.yhl.platform.common.annotation.FieldInterpretation;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;



/**
 * <code>OperationDTO</code>
 * <p>
 * OperationDTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-09-04 11:20:24
 */
@ApiModel(value = "DTO")
@Data
@SuperBuilder
@NoArgsConstructor
public class RzzOperationDTO implements Serializable {
	
    private static final long serialVersionUID = 839471863274739183L;
	
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 工序代码
     */
    @ApiModelProperty(value = "工序代码")
    private String operationCode;
    /**
     * 制造订单ID
     */
    @ApiModelProperty(value = "制造订单ID")
    private String orderId;
    /**
     * 计划批次ID
     */
    @ApiModelProperty(value = "计划批次ID")
    private String planUnitId;
    /**
     * 路径步骤ID
     */
    @ApiModelProperty(value = "路径步骤ID")
    private String routingStepId;
    /**
     * 标准工艺ID
     */
    @ApiModelProperty(value = "标准工艺ID")
    private String standardStepId;
    /**
     * 路径步骤顺序号
     */
    @ApiModelProperty(value = "路径步骤顺序号")
    private Integer routingStepSequenceNo;
    /**
     * 前工序顺序号
     */
    @ApiModelProperty(value = "前工序顺序号")
    private String preRoutingStepSequenceNo;
    /**
     * 后工序顺序号
     */
    @ApiModelProperty(value = "后工序顺序号")
    private String nextRoutingStepSequenceNo;
    /**
     * 库存点物品ID
     */
    @ApiModelProperty(value = "库存点物品ID")
    private String productStockPointId;
    /**
     * 物品ID
     */
    @ApiModelProperty(value = "物品ID")
    private String productId;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 计划资源ID
     */
    @ApiModelProperty(value = "计划资源ID")
    private String plannedResourceId;
    /**
     * 是否冻结
     */
    @ApiModelProperty(value = "是否冻结")
    private String frozen;
    /**
     * 计划状态
     */
    @ApiModelProperty(value = "计划状态")
    private String planStatus;
    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private String orderType;
    /**
     * 齐套状态
     */
    @ApiModelProperty(value = "齐套状态")
    private String kitStatus;
    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间")
    private Integer processingTime;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 最早开始时间
     */
    @ApiModelProperty(value = "最早开始时间")
    private Date earliestStartTime;
    /**
     * 最晚结束时间
     */
    @ApiModelProperty(value = "最晚结束时间")
    private Date latestEndTime;
    /**
     * 计算的最早开始时间
     */
    @ApiModelProperty(value = "计算的最早开始时间")
    private Date calcEarliestStartTime;
    /**
     * 计算的最晚结束时间
     */
    @ApiModelProperty(value = "计算的最晚结束时间")
    private Date calcLatestEndTime;

    /**
     * 持续任务
     */
    @ApiModelProperty(value = "持续任务")
    private String connectionTask;

    /**
     * 持续方式
     */
    @ApiModelProperty(value = "持续方式")
    private String connectionType;

    /**
     * 最大接续时间
     */
    @ApiModelProperty(value = "最大接续时间")
    private Integer maxConnectionDuration;

    /**
     * 最小接续时间
     */
    @ApiModelProperty(value = "最小接续时间")
    private Integer minConnectionDuration;

    /**
     * 子工序顺序
     */
    @ApiModelProperty(value = "子工序顺序")
    private Integer operationIndex;

    /**
     * 父工序ID
     */
    @ApiModelProperty(value = "父工序ID")
    private String parentId;

    /**
     * 分割类别
     */
    @ApiModelProperty(value = "分割类别")
    private String partitionType;

    /**
     * 资源固定标志
     */
    @ApiModelProperty(value = "资源固定标志")
    private String resourceFixed;

    /**
     * 时间固定标志
     */
    @ApiModelProperty(value = "时间固定标志")
    private String timeFixed;

    /**
     * 锁定状态
     */
    @ApiModelProperty(value = "锁定状态")
    private String lockStatus;

    /**
     * 延期原因
     */
    @ApiModelProperty(value = "延期原因")
    private String delayReason;

    /**
     * 未排程原因
     */
    @ApiModelProperty(value = "未排程原因")
    private String unscheduledReason;

    /**
     * 是否上次插单
     */
    @ApiModelProperty(value = "是否上次插单")
    private String lastInsertion;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 更新时间
     */
    private Date modifyTime;


    /**
     * 前工序IDS
     */
    @ApiModelProperty(value = "前工序IDS")
    private String preOperationIds;
    /**
     * 资源IDS
     */
    @ApiModelProperty(value = "资源IDS")
    private String resourceIds;
    /**
     * 后工序IDS
     */
    @ApiModelProperty(value = "后工序IDS")
    private String nextOperationIds;
    /**
     * 用户指定制造时间
     */
    @ApiModelProperty(value = "用户指定制造时间")
    private Integer appointProductionTime;
    /**
     * 用户指定数量
     */
    @ApiModelProperty(value = "用户指定数量")
    private Integer appointQuantity;
    /**
     * 用户指定主资源
     */
    @ApiModelProperty(value = "用户指定主资源")
    private String appointMainResourceId;
    /**
     * 指定开始时刻
     */
    @ApiModelProperty(value = "指定开始时刻")
    private Date appointStartTime;
    /**
     * 指定结束时刻
     */
    @ApiModelProperty(value = "指定结束时刻")
    private Date appointEndTime;
    /**
     * 分割比率
     */
    @ApiModelProperty(value = "分割比率")
    private BigDecimal partitionRate;
    /**
     * 切割数
     */
    @ApiModelProperty(value = "切割数")
    private Integer partitionNum;
    /**
     * 分割批量
     */
    @ApiModelProperty(value = "分割批量")
    private BigDecimal partitionBatch;
    /**
     * 最大分割批量
     */
    @ApiModelProperty(value = "最大分割批量")
    private BigDecimal maxPartitionBatch;
    /**
     * 最小分割批量
     */
    @ApiModelProperty(value = "最小分割批量")
    private BigDecimal minPartitionBatch;
    /**
     * 分割批量单位
     */
    @ApiModelProperty(value = "分割批量单位")
    private String partitionBatchUnit;
    /**
     * 分割尾数处理
     */
    @ApiModelProperty(value = "分割尾数处理")
    private String partitionMantissaDeal;
    /**
     * 计划主资源ID
     */
    @ApiModelProperty(value = "计划主资源ID")
    private String plannedMainResourceId;
    /**
     * 上次计划主资源ID
     */
    @ApiModelProperty(value = "上次计划主资源ID")
    private String lastMainResourceId;
    /**
     * 计划工具资源ID
     */
    @ApiModelProperty(value = "计划工具资源ID")
    private String plannedToolResourceId;
    /**
     * 上次计划工具资源ID
     */
    @ApiModelProperty(value = "上次计划工具资源ID")
    private String lastToolResourceId;
    /**
     * 计划技能ID
     */
    @ApiModelProperty(value = "计划技能ID")
    private String plannedSkillId;
    /**
     * 上次计划技能ID
     */
    @ApiModelProperty(value = "上次计划技能ID")
    private String lastSkillId;
    /**
     * 设置开始时刻
     */
    @ApiModelProperty(value = "设置开始时刻")
    private Date setupStartTime;
    /**
     * 上次设置开始时刻
     */
    @ApiModelProperty(value = "上次设置开始时刻")
    private Date lastSetupStartTime;
    /**
     * 设置结束时刻
     */
    @ApiModelProperty(value = "设置结束时刻")
    private Date setupEndTime;
    /**
     * 上次设置结束时刻
     */
    @ApiModelProperty(value = "上次设置结束时刻")
    private Date lastSetupEndTime;
    /**
     * 设置时间
     */
    @ApiModelProperty(value = "设置时间")
    private Integer setupDuration;
    /**
     * 制造开始时刻
     */
    @ApiModelProperty(value = "制造开始时刻")
    private Date productionStartTime;
    /**
     * 上次制造开始时刻
     */
    @ApiModelProperty(value = "上次制造开始时刻")
    private Date lastProductionStartTime;
    /**
     * 制造结束时刻
     */
    @ApiModelProperty(value = "制造结束时刻")
    private Date productionEndTime;
    /**
     * 上次制造结束时刻
     */
    @ApiModelProperty(value = "上次制造结束时刻")
    private Date lastProductionEndTime;
    /**
     * 制造时长
     */
    @ApiModelProperty(value = "制造时长")
    private Integer productionDuration;
    /**
     * 锁定开始时刻
     */
    @ApiModelProperty(value = "锁定开始时刻")
    private Date lockStartTime;
    /**
     * 上次锁定开始时刻
     */
    @ApiModelProperty(value = "上次锁定开始时刻")
    private Date lastLockStartTime;
    /**
     * 锁定结束时刻
     */
    @ApiModelProperty(value = "锁定结束时刻")
    private Date lockEndTime;
    /**
     * 上次锁定结束时刻
     */
    @ApiModelProperty(value = "上次锁定结束时刻")
    private Date lastLockEndTime;
    /**
     * 锁定时间
     */
    @ApiModelProperty(value = "锁定时间")
    private Integer lockDuration;
    /**
     * 清洗开始时刻
     */
    @ApiModelProperty(value = "清洗开始时刻")
    private Date cleanupStartTime;
    /**
     * 上次清洗开始时刻
     */
    @ApiModelProperty(value = "上次清洗开始时刻")
    private Date lastCleanupStartTime;
    /**
     * 清洗结束时刻
     */
    @ApiModelProperty(value = "清洗结束时刻")
    private Date cleanupEndTime;
    /**
     * 上次清洗结束时刻
     */
    @ApiModelProperty(value = "上次清洗结束时刻")
    private Date lastCleanupEndTime;
    /**
     * 清洗时间
     */
    @ApiModelProperty(value = "清洗时间")
    private Integer cleanupDuration;
    /**
     * 投料结束时间
     */
    @ApiModelProperty(value = "投料结束时间")
    private Date feedFinishTime;
    /**
     * 工序类型
     */
    @ApiModelProperty(value = "工序类型")
    private String operationType;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;

    /**
     * 是否本次算法生成
     */
    @FieldInterpretation("是否本次算法生成")
    private Boolean algo = false;
    
    @ExcelIgnore
    @ApiModelProperty(value = "行数")
    private Integer rowIndex;

    @ExcelIgnore
    @ApiModelProperty(value = "版本号")
    private Integer versionValue;
    
    
    
	
	//缺失字段
//	/**
//     * 未排程原因
//     */
//    private String unscheduledReason;
//    
//    /**
//     * 分割类别
//     */
//    private String partitionType;
//    
//    /**
//     * 制造开始时刻
//     */
//    private Date productionStartTime;
//    
//    /**
//     * 制造结束时刻
//     */
//    private Date productionEndTime;
//    
//    
//    /**
//     * 设置开始时刻
//     */
//    private Date setupStartTime;
//    
//    /**
//     * 设置结束时刻
//     */
//    private Date setupEndTime;
//    
//    /**
//     * 清洗开始时刻
//     */
//    private Date cleanupStartTime;
//    
//    /**
//     * 清洗结束时刻
//     */
//    private Date cleanupEndTime;
//    
//    /**
//     * 计划主资源ID
//     */
//    private String plannedMainResourceId;
//    
//    /**
//     * 计划工具资源ID
//     */
//    private String plannedToolResourceId;
//    
//    /**
//     * 制造时长
//     */
//    private Integer productionDuration;
//    
//    /**
//     * 设置时间
//     */
//    private Integer setupDuration;
//    
//    /**
//     * 清洗时间
//     */
//    private Integer cleanupDuration;
    
	
}
