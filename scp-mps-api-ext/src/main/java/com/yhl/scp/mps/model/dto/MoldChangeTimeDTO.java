package com.yhl.scp.mps.model.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MoldChangeTimeDTO</code>
 * <p>
 * 换模换型时间DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-13 15:18:04
 */
@ApiModel(value = "换模换型时间DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MoldChangeTimeDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 125698302953738080L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    private String stockPointName;
    /**
     * 工序代码
     */
    @ApiModelProperty(value = "工序代码")
    private String operationCode;
    /**
     * 工序名称
     */
    @ApiModelProperty(value = "工序名称")
    private String operationName;
    /**
     * 资源类型
     */
    @ApiModelProperty(value = "资源类型")
    private String resourceType;
    /**
     * 资源代码
     */
    @ApiModelProperty(value = "资源代码")
    private String resourceCode;
    /**
     * 资源名称
     */
    @ApiModelProperty(value = "资源名称")
    private String resourceName;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    private String productName;
    /**
     * 换模时间（分钟）
     */
    @ApiModelProperty(value = "换模时间（分钟）")
    private Long dieChangeTime;

    /**
     * 外换模时间（分钟）
     */
    @ApiModelProperty(value = "外换模时间（分钟）")
    private Long outsideDieChangeTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * kid
     */
    @ApiModelProperty(value = "kid")
    private String kid;

}
