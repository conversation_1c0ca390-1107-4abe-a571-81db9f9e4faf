package com.yhl.scp.mps.algorithm.schedule.input;

import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.mds.basic.resource.enums.ResourceTypeEnum;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mps.algorithm.fulfillment.RzzFulfillment;
import com.yhl.scp.mps.algorithm.input.pojo.Adjustment;
import com.yhl.scp.mps.coating.vo.MpsCoatingChangeTimeVO;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.productionCapacity.vo.LoadingPositionCapacityVO;
import com.yhl.scp.mps.productionCapacity.vo.ProductionCapacityVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 排程算法输入数据
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RzzBaseAlgorithmData {

    public static final Map<String, String> RESOURCE_TYPE_TRANS_MAP = new HashMap<String, String>() {
        private static final long serialVersionUID = 895340399768066852L;

        {
            put(ResourceTypeEnum.SINGLE.name(), "singleResources");
            put(ResourceTypeEnum.CONTINUOUS.name(), "continuousResources");
            put(ResourceTypeEnum.MULTIPLE.name(), "multipleResources");
            put(ResourceTypeEnum.FURNACE.name(), "furnaceResources");
        }
    };
    /**
     * 工序调整信息
     */
    List<Adjustment> adjustments;
    /**
     * 手工调整-数量只有数量发生了变化
     */
    Boolean onlyQtyChangeFlag;
    List<MoldChangeTimeVO> moldChangeTimeVOS;
    List<StandardStepVO> standardStepVOS;
    /**
     * 资源
     */
    private Map<String, List<RzzResourceData>> resourceMap;
    /**
     * 制造订单
     */
    private List<RzzWorkOrderData> workOrders;
    /**
     * 工序
     */
    private List<RzzOperationData> operations;
    /**
     * 物料
     */
    private List<RzzProductInSockingPointData> productInSockingPoints;
    /**
     * 供需关系
     */
    private List<RzzFulfillment> fulfillmentList;
    /**
     * 排程参数
     */
    private ScheduleParamDetail scheduleParamDetail;
    /**
     * 待排工序列表(operationsNeedSchedule)
     */
    private List<String> needScheduleOperationIds;
    /**
     * 记录物品对应的库存点编码和装车位置
     */
    private Map<String, String> productIdOfStockAndLoadingPositionMap;
    /**
     * 记录物品对应库存点编码
     */
    private Map<String, String> productIdOfStockMap;
    /**
     * 记录物品id对应code
     */
    private Map<String, String> productIdOfCodeMap;
    /**
     * 记录 库存点#装车位置#工序 对应的最大库容
     */
    private Map<String, ProductionCapacityVO> withLoadingPositionOfCapcityMap;
    /**
     * 记录 库存点#工序 对应的最大库容
     */
    private Map<String, ProductionCapacityVO> withNotLoadingPositionOfCapcityMap;
    /**
     * 记录 库存点#装车位置#工序 对应的最大库容以及实时库存（后工序库容限制）
     */
    private Map<String, LoadingPositionCapacityVO> loadingPositionOfCapcityMap;
    /**
     * 换模时间
     */
    private List<RzzMoldChangeTimeData> moldChangeTimeDataList;
    /**
     * 镀膜设备对应镀膜保养量
     */
    private Map<String, Pair<BigDecimal, String>> equipmentMaintenanceQuantityMap;
    /**
     * 连续炉开机规则限制资源
     */
    private List<String> lxResourceIds;
    /**
     * 镀膜切换时间
     */
    private List<MpsCoatingChangeTimeVO> coatingChangeTimeList;
    /**
     * 膜系数据字典
     */
    private Map<String, String> filmsCollectionValueMap;
    /**
     * 特殊工艺产能限制
     */
    private List<RzzSpecialLimitData> specialLimitDataList;
    /**
     * 自动排程烘弯物理资源id
     */
    private Set<String> dryBendingResourceIds;
    private Map<String, Pair<String, String>> ruleParams;
    /**
     * 安全库存水位
     */
    private Map<String, List<SafetyStockLevelVO>> stockLevelVOMap;
    /**
     * 延期调整建议对应子工序id
     */
    private List<String> delayAdjustOperationIds;
    /**
     * 压制台资源ids
     */
    private List<String> splitResourceIds;
    /**
     * 计划期间
     */
    private PlanningHorizonVO planningHorizon;
    /**
     * 生产反馈数据
     */
    private List<RzzOperationFeedbackVO> feedback;
    /**
     * 是否倒排
     */
    private Boolean direction = Boolean.TRUE;
    /**
     * 是否计划调整
     */
    private Boolean whetherAdjust = Boolean.FALSE;
    /**
     * 需要连续生产的工序id
     */
    private List<String> continuousProductionProcessIds;
    /**
     * 生产提前期数据
     */
    private Map<String, Pair<BigDecimal, BigDecimal>> productionLeadTimeMap;
    /**
     * 产品特殊工艺基础数据相关参数
     */
    private Map<String, String> membraneSystemMap;
    private Map<String, String> clampOfProductMap;
    private Map<String, String> itemFlagOfProductMap;
    private Map<String, String> attr1OfProductMap;
    private Map<String, String> lineGroupOfProductMap;
    private Map<String, String> hudOfProductMap;
    private Map<String, String> techonlogyTypeMap;
    // 压制台工具资源
    private Set<String> yztResourceIds;
    private List<String> yztResourceCodes;
    // 压制台换模时间(秒)
    private Integer yztSwitchSeconds;
    private Map<String, WorkOrderVO> workOrderVOMap;
    private List<OperationVO> sourceOperations;
    /**
     * 排产类型
     */
    private String scheduleType;
    /**
     * 压制台对应工具资源id
     */
    private List<String> yztResourceIdsList;
    /**
     * 压制台对应工具资源
     */
    private List<PhysicalResourceVO> yztPhysicalResourceVOS;
    /**
     * 工序对应优先级最高的资源id
     */
    private Map<String, String> operationOnResourceIdMap;

    private LocalDateTime abnormalTime;

    private Set<String> abnormalOperationIds;

    public boolean ruleEnable(String ruleName) {
        return this.getRuleParams().containsKey(ruleName)
                && YesOrNoEnum.YES.getCode().equals(this.getRuleParams().get(ruleName).getLeft());
    }

    public String ruleValue(String ruleName) {
        return this.getRuleParams().containsKey(ruleName)
                ? this.getRuleParams().get(ruleName).getRight()
                : null;
    }


}
