package com.yhl.scp.mps.plan.vo;

import com.yhl.scp.sds.extension.order.vo.OperationTaskVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * <code>MasterPlanTaskVO</code>
 * <p>
 * MasterPlanTaskVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/1/3 11:07
 */
@ApiModel(value = "VO")
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MasterPlanTaskVO extends OperationTaskVO {

    private static final long serialVersionUID = 4300512052995080341L;

    private String uuid;

    @ApiModelProperty("标准工艺id")
    private String standardStepId;

    @ApiModelProperty("标准工艺类型")
    private String standardStepType;

    @ApiModelProperty("标准工艺名称")
    private String standardStepName;

    @ApiModelProperty(value = "标准工艺代码")
    private String standardStepCode;

    @ApiModelProperty(value = "生产时间")
    private BigDecimal productionTime;

    @ApiModelProperty(value = "节拍")
    private BigDecimal unitHour;

    @ApiModelProperty(value = "箱子类型")
    private String boxType;

    @ApiModelProperty(value = "工序顺序号")
    private Integer routingStepNo;

    @ApiModelProperty(value = "物品代码")
    private String productStockPointCode;

    @ApiModelProperty(value = "工序代码")
    private String operationCode;

    @ApiModelProperty(value = "同产品第一工序任务标识")
    private Boolean firstInEachProduct;

    @ApiModelProperty(value = "成品代码")
    private String fgProductCode;

    @ApiModelProperty(value = "是否连续炉")
    private Boolean whetherHwResource;

    @ApiModelProperty(value = "模具数量限制")
    private Integer moldLimitQuantity;

    @ApiModelProperty(value = "预处理排产时间")
    private String preOperationTimeStr;

}