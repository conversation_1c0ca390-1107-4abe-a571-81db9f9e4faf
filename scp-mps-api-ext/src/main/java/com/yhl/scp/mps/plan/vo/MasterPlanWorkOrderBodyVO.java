package com.yhl.scp.mps.plan.vo;

import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO2;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MasterPlanWorkOrderBodyVO implements Serializable {

    private static final long serialVersionUID = 1643345530276043945L;
    /**
     * 制造订单交期
     */
    private Date dueDate;

    /**
     * 制造订单id
     */
    private String workOrderId;

    /**
     * 工序名称
     */
    private String operationName;

    /**
     * 工序代码
     */
    private Integer operationCode;

    /**
     * 工序id
     */
    private String operationId;

    /**
     * 标准工艺id
     */
    private String standardStepId;

    /**
     * 标准工艺代码
     */
    private String standardStepCode;

    /**
     * 标准工艺类型
     */
    private String standardStepType;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源代码
     */
    private String resourceCode;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 资源名称
     */
    private String toolResourceName;

    /**
     * 资源代码
     */
    private String toolResourceCode;

    /**
     * 资源id
     */
    private String toolResourceId;

    /**
     * 本厂编码
     */
    private String productCode;

    /**
     * 物料名称
     */
    private String productName;

    /**
     * 物品id
     */
    private String productId;

    /**
     * 工单号
     */
    private String workOrderNumber;

    /**
     * 顶层工单号
     */
    private String topWorkOrderNumber;

    /**
     * 排产数量
     */
    private String topWorkOrderQuantity;

    /**
     * 产品类型
     */
    private String productType;

    /**
     * 节拍
     */
    private String beat;

    /**
     * 换模时间
     */
    private String moldChangeTime;

    /**
     * 包装方式
     */
    private String packagingMethod;

    /**
     * 风栅类型
     */
    private String grilleType;

    /**
     * 生产模式
     */
    private String productionMode;

    /**
     * 预处理
     */
    private String preTreatment;

    /**
     * 印刷
     */
    private String printing;

    /**
     * 包装
     */
    private String postPackaging;

    /**
     * 合片
     */
    private String postLamination;

    /**
     * 成型
     */
    private String suppress;

    /**
     * 合计
     */
    private String totalTime;

    /**
     * hud
     */
    private String hud;

    /**
     * 加丝
     */
    private String wired;

    /**
     * 宽
     */
    private String weight;

    /**
     * 长
     */
    private String length;

    /**
     * 颜色
     */
    private String color;

    /**
     * 烘弯排序每组颜色
     */
    private String hwGroupColor;

    /**
     * 加热线
     */
    private String heaterWire;

    /**
     * 排产数量
     */
    private String plannedQuantity;

    /**
     * 父工序排产数量
     */
    private String parentPlannedQuantity;

    /**
     * 报工数量
     */
    private String reportingQuantity;

    /**
     * 排产日期
     */
    private String planDate;

    private Date planDateTime;

    /**
     * 排产结束日期
     */
    private String planEndDate;

    private Date planEndDateTime;

    /**
     * 生产时间
     */
    private String productionTime;

    /**
     * 是否延期
     */
    private String delay;

    /**
     * 工单状态
     */
    private String workOrderStatus;

    /**
     * 齐套状态
     */
    private String kitStatus;
    private String kitStatusCode;

    private String planStatus;

    /**
     * 成套数量
     */
    private BigDecimal completeSetQuantity;

    /**
     * 综合成品率
     */
    private BigDecimal comprehensiveYield;

    /**
     * 动态表头
     */
    private List<String> header;

    /**
     * 动态内容，用于覆盖发货计划
     */
    private List<Map<String, Object>> dynamicData;

    /**
     * 异常告警颜色，红色，黄色
     */
    private String errorColour;

    /**
     * 半成品库存
     */
    private String semiFinishInventory;

    /**
     * 成品库存
     */
    private String finishInventory;

    /**
     * 工序任务编码
     */
    private String operationStepCode;

    /**
     * 成品代码
     */
    private String parentProductCode;

    /**
     * 风险等级
     */
    private String partRiskLevel;

    /**
     * 单箱片数
     */
    private Integer piecePerBox;

    /**
     * 同产品第一工序任务标识
     */
    private Boolean firstInEachProduct;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 是否连续炉资源
     */
    private Boolean whetherHwResource;

    /**
     * 模具数量限制
     */
    private Integer moldLimitQuantity;

    /**
     * 计划单号
     */
    private String planNo;

    /**
     * 工单号
     */
    private String erpOrderNo;

    private OperationVO operationVO;

    /**
     * 试制单号
     */
    private String testOrderNumber;

    /**
     * 未计划
     */
    private Boolean unPlan = false;

    /**
     * 异常班次
     */
    private Boolean abnormalShift = false;

    /**
     * 空闲班次
     */
    private Boolean freeShift = false;

    /**
     * 制造订单下发状态
     */
    private String issuedStatus;

    /**
     * 工序任务ID
     */
    private String operationTaskId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 固化时间
     */
    private BigDecimal curingTime;

    /**
     * 当月预测剩余量
     */
    private BigDecimal currentMonthForecastRemainQuantity;

    /**
     * 下月预测量
     */
    private BigDecimal nextMonthForecastQuantity;
    /**
     * 最小安全库存天数
     */
    private BigDecimal minStockDay;

    /**
     * 预处理生产时间
     */
    private String preOperationStr;

    /**
     * 工装大类
     */
    private String toolingCategory;

    /**
     * 变更通知标识
     */
    private String changeFlag;

    private List<DeliveryPlanPublishedCompareDayVO2> compareDayVO2List;

    /**
     * 发货计划组
     */
    private Map<String, List<DeliveryPlanVO2>> deliveryPlanGroup;

}