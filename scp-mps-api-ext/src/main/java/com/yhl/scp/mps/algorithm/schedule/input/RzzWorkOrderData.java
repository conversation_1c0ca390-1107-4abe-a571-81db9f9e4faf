package com.yhl.scp.mps.algorithm.schedule.input;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * <code>WorkOrder</code>
 * <p>
 * 制造订单
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-20 09:54:08
 */
@Data
public class RzzWorkOrderData implements Serializable {
	
    private static final long serialVersionUID = -5917840557438598617L;

    /**
     * 订单号
     */
    private String id;
    /**
     * 库存点ID
     */
    private String stockPointId;
    /**
     * 产品ID
     */
    private String productId;
    /**
     * 交期
     * "YYYY-MM-DD hh:mm:ss"
     */
    private Date dueDate;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 优先级/默认值越小越优先
     */
    private Integer priority;
    /**
     * 期末库存最小安全库存差值
     */
    private Integer endingInventoryMinSafeDiff;
    
    /**
     * 接单日期
     * "YYYY-MM-DD"
     */
    private Date receiveDate;
    
    /**
     * 最早开始时间
     * "YYYY-MM-DD hh:mm:ss"
     */
    private Date earliestBeginTime;
    
    /**
     * 制造订单规格列表 com.yhl.aps.api.obj.Spec
     */
    private List<Map<String, Object>> spec;


    /**
     * 顶层制造订单列表
     */

    private List<RzzWorkOrderData> topWorkOrderList;

}
