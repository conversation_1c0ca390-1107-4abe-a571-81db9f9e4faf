package com.yhl.scp.mps.dispatch.ams.input;

import com.yhl.scp.mps.dispatch.enums.ProductClassificationEnum;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class ProductInSockingPointJsonData {

    private static final long serialVersionUID = 5683235671591367004L;

    protected static final Map<String, String> PRODUCT_TYPE_TRANS_MAP = new HashMap<String, String>() {
        /**
         *
         */
        private static final long serialVersionUID = -2523409121086119806L;

        {
            put(ProductClassificationEnum.FINISHED.name(), "FinishedProduct");
            put(ProductClassificationEnum.SEMI_FINISHED.name(), "SemiFinishedProduct");
            put(ProductClassificationEnum.INTERMEDIATE.name(), "IntermediateProduct");
            put(ProductClassificationEnum.MATERIAL.name(), "RawMaterial");
        }
    };

    private String stockingPointId;

    private String productId;

    private String productCode;

    private String productName;

    private String productSeries;

    /**
     * 成品/半成品/中间品/原材料 FinishedProduct/SemiFinishedProduct/IntermediateProduct/RawMaterial
     */
    private String type;

    private Integer priority;

    /**
     * 规格信息<numSpec/strSpec> 数字规则/字符规则
     */
    private List<Map<String, Map<String, Object>>> spec;


    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getStockingPointId() {
        return stockingPointId;
    }

    public void setStockingPointId(String stockingPointId) {
        this.stockingPointId = stockingPointId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductSeries() {
        return productSeries;
    }

    public void setProductSeries(String productSeries) {
        this.productSeries = productSeries;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public List<Map<String, Map<String, Object>>> getSpec() {
        return spec;
    }

    public void setSpec(List<Map<String, Map<String, Object>>> spec) {
        this.spec = spec;
    }


}
