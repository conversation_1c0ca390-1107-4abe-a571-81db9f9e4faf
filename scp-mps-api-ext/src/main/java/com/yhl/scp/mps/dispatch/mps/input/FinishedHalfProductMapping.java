package com.yhl.scp.mps.dispatch.mps.input;

import lombok.*;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * <code>FinishedHalfProductMapping</code>
 * <p>
 * 成品/半品 库存映射关系
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-15 16:33:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FinishedHalfProductMapping {

    /**
     * 成品id
     */
    private String finishedProductId;

    /**
     * 成品code
     */
    private String finishedProductCode;

    /**
     * 半品id
     */
    private String halfProductId;

    /**
     * 半品code
     */
    private String halfProductCode;

    /**
     * 库存数量
     */
    private BigDecimal stockQty;

    /**
     * 库存差值
     */
    private BigDecimal stockQtyDifference;

    /**
     * 是否解析主物料
     */
    private String whetherMainMaterial;

}
