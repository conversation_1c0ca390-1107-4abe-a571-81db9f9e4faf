spring:
  application:
    name: scp-gateway-service
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
    web-application-type: reactive
  cloud:
#    gateway:
#      default-filters:
#        - name: Retry
#          args:
#            retries: 0
    loadbalancer:
      retry:
        enabled: false
        retryOnAllOperations: false
        #maxRetriesOnSameServiceInstance: 0
        #maxRetriesOnNextServiceInstance: 3
logging:
  level:
    org.springframework.cloud.gateway: DEBUG        # 网关核心日志
    org.springframework.cloud.loadbalancer: DEBUG   # 负载均衡日志
    org.springframework.web.reactive: DEBUG         # WebFlux日志
    reactor.netty: DEBUG                           # Netty日志(网关底层)
    root: INFO
