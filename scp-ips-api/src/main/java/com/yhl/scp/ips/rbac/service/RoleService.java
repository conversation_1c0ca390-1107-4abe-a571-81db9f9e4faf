package com.yhl.scp.ips.rbac.service;

import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.rbac.entity.Role;

import java.util.List;

/**
 * <code>RoleService</code>
 * <p>
 * RoleService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-20 16:09:16
 */
public interface RoleService {

	BaseResponse doCreate(Role role);
	
	BaseResponse doUpdate(Role role);

	List<Role> selectPage(Pagination pagination, String sortParam, String queryCriteriaParam);

	void doDelete(String roleId);

	void doCreateRoleResources(String roleId, List<String> resourceIdList);

	List<Role> selectRoles();
	
	Role getById(String roleId);

	List<Role> selectByIds(List<String> roleIds);
	
	List<Role> selectByUserId(String userId);

    List<Role> selectByName(String name);

	boolean whetherOpenWorkBench();

}
