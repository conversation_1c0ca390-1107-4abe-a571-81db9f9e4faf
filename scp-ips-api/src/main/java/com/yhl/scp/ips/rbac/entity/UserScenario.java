package com.yhl.scp.ips.rbac.entity;

import java.io.Serializable;

/**
 * <code>UserScenario</code>
 * <p>
 * 用户场景
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 18:34:25
 */
public class UserScenario implements Serializable {

    private static final long serialVersionUID = -358843641623612340L;

    private String userId;

    private String scenarioId;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getScenarioId() {
        return scenarioId;
    }

    public void setScenarioId(String scenarioId) {
        this.scenarioId = scenarioId;
    }
}
