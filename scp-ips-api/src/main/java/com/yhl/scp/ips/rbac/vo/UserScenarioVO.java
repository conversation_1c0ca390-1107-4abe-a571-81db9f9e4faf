package com.yhl.scp.ips.rbac.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>UserScenarioVO</code>
 * <p>
 * 用户场景VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 18:49:08
 */
@ApiModel("用户场景VO")
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class UserScenarioVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 9174852776993158840L;

    @FieldInterpretation(value = "用户ID")
    @ApiModelProperty(value = "用户ID")
    private String userId;

    @FieldInterpretation(value = "场景ID")
    @ApiModelProperty(value = "场景ID")
    private String scenarioId;

    @FieldInterpretation(value = "场景名称")
    @ApiModelProperty(value = "场景名称")
    private String scenarioName;

    @FieldInterpretation(value = "模块代码")
    @ApiModelProperty(value = "模块代码")
    private String moduleCode;

    @FieldInterpretation(value = "数据库名")
    @ApiModelProperty(value = "数据库名")
    private String dataBaseName;

    @Override
    public void clean() {

    }
}
