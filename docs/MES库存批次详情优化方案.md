# MES库存批次详情接口优化方案

## 背景
MES系统的库存数据只在每天早上8点和下午3点进行更新，更新时间约20分钟。其他时间调用MES接口获取的都是快照数据，存在性能浪费。

## 优化策略

### 1. 智能缓存机制
- **缓存时机**: 在MES数据更新完成后（8:20和15:20）自动缓存数据
- **缓存有效期**: 25小时，确保覆盖到下次更新
- **版本控制**: 使用数据版本号确保缓存一致性

### 2. 定时任务预加载
- **执行时间**: 每天8:20和15:20
- **任务内容**: 自动调用MES接口并缓存最新数据
- **框架**: 使用xxl-job进行任务调度

### 3. 智能接口选择
- **更新时间段内**: 实时调用MES接口
- **非更新时间段**: 使用缓存数据
- **降级策略**: 缓存失效时自动调用真实接口

## 实现细节

### 1. 定时任务配置

#### xxl-job任务配置
```
任务名称: MES库存批次详情同步
JobHandler: mesInventoryBatchDetailJobHandler
调度类型: CRON
Cron表达式: 0 20 8,15 * * ?
描述: 每天8:20和15:20执行MES库存数据同步
```

#### 任务参数说明
- 无需额外参数，任务会自动获取所有需要同步的租户配置
- 支持多租户并发同步
- 自动记录同步日志和状态

### 2. 缓存策略

#### Redis缓存键设计
```
mes:inventory:batch:detail:{plantId}     # 库存数据缓存
mes:inventory:data:version               # 全局数据版本号
mes:inventory:cache:update:{tenantCode}  # 租户更新状态
```

#### 数据版本号格式
```
yyyyMMdd_HH
例如: 20241015_08 (2024年10月15日上午8点版本)
     20241015_15 (2024年10月15日下午3点版本)
```

### 3. 接口优化

#### 新增智能接口
```
GET /inventoryBatchDetail/smart?stockPoint={stockPoint}
```

#### 接口逻辑
1. 检查当前时间是否在MES更新时间段
2. 更新时间段内：实时同步数据
3. 非更新时间段：使用缓存数据
4. 缓存失效：自动降级到实时接口

### 4. 时间判断逻辑

#### MES数据更新时间窗口
- **上午**: 7:50-8:20 (提前10分钟开始，确保及时更新)
- **下午**: 14:50-15:20 (提前10分钟开始，确保及时更新)

#### 缓存使用策略
- **8:20-14:50**: 使用上午8点版本数据
- **15:20-次日7:50**: 使用下午3点版本数据
- **更新时间段**: 不使用缓存，实时调用接口

## 性能提升效果

### 1. 响应时间优化
- **缓存命中**: 响应时间从秒级降到毫秒级
- **减少网络调用**: 避免不必要的MES接口调用
- **并发处理**: 支持高并发访问缓存数据

### 2. 系统资源节约
- **减少MES系统负载**: 每天只在必要时调用2次
- **降低网络带宽**: 大幅减少数据传输
- **提高系统稳定性**: 减少外部依赖调用

### 3. 用户体验提升
- **快速响应**: 大部分时间都能快速获得数据
- **数据一致性**: 通过版本控制确保数据准确性
- **智能降级**: 异常情况下自动切换到实时接口

## 部署步骤

### 1. 代码部署
1. 部署优化后的MesInventoryBatchDetailHandler
2. 部署新的定时任务MesInventoryBatchDetailJobHandler
3. 部署优化后的InventoryBatchDetailController

### 2. xxl-job配置
1. 登录xxl-job管理后台
2. 创建新的定时任务
3. 配置Cron表达式: `0 20 8,15 * * ?`
4. 设置JobHandler: `mesInventoryBatchDetailJobHandler`
5. 启动任务

### 3. Redis配置
确保Redis服务正常运行，支持缓存功能

### 4. 监控配置
1. 监控定时任务执行状态
2. 监控缓存命中率
3. 监控接口响应时间

## 注意事项

### 1. 时区设置
确保服务器时区设置正确，避免时间判断错误

### 2. Redis高可用
建议配置Redis集群或主从，确保缓存服务稳定

### 3. 异常处理
- 定时任务执行失败时会记录日志
- 缓存失效时自动降级到实时接口
- 支持手动触发同步功能

### 4. 数据一致性
- 通过版本号控制确保数据一致性
- 支持强制刷新缓存功能
- 定时任务失败时不影响实时接口调用

## 监控指标

### 1. 性能指标
- 接口平均响应时间
- 缓存命中率
- 定时任务执行成功率

### 2. 业务指标
- 数据同步成功率
- 数据版本一致性
- 用户访问满意度

### 3. 系统指标
- MES接口调用次数减少比例
- 系统资源使用率
- 错误率和异常情况