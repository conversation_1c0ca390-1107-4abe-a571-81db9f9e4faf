package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordDTO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * description: 获取mes系统的收发货记录 插入发售或记录日志表
 * author：李杰
 * email: <EMAIL>
 * date: 2024/10/31
 */
@Component
@Slf4j
public class WareHouseReleaseHandler extends SyncDataHandler<List<WarehouseReleaseRecordDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<WarehouseReleaseRecordDTO> warehouseReleaseRecordDTOS) {
        Date lastUpdateDate = warehouseReleaseRecordDTOS.stream().map(WarehouseReleaseRecordDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES收发货记录数据:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            //获取MES的token
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            //mes收发货记录地址接口
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            //获取params参数
            String apiParams = apiConfigVO.getApiParams();
//            String url = apiUri + "/" + systemNumber + "/" + this.sequenceService.getSuffix(systemNumber, getCommand(), 5) + "?serverId=" + apiParams;
            String url = apiUri + "?serverId=" + apiParams;
            Date beginDate;
            Date currentDate;
            if (Objects.nonNull(params.get("triggerType"))) {
                //由于MES设置前后时间不可超过七天，所以将时间分段调用
                beginDate = DateUtils.stringToDate(params.get("lastUpdateDate").toString(), DateUtils.COMMON_DATE_STR1);
                currentDate = DateUtils.stringToDate(params.get("endDate").toString(), DateUtils.COMMON_DATE_STR1);
            } else {
                beginDate = DateUtils.stringToDate(this.getSyncRefValue(apiConfigVO, params), DateUtils.COMMON_DATE_STR1);
                currentDate = new Date();
            }
            if (beginDate.compareTo(currentDate) == 1) {
                log.error("时间错误，lastUpdateDate不能大于endDate");
                return null;
            }
            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},url={},lastUpdateDateStr={},currentDate={}", mesToken, apiUri, apiParams,
                        url, beginDate, currentDate);
            }
            //请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            //请求体
            int size = Objects.isNull(apiConfigVO.getOffsetSize()) ? 5000 : apiConfigVO.getOffsetSize();
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("reqCode", "FY_PKN_SHIPPINGLIST_FOR_BPIM");
            paramMap.put("pageSize", size);

            int period = (int) DateUtil.between(beginDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            List<Object> arrayList = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                for (int page = 1; ; page++) {
                    paramMap.put("currentPage", page);
                    paramMap.put("beginTime", DateUtils.dateToString(beginDate, DateUtils.COMMON_DATE_STR1));
                    Date endDate = org.apache.commons.lang3.time.DateUtils.addDays(beginDate, calculatePeriod);
                    if (endDate.compareTo(currentDate) == 1) {
                        endDate = currentDate;
                    }
                    paramMap.put("endTime", DateUtils.dateToString(endDate, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() == statusCodeValue) {
                        String body = responseEntity.getBody();
                        log.info("同步MES运输数据完成,返回数据:{}!", body);
                        MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                        MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                        extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                        if (Objects.nonNull(data)) {
                            arrayList.addAll(data.getMessage());
                            if (data.getCurrentPage() >= data.getTotalPage()) {
                                beginDate = endDate;
                                break;
                            }
                        }
                    } else {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                }
            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, arrayList.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSONObject.toJSONString(arrayList);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<WarehouseReleaseRecordDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        return JSONObject.parseArray(body, WarehouseReleaseRecordDTO.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> paramMap, List<WarehouseReleaseRecordDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, paramMap);
        if (CollectionUtils.isEmpty(list)) {
            log.error("MES获取仓库收发货记录数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        list.stream().forEach(x -> {
                    x.setIsReceive(StringUtils.isEmpty(x.getIsReceive()) ? "N" : x.getIsReceive());
                    x.setSourceType("MES");
                }
        );

        dfpFeign.syncWareHouseReleaseData(scenario, list);

        if (Objects.isNull(paramMap.get("triggerType"))) {
            this.saveSyncCtrl(apiConfigVO, paramMap, list);
        }
        return "同步成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.WAREHOUSE_RELEASE.getCode());
    }
}
