package com.yhl.scp.dcp.externalApi.handler.grp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.grp.GrpPartMappingData;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mdm.MdmResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * description:获取GRP零件映射
 * author：李杰
 * email: <EMAIL>
 * date: 2024/10/22
 */
@Slf4j
@Component("partRelationMappingHandler4GRP")
public class PartRelationMappingHandler extends SyncDataHandler<List<PartRelationMapVO>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("organizeId");
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<PartRelationMapVO> partRelationMapVOS) {
        Date lastUpdateDate = partRelationMapVOS.stream().map(PartRelationMapVO::getModifyTime).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步GRP零件映射:{},{}", apiConfigVO, params);
        }
        try {
        //获取ERP的token
        String token = authHandler.handle(MapUtil.newHashMap());
        //erp地址接口
        String apiUri = apiConfigVO.getApiUri();
        //管理员编码
        String systemNumber = apiConfigVO.getSystemNumber();
        //获取流水号
        String flowNumber = sequenceService.getSuffix(systemNumber, getCommand(), 6);
        String url = apiUri + "?token=" + token;
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String txDate = format.format(new Date());
        HashMap<String, Object> map = new HashMap<>();
        map.put("serialNo", flowNumber);
        map.put("txDate", txDate);
        map.put("lang", "zh_CN");
        String lastUpdateStr ;
        if (Objects.nonNull(params.get("triggerType"))){
            lastUpdateStr = params.get("beginTime").toString();
        }else{
            lastUpdateStr = this.getSyncRefValue(apiConfigVO,params);
        }
        //orgId和ouId  从mds_stock_point取  324代表上海轿车
        Integer orgId = Integer.parseInt( params.get("organizeId").toString());
        GrpPartMappingData grpParamsData = GrpPartMappingData
                .builder()
                .lastUpdateDate(lastUpdateStr)
                .orgId(orgId)
                .ouId(orgId).build();
        map.put("data", grpParamsData);
        String bodyStr = JSONObject.toJSONString(map);
        if (log.isInfoEnabled()) {
            log.info("token={},apiUri={},systemNumber={},flowNumber={},url={},bodyStr={}", token, apiUri
                    , systemNumber, flowNumber, url, bodyStr);
        }
        HttpEntity httpEntity = new HttpEntity(bodyStr, httpHeaders);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
        int statusCodeValue = responseEntity.getStatusCodeValue();
        if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
            throw new BusinessException(StrUtil.format("同步GRP的EDI装车预测失败,HTTP状态码:{}!", statusCodeValue));
        }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
        String body = responseEntity.getBody();
        log.info("同步GRP零件映射完成,返回数据:{}!", body);
        return body;
        }catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<PartRelationMapVO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("GRP零件映射数据为空！");
            return null;
        }
        MdmResponse object1 = JSONObject.parseObject(body, MdmResponse.class);
        if (!object1.getSuccess()){
            log.error("GRP获取零件映射失败！");
            return null;
        }
        Object data = object1.getData();
        String jsonString = JSONObject.toJSONString(data);
        List<PartRelationMapVO> list = JSONObject.parseArray(jsonString, PartRelationMapVO.class);
        //GRP数据
        list.stream().forEach(x -> x.setSourceType("GRP"));
        return list;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<PartRelationMapVO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("GRP获取零件映射数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        //grp零件映射
        dfpFeign.updatePartMappingData(scenario, list);
        if (Objects.isNull(params.get("triggerType"))){
            this.saveSyncCtrl(apiConfigVO, params, list);
        }
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.GRP.getCode(), ApiCategoryEnum.PART_MAPPING.getCode());
    }
}
