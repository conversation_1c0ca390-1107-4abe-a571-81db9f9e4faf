package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.bom.dto.ProductAboutBomDTO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * description: 接口文档bom 实际对应 的bom数据输入物品
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/12
 */
@Component
@Slf4j
public class RoutingStepInputHandler extends SyncDataHandler<List<ProductAboutBomDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ProductAboutBomDTO> productAboutBomDTOS) {
        Date lastUpdateDate = productAboutBomDTOS.stream().map(ProductAboutBomDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }


    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("organizationCode");
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP的bom数据:{},{}", apiConfigVO, params);
        }
        try {
            //获取ERP的token
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri
                    + "?token=" + erpToken + "&organizationCode=" + params.get("organizationCode");
            //判断是手动还是定时
            String beginDate;
            String currentDate;
            if (Objects.nonNull(params.get("triggerType"))) {
                //由于MES设置前后时间不可超过七天，所以将时间分段调用
                beginDate = params.get("lastUpdateDate").toString();
                currentDate = params.get("endDate").toString();
            } else {
                beginDate = this.getSyncRefValue(apiConfigVO, params);
                currentDate = DateUtils.dateToString(new Date(),DateUtils.COMMON_DATE_STR1);
            }
            url = url + "&lastUpdateDate=" + beginDate + "&endDate=" + currentDate;
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},systemNumber={},lastUpdateDate={},endDate={},url={}", erpToken, apiUri
                        , systemNumber, beginDate, currentDate, url);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步ERP的bom数据失败,HTTP状态码:{}!", statusCodeValue));
            }
            ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
            if (log.isInfoEnabled()) {
                log.info("同步ERP的bom数据完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
            }
            if (erpResponse.getSuccess()) {
                extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
                return JSONObject.toJSONString(erpResponse.getData());
            } else {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                log.error("同步ERP的bom数据报错，{}!", erpResponse.getMessage());
                return null;
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<ProductAboutBomDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<ProductAboutBomDTO> routingStepInputSyncDTOList = JSONObject.parseArray(body, ProductAboutBomDTO.class);
        return routingStepInputSyncDTOList;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ProductAboutBomDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("ERP获取的bom数据输入物品数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();

        mdsFeign.syncProductAboutBomData(scenario, list);
        if (Objects.isNull(params.get("triggerType"))){
            this.saveSyncCtrl(apiConfigVO, params, list);
        }
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.ROUTING_STEP_INPUT.getCode());
    }
}
