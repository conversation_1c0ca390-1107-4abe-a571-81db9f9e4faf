package com.yhl.scp.dcp.oauth.controller;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderApiDTO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <code>TestController</code>
 * <p>
 * TestController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-24 11:51:09
 */
@RestController
@RequestMapping("/")
@Slf4j
public class TestController {

    @Autowired
    private PasswordEncoder passwordEncoder;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @GetMapping("openapi/private")
    // @PreAuthorize("hasRole('USER')")
    public ResponseEntity<String> test() {
        return ResponseEntity.ok("Protected Resource");
    }

    @GetMapping("public")
    public ResponseEntity<String> test1() {
        log.info("passwordEncoder0:{}", passwordEncoder.encode("123456"));
        PasswordEncoder passwordEncoder1 =  new BCryptPasswordEncoder();
        String encode = passwordEncoder1.encode("654321");
        log.info("passwordEncoder1:{}", encode);
        return ResponseEntity.ok("Granted Resource");
    }
   @PostMapping("openapi/dockingOrder/feedback")
   public ResponseEntity<String> feedback(@RequestBody DeliveryDockingOrderApiDTO deliveryDockingOrderApiDTO) {
       log.info("feedback payload:{}", deliveryDockingOrderApiDTO);
       BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.DFP.getCode(),
               TenantCodeEnum.FYQB.getCode());
       dfpFeign.dockingOrderFeedback(mdsScenario.getData(), deliveryDockingOrderApiDTO);
       return ResponseEntity.ok("Granted Resource");
   }
}
