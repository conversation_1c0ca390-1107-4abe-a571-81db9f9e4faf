package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.plan.dto.MasterPlanRelationLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * description: 同步ERP的工单号
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/13
 */
@Component
@Slf4j
public class MasterPlanRelationHandler extends SyncDataHandler<List<MasterPlanRelationLogDTO>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("organizationId");
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MasterPlanRelationLogDTO> masterPlanRelationLogDTOS) {
        Date lastUpdateDate = masterPlanRelationLogDTOS.stream().map(MasterPlanRelationLogDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR3);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP的工单号:{},{}", apiConfigVO, params);
        }
        String lastUpdateStr = this.getSyncRefValue(apiConfigVO, params);
        if (lastUpdateStr.length() > 10){
            Date date = DateUtils.stringToDate(lastUpdateStr);
            lastUpdateStr = DateUtils.dateToString(date,DateUtils.COMMON_DATE_STR3);
        }
        try {
            //获取ERP的token
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            //工单号地址接口
            String apiUri = apiConfigVO.getApiUri() + "?token=" + erpToken;
            //组装回传后的数据
            List<Object> arrayList = new ArrayList<>();
            //每条数据需要传的参数集合
            HashMap<String, Object> map = MapUtil.newHashMap();
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            //请求体筛入数据
            map.put("orgId", Integer.parseInt(params.get("organizationId").toString()));
            map.put("lastUpdateDate", lastUpdateStr);
            String bodyStr = JSONObject.toJSONString(map);
            if (log.isInfoEnabled()) {
                log.info("该条同步ERP的工单号参数,bodyStr={}，apiUri={}", bodyStr, apiUri);
            }
            HttpEntity httpEntity = new HttpEntity(bodyStr, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(apiUri, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                throw new BusinessException(StrUtil.format("同步ERP的工单号失败,HTTP状态码:{}!", statusCodeValue));
            }
            ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
            if (log.isInfoEnabled()) {
                log.info("同步ERP的工单号完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
            }
            if (erpResponse.getSuccess()) {
                Object data = erpResponse.getData();
                List<Object> objects = JSONObject.parseArray(JSONObject.toJSONString(data), Object.class);
                extApiLogService.updateResponse(mainLog, null, 1, DcpConstants.TASKS_STATUS_SUCCESS);
                return JSONObject.toJSONString(objects);
            } else {
                log.error("同步ERP的工单号报错，{}!", erpResponse.getMessage());
                throw new BusinessException(StrUtil.format("同步ERP的工单号报错:{}!", erpResponse.getMessage()));
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw new BusinessException(StrUtil.format("组织参数为{}以及时间参数为{}的同步ERP的工单号报错:{}!",params.get("organizationId"), lastUpdateStr, e.getMessage()));
        }
    }

    @Override
    protected List<MasterPlanRelationLogDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<MasterPlanRelationLogDTO> routingStepInputSyncDTOList = JSONObject.parseArray(body, MasterPlanRelationLogDTO.class);
        return routingStepInputSyncDTOList;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MasterPlanRelationLogDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("同步ERP的工单号数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MPS.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        mpsFeign.syncMasterPlanRelation(scenario, list);
        this.saveSyncCtrl(apiConfigVO, params, list);
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.MASTER_PLAN_RELATION.getCode());
    }
}
