package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPoCreate;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.AbstractHandler;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightShippedMapVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <code>PoCreateHandler</code>
 * <p>
 * 采购PO创建
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-26 11:10:53
 */
@Component
@Slf4j
public class PoCreateHandler extends AbstractHandler<List<ErpPoCreate>> {
    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private AuthHandler authHandler;

    @Resource
    private ExtApiLogService extApiLogService;

    private String logId;

    private String type;

    @Override
    protected List<ErpPoCreate> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("ERP获取PO创建为空");
            return null;
        }
        return JSON.parseArray(body, ErpPoCreate.class);
    }


    /**
     * 处理报文体
     *
     * @param apiConfigVO
     * @param params
     * @param o
     * @return
     */
    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                List<ErpPoCreate> o) {
        if (CollectionUtils.isEmpty(o)) {
            log.error("PO创建为空");
            return this.logId;
        }

        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        log.info("开始创建PO");
        if (type.equals(ObjectTypeEnum.MATERIAL_INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL.getCode())) {
            mrpFeign.handlePoCreate(scenario.getData(), o);
        } else {
            mrpFeign.handlePoCreateQuay(scenario.getData(), o);
        }

        log.info("结束创建PO");
        // 保存同步控制信息
        return this.logId;
    }

    /**
     * 调用后台请求
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("开始同步ERP_PO创建:{},{}", apiConfigVO, params);
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String jsonString = params.get("createList").toString();
            List<ErpPoCreate> createList = JSON.parseArray(jsonString,
                    ErpPoCreate.class);
            List<ErpPoCreate> filteredList = createList.stream()
                    .collect(Collectors.toMap(
                            vo -> vo.getBpimStockPointCode() + "-" + vo.getBpimContainerNumber() + "-" + vo.getBuyer(),
                            vo -> vo,
                            (existing, replacement) -> existing // 保留第一个出现的值
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            type = params.get("type").toString();
            String url = apiUri + "?token=" + erpToken;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            List<ErpPoCreate> result = Lists.newArrayList();
            int poolSize = Math.min(filteredList.size(), 10); // 最大并发数不超过 10
            ExecutorService executor = Executors.newFixedThreadPool(poolSize);
            List<CompletableFuture<InventoryOceanFreightShippedMapVO>> futures = new ArrayList<>();

            for (ErpPoCreate vo : filteredList) {
                String key = vo.getBpimStockPointCode() + "-" + vo.getBpimContainerNumber() + "-" + vo.getBuyer();
                List<ErpPoCreate> detailVosSourceList = createList.stream()
                        .filter(detailVO -> (detailVO.getBpimStockPointCode() + "-" + detailVO.getBpimContainerNumber() + "-" + detailVO.getBuyer()).equals(key))
                        .collect(Collectors.toList());
                List<ErpPoCreate.PoLines> lineList = new ArrayList<>();
                for (ErpPoCreate detailVosSource : detailVosSourceList) {
                    List<ErpPoCreate.PoLines> lines = detailVosSource.getLines();
                    for (ErpPoCreate.PoLines poLines : lines) {
                        poLines.setNote(detailVosSource.getBpimId());
                    }
                    lineList.addAll(lines);
                }
                vo.setLines(lineList);
                if (log.isInfoEnabled()) {
                    log.info("erpToken={},apiUri={},url={},bodyStr={}", erpToken, apiUri
                            ,  url, params);
                }

                // 打印请求体内容
                System.out.println("JSON Request: " + JSON.toJSONString(vo));
                HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(vo), httpHeaders);
                CompletableFuture<InventoryOceanFreightShippedMapVO> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                        int statusCodeValue = responseEntity.getStatusCodeValue();
                        if (HttpStatus.OK.value() != statusCodeValue) {
                            extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                            throw new BusinessException(StrUtil.format("上传ERP_PO创建请求失败,HTTP状态码:{}!", statusCodeValue));
                        }
                        System.out.println("PO创建 JSON response: " + JSON.toJSONString(responseEntity));
                        String body = responseEntity.getBody();
                        ErpResponse erpResponse = JSON.parseObject(body, ErpResponse.class);
                        if (erpResponse.getSuccess()) {
                            Object data = erpResponse.getData();
                            ErpPoCreate erpPoCreates = JSON.parseObject(data.toString(), ErpPoCreate.class);
                            for (ErpPoCreate detailVosSource : detailVosSourceList) {
                                detailVosSource.setPoNo(erpPoCreates.getPoNo());
                                List<ErpPoCreate.PoLines> lines = erpPoCreates.getLines();
                                List<ErpPoCreate.PoLines> linesDetail = detailVosSource.getLines();
                                for (ErpPoCreate.PoLines poLines : lines) {
                                    if (detailVosSource.getBpimId().equals(poLines.getNote())) {
                                        for (ErpPoCreate.PoLines poLinesDetail : linesDetail) {
                                            poLinesDetail.setLineNum(poLines.getLineNum());
                                        }
                                    }

                                }

                                result.add(detailVosSource);
                                mainLog.setApplyCount(mainLog.getApplyCount() == null ? 1 : mainLog.getApplyCount() + 1);
                            }
                        } else {
                            String remark;
                            if (StringUtils.isEmpty(mainLog.getRemark())) {
                                remark = erpResponse.getMessage();
                            } else {
                                remark = mainLog.getRemark() + "\n" + erpResponse.getMessage();
                            }
                            mainLog.setRemark(remark);
                        }
                        mainLog.setResolveCount(createList.size());
                        extApiLogService.updateResponse(mainLog, responseEntity, createList.size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    } catch (Exception e) {
                        log.error("调用PO创建接口异常，vo={}", vo, e);
                    }
                    return null;
                }, executor);
                futures.add(future);
            }
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );
            allFutures.join();
            log.info("上传ERP_PO创建处理完成,返回数据:{}!", result);
            this.logId = mainLog.getId();
            if (result.size() == 0) {
                throw new BusinessException("ERP_PO创建处理失败:" + mainLog.getRemark());
            }
            return JSON.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.PO_CREATE.getCode());
    }
}
