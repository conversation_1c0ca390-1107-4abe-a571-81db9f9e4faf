package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.consignmentProduct.dto.ConsignmentProductDTO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * description: 获取ERP的委托产品关系数据
 * author：李杰
 * email: <EMAIL>
 * date: 2024/12/12
 */
@Component
@Slf4j
public class ConsignmentProductHandler extends SyncDataHandler<List<ConsignmentProductDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP委托产品关系数据:{},{}", apiConfigVO, params);
        }
        try {
            //获取ERP的token
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri ;
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},systemNumber={},url={}", erpToken, apiUri, systemNumber, url);
            }
            //请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + erpToken);
            String[] split = params.get("organizeIds").toString().split(",");
            List<Object> arrayList = new ArrayList<>();
            for (String orgId : split) {
                //请求体
                HashMap<String, Object> map = MapUtil.newHashMap();
                map.put("orgId",Integer.valueOf(orgId));
                // 创建子日志
                ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                        httpHeaders.toString(),
                        JSONObject.toJSONString(map));
                HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(map), httpHeaders);
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                int statusCodeValue = responseEntity.getStatusCodeValue();
                if (HttpStatus.OK.value() != statusCodeValue) {
                    extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    throw new BusinessException(StrUtil.format("同步ERP委托产品关系失败,HTTP状态码:{}!", statusCodeValue));
                }
                ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
                if (log.isInfoEnabled()) {
                    log.info("同步ERP委托产品关系完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
                }
                if (erpResponse.getSuccess()) {
                    Object data = erpResponse.getData();
                    List<Object> objects = JSONObject.parseArray(JSONObject.toJSONString(data), Object.class);
                    arrayList.addAll(objects);
                    extApiLogService.updateResponse(subLog, responseEntity, objects.size(), DcpConstants.TASKS_STATUS_ERROR);
                } else {
                    extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    log.error("同步ERP委托产品关系报错，{}!", erpResponse.getMessage());
                }
            }
            extApiLogService.updateResponse(mainLog, null, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            return JSONObject.toJSONString(arrayList);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("organizeIds");
    }

    @Override
    protected List<ConsignmentProductDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<ConsignmentProductDTO> routingStepInputSyncDTOList = JSONObject.parseArray(body, ConsignmentProductDTO.class);
        return routingStepInputSyncDTOList;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ConsignmentProductDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("ERP获取委托产品关系数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        dfpFeign.syncConsignmentProductData(scenario, list);
        this.saveSyncCtrl(apiConfigVO, params, list);
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.CONSIGNMENT_PRODUCT.getCode());
    }
}
