package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <code>FYSLInventoryBatchDetail</code>
 * <p>
 * ERP库存批次现有量接口
 * 同步方式：全量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-05 11:34:36
 */
@Component
@Slf4j
public class ErpInventoryBatchDetailHandler extends SyncDataHandler<List<ErpInventoryBatchDetail>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected List<ErpInventoryBatchDetail> convertData(String body) {

        if (StringUtils.isBlank(body)) {
            log.error("ERP同步库存批次现有量为空！");
            return Collections.emptyList();
        }
        return JSON.parseArray(body, ErpInventoryBatchDetail.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpInventoryBatchDetail> erpInventoryBatchDetails) {
        String orgId = (String) params.get("orgId");
        StopWatch handleWatch = new StopWatch("handleBody数据处理-库存点" + orgId);

        long handleStartTime = System.currentTimeMillis();

        handleWatch.start("保存同步控制时间");
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        handleWatch.stop();

        handleWatch.start("参数校验");
        if (CollectionUtils.isEmpty(erpInventoryBatchDetails)) {
            log.error("ERP库存批次现有量数据为空");
            handleWatch.stop();
            log.info("库存点{}: {}", orgId, handleWatch.prettyPrint(TimeUnit.SECONDS));
            return null;
        }
        handleWatch.stop();

        try {
            handleWatch.start("调用DFP服务");
            Object scenarioObj = params.get("scenario");
            Map<String, Object> scenarioMap = (Map<String, Object>) scenarioObj;
            String dataBaseName = (String) scenarioMap.get("dataBaseName");
            log.info("OrgId:{},ERP库存批次现有量数据大小:{}", orgId, erpInventoryBatchDetails.size());

            dfpFeign.handleErpInventoryBatchDetail(dataBaseName, erpInventoryBatchDetails, orgId);
            handleWatch.stop();

            handleWatch.start("保存同步控制信息");
            this.saveSyncCtrl(apiConfigVO, params, erpInventoryBatchDetails);
            handleWatch.stop();

            long handleEndTime = System.currentTimeMillis();
            double totalHandleTimeSeconds = (handleEndTime - handleStartTime) / 1000.0;

            log.info("库存点{}: ERP库存批次数据处理完成，数据量：{}，总处理耗时：{}秒", orgId, erpInventoryBatchDetails.size(), totalHandleTimeSeconds);
            log.info("库存点{}: {}", orgId, handleWatch.prettyPrint(TimeUnit.SECONDS));

            return "处理成功";
        } catch (Exception e) {
            long errorTime = System.currentTimeMillis();
            double errorTimeSeconds = (errorTime - handleStartTime) / 1000.0;
            log.error("库存点{}: 调用ERP库存批次接口异常，已执行耗时：{}秒，错误：{}", orgId, errorTimeSeconds, e.getLocalizedMessage());
            log.info("库存点{}: {}", orgId, handleWatch.prettyPrint(TimeUnit.SECONDS));
            return "处理ERP库存批次现有量数据时发生错误: " + e.getMessage();
        }
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP库存批次:{},{}", apiConfigVO, params);
        }

        long methodStartTime = System.currentTimeMillis();
        Object scenarioObj = params.get("scenario");
        Map<String, Object> scenarioMap = (Map<String, Object>) scenarioObj;
        String scenario = (String) scenarioMap.get("dataBaseName");
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);

        StopWatch mainWatch = new StopWatch("ERP库存批次明细接口同步");

        try {
            String orgId = (String) params.get("orgId");

            mainWatch.start("请求ERPToken");
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            mainWatch.stop();

            String apiUri = apiConfigVO.getApiUri();
            String url = apiUri + "?token=" + erpToken;
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            String currentUrl = url + "&orgId=" + params.get("orgId");

            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},orgId={},url={}", erpToken, apiUri, orgId, url);
            }

            mainWatch.start("请求ERP库存批次明细接口数据");
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(currentUrl, String.class);
            mainWatch.stop();

            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);

                throw new BusinessException(StrUtil.format("同步ERP库存批次现有量请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);

            String body = responseEntity.getBody();

            log.info("库存点{},同步ERP库存批次现有量完成,返回数据:{}!", orgId, body);

            mainWatch.start("解析ERP响应数据");
            ErpResponse erpResponse = JSONObject.parseObject(body, ErpResponse.class);
            mainWatch.stop();

            // 主流程统计输出
            log.info(mainWatch.prettyPrint(TimeUnit.SECONDS));

            long methodEndTime = System.currentTimeMillis();
            double totalTimeSeconds = (methodEndTime - methodStartTime) / 1000.0;
            log.info("ERP库存批次数据获取完成，总耗时：{}秒", totalTimeSeconds);

            return JSONObject.toJSONString(erpResponse.getData());

        } catch (Exception e) {
            long errorTime = System.currentTimeMillis();
            double errorTimeSeconds = (errorTime - methodStartTime) / 1000.0;
            log.error("调用ERP库存批次接口异常，已执行耗时：{}秒，错误：{}", errorTimeSeconds, e.getLocalizedMessage());
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw new BusinessException("ERP库存批次调用异常", e.getLocalizedMessage());
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.ERP_INVENTORY_BATCH_DETAIL.getCode());
    }


    @Override
    protected String getSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return super.getSyncRefValue(apiConfigVO, params);
    }

    /**
     * 获取同步分组值
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("stockPointCode");
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpInventoryBatchDetail> erpInventoryBatchDetails) {
        return super.computeMaxSyncRefValue(apiConfigVO, params, erpInventoryBatchDetails);
    }
}
