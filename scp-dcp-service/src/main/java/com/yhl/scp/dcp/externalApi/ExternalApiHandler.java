package com.yhl.scp.dcp.externalApi;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.StringUtils;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.externalApi.handler.Handler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName ExternalApiHandler
 * @Description TODO
 * @Date 2024-08-27 11:01:22
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Component
@Slf4j
public class ExternalApiHandler {

    private final Map<String, Handler> strategyMap = new HashMap<>();

    /**
     * 注入所有继承了AbstractHandler接口的Bean
     *
     * @param strategyMap
     */
    @Autowired
    public ExternalApiHandler(Map<String, Handler> strategyMap) {
        this.strategyMap.clear();
        strategyMap.forEach(
                (k, v) -> {
                    this.strategyMap.put(v.getCommand(), v);
                    v.getCommand();
                });
    }

    /**
     * 保存数据
     */
    public BaseResponse<String> handle(String command, Map<String, Object> params) {
        if (StringUtils.isBlank(command) || !strategyMap.containsKey(command)) {
            log.error("不支持处理的消息:{}", command);
            throw new BusinessException("不支持处理的消息:" + command);
        }
        try {
            Handler handler = strategyMap.get(command);
            String handle = handler.handle(params);
            return BaseResponse.success(BaseResponse.OP_SUCCESS, handle);
        } catch (Exception e) {
            String errMsg = StrUtil.format("消息处理异常!消息:{},异常:{}",command,e.getMessage());
            log.error(errMsg,e);
            throw new BusinessException(errMsg, e);
        }
    }
}
