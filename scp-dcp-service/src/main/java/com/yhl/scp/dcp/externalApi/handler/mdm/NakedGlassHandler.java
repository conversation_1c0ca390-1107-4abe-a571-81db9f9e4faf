package com.yhl.scp.dcp.externalApi.handler.mdm;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.nakedGlass.dto.NakedGlassDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * description: 获取裸玻成品映射
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/27
 */
@Slf4j
@Component
public class NakedGlassHandler extends SyncDataHandler<List<NakedGlassDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<NakedGlassDTO> list) {
        return DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            if (log.isInfoEnabled()) {
                log.info("开始同步MDM的裸玻成品映射数据:{},{}", apiConfigVO, params);
            }
            //获取MDM的token
            String token = authHandler.handle(MapUtil.newHashMap());
            //mdm裸玻成品映射地址接口
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();

            String url = apiUri
                    + "?token=" + token;
            if (log.isInfoEnabled()) {
                log.info("token={},apiUri={},systemNumber={},url={}", token, apiUri
                        , systemNumber, url);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() == statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            } else {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
            }
            Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MDM同步裸玻成品映射失败！");
            String body = responseEntity.getBody();
            log.info("同步MDM的裸玻成品映射数据请求完成，返回数据:{}!", body);

            ErpResponse erpResponse = JSONObject.parseObject(body, ErpResponse.class);
            return JSONObject.toJSONString(erpResponse.getData());
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<NakedGlassDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<NakedGlassDTO> partRelationMDMMapVOS = JSONObject.parseArray(body, NakedGlassDTO.class);
        return partRelationMDMMapVOS;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<NakedGlassDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("MDM获取裸玻成品映射数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MPS.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        //对象
        mpsFeign.syncNakedGlassData(scenario, list);
        this.saveSyncCtrl(apiConfigVO, params, list);
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MDM.getCode(), ApiCategoryEnum.NAKED_GLASS.getCode());
    }

}
