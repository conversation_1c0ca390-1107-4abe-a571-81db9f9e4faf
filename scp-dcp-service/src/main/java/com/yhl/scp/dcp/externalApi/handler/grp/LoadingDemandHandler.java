package com.yhl.scp.dcp.externalApi.handler.grp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandInterfaceLogDTO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <code>ShanghaiGrpLoadingDemandHandler</code>
 * <p>
 * EDI装车需求接口
 * 同步方式：增量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 14:57:15
 */
@Component
@Slf4j
public class LoadingDemandHandler extends SyncDataHandler<List<FdpOriginDemandInterfaceLogDTO>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("organizeId");
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<FdpOriginDemandInterfaceLogDTO> fdpOriginDemandInterfaceLogDTOS) {
        Date lastUpdateDate = fdpOriginDemandInterfaceLogDTOS.stream().map(FdpOriginDemandInterfaceLogDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步GRP装车需求:{},{}", apiConfigVO, params);
        }
        // 记录整个方法的开始时间
        long methodStartTime = System.currentTimeMillis();

        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);

        try {

            //获取GRP的token
            String token = authHandler.handle(MapUtil.newHashMap());
            //erp地址接口
            String apiUri = apiConfigVO.getApiUri();
            //管理员编码
//            String systemNumber = apiConfigVO.getSystemNumber();
            //获取流水号
            String flowNumber = IdUtil.fastSimpleUUID();
//            String url = apiUri + "/" + systemNumber + "/" + flowNumber
//                    + "?token=" + token;
            String url = apiUri + "?token=" + token;

            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String txDate = format.format(new Date());
            HashMap<String, Object> map = new HashMap<>();
            map.put("serialNo", flowNumber);
            map.put("txDate", txDate);
            map.put("lang", "zh_CN");
            Map<String, Object> dataMap = MapUtil.newHashMap();
            dataMap.put("orgId", params.get("organizeId"));
            dataMap.put("lastUpdateDate", this.getSyncRefValue(apiConfigVO, params));
            map.put("data", dataMap);
            String bodyStr = JSONObject.toJSONString(map);
            if (log.isInfoEnabled()) {
                log.info("token={},apiUri={},flowNumber={},url={},bodyStr={}", token, apiUri,flowNumber, url, bodyStr);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity httpEntity = new HttpEntity(bodyStr, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步GRP的EDI装车需求失败,HTTP状态码:{}!", statusCodeValue));
            }
            String body = responseEntity.getBody();
            log.info("请求同步GRP的EDI装车需求完成,返回数据:{}!", body);

            // 计算整个方法的总耗时
            long methodEndTime = System.currentTimeMillis();
            double totalTimeSeconds = (methodEndTime - methodStartTime) / 1000.0;
            log.info("GRP装车需求数据获取完成，总耗时：{}秒", totalTimeSeconds);

            return body;

        } catch (Exception e) {
            // 计算异常情况下的耗时
            long errorTime = System.currentTimeMillis();
            double errorTimeSeconds = (errorTime - methodStartTime) / 1000.0;
            log.error("调用GRP装车需求接口异常，已执行耗时：{}秒，错误：{}", errorTimeSeconds, e.getLocalizedMessage());
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw new BusinessException("GRP装车需求调用异常", e.getLocalizedMessage());
        }
    }

    @Override
    protected List<FdpOriginDemandInterfaceLogDTO> convertData(String body) {
        Map<String, Object> map = JSONObject.parseObject(body, Map.class);
        if (MapUtils.isEmpty(map)) {
            log.error("GRP获取装车需求数据失败！");
            return Lists.newArrayList();
        }
        Object object = map.get("data");
        if (Objects.isNull(object)) {
            log.error("解析数据为空！");
            return Lists.newArrayList();
        }
        String jsonString = JSONObject.toJSONString(object);
        return JSONObject.parseArray(jsonString, FdpOriginDemandInterfaceLogDTO.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<FdpOriginDemandInterfaceLogDTO> list) {
        // 记录数据处理开始时间
        long handleStartTime = System.currentTimeMillis();

        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(list)) {
            log.error("GRP获取装车需求数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        list.forEach(dto -> {
            dto.setImportType(ApiSourceEnum.GRP.getCode());
            dto.setSubmissionType("DAY");
            dto.setEnabled(YesOrNoEnum.YES.getCode());
        });
        // 记录服务调用的开始时间

        long serviceCallStartTime = System.currentTimeMillis();

        dfpFeign.syncOriginDemandLog(scenario, list, ApiSourceEnum.GRP.getCode());
        this.saveSyncCtrl(apiConfigVO, params, list);


        // 计算总处理时间和服务调用时间
        long handleEndTime = System.currentTimeMillis();
        double totalHandleTimeSeconds = (handleEndTime - handleStartTime) / 1000.0;
        double serviceCallTimeSeconds = (handleEndTime - serviceCallStartTime) / 1000.0;

        log.info("GRP装车需求数据处理完成，数据量：{}，总处理耗时：{}秒，其中服务调用耗时：{}秒",
                list.size(), totalHandleTimeSeconds, serviceCallTimeSeconds);

        return "同步数据成功";
    }


    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.GRP.getCode(), ApiCategoryEnum.LOADING_DEMAND.getCode());
    }
}
