package com.yhl.scp.dcp.common.controller;

import cn.hutool.json.JSONUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.util.FileUtils;
import com.yhl.scp.dcp.apiConfig.service.ApiConfigService;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import com.yhl.scp.dcp.externalApi.ExternalApiHandler;
import com.yhl.scp.dcp.externalApi.handler.Handler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @ClassName ExternalApiController
 * @Description TODO
 * @Date 2024-09-09 09:21:05
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Slf4j
@Api(tags = "外部接口控制器")
@RestController
@RequestMapping("externalApi")
public class ExternalApiController extends BaseController {

    @Resource
    private ExternalApiHandler externalApiHandler;

    @Resource
    private ApiConfigService apiConfigService;

    @Resource
    private ExtApiLogService extApiLogService;

    @ApiOperation("调用外部系统接口")
    @PostMapping("call")
    public BaseResponse<String> callExternalApi(@RequestParam("tenantCode") String tenantCode,
                                                @RequestParam("apiSource") String apiSource,
                                                @RequestParam("apiCategory") String apiCategory,
                                                @RequestBody Map<String, Object> params) {
        String command = String.join(Handler.CMD_DELIMITER, tenantCode, apiSource, apiCategory);
        log.info("调用外部系统接口：{}", command);
        // 清空线程数据源
        DynamicDataSourceContextHolder.clearDataSource();
        return externalApiHandler.handle(command, params);
    }

    @ApiOperation("调用外部系统带文件接口")
    @RequestMapping(method = RequestMethod.POST, value = "callWithAttach", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BaseResponse<String> callWithAttach(@RequestPart("data") String data, @RequestPart("files") MultipartFile[] multipartFiles) throws Exception {
        Map dataMap = JSONUtil.toBean(data, Map.class);
        dataMap.put("files", FileUtils.convertBatch(Arrays.asList(multipartFiles)));
        String command = String.join(Handler.CMD_DELIMITER, (String) dataMap.get("tenantCode"), (String) dataMap.get("apiSource"), (String) dataMap.get("apiCategory") + "");
        return externalApiHandler.handle(command, dataMap);
    }

    @ApiOperation("获取接口日志")
    @PostMapping("apiLog")
    public ExtApiLogVO getExtApiLog(@RequestParam(value = "id") String id) {
        return extApiLogService.selectByPrimaryKey(id);
    }

    @ApiOperation("根据参数获取外部接口标识")
    @PostMapping("apiMapLog")
    List<ExtApiLogVO> getByParamsExtApiLog(@RequestBody Map<String, Object> params) {
        return extApiLogService.selectByParams(params);
    }
}