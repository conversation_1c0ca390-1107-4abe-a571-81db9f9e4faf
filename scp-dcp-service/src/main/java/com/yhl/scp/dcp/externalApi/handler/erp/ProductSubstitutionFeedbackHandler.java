package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * description: 同步替代料的优先级给ERP
 * author：李杰
 * email: <EMAIL>
 * date: 2025/3/17
 */
@Component
@Slf4j
public class ProductSubstitutionFeedbackHandler extends SyncDataHandler<List> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP的BOM替代料返回优先级:{},{}", apiConfigVO, params);
        }
        try {
            //获取ERP的token
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            //BOM替代料返回优先级地址接口
            String apiUri = apiConfigVO.getApiUri() + "?token=" + erpToken;
            String systemNumber = apiConfigVO.getSystemNumber();

            Object syncListObject = params.get("syncList");
            List<Map<String, Object>> syncList = new ArrayList<>();
            if (syncListObject instanceof List<?>){
                syncList = (List<Map<String, Object>>) syncListObject;
            } else {
                throw new BusinessException("同步数据失败，syncList参数类型错误!");
            }
            if (log.isInfoEnabled()) {
                log.info("systemNumber={},params={},apiUri={},erpToken={}", systemNumber, syncList, apiUri, erpToken);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(syncList), httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(apiUri, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                throw new BusinessException(StrUtil.format("同步ERP的BOM替代料返回优先级失败,HTTP状态码:{}!", statusCodeValue));
            }
            ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
            if (log.isInfoEnabled()) {
                log.info("同步ERP的BOM替代料返回优先级完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
            }
            if (!erpResponse.getSuccess()) {
                log.error("同步ERP的BOM替代料返回优先级报错，{}!", erpResponse.getMessage());
            }
        } catch (Exception e) {
            log.error("同步ERP的BOM替代料返回优先级报错，{}!", e.getMessage());
            throw new BusinessException("同步ERP的BOM替代料返回优先级报错，!" + e.getMessage());
        }
        return "同步数据完成";
    }

    @Override
    protected List convertData(String body) {
        return null;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List list) {
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.PRODUCT_SUBSTITUTION_FEEDBACK.getCode());
    }
}
