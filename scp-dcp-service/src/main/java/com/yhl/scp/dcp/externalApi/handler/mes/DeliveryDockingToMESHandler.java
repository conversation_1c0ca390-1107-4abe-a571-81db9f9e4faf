package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesTransportRouting;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.common.enums.StatusEnum;
import com.yhl.scp.dfp.deliverydockingorder.dto.MesEdiInterfaceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * description: 发送理货单给MES
 * author：李杰
 * email: <EMAIL>
 * date: 2024/12/30
 */
@Component
@Slf4j
public class DeliveryDockingToMESHandler extends SyncDataHandler<List> {
    @Autowired
    private AuthHandler authHandler;
    @Autowired
    private ExtApiLogService extApiLogService;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步理货单数据给MES，数据需求:{},{}", apiConfigVO, params);
        }
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
            String systemNumber = apiConfigVO.getSystemNumber();
            //获取流水号
            String flowNumber = this.sequenceService.getSuffix(systemNumber, getCommand(), 6);
            String url = apiUri  + apiParams;
            //获取body参数
            Object bodyList = params.get("data");
            String bodyStr = JSONObject.toJSONString(bodyList);
            if (log.isInfoEnabled()) {
                log.info("提交理货单到MES,请求数据：token={},apiUri={},systemNumber={},flowNumber={},url={},bodyStr={}", mesToken, apiUri
                        , systemNumber, flowNumber, url, bodyStr);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> httpEntity = new HttpEntity<>(bodyStr, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            String body = responseEntity.getBody();
            if (log.isInfoEnabled()) {
                log.info("提交理货单到MES,HTTP状态码:{},返回数据:{}!", statusCodeValue, body);
            }
            if (HttpStatus.OK.value() != statusCodeValue) {
                throw new BusinessException(StrUtil.format("提交理货单到MES失败,HTTP状态码:{}!", statusCodeValue));
            }
            MesResponse mesResponse = JSONObject.parseObject(body, MesResponse.class);
            if (mesResponse.getCode() == 0) {
                extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_SUCCESS);
                return null;
            } else {
                String errorStr = this.checkStr(mesResponse.getData().getMessage().toString());
                throw new BusinessException(StrUtil.format("MES接口报错:{}!", errorStr));
            }
        } catch (Exception e) {
            log.error("提交理货单到MES异常:{}", e.getMessage(), e);
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw new BusinessException(e.getMessage());
        }
    }

    /**
     * 截取报错
     *
     * @param errorContent
     * @return
     */
    private String checkStr(String errorContent) {
        int i = errorContent.indexOf("-1@");
        if (i != -1) {
            errorContent = errorContent.substring(i + 3, errorContent.length() - 1);
        }
        String[] split = errorContent.split("原因");
        if (split.length > 1) {
            StringBuffer backContent = new StringBuffer(split[0]);
            for (int j = 1; j < split.length; j++) {
                backContent.append("<br/>原因").append(split[j]);
            }
            return backContent.toString();
        } else {
            return errorContent;
        }
    }

    @Override
    protected List<MesTransportRouting> convertData(String body) {
        return null;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> paramMap, List mesTransportRoutings) {
        try {
            Object object = paramMap.get("data");
            String jsonString = JSONObject.toJSONString(object);
            MesEdiInterfaceDTO mesEdiInterfaceDTO = JSONObject.parseObject(jsonString, MesEdiInterfaceDTO.class);
            Map<String, Object> map = MapUtil.newHashMap();
            map.put("deliveryDockingNumber", mesEdiInterfaceDTO.getReqNum());
            if ("RELEASE".equals(mesEdiInterfaceDTO.getReqStatus())) {
                map.put("status", StatusEnum.SUBMIT.getCode());
            } else {
                map.put("status", StatusEnum.CANCELLED.getCode());
            }
            return JSONObject.toJSONString(map);
        } catch (Exception e) {
            log.error("提交理货单到MES,处理返回数据异常:{}", e.getMessage(), e);
            return "提交理货单到MES,处理返回数据异常: " + e.getMessage();
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.DELIVERY_DOCKING_MES.getCode());
    }

}