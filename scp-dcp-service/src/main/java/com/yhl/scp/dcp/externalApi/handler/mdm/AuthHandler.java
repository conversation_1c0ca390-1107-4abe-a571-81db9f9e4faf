package com.yhl.scp.dcp.externalApi.handler.mdm;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mdm.MdmResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mdm.MdmToken;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.externalApi.handler.TokenHandler;
import com.yhl.scp.dcp.token.Token;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * description:
 * author：junkai.li
 * date: 2024/10/24
 */
@Slf4j
@Component("authHandler4MDM")
public class AuthHandler extends TokenHandler {

    @Override
    protected Token getToken(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始请求MDM接口TOKEN:{},{}", apiConfigVO, params);
        }
        String apiUri = apiConfigVO.getApiUri();
        String systemNumber = apiConfigVO.getSystemNumber();
        String url = apiUri ;
        if (log.isInfoEnabled()) {
            log.info("apiUri={},systemNumber={},url={}", apiUri, systemNumber, url);
        }
        String responseData = restTemplate.postForObject(url, null, String.class);
        log.info("请求MDM接口TOKEN完成,返回数据:{}!", responseData);
        MdmResponse mdmResponse = JSONObject.parseObject(responseData, MdmResponse.class);
        Assert.isTrue(mdmResponse.getSuccess(), "请求MDM获取token返回错误");

        Object data = mdmResponse.getData();
        MdmToken mdmToken = JSONObject.parseObject(data.toString(), MdmToken.class);
        return Token.builder()
                .token(mdmToken.getToken())
                .expiresIn(mdmToken.getExpiresIn())
                .build();
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MDM.getCode(),
                ApiCategoryEnum.AUTH.getCode());
    }

}
