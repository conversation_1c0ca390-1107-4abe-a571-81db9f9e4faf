package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpDeleteGroup;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpOriginalFilmFFOnway;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dcp.sync.vo.SyncCtrlVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>OriginalFilmFFInventoryHandler</code>
 * <p>
 * 原片浮法在途库存
 * 同步方式：增量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 11:35:33
 */
@Component
@Slf4j
public class OriginalFilmFFOnwayHandler extends SyncDataHandler<List<ErpOriginalFilmFFOnway>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected List<ErpOriginalFilmFFOnway> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("ERP获取原片浮法在途库存为空");
            return null;
        }
        List<ErpOriginalFilmFFOnway> erpOriginalFilmFFInventories = JSONObject.parseArray(body, ErpOriginalFilmFFOnway.class);
        return erpOriginalFilmFFInventories;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpOriginalFilmFFOnway> erpOriginalFilmFFInventories) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(erpOriginalFilmFFInventories)) {
            log.error("原片浮法在途库存为空");
            return null;
        }
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(), TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        mrpFeign.handleOriginalFilmFFOnway(scenario.getData(), erpOriginalFilmFFInventories);
        this.saveSyncCtrl(apiConfigVO, params, erpOriginalFilmFFInventories);
        return null;
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP原片浮法在途库存:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
//            String systemNumber = apiConfigVO.getSystemNumber();
//            String url = apiUri + "/" + systemNumber + "/" + sequenceService.getSuffix(systemNumber, getCommand(), 5)
//                    + "?token=" + erpToken;
            String url = apiUri + "?token=" + erpToken;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            String currentUrl = url + "&orgCode=" + params.get("stockPointCode") +"&customerNumber=" + params.get("customerNumber")+ "&lastUpdateDate=" + lastUpdateDate;

            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},lastUpdateDate={},url={},currentUrl={}", erpToken, apiUri
                        , lastUpdateDate, url,currentUrl);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(currentUrl, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步ERP原片浮法在途库存请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            String body = responseEntity.getBody();

            log.info("同步ERP原片浮法在途库存处理完成,返回数据:{}!", body);
            ErpResponse erpResponse = JSONObject.parseObject(body, ErpResponse.class);
            return JSONObject.toJSONString(erpResponse.getData());

        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.ORIGINAL_FILM_FF_ONWAY.getCode());
    }
    @Override
    protected String getSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        SyncCtrlVO syncCtrlVO = syncCtrlService.getSyncCtrl(apiConfigVO, this.getSyncGroupValue(apiConfigVO, params));
        return ObjUtil.isNotNull(syncCtrlVO) ? syncCtrlVO.getReferValue() : DateUtil.offsetDay(DateTime.now(), -7).toString(DatePattern.PURE_DATE_PATTERN);
    }
    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("stockPointCode");
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpOriginalFilmFFOnway> erpOriginalFilmFFInventories) {
        Date lastUpdateDate = erpOriginalFilmFFInventories.stream().map(ErpOriginalFilmFFOnway::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DatePattern.PURE_DATE_PATTERN);
    }
}
