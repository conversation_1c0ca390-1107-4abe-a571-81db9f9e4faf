package com.yhl.scp.dcp.externalApi.handler.mes;


import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialDeliveryNote;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.product.dto.ChainLineInventoryLogDTO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description:
 * author：zyh
 * date: 2024/12/07
 */
@Component
@Slf4j
public class MaterialDeliveryNoteHandler extends SyncDataHandler<List<MesMaterialDeliveryNote>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private ExtApiLogService extApiLogService;



    @Override
    protected List<MesMaterialDeliveryNote> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES同步材料在途数据为空！");
            return Collections.emptyList();
        }
        return JSON.parseArray(body, MesMaterialDeliveryNote.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesMaterialDeliveryNote> mesMaterialDeliveryNotes) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(mesMaterialDeliveryNotes)) {
            log.error("材料在途数据为空");
            return null;
        }
        try {
            mesMaterialDeliveryNotes = mesMaterialDeliveryNotes.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    mesMaterialDeliveryNote -> mesMaterialDeliveryNote.getGroupNum() + "_" + mesMaterialDeliveryNote.getItemCode() + "_" + mesMaterialDeliveryNote.getPoNumber() +"_"+mesMaterialDeliveryNote.getLineNum()+ "_" + mesMaterialDeliveryNote.getTicketStatus()+"_"+mesMaterialDeliveryNote.getDeliveryQty(),
                                    dto -> dto,
                                    (dto1, dto2) -> dto1.getLastUpdateDate().after(dto2.getLastUpdateDate()) ? dto1 : dto2
                            ),
                            map -> new ArrayList<>(map.values())
                    ));
            BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                    TenantCodeEnum.FYQB.getCode());
            Assert.isTrue(mdsScenario.getSuccess(), mdsScenario.getMsg());

            mrpFeign.handleMaterialDeliveryNote(mdsScenario.getData(), mesMaterialDeliveryNotes);
            this.saveSyncCtrl(apiConfigVO, params, mesMaterialDeliveryNotes);
            return "处理成功";
        } catch (Exception e) {
            return "处理材料在途时发生错误: " + e.getMessage();
        }
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始请求MES材料在途:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
            String url = apiUri +  apiParams;
            String reqCode = "FY_DLY_GROUP_ONROAD_FOR_BPIM";

            String lastUpdateDateStr = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDateStr, DateUtils.COMMON_DATE_STR3);
            Date currentDate = DateUtils.truncateTimeOfDate(new Date());
            if (log.isInfoEnabled()) {
                log.info("apiUri={},url={},reqCode={},lastUpdateDateStr={},currentDate={}", apiUri,
                        url, reqCode, lastUpdateDateStr, currentDate);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            int period = (int) DateUtil.between(calculateDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            Date beginTime = calculateDate;
            List<Object> result = Lists.newArrayList();
            for (int i = 0; i < count; i++) {
                int currentPage = 1;
                boolean hasNextSize = true;
                while (hasNextSize) {
                    HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                    paramMap.put("currentPage", currentPage);
                    paramMap.put("pageSize", 10000);
                    paramMap.put("reqCode", reqCode);
                    paramMap.put("beginTime", DateUtils.dateToString(beginTime, DateUtils.COMMON_DATE_STR1));
                    Date endTime = org.apache.commons.lang3.time.DateUtils.addDays(beginTime, calculatePeriod);
                    paramMap.put("endTime", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() != statusCodeValue) {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                    Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步材料在途失败！");
                    String body = responseEntity.getBody();
                    log.info("请求MES材料在途完成,返回数据:{}!", body);
                    MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                    MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                    extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    if (Objects.nonNull(data)) {
                        result.addAll(data.getMessage());
                        if (data.getTotalPage() <= data.getCurrentPage()) {
                            hasNextSize = false;
                            beginTime = endTime;
                        } else {
                            currentPage++;
                        }
                    }
                }

            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, result.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSON.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    /**
     * 计算最新更新时间
     *
     * @param mesMaterialDeliveryNotes
     * @return
     */
    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<MesMaterialDeliveryNote> mesMaterialDeliveryNotes) {
        Date lastUpdateDate =
                mesMaterialDeliveryNotes.stream().map(MesMaterialDeliveryNote::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.MATERIAL_DELIVERY_NOTE.getCode());
    }

}
