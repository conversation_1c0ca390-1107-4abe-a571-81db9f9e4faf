package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpProductStockPointBase;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>ProductStockPointBaseHandler</code>
 * <p>
 * 产品工艺基础数据接口同步
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 15:12:37
 */
@Component
@Slf4j
public class ProductStockPointBaseHandler extends SyncDataHandler<List<ErpProductStockPointBase>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<ErpProductStockPointBase> erpProductStockPointBases) {
        Date lastUpdateDate =
                erpProductStockPointBases.stream().map(ErpProductStockPointBase::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP产品工艺基础数据:{},{}", apiConfigVO, params);
        }
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String url = apiUri + "?token=" + erpToken;
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(),
                    TenantCodeEnum.FYQB.getCode());
            BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(mdsScenario.getData(), "OP_BUSINESS_AREA", "EXTERNAL_REQ", null);
            String rangeData = scenarioBusinessRange.getData().getRangeData();
            String currentUrl = url + "&organizationCode= "+rangeData+"_上海汽车玻璃"  + "&lastUpdateDate=" + lastUpdateDate;
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},lastUpdateDate={},url={},currentUrl={}", erpToken, apiUri
                        , lastUpdateDate, url, currentUrl);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(currentUrl, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                throw new BusinessException(StrUtil.format("同步ERP产品工艺基础数据,HTTP状态码:{}!", statusCodeValue));
            }
            ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
            if (log.isInfoEnabled()) {
                log.info("同步ERP产品工艺基础数据完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
            }
            if (erpResponse.getSuccess()) {
                extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
                return JSONObject.toJSONString(erpResponse.getData());
            } else {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                log.error("同步ERP产品工艺基础数据报错，{}!", erpResponse.getMessage());
                return null;
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }


    @Override
    protected List<ErpProductStockPointBase> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<ErpProductStockPointBase> erpProductStockPointBaseList = JSONObject.parseArray(body, ErpProductStockPointBase.class);
        return erpProductStockPointBaseList;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                List<ErpProductStockPointBase> erpProductStockPointBases) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(erpProductStockPointBases)) {
            log.error("ERP同步产品工艺基础数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(),
                TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();

        mdsFeign.syncProductStockPointBase(scenario, erpProductStockPointBases);
        this.saveSyncCtrl(apiConfigVO, params, erpProductStockPointBases);
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.PRODUCT_STOCK_POINT_BASE.getCode());
    }
}
