package com.yhl.scp.dcp.externalApi.handler.iam;

import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.IamResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.dto.DeptDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * description:全量同步组织
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/7
 */
@Component
@Slf4j
public class AllOrgHandler extends SyncDataHandler<List<DeptDTO>> {
    @Resource
    private AuthHandler iamAuthHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String,Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步IAM的组织数据:{},{}", apiConfigVO, params);
        }
        try {
            //获取ERP的token
            String token = iamAuthHandler.getToken();
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri ;
            //设置请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", token);
            //设置请求体
            HashMap<String, Object> map = new HashMap<>();
            int size = Objects.isNull(apiConfigVO.getOffsetSize()) ? 5000 : apiConfigVO.getOffsetSize();
            //数据集合
            List<DeptDTO> arrayList = new ArrayList<>();
            for (int page = 1; ; page++) {
                map.put("page", page);
                map.put("size", size);
                if (log.isInfoEnabled()) {
                    log.info("apiUri={},systemNumber={},url={},bodyStr={},token={}", apiUri
                            , systemNumber, url, map, token);
                }
                // 创建子日志
                ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                        httpHeaders.toString(),
                        JSONObject.toJSONString(map));
                HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(map), httpHeaders);
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                int statusCodeValue = responseEntity.getStatusCodeValue();
                Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "IAM同步组织失败！");
                String body = responseEntity.getBody();
                log.info("同步IAM的组织数据请求完成，返回数据:{}!", body);
                IamResponse iamResponse = JSONObject.parseObject(responseEntity.getBody(), IamResponse.class);
                if ("0".equals(iamResponse.getCode())) {
                    String str = JSONObject.toJSONString(iamResponse.getData());
                    Map<String, Object> map1 = JSONObject.parseObject(str, Map.class);
                    List<DeptDTO> object = (List) map1.get("list");
                    arrayList.addAll(object);
                    extApiLogService.updateResponse(subLog, responseEntity, object.size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    if (Integer.parseInt(map1.get("total").toString()) < size) {
                        extApiLogService.updateResponse(mainLog, null, arrayList.size(), DcpConstants.TASKS_STATUS_SUCCESS);
                        return JSONObject.toJSONString(arrayList);
                    }
                } else {
                    extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
                    log.error("同步IAM组织报错，{}", iamResponse.getMsg());
                    return null;
                }
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<DeptDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        List<DeptDTO> partRelationMDMMapVOS = JSONObject.parseArray(body, DeptDTO.class);
        return partRelationMDMMapVOS;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<DeptDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("IAM获取组织数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.IPS.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        ipsNewFeign.syncDeptData(scenario, list);
        this.saveSyncCtrl(apiConfigVO, params, list);
        return "同步数据成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.IAM.getCode(), ApiCategoryEnum.ALL_ORGANIZATION.getCode());
    }

}
