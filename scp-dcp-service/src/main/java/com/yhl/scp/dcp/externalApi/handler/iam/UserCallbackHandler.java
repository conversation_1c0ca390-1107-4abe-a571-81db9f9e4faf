package com.yhl.scp.dcp.externalApi.handler.iam;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.service.ApiConfigService;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.core.SequenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.yhl.scp.dcp.externalApi.handler.Handler.CMD_DELIMITER;

/**
 * description: 回调账号状态给iam
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/19
 */
@Component
@Slf4j
public class UserCallbackHandler {
    @Resource
    private AuthHandler iamAuthHandler;

    @Resource
    private ApiConfigService apiConfigService;

    @Resource
    private SequenceService sequenceService;

    @Resource
    private RestTemplate restTemplate;

    public boolean callApi(List<String> params) {
        ApiConfigVO apiConfigVO = apiConfigService.getByCommand(this.getCommand());
        if (log.isInfoEnabled()) {
            log.info("开始回调IAM的账户数据:{}", apiConfigVO);
        }
        String token = iamAuthHandler.getToken();
        String apiUri = apiConfigVO.getApiUri();
        String systemNumber = apiConfigVO.getSystemNumber();
        String url = apiUri ;
        if (log.isInfoEnabled()) {
            log.info("token={},apiUri={},systemNumber={},url={}", token, apiUri, systemNumber, url);
        }
        //设置请求头
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.set("Authorization", token);
        HashMap<String, Object> map = MapUtil.newHashMap();
        for (String id: params) {
            if (log.isInfoEnabled()) {
                log.info("token={},apiUri={},systemNumber={},url={},map={}", token, apiUri
                        , systemNumber, url, params);
            }
            map.put("ids",id);
            HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(map), httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url , httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "回调IAM用户失败！");
            Map<String,Object> parse = JSONObject.parseObject(responseEntity.getBody(), Map.class);
            if (!("操作成功".equals(parse.get("msg")) || ("Request Succeeded").equals(parse.get("msg")))){
                log.error(responseEntity.getBody().toString());
                throw new BusinessException("回调IAM用户报错");
            }
        }
        log.info("回调IAM的用户数据请求完成");
        return true;
    }

    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.IAM.getCode(), ApiCategoryEnum.USER_CALLBACK.getCode());
    }

}