package com.yhl.scp.dcp.externalApi.handler.mdm;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mdm.MdmProductionTime;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mdm.MdmResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * description:
 * author：junkai.li
 * date: 2024/10/24
 */
@Slf4j
@Component("productHandler4MDM")
public class ProductionTimeHandler extends SyncDataHandler<List<MdmProductionTime>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<MdmProductionTime> mdmProductionTimes) {
        Date lastUpdateDate = mdmProductionTimes.stream().map(MdmProductionTime::getLastUpdateTime).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected List<MdmProductionTime> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MDM获取外部物料数据为空");
            return null;
        }
        List<MdmProductionTime> productStockPointList = JSONObject.parseArray(body, MdmProductionTime.class);
        return productStockPointList;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MdmProductionTime> mdmProductionTimes) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(mdmProductionTimes)) {
            log.error("物料为空");
            return null;
        }
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(),
                TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        newMdsFeign.handleProductStockPoints1(scenario.getData(), mdmProductionTimes);
        this.saveSyncCtrl(apiConfigVO, params, mdmProductionTimes);
        return null;
    }

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("stockPointCode");
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步MDM产品时间:{},{}", apiConfigVO, params);
        }
        try {
            String mdmToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri + "?token=" + mdmToken;
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            String currentUrl = url + "&organizationCode=" + params.get("stockPointCode") + "&lastUpdateDate=" + lastUpdateDate;
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},systemNumber={},lastUpdateDate={},url={},currentUrl={}", mdmToken, apiUri
                        , systemNumber, lastUpdateDate, url, currentUrl);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(currentUrl, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                throw new BusinessException(StrUtil.format("同步MDM产品时间失败,HTTP状态码:{}!", statusCodeValue));
            }
            ErpResponse erpResponse = JSONObject.parseObject(responseEntity.getBody(), ErpResponse.class);
            if (log.isInfoEnabled()) {
                log.info("同步MDM产品时间完成,返回数据:{}!", JSONUtil.toJsonStr(erpResponse));
            }
            if (erpResponse.getSuccess()) {
                extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
                return JSONObject.toJSONString(erpResponse.getData());
            } else {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                log.error("同步MDM产品时间报错，{}!", erpResponse.getMessage());
                return null;
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MDM.getCode(),
                ApiCategoryEnum.PRODUCT_TIME.getCode());
    }
}
