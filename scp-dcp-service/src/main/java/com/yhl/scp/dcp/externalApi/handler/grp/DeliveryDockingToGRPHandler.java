package com.yhl.scp.dcp.externalApi.handler.grp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.grp.GrpEdiDelivery;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.grp.GrpEdiDeliveryDetail;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * description:EDI理货单数据到GRP
 * author：李杰
 * email: <EMAIL>
 * date: 2024/12/19
 */
@Component
@Slf4j
public class DeliveryDockingToGRPHandler extends SyncDataHandler<List<GrpEdiDeliveryDetail>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步理货单数据给GRP，数据需求:{},{}", apiConfigVO, params);
        }
        try {
            //获取GRP的token
            String token = authHandler.handle(MapUtil.newHashMap());
            //erp地址接口
            String apiUri = apiConfigVO.getApiUri();
            //管理员编码
            String systemNumber = apiConfigVO.getSystemNumber();
            //获取流水号
            String flowNumber = this.sequenceService.getSuffix(systemNumber, getCommand(), 6);
            String url = apiUri
                    + "?token=" + token;
            //获取body参数
            Object bodyList = params.get("list");
            String bodyStr = JSONObject.toJSONString(bodyList);
            if (log.isInfoEnabled()) {
                log.info("token={},apiUri={},systemNumber={},flowNumber={},url={},bodyStr={}", token, apiUri
                        , systemNumber, flowNumber, url, bodyStr);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity httpEntity = new HttpEntity(bodyStr, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
//                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步GRP的EDI装车需求失败,HTTP状态码:{}!", statusCodeValue));
            }
            String body = responseEntity.getBody();
            GrpEdiDelivery grpEdiDelivery = JSONObject.parseObject(body, GrpEdiDelivery.class);
            if (grpEdiDelivery.isSuccess()) {
                extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_SUCCESS);
                log.info("请求同步GRP的EDI装车需求完成,返回数据:{}!", body);
                return JSONObject.toJSONString(grpEdiDelivery.getRows());
            } else {
                log.error("数据报错，{}", grpEdiDelivery.getMessage());
                throw new BusinessException(StrUtil.format("调用接口报错:{}!", grpEdiDelivery.getMessage()));
            }
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<GrpEdiDeliveryDetail> convertData(String body) {
        if (StringUtils.isEmpty(body)) {
            return null;
        }
        return JSONObject.parseArray(body, GrpEdiDeliveryDetail.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<GrpEdiDeliveryDetail> list) {
        if (list.isEmpty()) {
            log.info("同步的数据为空");
            return null;
        }
        //获取模块标识
//        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(),
//                TenantCodeEnum.FYQB.getCode());
//        String scenario = defaultScenario1.getData();
//        dfpFeign.updateDeliveryDockingStatus(scenario,list);
        return JSONObject.toJSONString(list);
    }


    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.GRP.getCode(), ApiCategoryEnum.DELIVERY_DOCKING_GRP.getCode());
    }
}
