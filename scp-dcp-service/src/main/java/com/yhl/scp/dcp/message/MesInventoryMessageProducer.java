package com.yhl.scp.dcp.message;

import com.alibaba.fastjson.JSONObject;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesInventoryBatchDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * MES库存数据消息生产者
 * 用于解耦DCP和DFP服务，避免同步调用导致的重复处理问题
 * 
 * <AUTHOR>
 * @since 2024-10-15
 */
@Component
@Slf4j
public class MesInventoryMessageProducer {

    /**
     * 发送MES库存数据到消息队列
     * 
     * @param scenario 场景
     * @param mesInventoryBatchDetails MES库存数据
     * @param orgIds 组织ID列表
     */
    public void sendInventoryMessage(String scenario, 
                                   List<MesInventoryBatchDetail> mesInventoryBatchDetails, 
                                   List<String> orgIds) {
        try {
            MesInventoryMessage message = new MesInventoryMessage();
            message.setScenario(scenario);
            message.setMesInventoryBatchDetails(mesInventoryBatchDetails);
            message.setOrgIds(orgIds);
            message.setTimestamp(System.currentTimeMillis());
            message.setMessageId(generateMessageId(orgIds));
            
            // 这里可以集成RabbitMQ、RocketMQ或Kafka
            // 示例使用本地队列模拟
            sendToQueue(message);
            
            log.info("MES库存数据已发送到消息队列，场景：{}，数据量：{}，库存点：{}", 
                scenario, mesInventoryBatchDetails.size(), orgIds);
                
        } catch (Exception e) {
            log.error("发送MES库存数据到消息队列失败", e);
            throw new RuntimeException("消息发送失败", e);
        }
    }
    
    /**
     * 发送到队列（这里可以替换为实际的MQ实现）
     */
    private void sendToQueue(MesInventoryMessage message) {
        // 实际实现中，这里应该是MQ的发送逻辑
        // 例如：rabbitTemplate.convertAndSend("mes.inventory.queue", message);
        log.info("消息已发送到队列：{}", JSONObject.toJSONString(message));
    }
    
    /**
     * 生成消息ID
     */
    private String generateMessageId(List<String> orgIds) {
        return "MES_INV_" + String.join("_", orgIds) + "_" + System.currentTimeMillis();
    }
    
    /**
     * MES库存消息实体
     */
    public static class MesInventoryMessage {
        private String messageId;
        private String scenario;
        private List<MesInventoryBatchDetail> mesInventoryBatchDetails;
        private List<String> orgIds;
        private long timestamp;
        
        // getters and setters
        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }
        
        public String getScenario() { return scenario; }
        public void setScenario(String scenario) { this.scenario = scenario; }
        
        public List<MesInventoryBatchDetail> getMesInventoryBatchDetails() { return mesInventoryBatchDetails; }
        public void setMesInventoryBatchDetails(List<MesInventoryBatchDetail> mesInventoryBatchDetails) { 
            this.mesInventoryBatchDetails = mesInventoryBatchDetails; 
        }
        
        public List<String> getOrgIds() { return orgIds; }
        public void setOrgIds(List<String> orgIds) { this.orgIds = orgIds; }
        
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }
    }
}