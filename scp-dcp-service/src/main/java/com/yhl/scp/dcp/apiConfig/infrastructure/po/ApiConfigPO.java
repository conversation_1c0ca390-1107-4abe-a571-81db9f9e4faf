package com.yhl.scp.dcp.apiConfig.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

public class ApiConfigPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -56528298611926079L;

    /**
     * 主键
     */
    private String id;
    /**
     * 接口来源（ERP/OA/EDI/MES等）
     */
    private String apiSource;
    /**
     * 接口分类（鉴权，物料，客户等）
     */
    private String apiCategory;
    /**
     * 接口名称
     */
    private String apiName;
    /**
     * 接口地址
     */
    private String apiUri;
    /**
     * 系统号
     */
    private String systemNumber;
    /**
     * 请求头
     */
    private String apiHeaders;
    /**
     * 接口参数
     */
    private String apiParams;
    /**
     * 请求体
     */
    private String apiBody;
    /**
     * 租户ID
     */
    private String tenantId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    private String enabled;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 数据表版本
     */
    private Integer versionValue;
    /**
     * 接口编码
     */
    private String apiCode;
    /**
     * 接口方法
     */
    private String apiMethod;
    /**
     * 同步类型
     */
    private String syncType;
    /**
     * 偏移量
     */
    private Integer offsetSize;
    /**
     * 协议
     */
    private String apiProtocol;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getApiSource() {
        return apiSource;
    }

    public void setApiSource(String apiSource) {
        this.apiSource = apiSource;
    }

    public String getApiCategory() {
        return apiCategory;
    }

    public void setApiCategory(String apiCategory) {
        this.apiCategory = apiCategory;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getApiParams() {
        return apiParams;
    }

    public void setApiParams(String apiParams) {
        this.apiParams = apiParams;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

    public String getSystemNumber() {
        return systemNumber;
    }

    public void setSystemNumber(String systemNumber) {
        this.systemNumber = systemNumber;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getApiHeaders() {
        return apiHeaders;
    }

    public void setApiHeaders(String apiHeaders) {
        this.apiHeaders = apiHeaders;
    }

    public String getApiBody() {
        return apiBody;
    }

    public void setApiBody(String apiBody) {
        this.apiBody = apiBody;
    }

    public String getApiCode() {
        return apiCode;
    }

    public void setApiCode(String apiCode) {
        this.apiCode = apiCode;
    }

    public String getApiMethod() {
        return apiMethod;
    }

    public void setApiMethod(String apiMethod) {
        this.apiMethod = apiMethod;
    }

    public String getSyncType() {
        return syncType;
    }

    public void setSyncType(String syncType) {
        this.syncType = syncType;
    }

    public Integer getOffsetSize() {
        return offsetSize;
    }

    public void setOffsetSize(Integer offsetSize) {
        this.offsetSize = offsetSize;
    }

    public String getApiProtocol() {
        return apiProtocol;
    }

    public void setApiProtocol(String apiProtocol) {
        this.apiProtocol = apiProtocol;
    }
}
