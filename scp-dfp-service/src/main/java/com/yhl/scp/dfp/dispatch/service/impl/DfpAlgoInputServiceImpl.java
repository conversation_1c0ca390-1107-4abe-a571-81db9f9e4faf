package com.yhl.scp.dfp.dispatch.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.BigDecimalUtils;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandVersionPO;
import com.yhl.scp.dfp.demand.service.DemandForecastEstablishmentService;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO;
import com.yhl.scp.dfp.dispatch.input.DfpAlgoInput;
import com.yhl.scp.dfp.dispatch.input.HistoryOrderInput;
import com.yhl.scp.dfp.dispatch.input.OemForecastInput;
import com.yhl.scp.dfp.dispatch.input.OemProjectInfoInput;
import com.yhl.scp.dfp.dispatch.input.OemStockInfoInput;
import com.yhl.scp.dfp.dispatch.input.OemVehicleModelInput;
import com.yhl.scp.dfp.dispatch.input.PassengerCarMarketInfoInput;
import com.yhl.scp.dfp.dispatch.input.ProjectMappingInput;
import com.yhl.scp.dfp.dispatch.service.DfpAlgoInputService;
import com.yhl.scp.dfp.forecast.service.HistoryForecastDataService;
import com.yhl.scp.dfp.forecast.vo.HistoryForecastDataVO;
import com.yhl.scp.dfp.oem.service.OemInventoryService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelMapService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemInventoryVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelMapVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.passenger.service.PassengerCarSaleService;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DfpAlgoInputServiceImpl implements DfpAlgoInputService {

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private PassengerCarSaleService passengerCarSaleService;

    @Resource
    private OemVehicleModelMapService oemVehicleModelMapService;

    @Resource
    private OemInventoryService oemInventoryService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Resource
    private DemandForecastVersionService demandForecastVersionService;

    @Resource
    private DemandForecastEstablishmentService demandForecastEstablishmentService;

    @Resource
    private OemService oemService;

    @Resource
    private HistoryForecastDataService historyForecastDataService;
    
    @Resource
    protected DemandVersionDao demandVersionDao;

    @Override
    public DfpAlgoInput getDfpAlgorithmData(String scenario, String kpiData) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        log.info("dfp算法预测收集数据开始-scenario: {}, kpiData: {}", scenario, kpiData);
        
        //获取对应的计划周期时间
        DemandVersionPO demandVersion = demandVersionDao.selectByPrimaryKey(kpiData);
        String planPeriod = demandVersion.getPlanPeriod();
        // 仓库收发货数据 TODO
        List<HistoryOrderInput> historyOrderInput = getHistoryOrderInput(scenario, planPeriod);
        // 主机厂预测数据 TODO
        List<OemForecastInput> oemForecastInput = getOemForecastInput(scenario, historyOrderInput);
        // 乘用车市场信息表
        List<PassengerCarMarketInfoInput> passengerCarMarketInfoInput = getPassengerCarMarketInfoInput(scenario);
        // 主机厂车型映射表
        List<ProjectMappingInput> projectMappingInput = getProjectMappingInput(scenario);
        // 主机厂车型信息
        List<OemProjectInfoInput> oemProjectInfoInput = getOemProjectInfoInput(scenario, historyOrderInput);
        // 主机厂库存信息
        List<OemStockInfoInput> oemStockInfoInput = getOemStockInfoInput(scenario);
        // 主机厂车型信息表
        List<OemVehicleModelInput> oemVehicleModel = getOemVehicleModel(scenario);
        log.info("dfp算法预测收集数据结束");

        DynamicDataSourceContextHolder.clearDataSource();
        return DfpAlgoInput.builder()
                .historyOrderInputList(historyOrderInput)
                .oemForecastInputList(oemForecastInput)
                .passengerCarMarketInfoInputList(passengerCarMarketInfoInput)
                .projectMappingInputList(projectMappingInput)
                .oemProjectInfoInputList(oemProjectInfoInput)
                .oemStockInfoInputList(oemStockInfoInput)
                .oemVehicleModelInputList(oemVehicleModel)
                .build();
    }

    private List<OemVehicleModelInput> getOemVehicleModel(String scenario) {
        List<OemVehicleModelInput> oemVehicleModelInputList = new ArrayList<>();
        List<OemVehicleModelVO> oemVehicleModelVOS = oemVehicleModelService.selectAllVO();
        for (OemVehicleModelVO oemVehicleModelVO : oemVehicleModelVOS) {
            OemVehicleModelInput oemVehicleModelInput = OemVehicleModelInput.builder()
                    .oemCode(oemVehicleModelVO.getOemCode())
                    .oemName(oemVehicleModelVO.getOemName())
                    .oemVehicleModelCode("\"" + oemVehicleModelVO.getOemVehicleModelCode() + "\"")
                    .vehicleModelPrice(oemVehicleModelVO.getVehicleModelPrice())
                    .vehicleType(oemVehicleModelVO.getVehicleType())
                    .build();
            oemVehicleModelInputList.add(oemVehicleModelInput);
        }
        log.info("主机厂车型价格信息数据行：{}", oemVehicleModelInputList.size());
        return oemVehicleModelInputList;
    }

    private List<OemStockInfoInput> getOemStockInfoInput(String scenario) {
        List<OemStockInfoInput> oemStockInfoInput = new ArrayList<>();
        List<OemInventoryVO> oemInventoryVOS = oemInventoryService.selectAll();
        for (OemInventoryVO oemInventoryVO : oemInventoryVOS) {
            OemStockInfoInput stockInfoInput = OemStockInfoInput.builder()
                    .oemCode(oemInventoryVO.getOemCode())
                    .projectCode(oemInventoryVO.getOemVehicleModelCode())
                    .date(null != oemInventoryVO.getInventoryTime() ?
                            DateUtils.dateToString(oemInventoryVO.getInventoryTime()) : null)
                    .qty(oemInventoryVO.getInventoryQty())
                    .build();
            oemStockInfoInput.add(stockInfoInput);
        }
        log.info("主机厂库存信息数据行：{}", oemStockInfoInput.size());
        return oemStockInfoInput;
    }

    private List<OemProjectInfoInput> getOemProjectInfoInput(String scenario,
                                                             List<HistoryOrderInput> historyOrderInput) {
        List<OemProjectInfoInput> oemProjectInfoInputList = new ArrayList<>();
        Map<String, List<String>> itemCodeMap = new HashMap<>();
        historyOrderInput.forEach(e -> {
            String productCode = e.getItemCode().split("&")[1];
            List<String> itemCodes = itemCodeMap.get(productCode);
            if (CollectionUtils.isEmpty(itemCodes)) {
                itemCodes = new ArrayList<>();
            }
            itemCodes.add(e.getItemCode());
            itemCodeMap.put(productCode, itemCodes);
        });
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<String> productCodeList = new ArrayList<>(itemCodeMap.keySet());
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(scenario,
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "stockPointCode", rangeData,
                        "productCodeList", productCodeList));
        for (NewProductStockPointVO newProductStockPointVO : newProductStockPointVOS) {
            String productCode = newProductStockPointVO.getProductCode();
            List<String> itemCodes = itemCodeMap.get(productCode);
            for (String itemCode : itemCodes) {
                OemProjectInfoInput oemProjectInfoInput = OemProjectInfoInput.builder()
                        .itemCode(itemCode)
                        .sopDate(null != newProductStockPointVO.getProductSop() ?
                                DateUtils.dateToString(newProductStockPointVO.getProductSop()) : null)
                        .eopDate(null != newProductStockPointVO.getProductEop() ?
                                DateUtils.dateToString(newProductStockPointVO.getProductEop()) : null)
                        .build();
                oemProjectInfoInputList.add(oemProjectInfoInput);
            }
        }
        log.info("主机厂车型信息数据行：{}", oemProjectInfoInputList.size());
        return oemProjectInfoInputList;
    }

    private List<ProjectMappingInput> getProjectMappingInput(String scenario) {
        List<ProjectMappingInput> projectMappingInputList = new ArrayList<>();
        List<OemVehicleModelMapVO> oemVehicleModelMapVOS = oemVehicleModelMapService.selectAll();
        for (OemVehicleModelMapVO oemVehicleModelMapVO : oemVehicleModelMapVOS) {
        	String vehicleModelCode = oemVehicleModelMapVO.getVehicleModelCode();
        	if (StrUtil.isNotEmpty(vehicleModelCode)) {
        		vehicleModelCode = "\"" + vehicleModelCode + "\"";
            }
            ProjectMappingInput projectMappingInput = ProjectMappingInput.builder()
                    .globalProjectCode(oemVehicleModelMapVO.getVehicleModelName())
                    .projectCode(vehicleModelCode)
                    .oemCode(oemVehicleModelMapVO.getOemCode())
                    .build();
            projectMappingInputList.add(projectMappingInput);
        }
        log.info("主机厂车型映射表数据行：{}", projectMappingInputList.size());
        return projectMappingInputList;
    }

    private List<PassengerCarMarketInfoInput> getPassengerCarMarketInfoInput(String scenario) {
        List<PassengerCarMarketInfoInput> passengerCarMarketInfoInputList = new ArrayList<>();
        List<PassengerCarSaleVO> passengerCarSaleVOS = passengerCarSaleService.selectAll();
        for (PassengerCarSaleVO passengerCarSaleVO : passengerCarSaleVOS) {
            PassengerCarMarketInfoInput build = PassengerCarMarketInfoInput.builder()
                    .globalProjectCode(passengerCarSaleVO.getOemCode())
                    .qty(passengerCarSaleVO.getSaleQuantity())
                    .date(null != passengerCarSaleVO.getSaleTime() ?
                            DateUtils.dateToString(passengerCarSaleVO.getSaleTime(), DateUtils.COMMON_DATE_STR3) : null)
                    .build();
            passengerCarMarketInfoInputList.add(build);
        }
        log.info("乘用车市场信息表数据行：{}", passengerCarMarketInfoInputList.size());
        return passengerCarMarketInfoInputList;
    }

    private List<OemForecastInput> getOemForecastInput(String scenario, List<HistoryOrderInput> historyOrderInput) {
        /*List<OemForecastInput> oemForecastInputList = new ArrayList<>();
        Map<String, Object> params = new HashMap<>(2);
        params.put("demandCategory", "OUTPUT_DEMAND");
        List<CleanForecastDataVO> cleanForecastDataVOS = cleanForecastDataService.selectByParams(params);
        List<CleanForecastDataDetailVO> cleanForecastDataDetailVOS = cleanForecastDataDetailService.selectAll();
        Map<String, List<CleanForecastDataDetailVO>> forecastMap = cleanForecastDataDetailVOS.stream()
                .collect(Collectors.groupingBy(CleanForecastDataDetailVO::getCleanForecastDataId));
        for (CleanForecastDataVO cleanForecastDataVO : cleanForecastDataVOS) {
            String id = cleanForecastDataVO.getId();
            if (!forecastMap.containsKey(id)) continue;
            List<CleanForecastDataDetailVO> cleanForecastDataDetailVOSOnValue = forecastMap.get(id);
            for (CleanForecastDataDetailVO cleanForecastDataDetailVO : cleanForecastDataDetailVOSOnValue) {
                String vehicleModelCode = cleanForecastDataVO.getVehicleModelCode();
                if (StrUtil.isNotEmpty(vehicleModelCode)) {
                    vehicleModelCode = "\"" + vehicleModelCode + "\"";
                }
                OemForecastInput oemForecastInput = OemForecastInput.builder()
                        .oemCode(cleanForecastDataVO.getOemCode())
                        .projectCode(vehicleModelCode)
                        .productCode(cleanForecastDataVO.getProductCode())
                        .foreSaleDate(DateUtils.dateToString(cleanForecastDataDetailVO.getForecastTime(), DateUtils
                        .COMMON_DATE_STR3))
                        .foreGenDate(DateUtils.dateToString(cleanForecastDataDetailVO.getForecastTime(), DateUtils
                        .COMMON_DATE_STR3))
                        .modifyTime(DateUtils.dateToString(cleanForecastDataDetailVO.getModifyTime(), DateUtils
                        .COMMON_DATE_STR1))
                        .foreQty(null != cleanForecastDataDetailVO.getForecastQuantity() ?
                                BigDecimalUtils.toBigDecimal(cleanForecastDataDetailVO.getForecastQuantity()) : null)
                        .build();
                oemForecastInputList.add(oemForecastInput);
            }
        }
        log.info("主机厂预测数据数据行：{}", oemForecastInputList.size());*/

        // 1.获取最新的需求预测版本id，获取该版本需求预测编制下需求类型为量产需求的数据
        List<DemandForecastEstablishmentVO> demandForecastEstablishmentList =
                selectLatestDemandForecastEstablishmentList(scenario);
        List<OemForecastInput> oemForecastInputList = new ArrayList<>();
        List<String> unKeys = new ArrayList<>();
        for (DemandForecastEstablishmentVO demandForecastEstablishmentVO : demandForecastEstablishmentList) {
            String vehicleModelCode = demandForecastEstablishmentVO.getVehicleModelCode();
            if (StrUtil.isNotEmpty(vehicleModelCode)) {
                vehicleModelCode = "\"" + vehicleModelCode + "\"";
            }
            String oemCode = demandForecastEstablishmentVO.getOemCode();
            String productCode = demandForecastEstablishmentVO.getProductCode();
            String forecastTimeStr = DateUtils.dateToString(demandForecastEstablishmentVO.getForecastTime(),
                    DateUtils.COMMON_DATE_STR3);
            OemForecastInput oemForecastInput = OemForecastInput.builder()
                    .oemCode(oemCode)
                    .projectCode(vehicleModelCode)
                    .productCode(oemCode + "&" + productCode)
                    .foreSaleDate(forecastTimeStr)
                    .foreGenDate(forecastTimeStr)
                    .modifyTime(DateUtils.dateToString(demandForecastEstablishmentVO.getModifyTime(),
                            DateUtils.COMMON_DATE_STR1))
                    .foreQty(null != demandForecastEstablishmentVO.getCustomerForecast() ?
                            BigDecimalUtils.toBigDecimal(demandForecastEstablishmentVO.getCustomerForecast()) : null)
                    .build();
            oemForecastInputList.add(oemForecastInput);
            String unKey = String.join("#", oemCode, vehicleModelCode, productCode, forecastTimeStr);
            unKeys.add(unKey);
        }

        // 2.获取仓库收发货数据中的主机厂编码，默认查3年内的数据
        List<String> oemCodeList =
                historyOrderInput.stream().map(HistoryOrderInput::getOemCode).distinct().collect(Collectors.toList());
        Date monthFirstDay = DateUtils.getMonthFirstDay(new Date());
        Date startForecastTime = DateUtils.moveMonth(monthFirstDay, -36);
        List<HistoryForecastDataVO> historyForecastDataList = historyForecastDataService.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(),
                "demandCategory", "OUTPUT_DEMAND",
                "startForecastTime", DateUtils.dateToString(startForecastTime, DateUtils.COMMON_DATE_STR1),
                "oemCodeList", oemCodeList));
        for (HistoryForecastDataVO historyForecastDataVO : historyForecastDataList) {
            String vehicleModelCode = historyForecastDataVO.getVehicleModelCode();
            if (StrUtil.isNotEmpty(vehicleModelCode)) {
                vehicleModelCode = "\"" + vehicleModelCode + "\"";
            }
            String oemCode = historyForecastDataVO.getOemCode();
            String productCode = historyForecastDataVO.getProductCode();
            String forecastTimeStr = DateUtils.dateToString(historyForecastDataVO.getForecastTime(),
                    DateUtils.COMMON_DATE_STR3);
            String unKey = String.join("#", oemCode, vehicleModelCode, productCode, forecastTimeStr);
            if (unKeys.contains(unKey)) {
                continue;
            }
            OemForecastInput oemForecastInput = OemForecastInput.builder()
                    .oemCode(oemCode)
                    .projectCode(vehicleModelCode)
                    .productCode(oemCode + "&" + productCode)
                    .foreSaleDate(forecastTimeStr)
                    .foreGenDate(forecastTimeStr)
                    .modifyTime(DateUtils.dateToString(historyForecastDataVO.getModifyTime(),
                            DateUtils.COMMON_DATE_STR1))
                    .foreQty(null != historyForecastDataVO.getForecastQuantity() ?
                            BigDecimalUtils.toBigDecimal(historyForecastDataVO.getForecastQuantity()) : null)
                    .build();
            oemForecastInputList.add(oemForecastInput);
        }
        log.info("主机厂预测数据数据行：{}", oemForecastInputList.size());
        return oemForecastInputList;
    }

    private List<HistoryOrderInput> getHistoryOrderInput(String scenario, String planPeriodStr) {
        // 1.获取最新的需求预测版本id，获取该版本需求预测编制下需求类型为量产需求的数据
        List<DemandForecastEstablishmentVO> demandForecastEstablishmentList =
                selectLatestDemandForecastEstablishmentList(scenario);
        // 2.通过主机厂编码去主机厂车型信息（mds_oem_vehicle_model）查询数据
        List<String> oemCodeList =
                demandForecastEstablishmentList.stream().map(DemandForecastEstablishmentVO::getOemCode)
                        .distinct().collect(Collectors.toList());
        List<OemVehicleModelVO> oemVehicleModelVOS = oemVehicleModelService.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(),
                "oemCodeList", oemCodeList));
        // 3.通过车型信息查找对应的物品数据信息（库存点都是SJ）
        List<String> oemVehicleModelCodes = oemVehicleModelVOS.stream().map(OemVehicleModelVO::getOemVehicleModelCode)
                .distinct().collect(Collectors.toList());
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(scenario,
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), "stockPointCode", rangeData,
                        "vehicleCodeList", oemVehicleModelCodes));
        Map<String, NewProductStockPointVO> newProductStockPointMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, e -> e, (v1, v2) -> v1));
        List<String> productCodes =
                newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).collect(Collectors.toList());
        // 通过物品编码去仓库发货记录查到对应的数据
        List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOS =
                warehouseReleaseRecordService.selectGroupByItemCodes(productCodes);
        warehouseReleaseRecordVOS.removeIf(Objects::isNull);
        warehouseReleaseRecordVOS =
                warehouseReleaseRecordVOS.stream().filter(e -> StringUtils.isNotEmpty(e.getShipmentLocatorCode()))
                        .collect(Collectors.toList());
        // 通过货位查找主机厂档案信息
        List<String> shipmentLocatorCodes =
                warehouseReleaseRecordVOS.stream().map(WarehouseReleaseRecordVO::getShipmentLocatorCode)
                        .distinct().collect(Collectors.toList());
        List<OemVO> oemVOList = oemService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                "targetStockLocations", shipmentLocatorCodes));
        Map<String, List<OemVO>> oemVOMap = oemVOList.stream()
        		.collect(Collectors.groupingBy(OemVO::getTargetStockLocation));
        Date planPeriodLastDay = DateUtils.getMonthLastDay(DateUtils.stringToDate(planPeriodStr, "yyyyMM"));
        Map<String, HistoryOrderInput> historyOrderInputMap = new HashMap<>();
        for (WarehouseReleaseRecordVO warehouseReleaseRecordVO : warehouseReleaseRecordVOS) {
            String itemCode = warehouseReleaseRecordVO.getItemCode();
            NewProductStockPointVO newProductStockPointVO = newProductStockPointMap.get(itemCode);
            if (newProductStockPointVO == null) {
                continue;
            }
            String vehicleModelCode = newProductStockPointVO.getVehicleModelCode();
            if (StrUtil.isNotEmpty(vehicleModelCode)) {
                vehicleModelCode = "\"" + vehicleModelCode + "\"";
            }
            List<OemVO> oemVOs = oemVOMap.get(warehouseReleaseRecordVO.getShipmentLocatorCode());
            if (CollectionUtils.isEmpty(oemVOs)) {
                continue;
            }
            String sumQty = warehouseReleaseRecordVO.getSumQty().toString();
            String createDate = DateUtils.dateToString(warehouseReleaseRecordVO.getCreationDate());
            //如果时间晚于计划周期，则不传给算法
            if(warehouseReleaseRecordVO.getCreationDate().after(planPeriodLastDay)) {
            	continue;
            }
            for (OemVO oemVO : oemVOs) {
            	String oemCode = oemVO.getOemCode();
                String unkey = String.join("#", itemCode, vehicleModelCode, oemCode, createDate);
                HistoryOrderInput historyOrderInput = historyOrderInputMap.get(unkey);
                if (historyOrderInput == null) {
                    historyOrderInput = HistoryOrderInput.builder()
                            .itemCode(oemCode + "&" + itemCode)
                            .projectCode(vehicleModelCode)
                            .oemCode(oemCode)
                            .sumQty(sumQty)
                            .createDate(createDate)
                            .build();
                } else {
                    sumQty =
                            new BigDecimal(historyOrderInput.getSumQty()).add(warehouseReleaseRecordVO.getSumQty()).toString();
                    historyOrderInput.setSumQty(sumQty);
                }
                historyOrderInputMap.put(unkey, historyOrderInput);
			}
        }
        List<HistoryOrderInput> historyOrderInputList = new ArrayList<>(historyOrderInputMap.values());
        log.info("仓库收发货数据数据行：{}", historyOrderInputList.size());
        return historyOrderInputList;
    }

    /**
     * 获取最新的需求预测版本id，获取该版本需求预测编制下需求类型为量产需求的数据
     * @return
     */
    private List<DemandForecastEstablishmentVO> selectLatestDemandForecastEstablishmentList(String scenario) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        String latestVersionId = demandForecastVersionService.selectLatestVersionId();
        List<DemandForecastEstablishmentVO> demandForecastEstablishmentList = demandForecastEstablishmentService
                .selectByParams(ImmutableMap.of(
                        "enabled", YesOrNoEnum.YES.getCode(),
                        "demandCategory", "OUTPUT_DEMAND",
                        "forecastVersionId", latestVersionId));
        return demandForecastEstablishmentList;
    }

}
