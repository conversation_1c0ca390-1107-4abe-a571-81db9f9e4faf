package com.yhl.scp.dfp.warehouse.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.warehouse.convertor.WarehouseReleaseToWarehouseConvertor;
import com.yhl.scp.dfp.warehouse.domain.entity.WarehouseReleaseToWarehouseDO;
import com.yhl.scp.dfp.warehouse.domain.service.WarehouseReleaseToWarehouseDomainService;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseToWarehouseDTO;
import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseToWarehouseDao;
import com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseToWarehousePO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseDayVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.utils.BasePOUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class WarehouseReleaseToWarehouseServiceImpl extends AbstractService implements WarehouseReleaseToWarehouseService {

    @Resource
    private WarehouseReleaseToWarehouseDao warehouseReleaseToWarehouseDao;

    @Resource
    private WarehouseReleaseToWarehouseDomainService warehouseReleaseToWarehouseDomainService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(WarehouseReleaseToWarehouseDTO warehouseReleaseToWarehouseDTO) {
        // 0.数据转换
        WarehouseReleaseToWarehouseDO warehouseReleaseToWarehouseDO = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Do(warehouseReleaseToWarehouseDTO);
        WarehouseReleaseToWarehousePO warehouseReleaseToWarehousePO = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Po(warehouseReleaseToWarehouseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        warehouseReleaseToWarehouseDomainService.validation(warehouseReleaseToWarehouseDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(warehouseReleaseToWarehousePO);
        warehouseReleaseToWarehouseDao.insert(warehouseReleaseToWarehousePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(WarehouseReleaseToWarehouseDTO warehouseReleaseToWarehouseDTO) {
        // 0.数据转换
        WarehouseReleaseToWarehouseDO warehouseReleaseToWarehouseDO = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Do(warehouseReleaseToWarehouseDTO);
        WarehouseReleaseToWarehousePO warehouseReleaseToWarehousePO = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Po(warehouseReleaseToWarehouseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        warehouseReleaseToWarehouseDomainService.validation(warehouseReleaseToWarehouseDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(warehouseReleaseToWarehousePO);
        warehouseReleaseToWarehouseDao.update(warehouseReleaseToWarehousePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<WarehouseReleaseToWarehouseDTO> list) {
        List<WarehouseReleaseToWarehousePO> newList = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        warehouseReleaseToWarehouseDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<WarehouseReleaseToWarehouseDTO> list) {
        List<WarehouseReleaseToWarehousePO> newList = WarehouseReleaseToWarehouseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        warehouseReleaseToWarehouseDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return warehouseReleaseToWarehouseDao.deleteBatch(idList);
        }
        return warehouseReleaseToWarehouseDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public WarehouseReleaseToWarehouseVO selectByPrimaryKey(String id) {
        WarehouseReleaseToWarehousePO po = warehouseReleaseToWarehouseDao.selectByPrimaryKey(id);
        return WarehouseReleaseToWarehouseConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "WAREHOUSE_RELEASE_TO_WAREHOUSE")
    public List<WarehouseReleaseToWarehouseVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "WAREHOUSE_RELEASE_TO_WAREHOUSE")
    public List<WarehouseReleaseToWarehouseVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<WarehouseReleaseToWarehouseVO> dataList = warehouseReleaseToWarehouseDao.selectByCondition(sortParam, queryCriteriaParam);
        WarehouseReleaseToWarehouseServiceImpl target = SpringBeanUtils.getBean(WarehouseReleaseToWarehouseServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<WarehouseReleaseToWarehouseVO> selectByParams(Map<String, Object> params) {
        List<WarehouseReleaseToWarehousePO> list = warehouseReleaseToWarehouseDao.selectByParams(params);
        return WarehouseReleaseToWarehouseConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<WarehouseReleaseToWarehouseVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<WarehouseReleaseToWarehouseVO> invocation(List<WarehouseReleaseToWarehouseVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<WarehouseReleaseToWarehouseVO> getInRoad(List<String> demandProductCodeList,
                                                    List<String> shipmentLocatorCodes) {
        return warehouseReleaseToWarehouseDao.selectInRoadWarehouse(demandProductCodeList, shipmentLocatorCodes);
    }

    @Override
    public List<String> selectTargetStockLocation() {
        return warehouseReleaseToWarehouseDao.selectTargetStockLocation();
    }

	@Override
	public List<WarehouseReleaseToWarehouseMonthVO> selectMonthVOByParams(Map<String, Object> params) {
		return warehouseReleaseToWarehouseDao.selectMonthVOByParams(params);
	}

	@Override
	public List<WarehouseReleaseToWarehouseDayVO> selectDayVOByParams(Map<String, Object> params) {
		return warehouseReleaseToWarehouseDao.selectDayVOByParams(params);
	}

}
