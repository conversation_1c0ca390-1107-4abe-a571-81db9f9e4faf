package com.yhl.scp.dfp.part.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.part.convertor.PartRelationMapConvertor;
import com.yhl.scp.dfp.part.domain.entity.PartRelationMapDO;
import com.yhl.scp.dfp.part.domain.service.PartRelationMapDomainService;
import com.yhl.scp.dfp.part.dto.PartRelationMapBasicExportDTO;
import com.yhl.scp.dfp.part.dto.PartRelationMapDTO;
import com.yhl.scp.dfp.part.infrastructure.dao.PartRelationMapDao;
import com.yhl.scp.dfp.part.infrastructure.po.PartRelationMapPO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>PartRelationMapServiceImpl</code>
 * <p>
 * 零件映射关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:25
 */
@Slf4j
@Service
public class PartRelationMapServiceImpl extends AbstractService implements PartRelationMapService {

    @Resource
    private PartRelationMapDao partRelationMapDao;

    @Resource
    private PartRelationMapDomainService partRelationMapDomainService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    public BaseResponse<Void> doCreate(PartRelationMapDTO partRelationMapDTO) {
        // 0.数据转换
        PartRelationMapDO partRelationMapDO = PartRelationMapConvertor.INSTANCE.dto2Do(partRelationMapDTO);
        PartRelationMapPO partRelationMapPO = PartRelationMapConvertor.INSTANCE.dto2Po(partRelationMapDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        partRelationMapDomainService.validation(partRelationMapDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(partRelationMapPO);
        partRelationMapDao.insertWithPrimaryKey(partRelationMapPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(PartRelationMapDTO partRelationMapDTO) {
        // 0.数据转换
        PartRelationMapDO partRelationMapDO = PartRelationMapConvertor.INSTANCE.dto2Do(partRelationMapDTO);
        PartRelationMapPO partRelationMapPO = PartRelationMapConvertor.INSTANCE.dto2Po(partRelationMapDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        partRelationMapDomainService.validation(partRelationMapDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(partRelationMapPO);
        partRelationMapDao.update(partRelationMapPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<PartRelationMapDTO> list) {
        List<PartRelationMapPO> newList = PartRelationMapConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        partRelationMapDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<PartRelationMapDTO> list) {
        List<PartRelationMapPO> newList = PartRelationMapConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        partRelationMapDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return partRelationMapDao.deleteBatch(idList);
        }
        return partRelationMapDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public PartRelationMapVO selectByPrimaryKey(String id) {
        return partRelationMapDao.selectVOByPrimaryKey(id);
    }

    @Override
    @Expression(value = "v_fdp_part_relation_map")
    public List<PartRelationMapVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_part_relation_map")
    public List<PartRelationMapVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<PartRelationMapVO> dataList = partRelationMapDao.selectByCondition(sortParam, queryCriteriaParam);
        PartRelationMapServiceImpl target = SpringBeanUtils.getBean(PartRelationMapServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<PartRelationMapVO> selectByParams(Map<String, Object> params) {
        List<PartRelationMapPO> list = partRelationMapDao.selectByParams(params);
        return PartRelationMapConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<PartRelationMapVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PART_RELATION_MAP.getCode();
    }

    @Override
    public List<PartRelationMapVO> invocation(List<PartRelationMapVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isEmpty(versionDTOList)) {
            return 0;
        }
        partRelationMapDomainService.checkDelete(versionDTOList);
        return partRelationMapDao.deleteBatchVersion(versionDTOList);
    }

    @Override
    public BaseResponse<Void> updateMDMPartMappingData(List<PartRelationMapVO> list) {
        list = list.stream()
                .filter(item -> StringUtils.isNotEmpty(item.getProductCode()) && StringUtils.isNotEmpty(item.getPartNumber()))
                .collect(Collectors.toList());
        List<PartRelationMapDTO> partRelationMapDTOS = PartRelationMapConvertor.INSTANCE.vo2Dtos(list);
        if (partRelationMapDTOS.isEmpty()){
            log.info("通过本厂编码不为空以及零件号不为空的过滤，MDM零件映射已无数据进行新增和修改。");
            return BaseResponse.success("通过本厂编码不为空以及零件号不为空的过滤，已无数据。");
        }
        //数据的插入
        List<PartRelationMapDTO> dtoS = new ArrayList<>();
        //存放存在断点时间的数据
        List<PartRelationMapDTO> addAs = new ArrayList<>();
        List<PartRelationMapPO> partRelationMapPOS = partRelationMapDao.selectByThreeParams(partRelationMapDTOS);
        //以物料编码+零件编码+版本号
        List<String> threeList = partRelationMapPOS.stream().map(item -> item.getProductCode() + "-" + item.getPartNumber() + "-" + item.getRelationVersion()).collect(Collectors.toList());
        for (PartRelationMapDTO dto : partRelationMapDTOS) {
            //判断是否有重复的
            if (!threeList.contains(dto.getProductCode() + "-" + dto.getPartNumber() + "-" + dto.getRelationVersion())) {
                dtoS.add(dto);
                if (Objects.nonNull(dto.getEffectiveStartTime()) && Objects.nonNull(dto.getOldProductCode())) {
                    addAs.add(dto);
                }
            }
            if (dtoS.size() >= 1000) {
                doCreateBatch(dtoS);
                dtoS.clear();
            }
        }
        if (!dtoS.isEmpty()) {
            doCreateBatch(dtoS);
        }
        if (!addAs.isEmpty()) {
            //过滤一样的原本厂编码数据
            List<PartRelationMapDTO> collect = addAs.stream().collect(Collectors.collectingAndThen(
                    Collectors.toMap(
                            PartRelationMapDTO::getOldProductCode,
                            dto -> dto,
                            (existing, replacement) -> existing
                    ),
                    map -> new ArrayList<>(map.values())
            ));
            //更新断点时间
            collect.stream().forEach(x -> {
                Map<String, Object> map = MapUtil.newHashMap();
                map.put("productCode", x.getOldProductCode());
                map.put("sourceType", "MDM");
                List<PartRelationMapVO> partRelationMapVOS = selectByParams(map);
                if (!partRelationMapVOS.isEmpty()){
                    partRelationMapVOS.stream().forEach(item -> {
                        item.setEffectiveEndTime(x.getEffectiveStartTime());
                    });
                    List<PartRelationMapDTO> partRelationMapDTOS1 = PartRelationMapConvertor.INSTANCE.vo2Dtos(partRelationMapVOS);
                    doUpdateBatch(partRelationMapDTOS1);
                }
            });
        }
        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Void> submission(String beginTime, String endTime, String id, String tenantCode, String resourceType) {
        try {
            log.info("开始同步零件映射");
            HashMap<String, Object> map = MapUtil.newHashMap();
            map.put("beginTime",beginTime);
            map.put("endTime",endTime);
            //id有指说明从定时过来 没值就是根据当前用户的租户来
            if (StringUtils.isEmpty(tenantCode)){
                tenantCode = SystemHolder.getTenantCode();
                SystemHolder.getCompanyCode();
                List<Scenario> data = ipsNewFeign.selectDefaultByTenantId(SystemHolder.getTenantCode());
                id = data.get(0).getAlternativeColumn();
                map.put("triggerType",YesOrNoEnum.YES.getCode());
            }
            map.put("organizeId", id);
            //获取GRP零件映射数据
            if (resourceType.contains("GRP") || resourceType.contains("grp")) {
                newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.GRP.getCode(),
                        ApiCategoryEnum.PART_MAPPING.getCode(), map);
            }
            //获取MES零件映射数据
            if (resourceType.contains("MES") || resourceType.contains("mes")) {
                newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                        ApiCategoryEnum.MES_PART_MAPPING.getCode(), map);
            }
            //获取MDM零件映射数据
            if (resourceType.contains("MDM") || resourceType.contains("mdm")) {
                newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MDM.getCode(),
                        ApiCategoryEnum.MDM_PART_MAPPING.getCode(), map);
            }
            return BaseResponse.success("同步操作完成");
        } catch (Exception e) {
            log.error("同步零件映射报错,{}", e.getMessage());
            throw new BusinessException("同步零件映射报错", e.getMessage());
        }
    }

    @Override
    public BaseResponse<List<PartRelationMapVO>> getDataByCodes(List<String> codes) {
        HashMap<String, Object> map = MapUtil.newHashMap();
        map.put("productCodeList",codes);
        List<PartRelationMapPO> partRelationMapPOS = partRelationMapDao.selectByParams(map);
        if (partRelationMapPOS.isEmpty()){
            return BaseResponse.success("数据库中没有匹配的数据");
        }
        List<PartRelationMapVO> partRelationMapVOS = PartRelationMapConvertor.INSTANCE.po2Vos(partRelationMapPOS);
        return BaseResponse.success(partRelationMapVOS);
    }

    @Override
    public BaseResponse<Void> updatePartRelationMappingData(List<PartRelationMapVO> list) {
        List<PartRelationMapDTO> partRelationMapDTOS = PartRelationMapConvertor.INSTANCE.vo2Dtos(list);
        List<PartRelationMapDTO> dtoS = new ArrayList<>();
        List<PartRelationMapDTO> updateDtoS = new ArrayList<>();
        //查询数据库中存在的数据
        List<PartRelationMapPO> partRelationMapPOS1 = partRelationMapDao.selectTwoParams(partRelationMapDTOS);
        //按照mappingId和sourceType分类
        Map<String, List<PartRelationMapPO>> dataMap = partRelationMapPOS1.stream().collect(Collectors.groupingBy(item -> item.getMappingId() + "-" + item.getCustomerId()));
        Set<String> mappingIdSourceTypeSet = dataMap.keySet();
        for (PartRelationMapDTO dto : partRelationMapDTOS) {
            dto.setEnabled(StringUtils.isBlank(dto.getEnabled()) ? YesOrNoEnum.YES.getCode()
                    : ("Y".equals(dto.getEnabled()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode()));
            if (mappingIdSourceTypeSet.contains(dto.getMappingId() + "-" + dto.getCustomerId())) {
                List<PartRelationMapPO> partRelationMapPOS = dataMap.get(dto.getMappingId() + "-" + dto.getCustomerId());
                PartRelationMapPO oldPo = partRelationMapPOS.get(0);
                String id = oldPo.getId();
                dto.setId(id);
                dto.setCreator(oldPo.getCreator());
                dto.setCreateTime(oldPo.getCreateTime());
                dto.setVersionValue(oldPo.getVersionValue());
                updateDtoS.add(dto);
            } else {
                dtoS.add(dto);
            }
            if (dtoS.size() >= 1000) {
                doCreateBatch(dtoS);
                dtoS.clear();
            }
            if (updateDtoS.size() >= 1000) {
                doUpdateBatch(updateDtoS);
                updateDtoS.clear();
            }
        }
        if (!dtoS.isEmpty()) {
            doCreateBatch(dtoS);
        }
        if (!updateDtoS.isEmpty()) {
            doUpdateBatch(updateDtoS);
        }

        return BaseResponse.success();
    }

    @Override
    public void export(HttpServletResponse response) {
        List<PartRelationMapPO> productionLimitPOList = partRelationMapDao.selectByParams(new HashMap<>(2));
        List<PartRelationMapBasicExportDTO> productionLotExportDTOS = PartRelationMapConvertor.INSTANCE.po2ExportDtos(productionLimitPOList);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("零件映射关系.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream(), PartRelationMapDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("零件映射关系")
                    .doWrite(productionLotExportDTOS);
        } catch (IOException e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public void doImport(Map<Integer, String> headers, List<Map<Integer, String>> data, int fixedColumns, Map<String, String> extMap) {
        List<PartRelationMapPO> PartRelationMapPOList = new ArrayList<>();
        for (Map<Integer, String> rowData : data) {
            PartRelationMapPO PartRelationMapPO = new PartRelationMapPO();
            if (rowData.get(0) == null) {
                throw new BusinessException("本厂编码为空");
            }
            if (rowData.get(1) == null) {
                throw new BusinessException("零件号为空");
            }

            if (rowData.get(2) == null) {
                throw new BusinessException("零件名称为空");
            }
            if (rowData.get(3) == null) {
                throw new BusinessException("是否生效为空");

            }

            PartRelationMapPO.setProductCode(rowData.get(0));
            PartRelationMapPO.setPartNumber(rowData.get(1));
            PartRelationMapPO.setPartName(rowData.get(2));
            PartRelationMapPO.setEnabled(rowData.get(3));
            PartRelationMapPO.setId(UUIDUtil.getUUID());
            partRelationMapDomainService.checkBussinessRule(PartRelationMapConvertor.INSTANCE.po2Do(PartRelationMapPO));
            PartRelationMapPOList.add(PartRelationMapPO);
        }
        BasePOUtils.insertBatchFiller(PartRelationMapPOList);
        partRelationMapDao.insertBatchWithPrimaryKey(PartRelationMapPOList);
    }

    @Override
    public void syncPartNumFromErp(Map<String, Object> params) {
        String productCode = (String) params.get("productCode");
        String partNumber = (String) params.get("partNumber");
        String sourceType = (String) params.get("sourceType");
        String partName = (String) params.get("partName");

        if (productCode == null || partNumber == null || sourceType == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        PartRelationMapPO record = partRelationMapDao.selectByProductCodeAndSourceType(productCode, sourceType);

        if (record == null) {
            PartRelationMapPO newRecord = new PartRelationMapPO();
            newRecord.setProductCode(productCode);
            newRecord.setPartNumber(partNumber);
            newRecord.setSourceType(sourceType);
            newRecord.setPartName(partName);
            newRecord.setCreator("BPIM");
            newRecord.setCreateTime(new Date());
            partRelationMapDao.insertBatch(Collections.singletonList(newRecord));
        } else {
            // 更新字段
            record.setPartNumber(partNumber);
            record.setPartName(partName);
            record.setModifier("BPIM");
            record.setModifyTime(new Date());
            partRelationMapDao.updateSelectiveById(record);
        }
    }
}
