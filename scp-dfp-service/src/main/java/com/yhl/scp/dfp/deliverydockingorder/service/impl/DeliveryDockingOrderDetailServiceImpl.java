package com.yhl.scp.dfp.deliverydockingorder.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.common.enums.StatusEnum;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.deliverydockingorder.convertor.DeliveryDockingOrderDetailConvertor;
import com.yhl.scp.dfp.deliverydockingorder.domain.entity.DeliveryDockingOrderDetailDO;
import com.yhl.scp.dfp.deliverydockingorder.domain.service.DeliveryDockingOrderDetailDomainService;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderDetailDTO;
import com.yhl.scp.dfp.deliverydockingorder.infrastructure.dao.DeliveryDockingOrderDao;
import com.yhl.scp.dfp.deliverydockingorder.infrastructure.dao.DeliveryDockingOrderDetailDao;
import com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderDetailPO;
import com.yhl.scp.dfp.deliverydockingorder.infrastructure.po.DeliveryDockingOrderPO;
import com.yhl.scp.dfp.deliverydockingorder.service.DeliveryDockingOrderDetailService;
import com.yhl.scp.dfp.deliverydockingorder.service.DeliveryDockingOrderService;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderDetailVO;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderVO;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.newProduct.enums.ProductTallyOrderModeEnum;
import com.yhl.scp.dfp.oem.enums.OemTallyOrderModeEnum;
import com.yhl.scp.dfp.oem.service.OemAddressInventoryLogService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandInterfaceLogDTO;
import com.yhl.scp.dfp.originDemand.infrastructure.dao.FdpOriginDemandInterfaceLogDao;
import com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandInterfaceLogPO;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandInterfaceLogService;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandInterfaceLogVO;
import com.yhl.scp.dfp.transport.service.TransportResourceService;
import com.yhl.scp.dfp.transport.service.TransportRoutingDetailService;
import com.yhl.scp.dfp.transport.service.TransportRoutingService;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>DeliveryDockingOrderDetailServiceImpl</code>
 * <p>
 * 发货对接单详情应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-29 13:58:17
 */
@Slf4j
@Service
public class DeliveryDockingOrderDetailServiceImpl extends AbstractService implements DeliveryDockingOrderDetailService {

    @Resource
    private DeliveryDockingOrderDetailDao deliveryDockingOrderDetailDao;

    @Resource
    private DeliveryDockingOrderDetailDomainService deliveryDockingOrderDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DeliveryDockingOrderDao deliveryDockingOrderDao;

    @Resource
    private OemService oemService;

    @Resource
    private OriginDemandVersionService originDemandVersionService;

    @Resource
    private TransportRoutingService transportRoutingService;

    @Resource
    private DeliveryPlanService deliveryPlanService;

    @Resource
    private DeliveryPlanVersionService deliveryPlanVersionService;

    @Resource
    private DeliveryDockingOrderDetailService deliveryDockingOrderDetailService;

    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    private OemAddressInventoryLogService oemAddressInventoryLogService;

    @Resource
    private TransportRoutingDetailService transportRoutingDetailService;

    @Resource
    private DeliveryPlanPublishedService deliveryPlanPublishedService;

    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @Resource
    private TransportResourceService transportResourceService;

    @Resource
    private FdpOriginDemandInterfaceLogService fdpOriginDemandInterfaceLogService;
    
    @Resource
    private FdpOriginDemandInterfaceLogDao fdpOriginDemandInterfaceLogDao;

    @Resource
    private DeliveryDockingOrderService deliveryDockingOrderService;

    private static final String N = "NO";

    private static final String Y = "YES";

    private static final String ENABLED = "enabled";

    private static final String OEMCODE = "oemCode";


    @Override
    public BaseResponse<Void> doCreate(DeliveryDockingOrderDetailDTO deliveryDockingOrderDetailDTO) {
        if (deliveryDockingOrderDetailDTO.getDeliveryDockingNumber() == null) {
            throw new BusinessException("新增明细前请先传递发货对接单号");
        }
        // 0.数据转换
        DeliveryDockingOrderDetailDO deliveryDockingOrderDetailDO = DeliveryDockingOrderDetailConvertor.INSTANCE.dto2Do(deliveryDockingOrderDetailDTO);
        DeliveryDockingOrderDetailPO deliveryDockingOrderDetailPO = DeliveryDockingOrderDetailConvertor.INSTANCE.dto2Po(deliveryDockingOrderDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryDockingOrderDetailDomainService.validation(deliveryDockingOrderDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryDockingOrderDetailPO);
        deliveryDockingOrderDetailDao.insert(deliveryDockingOrderDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DeliveryDockingOrderDetailDTO deliveryDockingOrderDetailDTO) {
        if (deliveryDockingOrderDetailDTO.getDeliveryDockingNumber() == null) {
            throw new BusinessException("修改明细前请先传递发货对接单号");
        }
        // 0.数据转换
        DeliveryDockingOrderDetailDO deliveryDockingOrderDetailDO = DeliveryDockingOrderDetailConvertor.INSTANCE.dto2Do(deliveryDockingOrderDetailDTO);
        DeliveryDockingOrderDetailPO deliveryDockingOrderDetailPO = DeliveryDockingOrderDetailConvertor.INSTANCE.dto2Po(deliveryDockingOrderDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryDockingOrderDetailDomainService.validation(deliveryDockingOrderDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryDockingOrderDetailPO);
        deliveryDockingOrderDetailDao.update(deliveryDockingOrderDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryDockingOrderDetailDTO> list) {
        List<DeliveryDockingOrderDetailPO> newList = DeliveryDockingOrderDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        for (int i = 0; i < newList.size(); i++) {
        	DeliveryDockingOrderDetailPO deliveryDockingOrderDetailPO = newList.get(i);
        	if(StringUtils.isEmpty(deliveryDockingOrderDetailPO.getDeliveryDockingLineNumber())) {
        		deliveryDockingOrderDetailPO.setDeliveryDockingLineNumber(i + "");		
        	}
		}
        deliveryDockingOrderDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryDockingOrderDetailDTO> list) {
        List<DeliveryDockingOrderDetailPO> newList = DeliveryDockingOrderDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryDockingOrderDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        List<String> failedProductCodes = new ArrayList<>();
        for (String id : idList) {
            DeliveryDockingOrderDetailPO dockingOrderDetailPO = deliveryDockingOrderDetailDao.selectByPrimaryKey(id);
            if (dockingOrderDetailPO == null) {
                failedProductCodes.add("ID: " + id + " 不存在");
                continue;
            }
            Map<String, Object> params = new HashMap<>(2);
            params.put("deliveryDockingNumber", dockingOrderDetailPO.getDeliveryDockingNumber());
            List<DeliveryDockingOrderPO> deliveryDockingOrderPOS = deliveryDockingOrderDao.selectByParams(params);
            if (CollectionUtils.isEmpty(deliveryDockingOrderPOS)) {
                failedProductCodes.add("无此对接单号的主体 " + dockingOrderDetailPO.getDeliveryDockingNumber());
                continue;
            }
            DeliveryDockingOrderPO deliveryDockingOrderPO = deliveryDockingOrderPOS.get(0);
            if (!StatusEnum.OPEN.getCode().equals(deliveryDockingOrderPO.getStatus())) {
                failedProductCodes.add("非打开状态不能删除: " + dockingOrderDetailPO.getProductCode());
            }
        }
        if (!failedProductCodes.isEmpty()) {
            throw new BusinessException(String.join(", ", failedProductCodes));
        }
        return deliveryDockingOrderDetailDao.deleteBatch(idList);
    }

    @Override
    public void doDeleteBatch(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        //过滤未保存的数据
        List<DeliveryDockingOrderDetailPO> deletedPOList = deliveryDockingOrderDetailDao.selectByPrimaryKeys(ids);
        if (CollectionUtils.isEmpty(deletedPOList)) {
        	return;
        }
        // 批量删除，删除时需要根据deliveryDockingNumber、deliveryDockingLineNumber、productCode确定唯一删除的数据
        String deliveryDockingNumber = deletedPOList.get(0).getDeliveryDockingNumber();
        Map<String, Object> params = new HashMap<>(2);
        params.put("deliveryDockingNumber", deliveryDockingNumber);
        List<DeliveryDockingOrderPO> deliveryDockingOrderPOS = deliveryDockingOrderDao.selectByParams(params);
        if (CollectionUtils.isEmpty(deliveryDockingOrderPOS)) {
            throw new BusinessException("无此对接单号的主体信息: " + deliveryDockingNumber);
        }
        if (StringUtils.isNotBlank(deliveryDockingOrderPOS.get(0).getMesTallyNumber())) {
            throw new BusinessException("非打开状态不能删除, 对接单号为： " + deliveryDockingOrderPOS.get(0).getDeliveryDockingNumber());
        }
        //删除发货对接单详情
        deletedDeliveryDockingOrderDetailList(ids, deliveryDockingOrderPOS.get(0).getOemCode());
    }

	private void deletedDeliveryDockingOrderDetailList(List<String> ids, String oemCode) {
		List<DeliveryDockingOrderDetailPO> deletedPOList = deliveryDockingOrderDetailDao.selectByPrimaryKeys(ids);
        if (CollectionUtils.isEmpty(deletedPOList)) {
        	return;
        }
		List<String> unkeyList = deletedPOList.stream()
        		.map(e -> e.getProductCode() + "#" + e.getDeliveryDockingLineNumber()).collect(Collectors.toList());
        //将产品编码，行号一样的数据一起删除
        List<DeliveryDockingOrderDetailPO> allDetailList = deliveryDockingOrderDetailDao.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"deliveryDockingNumber" , deletedPOList.get(0).getDeliveryDockingNumber()));
        for (DeliveryDockingOrderDetailPO detailPO : allDetailList) {
			if(!ids.contains(detailPO.getId()) 
					&& unkeyList.contains(detailPO.getProductCode() + "#" + detailPO.getDeliveryDockingLineNumber())) {
				deletedPOList.add(detailPO);
			}
		}
		//获取返回对接单理货单模式
        List<String> oemCodes = new ArrayList<>(Arrays.asList(oemCode.split(",")));
        List<OemVO> oemVOS = oemService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"oemCodes" , oemCodes));
        List<String> productCodes = deletedPOList.stream().map(DeliveryDockingOrderDetailPO::getProductCode)
        		.distinct().collect(Collectors.toList());
        String tallyOrderMode = this.getDeliveryDockingOrderTallyOrderMode(productCodes, oemVOS);
        //如果是GRP模式，则不能维护相同产品编码详情
        Boolean grpFlag = false;
        List<FdpOriginDemandInterfaceLogVO> orignDeamndInterfaceList = new ArrayList<>();
        if(OemTallyOrderModeEnum.GRP.getCode().equals(tallyOrderMode)) {
        	grpFlag = true;
        	List<String> relIds = deletedPOList.stream()
					.filter(e -> StringUtils.isNotEmpty(e.getDemandSourcesId()))
					.map(DeliveryDockingOrderDetailPO::getDemandSourcesId).distinct().collect(Collectors.toList());
        	if(CollectionUtils.isNotEmpty(relIds)) {
        		orignDeamndInterfaceList = fdpOriginDemandInterfaceLogService.selectByParams(ImmutableMap.of(
    	    			"enabled", YesOrNoEnum.YES.getCode(), 
    	    			"relIds" , relIds));
        	}
        }
		//按照
        Map<String,List<DeliveryDockingOrderDetailPO>> detailMap =
        		deletedPOList.stream().collect(Collectors.groupingBy(DeliveryDockingOrderDetailPO::getProductCode));
        //发运需求改动
        List<FdpOriginDemandInterfaceLogPO> updateOriginDemand = new ArrayList<>();
        List<String> deletedIds = new ArrayList<>();
        for (Map.Entry<String, List<DeliveryDockingOrderDetailPO>> entry : detailMap.entrySet()) {
        	List<DeliveryDockingOrderDetailPO> list = entry.getValue();
    		// 获取原始需求日志,还原Edi装车需求接口同步记录 可制单数量，已制单数量
    		List<FdpOriginDemandInterfaceLogVO> currorignDeamndInterfaceList = new ArrayList<>();
    		if(grpFlag) {
    			//根据relIds回退数量
    			List<String> relIds = list.stream()
    					.filter(e -> StringUtils.isNotEmpty(e.getDemandSourcesId()))
    					.map(DeliveryDockingOrderDetailPO::getDemandSourcesId).distinct().collect(Collectors.toList());
    			currorignDeamndInterfaceList = orignDeamndInterfaceList.stream()
    					.filter(e -> relIds.contains(e.getRelId())).collect(Collectors.toList());
    			currorignDeamndInterfaceList.sort(Comparator.comparing(FdpOriginDemandInterfaceLogVO::getOriginalShipTime).reversed());
    		}
        	for (DeliveryDockingOrderDetailPO deliveryDockingOrderDetailPO : list) {
        		//删除发货单明细数据
//        		deliveryDockingOrderDetailDao.deleteByParams(deliveryDockingOrderDetailPO.getDeliveryDockingNumber(),
//                        deliveryDockingOrderDetailPO.getDeliveryDockingLineNumber(), deliveryDockingOrderDetailPO.getProductCode());
        		deletedIds.add(deliveryDockingOrderDetailPO.getId());
        		BigDecimal deliveryQuantity = BigDecimal.valueOf(deliveryDockingOrderDetailPO.getDeliveryQuantity());
        		if(!YesOrNoEnum.YES.getCode().equals(deliveryDockingOrderDetailPO.getWrittenOffFlag())) {
        			continue;
        		}
        		String demandSourcesId = deliveryDockingOrderDetailPO.getDemandSourcesId();
        		for (FdpOriginDemandInterfaceLogVO updateInterfaceLog : currorignDeamndInterfaceList) {
        			if(!demandSourcesId.endsWith(updateInterfaceLog.getRelId())) {
        				continue;
        			}
        			//已制单数,可制单数量
        			BigDecimal orderedQty = updateInterfaceLog.getOrderedQty();
        			if(orderedQty == null && updateInterfaceLog.getShipQty().compareTo(BigDecimal.ZERO) > 0){
        				continue;
        			}
        			FdpOriginDemandInterfaceLogPO update = new FdpOriginDemandInterfaceLogPO();
        			update.setId(updateInterfaceLog.getId());
        			update.setVersionValue(updateInterfaceLog.getVersionValue());
        			if(orderedQty.compareTo(deliveryQuantity) >= 0) {
        				updateInterfaceLog.setOrderedQty(updateInterfaceLog.getOrderedQty().subtract(deliveryQuantity));
        				updateInterfaceLog.setAvailableQty(updateInterfaceLog.getShipQty().subtract(updateInterfaceLog.getOrderedQty()));
        				update.setOrderedQty(updateInterfaceLog.getOrderedQty());
        				update.setAvailableQty(updateInterfaceLog.getAvailableQty());
        				updateOriginDemand.add(update);
        				deliveryQuantity = BigDecimal.ZERO;
        				break;
        			}else {
        				updateInterfaceLog.setOrderedQty(BigDecimal.ZERO);
        				updateInterfaceLog.setAvailableQty(updateInterfaceLog.getShipQty().subtract(updateInterfaceLog.getOrderedQty()));
        				update.setOrderedQty(updateInterfaceLog.getOrderedQty());
        				update.setAvailableQty(updateInterfaceLog.getAvailableQty());
        				updateOriginDemand.add(update);
        				deliveryQuantity = deliveryQuantity.subtract(orderedQty);
        			}
				}
        		
        	}
        }
        if(CollectionUtils.isNotEmpty(updateOriginDemand)) {
        	BasePOUtils.updateBatchFiller(updateOriginDemand);
        	fdpOriginDemandInterfaceLogDao.updateBatchSelective(updateOriginDemand);
        }
        if(CollectionUtils.isNotEmpty(deletedIds)) {
        	deliveryDockingOrderDetailDao.deleteBatch(deletedIds);
        }
	}

    @Override
    public DeliveryDockingOrderDetailVO selectByPrimaryKey(String id) {
        DeliveryDockingOrderDetailPO po = deliveryDockingOrderDetailDao.selectByPrimaryKey(id);
        return DeliveryDockingOrderDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DELIVERY_DOCKING_ORDER_DETAIL")
    public List<DeliveryDockingOrderDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DELIVERY_DOCKING_ORDER_DETAIL")
    public List<DeliveryDockingOrderDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryDockingOrderDetailVO> dataList = deliveryDockingOrderDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryDockingOrderDetailServiceImpl target = SpringBeanUtils.getBean(DeliveryDockingOrderDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryDockingOrderDetailVO> selectByParams(Map<String, Object> params) {
        List<DeliveryDockingOrderDetailPO> list = deliveryDockingOrderDetailDao.selectByParams(params);
        return DeliveryDockingOrderDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DeliveryDockingOrderDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getBoxQuantity(String productCode, Integer deliveryQuantity) {
        if (StringUtils.isBlank(productCode) || deliveryQuantity == null) {
            throw new BusinessException("本厂编码或发货数量不能为空");
        }
        if (deliveryQuantity < 0) {
            throw new BusinessException("发货数量不能为负");
        }
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointByParams(scenario.getData(), ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"stockPointCode", rangeData,
    			"productCodes" , Collections.singletonList(productCode)));
        if (CollectionUtils.isEmpty(productList)) {
            throw new BusinessException(productCode + "未获取到对应的产品信息");
        }
        List<String> inventoryItemIds = productList.stream().filter( e -> StringUtils.isNotEmpty(e.getInventoryItemId()))
        		.map(NewProductStockPointVO::getInventoryItemId).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(inventoryItemIds)) {
        	throw new BusinessException(productCode + "未维护物品ID属性信息");
        }
        List<ProductBoxRelationVO> productBoxRelationVOS = newMdsFeign.selectProductBoxRelationByProductStockPointIds(scenario.getData(), inventoryItemIds);
        if (CollectionUtils.isEmpty(productBoxRelationVOS)) {
            throw new BusinessException("产品与成品箱关系无此物料编码，请维护数据后再试");
        }
        productBoxRelationVOS = productBoxRelationVOS.stream()
        		.filter( e -> e.getStandardLoad() != null && e.getStandardLoad() > 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productBoxRelationVOS)) {
        	throw new BusinessException("产品与成箱关系每箱标准装载量有误，请维护数据后再试");
        }
        List<ProductBoxRelationVO> filterProductBoxRelationVOS = productBoxRelationVOS.stream()
        		.filter( e -> e.getPriority() != null).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(filterProductBoxRelationVOS)) {
        	filterProductBoxRelationVOS.sort(Comparator.comparing(ProductBoxRelationVO::getPriority));
        	productBoxRelationVOS = filterProductBoxRelationVOS;
        }
        Integer piecePerBox = productBoxRelationVOS.get(0).getStandardLoad();
        // 计算deliveryQuantity数量所需要的箱子数量，每箱有piecePerBox片，需要多少箱子，结果上取整
        double boxesNeeded = Math.ceil((double) deliveryQuantity / piecePerBox);
        return String.valueOf((int) boxesNeeded);
    }

    @Override
    public List<DeliveryDockingOrderDetailVO> selectVOByParams(Map<String, Object> params) {
        if (params == null) {
            return Lists.newArrayList();
        }
        return deliveryDockingOrderDetailDao.selectVOByParams(params);
    }

    @Override
    public BaseResponse<String> doSave(List<DeliveryDockingOrderDetailDTO> deliveryDockingOrderDetailDTOS, String oemCode, String deliveryTime) {
        // 校验同对接单下本厂编码对应的edi标识是否一致
        if (CollectionUtils.isEmpty(deliveryDockingOrderDetailDTOS)) {
            throw new BusinessException("没有需要保存的数据");
        }
        //获取理货单模式(默认MES模式),优先获取物料上的理货单模式，如果物料上未维护，则获取主机厂上的理货单模式
        List<OemVO> oemVOS = getAndCheckOemCodes(oemCode);
        List<String> productCodes = deliveryDockingOrderDetailDTOS.stream()
        		.map(DeliveryDockingOrderDetailDTO::getProductCode).distinct().collect(Collectors.toList());
        String tallyOrderMode = getDeliveryDockingOrderTallyOrderMode(productCodes, oemVOS);
        
        //获取对接单号
        String deliveryDockingNumber = deliveryDockingOrderDetailDTOS.get(0).getDeliveryDockingNumber();
        //获取发货对接单数据
        DeliveryDockingOrderVO deliveryDockingData = deliveryDockingOrderService.getDeliveryDockingData(deliveryDockingNumber);
        //如果是GRP模式，则不能维护相同产品编码详情
        if(OemTallyOrderModeEnum.GRP.getCode().equals(tallyOrderMode)) {
        	if(productCodes.size() < deliveryDockingOrderDetailDTOS.size()) {
        		throw new BusinessException("当前理货单模式为GRP,不支持维护相同产品的发货单详情!");
        	}
        	//先删除，进行数量回退，再新增
            List<String> saveIds = deliveryDockingOrderDetailDTOS.stream().map(DeliveryDockingOrderDetailDTO::getId).collect(Collectors.toList());
            this.deletedDeliveryDockingOrderDetailList(saveIds, deliveryDockingData.getOemCode());
        }
        
        //记录新数据
        List<DeliveryDockingOrderDetailDTO> detailsToInsert = new ArrayList<>();
        //发运需求改动
        List<FdpOriginDemandInterfaceLogVO> updateOriginDemand = new ArrayList<>();

        // 查询已存在的记录
        List<DeliveryDockingOrderDetailPO> existingRecords = getExistingRecords(deliveryDockingOrderDetailDTOS);
        Map<String,List<DeliveryDockingOrderDetailPO>> existMap =
                existingRecords.stream().collect(Collectors.groupingBy(x -> x.getId()));
        //记录要删除的旧数据
        List<DeliveryDockingOrderDetailPO> deleteData = new ArrayList<>(existingRecords);
        //旧记录对发运需求的占用量 key -> demandSourcesId
        Map<String,Integer> existingDemandAssigned = getExistDemandAssigned(existingRecords);
        //旧纪录，没有分配数量的数据置为0，重新发送给grp
        List<DeliveryDockingOrderDetailDTO> remainExistingDetailDTOS = new ArrayList<>();
        // List<String> demandSourcesId = deliveryDockingOrderDetailDTOS.stream().map(DeliveryDockingOrderDetailDTO::getDemandSourcesId)
        //         .distinct().collect(Collectors.toList());
        //key -> productCode
        Map<String,List<DeliveryDockingOrderDetailDTO>> map =
                deliveryDockingOrderDetailDTOS.stream().collect(Collectors.groupingBy(DeliveryDockingOrderDetailDTO::getProductCode));

        //添加主机厂与产品编码的校验逻辑
        List<LabelValue<String>> productCodeLableList = deliveryDockingOrderService.getProductCodeByOemCode(oemCode);
        List<String> oemProductCodes = productCodeLableList.stream().map(LabelValue::getValue).collect(Collectors.toList());

        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(SystemHolder.getScenario(), productCodes);
        Map<String, String> productClassifyMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getProductClassify, (v1, v2) -> v1));
        
        String flagDeliveryPlan="";
//        // 获取原始需求日志
//        List<String> ediFlagOemCodes = oemVOS.stream()
//                .filter( e -> StringUtils.isBlank(e.getEdiFlag()) || e.getEdiFlag().equals(N))
//                .map(OemVO::getOemCode).collect(Collectors.toList());
//        if(!CollectionUtils.isEmpty(ediFlagOemCodes)) {
//        	Map<String, Object> paramsA = new HashMap<>(2);
//         	paramsA.put(ENABLED, YesOrNoEnum.YES.getCode());
//            paramsA.put("oemCodes", ediFlagOemCodes);
//            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = deliveryPlanPublishedService.selectByParams(paramsA);
//            List<String> deliveryOemCodes = deliveryPlanPublishedVOS.stream().map(DeliveryPlanPublishedVO::getOemCode)
//                    .distinct().collect(Collectors.toList());
//            ediFlagOemCodes.removeAll(deliveryOemCodes);
//            if (CollectionUtils.isNotEmpty(ediFlagOemCodes)) {
//                flagDeliveryPlan = "主机厂"+ ediFlagOemCodes + "无对应发货计划";
//            }
//        }
        //查询理货单模式为GRP的装车需求提报数据，Edi装车需求接口同步记录表
        Map<String, String> partNumberMap = new HashMap<>();
        Map<String, List<FdpOriginDemandInterfaceLogVO>> orignDeamndInterfaceMap = new HashMap<>();
        if(OemTallyOrderModeEnum.GRP.getCode().equals(tallyOrderMode)) {
        	String latestVersionId = originDemandVersionService.selectLatestVersionId();
            log.info("最新原始需求版本id:{}", latestVersionId);
            Map<String, Object> paramB = new HashMap<>(2);
            paramB.put(OEMCODE, oemCode);
            paramB.put("versionId", latestVersionId);
            paramB.put("productCodes", productCodes);
            log.info("查询最新版本装车需求条件:{}", paramB);
            List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOS = loadingDemandSubmissionService.selectByParams(paramB);
            partNumberMap = loadingDemandSubmissionVOS.stream()
            		.filter(e-> StringUtils.isNotEmpty(e.getPartNumber()))
            		.collect(Collectors.toMap(LoadingDemandSubmissionVO::getProductCode,
            				LoadingDemandSubmissionVO::getPartNumber,(v1, v2) -> v1));
            
            List<String> custItemNums = loadingDemandSubmissionVOS.stream()
            		.filter(e-> StringUtils.isNotEmpty(e.getPartNumber())).map(LoadingDemandSubmissionVO::getPartNumber)
            		.distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(custItemNums)) {
            	Map<String, Object> paramC = new HashMap<>(2);
                paramC.put(OEMCODE, oemCode);
                paramC.put("itemNums", productCodes);
                paramC.put("custItemNums", custItemNums);
                paramC.put("releaseStatus", "OPEN");
                List<FdpOriginDemandInterfaceLogVO> orignDeamndInterfaceList = fdpOriginDemandInterfaceLogService.selectByParams(paramC);
                orignDeamndInterfaceMap = orignDeamndInterfaceList.stream()
                		.collect(Collectors.groupingBy(e -> String.join("&", e.getItemNum(), e.getCustItemNum())));
            }
        }

        //数据遍历
        for (Map.Entry<String, List<DeliveryDockingOrderDetailDTO>> entry : map.entrySet()) {
        	//对于通用类别为BB、BG、BJ开头的物料（不用管“.”后面是什么后缀），不校验车型和主机厂的关系
        	String productClassify = productClassifyMap.getOrDefault(entry.getKey(), "");
        	if(!oemProductCodes.contains(entry.getKey())
        			&& (!productClassify.startsWith("BB") && !productClassify.startsWith("BG") && !productClassify.startsWith("BJ"))){
        		throw new BusinessException("产品编码" + entry.getKey() + "和主机厂"+ oemCode +"不匹配");
        	}
            List<DeliveryDockingOrderDetailDTO> list = entry.getValue();
            //理货单模式为GRP，需要查询装车需求提报数据
            List<FdpOriginDemandInterfaceLogVO> orignDeamndInterfaceList = new ArrayList<>();
            if(OemTallyOrderModeEnum.GRP.getCode().equals(tallyOrderMode)) {
            	String partNumber = partNumberMap.get(entry.getKey());
            	if(StringUtils.isEmpty(partNumber)) {
                	throw new BusinessException("无此" + oemCode + "与" + entry.getKey() + "最新版本装车需求，请维护数据后再试");
                }
            	orignDeamndInterfaceList = orignDeamndInterfaceMap.get(String.join("&", entry.getKey(), partNumber));
            	if(CollectionUtils.isEmpty(orignDeamndInterfaceList)) {
                	throw new BusinessException("无此" + oemCode + "与" + entry.getKey() + "edi需求");
                }
            	// 按originalShipTime时间排序
                orignDeamndInterfaceList.sort(Comparator.comparing(FdpOriginDemandInterfaceLogVO::getOriginalShipTime));
                resetOrderQuantity(orignDeamndInterfaceList,existingDemandAssigned);
                updateOriginDemand.addAll(orignDeamndInterfaceList);
            }
            //Iterator<DeliveryDockingOrderDetailPO> existingIterator = existingRecords.iterator();
            for (DeliveryDockingOrderDetailDTO deliveryDockingOrderDetailDTO : list) {
                //checkDeliveryQuantity(deliveryDockingOrderDetailDTO.getDeliveryQuantity());
                if (StringUtils.isBlank(deliveryDockingOrderDetailDTO.getProductCode()) || StringUtils.isBlank(oemCode)
                        || StringUtils.isBlank(deliveryTime)
                        || StringUtils.isBlank(deliveryDockingOrderDetailDTO.getDeliveryDockingLineNumber())
                        || StringUtils.isBlank(deliveryDockingOrderDetailDTO.getDeliveryDockingNumber())
                        || deliveryDockingOrderDetailDTO.getDeliveryQuantity() == null) {
                    throw new BusinessException("对接单行号、产品编码、主机厂编码、发货数量、对接单号、发货时间不能为空");
                }
                if (deliveryDockingOrderDetailDTO.getDeliveryQuantity() < 0) {
                    throw new BusinessException("发货数量不能小于或等于0");
                }

                //校验发货详情的发货时间和发货对接单的时间是否一致
            	String dockingDeliveryTimeStr = DateUtils.dateToString(deliveryDockingData.getDeliveryTime());
            	String dockingDetailDeliveryTimeStr = DateUtils.dateToString(deliveryDockingOrderDetailDTO.getDeliveryTime());
    			if(!Objects.equals(dockingDeliveryTimeStr, dockingDetailDeliveryTimeStr)) {
    				throw new BusinessException("产品编码：" + deliveryDockingOrderDetailDTO.getProductCode()
    				+ "，发货时间" + DateUtils.dateToString(deliveryDockingOrderDetailDTO.getDeliveryTime())+ "和对接单发货时间不一致!");
    			}

                int remainingQuantity = deliveryDockingOrderDetailDTO.getDeliveryQuantity();
                String key = deliveryDockingOrderDetailDTO.getId();
                List<DeliveryDockingOrderDetailPO> existDetailPOS = new ArrayList<>();
                if (StringUtils.isNotEmpty(key) && existMap.containsKey(key)) {
                    existDetailPOS = existMap.get(key);
                }
                Iterator<DeliveryDockingOrderDetailPO> existingIterator = existDetailPOS.iterator();
                for (FdpOriginDemandInterfaceLogVO logVO : orignDeamndInterfaceList) {
                    if (remainingQuantity <= 0) {
                        break;
                    }
                    //int demandAssigned = existingDemandAssigned.getOrDefault(logVO.getRelId(), 0);
                    //logVO已经和发运单需求分配量做了扣减，所以直接使用logVO的订单量即可
                    int originDemand = getOriginDemand(logVO);
                    if (originDemand <= 0){
                        continue;
                    }
                    DeliveryDockingOrderDetailDTO detailDTO = getDeliveryDockingOrderDetailDTO(deliveryDockingOrderDetailDTO, logVO);
                    if (existingIterator.hasNext()){
                        detailDTO.setId(existingIterator.next().getId());
                    }else {
                        detailDTO.setId(UUIDUtil.getUUID());
                    }
                    BigDecimal orderedQuantity = logVO.getOrderedQty();
                    if (orderedQuantity == null){
                        orderedQuantity = BigDecimal.ZERO;
                    }
                    BigDecimal shipQuantity = logVO.getShipQty();
                    if (shipQuantity == null){
                        shipQuantity = BigDecimal.ZERO;
                    }
                    if (remainingQuantity <= originDemand) {
                        detailDTO.setDeliveryQuantity(remainingQuantity);
                        detailDTO.setWrittenOffFlag(YesOrNoEnum.YES.getCode());                        
                        detailsToInsert.add(detailDTO);
                        remainingQuantity = 0;
                        logVO.setOrderedQty(orderedQuantity.add(new BigDecimal(detailDTO.getDeliveryQuantity())));
                        logVO.setAvailableQty(shipQuantity.subtract(logVO.getOrderedQty()));
                        break;
                    } else {
                        detailDTO.setDeliveryQuantity(originDemand);
                        //detailDTO.setId(UUIDUtil.getUUID());
                        detailDTO.setWrittenOffFlag(YesOrNoEnum.YES.getCode());
                        detailsToInsert.add(detailDTO);
                        remainingQuantity -= originDemand;
                    }
                    logVO.setOrderedQty(orderedQuantity.add(new BigDecimal(detailDTO.getDeliveryQuantity())));
                    logVO.setAvailableQty(shipQuantity.subtract(logVO.getOrderedQty()));
                }
                //理货单为MES/理货单是GRP但是发货数量为0
                if (CollectionUtils.isEmpty(orignDeamndInterfaceList)
                		|| (deliveryDockingOrderDetailDTO.getDeliveryQuantity() == 0 
                		&& OemTallyOrderModeEnum.GRP.getCode().equals(tallyOrderMode))){
                    DeliveryDockingOrderDetailDTO detailDTO =
                            getDeliveryDockingOrderDetailDTO(deliveryDockingOrderDetailDTO, null);
                    detailDTO.setDeliveryQuantity(deliveryDockingOrderDetailDTO.getDeliveryQuantity());
                    if (existingIterator.hasNext()){
                        detailDTO.setId(existingIterator.next().getId());
                        existingIterator.remove();
                    }else {
                        detailDTO.setId(UUIDUtil.getUUID());
                    }
                    detailsToInsert.add(detailDTO);
                    while (existingIterator.hasNext()){
                        existingIterator.next();
                        existingIterator.remove();
                    }
                } else if (remainingQuantity > 0){
                    throw new BusinessException("产品编码：" + deliveryDockingOrderDetailDTO.getProductCode()
                            + "，发货数量：" + deliveryDockingOrderDetailDTO.getDeliveryQuantity()+",发运需求不能满足发货数量分配"
                    );
                }
                //旧纪录，没有分配数量的数据置为0，重新发送给grp
                if (existingIterator.hasNext()){
                    while (existingIterator.hasNext()){
                        DeliveryDockingOrderDetailDTO detailDTO =
                                getDeliveryDockingOrderDetailDTO(deliveryDockingOrderDetailDTO, null);
                        DeliveryDockingOrderDetailPO po = existingIterator.next();
                        detailDTO.setId(po.getId());
                        detailDTO.setDemandSourcesId(po.getDemandSourcesId());
                        detailDTO.setDeliveryQuantity(0);
                        remainExistingDetailDTOS.add(detailDTO);
                    }
                }
            }
        }

        //获取发货对接单详情单数据
        HashMap<String, Object> detailMap = MapUtil.newHashMap();
        detailMap.put("deliveryDockingNumber", deliveryDockingNumber);
        List<DeliveryDockingOrderDetailVO> deliveryDetailData = deliveryDockingOrderDetailService.selectByParams(detailMap);
        //获取已存在的记录的id并进行过滤
        List<String> idsToDelete = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(deleteData)) {
            idsToDelete = deleteData.stream()
                    .map(DeliveryDockingOrderDetailPO::getId)
                    .collect(Collectors.toList());
            List<String> finalIdsToDelete = idsToDelete;
            deliveryDetailData =
                   deliveryDetailData.stream().filter(x -> !finalIdsToDelete.contains(x.getId())).collect
                   (Collectors.toList());
        }
        
        //当理货单模式为GRP时,对没有匹配到需求来源的产品进行提示
        List<String> errorDemandSourceCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(detailsToInsert) && OemTallyOrderModeEnum.GRP.getCode().equals(tallyOrderMode)) {
        	errorDemandSourceCodes = detailsToInsert.stream().filter( e->StringUtils.isEmpty(e.getDemandSourcesId()))
        			.map(DeliveryDockingOrderDetailDTO::getProductCode).distinct().collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(errorDemandSourceCodes)) {
        	throw new BusinessException("产品编码" + String.join(",", errorDemandSourceCodes) + "没有匹配到没有匹配到edi日需求需求");
        }
        //判断保存的数据是否已经发送过理货单给MES或GRP
        if (StringUtils.isNotEmpty(deliveryDockingData.getMesTallyNumber())){
            List<DeliveryDockingOrderDetailDTO> sumList = new ArrayList<>(detailsToInsert);
            sumList.addAll(remainExistingDetailDTOS);
            //拼接新数据并进行发送至grp和mes
            List<DeliveryDockingOrderDetailVO> collect = sumList.stream().map(x -> {
                DeliveryDockingOrderDetailVO detailVO = new DeliveryDockingOrderDetailVO();
                BeanUtils.copyProperties(x, detailVO);
                return detailVO;
            }).collect(Collectors.toList());
            deliveryDetailData.addAll(collect);
            //判断调用mes或grp
            deliveryDockingOrderService.doCheckData(deliveryDockingData, deliveryDetailData, deliveryDockingNumber);
        }
        //删除已存在的数据
        if (!idsToDelete.isEmpty()) {
            deliveryDockingOrderDetailDao.deleteBatch(idsToDelete);
        }
        //批量插入新的记录
        if (CollectionUtils.isNotEmpty(detailsToInsert)) {
            List<DeliveryDockingOrderDetailPO> newList = DeliveryDockingOrderDetailConvertor.INSTANCE.dto2Pos(detailsToInsert);
            BasePOUtils.insertBatchFiller(newList);
            deliveryDockingOrderDetailDao.insertBatchWithPrimaryKey(newList);
        }
        List<FdpOriginDemandInterfaceLogDTO> updateOriginDemandDTOS = new ArrayList<>();
        for (FdpOriginDemandInterfaceLogVO logVO : updateOriginDemand){
            FdpOriginDemandInterfaceLogDTO logDTO = new FdpOriginDemandInterfaceLogDTO();
            BeanUtils.copyProperties(logVO,logDTO);
            updateOriginDemandDTOS.add(logDTO);
        }
        if (CollectionUtils.isNotEmpty(updateOriginDemandDTOS)){
            fdpOriginDemandInterfaceLogService.doUpdateBatch(updateOriginDemandDTOS);
        }
        if (StringUtils.isNotBlank(flagDeliveryPlan)) {
            return BaseResponse.success("保存成功:" + flagDeliveryPlan);
        }else {
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        }
    }

    @Override
	public List<OemVO> getAndCheckOemCodes(String oemCode) {
		List<String> oemCodeList = new ArrayList<>(Arrays.asList(oemCode.split(",")));
		HashMap<String, Object> oemMap = MapUtil.newHashMap();
        oemMap.put("oemCodes", new ArrayList<>(Arrays.asList(oemCode.split(","))));
        oemMap.put(ENABLED, YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOS = oemService.selectByParams(oemMap);
        if (oemVOS.isEmpty()) {
        	log.error("该数据的主机厂编码是{}，但是在主机厂表中匹配不到数据。", oemCodeList.toString());
            throw new BusinessException("该数据的主机厂编码是" + oemCodeList.toString() + "，但是在主机厂表中匹配不到数据。");
        }else {
        	List<String> currOemCodes = oemVOS.stream().map(OemVO::getOemCode).collect(Collectors.toList());
        	List<String> requestOemCodes = new ArrayList<>(Arrays.asList(oemCode.split(",")));
        	requestOemCodes.removeAll(currOemCodes);
        	if(CollectionUtils.isNotEmpty(requestOemCodes)) {
        		log.error("该数据的主机厂编码是{}，但是在主机厂表中匹配不到数据。", requestOemCodes.toString());
                throw new BusinessException("该数据的主机厂编码是" + requestOemCodes.toString() + "，但是在主机厂表中匹配不到数据。");
        	}
        }
		return oemVOS;
	}

    public void resetOrderQuantity(List<FdpOriginDemandInterfaceLogVO> fdpOriginDemandInterfaceLogVOS,Map<String,
            Integer> existingDemandAssigned){
        if (CollectionUtils.isEmpty(fdpOriginDemandInterfaceLogVOS)){
            return;
        }
        fdpOriginDemandInterfaceLogVOS.forEach(x->{
            if (x.getOrderedQty() != null && existingDemandAssigned.containsKey(x.getRelId())){
                x.setOrderedQty(x.getOrderedQty().subtract(new BigDecimal(existingDemandAssigned.get(x.getRelId()))));
            }
        });

    }

    public List<DeliveryDockingOrderDetailPO> getExistingRecords(List<DeliveryDockingOrderDetailDTO> deliveryDockingOrderDetailDTOS){
    	List<String> ids = deliveryDockingOrderDetailDTOS.stream()
        		.filter(e -> StringUtils.isNotEmpty(e.getId()))
        		.map(DeliveryDockingOrderDetailDTO::getId).collect(Collectors.toList());
        return deliveryDockingOrderDetailDao.selectByPrimaryKeys(ids);
    }

    private int getOriginDemand(FdpOriginDemandInterfaceLogVO logVO){
        if (logVO.getShipQty() == null){
            return 0;
        }else if (logVO.getOrderedQty() == null){
            return logVO.getShipQty().intValue();
        }
        return logVO.getShipQty().subtract(logVO.getOrderedQty()).intValue();
    }

    private Map<String,Integer> getExistDemandAssigned(List<DeliveryDockingOrderDetailPO> DeliveryDockingOrderDetailPOS){
        Map<String,Integer> result = new HashMap<>();
        DeliveryDockingOrderDetailPOS.forEach(x->{
            Integer demandAssigned = result.computeIfAbsent(x.getDemandSourcesId(), k -> 0);
            result.put(x.getDemandSourcesId(),demandAssigned+x.getDeliveryQuantity());
        });
        return result;
    }
    private static DeliveryDockingOrderDetailDTO getDeliveryDockingOrderDetailDTO(DeliveryDockingOrderDetailDTO deliveryDockingOrderDetailDTO, FdpOriginDemandInterfaceLogVO logVO) {
        DeliveryDockingOrderDetailDTO detailDTO = new DeliveryDockingOrderDetailDTO();
        //detailDTO.setDeliveryQuantity(logVO.getShipQty().intValue());
        detailDTO.setDeliveryDockingNumber(deliveryDockingOrderDetailDTO.getDeliveryDockingNumber());
        detailDTO.setDeliveryDockingLineNumber(deliveryDockingOrderDetailDTO.getDeliveryDockingLineNumber());
        detailDTO.setDeliveryDockingId(deliveryDockingOrderDetailDTO.getDeliveryDockingId());
        detailDTO.setProductCode(deliveryDockingOrderDetailDTO.getProductCode());
        detailDTO.setOemCode(deliveryDockingOrderDetailDTO.getOemCode());
        detailDTO.setMustQuantity(deliveryDockingOrderDetailDTO.getMustQuantity());
        detailDTO.setMaterialEquipment(deliveryDockingOrderDetailDTO.getMaterialEquipment());
        detailDTO.setBoxNumber(deliveryDockingOrderDetailDTO.getBoxNumber());
        detailDTO.setDeliveryTime(deliveryDockingOrderDetailDTO.getDeliveryTime());
        detailDTO.setGrossWeight(deliveryDockingOrderDetailDTO.getGrossWeight());
        detailDTO.setVolume(deliveryDockingOrderDetailDTO.getVolume());
        detailDTO.setCabinetType(deliveryDockingOrderDetailDTO.getCabinetType());
        detailDTO.setBillLadingNumber(deliveryDockingOrderDetailDTO.getBillLadingNumber());
        detailDTO.setInvoiceNumber(deliveryDockingOrderDetailDTO.getInvoiceNumber());
        detailDTO.setActualDeliveryQuantity(deliveryDockingOrderDetailDTO.getActualDeliveryQuantity());
        detailDTO.setActualDeliveryBoxQuantity(deliveryDockingOrderDetailDTO.getActualDeliveryBoxQuantity());
        detailDTO.setStatus(deliveryDockingOrderDetailDTO.getStatus());
        detailDTO.setVersionCode(deliveryDockingOrderDetailDTO.getVersionCode());
        detailDTO.setDataSources(deliveryDockingOrderDetailDTO.getDataSources());
        if (logVO != null){
        	detailDTO.setDemandSourcesId(logVO.getRelId());
        }
        detailDTO.setBusinessType(deliveryDockingOrderDetailDTO.getBusinessType());
        detailDTO.setMarketType(deliveryDockingOrderDetailDTO.getMarketType());
        detailDTO.setRemark(deliveryDockingOrderDetailDTO.getRemark());
        return detailDTO;
    }

    private Set<String> getExistingEdiFlags(String deliveryDockingNumber) {
        Map<String, Object> params = new HashMap<>();
        params.put("deliveryDockingNumber", deliveryDockingNumber);
        List<DeliveryDockingOrderDetailPO> existingRecords = deliveryDockingOrderDetailDao.selectByParams(params);
        return existingRecords.stream()
                .map(DeliveryDockingOrderDetailPO::getProductCode)
                .collect(Collectors.toSet());
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DELIVERY_DOCKING_ORDER_DETAIL.getCode();
    }

    @Override
    public List<DeliveryDockingOrderDetailVO> invocation(List<DeliveryDockingOrderDetailVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }
    
    @Override
    public String getDeliveryDockingOrderTallyOrderMode(List<String> productCodeList, List<OemVO> oemVOS) {
		//先获取物料上的理货单模式，如果未获取到，再取主机厂上的理货单模式
        String tallyOrderMode = OemTallyOrderModeEnum.MES.getCode();
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointVO> productInfoList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"stockPointCode", rangeData, 
    			"productCodes" , productCodeList));
        List<String> queryProductCodes = productInfoList.stream().map(NewProductStockPointVO::getProductCode).collect(Collectors.toList());
        List<Object> allProductCodes = new ArrayList<>();
        allProductCodes.addAll(productCodeList);
        allProductCodes.removeAll(queryProductCodes);
        if(CollectionUtils.isNotEmpty(allProductCodes)) {
        	throw new BusinessException("未获取到："+ allProductCodes.toString() + "物料主数据信息");
        }
        List<String> tallyOrderModes = productInfoList.stream().map(e -> StringUtils.isEmpty(e.getTallyOrderMode()) 
		                		? "空": e.getTallyOrderMode()).distinct().collect(Collectors.toList());
        if(tallyOrderModes.size() > 1 && tallyOrderModes.contains(ProductTallyOrderModeEnum.GRP.getCode())){
        	throw new BusinessException("产品编码理货单模式不一致");
		}
        if(tallyOrderModes.size() == 1 && "空".equals(tallyOrderModes.get(0))) {
            	for (OemVO oemVO : oemVOS) {
        			if(StringUtils.isEmpty(oemVO.getTallyOrderMode())) {
        				throw new BusinessException("主机厂" + oemVO.getOemCode() + "理货单模式未维护");
        			}
                	if(OemTallyOrderModeEnum.GRP.getCode().equals(oemVO.getTallyOrderMode()) && oemVOS.size() > 1) {
        				throw new BusinessException("主机厂" + oemVO.getOemCode() + "存在理货单模式为GRP");
        			}
        		}
            tallyOrderMode = oemVOS.get(0).getTallyOrderMode();
        }else {
        	tallyOrderModes.remove("空");
        	tallyOrderMode = tallyOrderModes.get(0);
        }
		return tallyOrderMode;
	}

}
