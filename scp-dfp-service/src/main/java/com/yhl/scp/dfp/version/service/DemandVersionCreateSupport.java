package com.yhl.scp.dfp.version.service;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.dfp.clean.service.*;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDao;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDetailDao;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastVersionDao;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanVersionDao;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanDetailService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastEstablishmentDao;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.dao.ProjectForecastVersionDao;
import com.yhl.scp.dfp.demand.service.DemandForecastEstablishmentService;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.origin.infrastructure.dao.OriginDemandVersionDao;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.projectForecast.infrastructure.dao.ProjectForecastPresentationDao;
import com.yhl.scp.dfp.projectForecast.infrastructure.dao.ProjectForecastPresentationDetailDao;
import com.yhl.scp.dfp.stock.service.InventoryShiftService;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import java.util.Collections;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <code>DemandVersionCreateSupport</code>
 * <p>
 * 版本创建通用数据查询类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-04 13:34:14
 */
public class DemandVersionCreateSupport {

    @Resource
    protected OemService oemService;

    @Resource
    protected DemandVersionDao demandVersionDao;

    @Resource
    protected OriginDemandVersionService originDemandVersionService;

    @Resource
    protected OriginDemandVersionDao originDemandVersionDao;

    @Resource
    protected DemandForecastVersionDao demandForecastVersionDao;

    @Resource
    protected DemandForecastEstablishmentDao demandForecastEstablishmentDao;

    @Resource
    protected DemandVersionService demandVersionService;

    @Resource
    protected ConsistenceDemandForecastVersionDao consistenceDemandForecastVersionDao;

    @Resource
    protected ConsistenceDemandForecastDataDao consistenceDemandForecastDataDao;

    @Resource
    protected ConsistenceDemandForecastDataDetailDao consistenceDemandForecastDataDetailDao;

    @Resource
    protected DeliveryPlanVersionDao deliveryPlanVersionDao;

    @Resource
    protected ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;

    @Resource
    protected ConsistenceDemandForecastDataService consistenceDemandForecastDataService;

    @Resource
    protected ConsistenceDemandForecastDataDetailService consistenceDemandForecastDataDetailService;

    @Resource
    protected IpsFeign ipsFeign;

    @Resource
    protected IpsNewFeign ipsNewFeign;

    @Resource
    protected NewMdsFeign newMdsFeign;

    @Resource
    protected ProjectForecastVersionDao projectForecastVersionDao;

    @Resource
    protected ProjectForecastPresentationDao projectForecastPresentationDao;

    @Resource
    protected ProjectForecastPresentationDetailDao projectForecastPresentationDetailDao;

    @Resource
    protected DemandForecastEstablishmentService demandForecastEstablishmentService;

    @Resource
    protected CleanDemandDataDetailService cleanDemandDataDetailService;

    @Resource
    protected CleanForecastDataService cleanForecastDataService;

    @Resource
    protected CleanForecastDataDetailService cleanForecastDataDetailService;

    @Resource
    protected CleanAlgorithmDataService cleanAlgorithmDataService;

    @Resource
    protected LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    @Resource
    protected LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    protected DeliveryPlanService deliveryPlanService;

    @Resource
    protected DeliveryPlanDetailService deliveryPlanDetailService;

    @Resource
    protected InventoryShiftService inventoryShiftService;

    /**
     * 需求版本管理最新版本号查询(日需求、滚动预测、预测算法)
     *
     * @param versionType 版本类型
     * @param planPeriod  计划周期
     * @param creator     创建人
     * @return java.lang.String
     */
    @SuppressWarnings("unused")
    protected String selectLatestVersionCodeByVersionTypeAndPlanPeriod(String versionType, String planPeriod,
                                                                       String creator) {
        return demandVersionDao.selectLatestVersionCodeByVersionTypeAndPlanPeriod(versionType, planPeriod, creator);
    }

    /**
     * 根据用户权限获取oemCode
     *
     * @return java.util.List<com.yhl.scp.dfp.oem.vo.OemVO>
     */
    protected List<OemVO> getOemCodeByUserPermission() {
        return oemService.getOemCodeByUserPermission();
    }

    /**
     * 根据版本id获取oemCode
     *
     * @param versionId 版本id
     * @return java.util.List<java.lang.String>
     */
    @SuppressWarnings("unused")
    protected List<String> getOemCodeByVersionId(String versionId) {
        List<DemandVersionVO> demandVersionVOS = this.demandVersionService.selectByParams(ImmutableMap.of(
                "parentVersionId", versionId));
        if (CollectionUtils.isNotEmpty(demandVersionVOS)) {
            return demandVersionVOS.stream().map(DemandVersionVO::getOemCode)
                    .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    /**
     * 获取最新版本ID
     *
     * @return java.lang.String
     */
    protected String selectLatestVersionId() {
        return originDemandVersionDao.selectLatestVersionId();
    }

}