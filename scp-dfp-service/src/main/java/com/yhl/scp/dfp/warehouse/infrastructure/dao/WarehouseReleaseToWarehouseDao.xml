<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseToWarehouseDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseToWarehousePO">
        <!--@Table fdp_warehouse_release_to_warehouse-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="plant_code" jdbcType="VARCHAR" property="plantCode"/>
        <result column="is_receive" jdbcType="VARCHAR" property="isReceive"/>
        <result column="list_num" jdbcType="VARCHAR" property="listNum"/>
        <result column="req_num" jdbcType="VARCHAR" property="reqNum"/>
        <result column="bar_num" jdbcType="VARCHAR" property="barNum"/>
        <result column="box_num" jdbcType="VARCHAR" property="boxNum"/>
        <result column="item_code" jdbcType="VARCHAR" property="itemCode"/>
        <result column="descriptions" jdbcType="VARCHAR" property="descriptions"/>
        <result column="sum_qty" jdbcType="VARCHAR" property="sumQty"/>
        <result column="container_num" jdbcType="VARCHAR" property="containerNum"/>
        <result column="bill_of_lading_num" jdbcType="VARCHAR" property="billOfLadingNum"/>
        <result column="ship_company" jdbcType="VARCHAR" property="shipCompany"/>
        <result column="instock_source" jdbcType="VARCHAR" property="instockSource"/>
        <result column="attribute1" jdbcType="VARCHAR" property="attribute1"/>
        <result column="attribute2" jdbcType="VARCHAR" property="attribute2"/>
        <result column="shipment_warehouse_code" jdbcType="VARCHAR" property="shipmentWarehouseCode"/>
        <result column="shipment_locator_code" jdbcType="VARCHAR" property="shipmentLocatorCode"/>
        <result column="req" jdbcType="VARCHAR" property="req"/>
        <result column="line_num" jdbcType="VARCHAR" property="lineNum"/>
        <result column="type_coode" jdbcType="VARCHAR" property="typeCoode"/>
        <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate"/>
        <result column="consigner" jdbcType="VARCHAR" property="consigner"/>
        <result column="in_warehouse_time" jdbcType="TIMESTAMP" property="inWarehouseTime"/>
        <result column="acreage" jdbcType="VARCHAR" property="acreage"/>
        <result column="acreage_sum" jdbcType="VARCHAR" property="acreageSum"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="lot_number" jdbcType="VARCHAR" property="lotNumber"/>
        <result column="customer_number" jdbcType="VARCHAR" property="customerNumber"/>
        <result column="ebs_site_id" jdbcType="VARCHAR" property="ebsSiteId"/>
        <result column="cust_part" jdbcType="VARCHAR" property="custPart"/>
        <result column="cust_po" jdbcType="VARCHAR" property="custPo"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="estimated_arrive_port_time" jdbcType="TIMESTAMP" property="estimatedArrivePortTime"/>
        <result column="actual_arrive_port_time" jdbcType="TIMESTAMP" property="actualArrivePortTime"/>
        <result column="estimated_completion_time" jdbcType="TIMESTAMP" property="estimatedCompletionTime"/>
        <result column="actual_completion_time" jdbcType="TIMESTAMP" property="actualCompletionTime"/>
        <result column="car_num" jdbcType="VARCHAR" property="carNum"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,plant_code,is_receive,list_num,req_num,bar_num,box_num,item_code,descriptions,sum_qty,container_num,bill_of_lading_num,ship_company,instock_source,attribute1,attribute2,shipment_warehouse_code,shipment_locator_code,req,line_num,type_coode,creation_date,consigner,in_warehouse_time,acreage,acreage_sum,remark,enabled,creator,create_time,modifier,modify_time,version_value,kid,lot_number,customer_number,ebs_site_id,cust_part,cust_po,source_type,estimated_arrive_port_time,actual_arrive_port_time,estimated_completion_time,actual_completion_time,car_num
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.plantCode != null and params.plantCode != ''">
                and plant_code = #{params.plantCode,jdbcType=VARCHAR}
            </if>
            <if test="params.isReceive != null and params.isReceive != ''">
                and is_receive = #{params.isReceive,jdbcType=VARCHAR}
            </if>
            <if test="params.listNum != null and params.listNum != ''">
                and list_num = #{params.listNum,jdbcType=VARCHAR}
            </if>
            <if test="params.reqNum != null and params.reqNum != ''">
                and req_num = #{params.reqNum,jdbcType=VARCHAR}
            </if>
            <if test="params.barNum != null and params.barNum != ''">
                and bar_num = #{params.barNum,jdbcType=VARCHAR}
            </if>
            <if test="params.boxNum != null and params.boxNum != ''">
                and box_num = #{params.boxNum,jdbcType=VARCHAR}
            </if>
            <if test="params.itemCode != null and params.itemCode != ''">
                and item_code = #{params.itemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.itemCodes != null and params.itemCodes.size()">
                and item_code in
                <foreach collection="params.itemCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.descriptions != null and params.descriptions != ''">
                and descriptions = #{params.descriptions,jdbcType=VARCHAR}
            </if>
            <if test="params.sumQty != null">
                and sum_qty = #{params.sumQty,jdbcType=VARCHAR}
            </if>
            <if test="params.containerNum != null and params.containerNum != ''">
                and container_num = #{params.containerNum,jdbcType=VARCHAR}
            </if>
            <if test="params.billOfLadingNum != null and params.billOfLadingNum != ''">
                and bill_of_lading_num = #{params.billOfLadingNum,jdbcType=VARCHAR}
            </if>
            <if test="params.shipCompany != null and params.shipCompany != ''">
                and ship_company = #{params.shipCompany,jdbcType=VARCHAR}
            </if>
            <if test="params.instockSource != null and params.instockSource != ''">
                and instock_source = #{params.instockSource,jdbcType=VARCHAR}
            </if>
            <if test="params.attribute1 != null and params.attribute1 != ''">
                and attribute1 = #{params.attribute1,jdbcType=VARCHAR}
            </if>
            <if test="params.attribute2 != null and params.attribute2 != ''">
                and attribute2 = #{params.attribute2,jdbcType=VARCHAR}
            </if>
            <if test="params.shipmentWarehouseCode != null and params.shipmentWarehouseCode != ''">
                and shipment_warehouse_code = #{params.shipmentWarehouseCode,jdbcType=VARCHAR}
            </if>
            <if test="params.shipmentLocatorCode != null and params.shipmentLocatorCode != ''">
                and shipment_locator_code = #{params.shipmentLocatorCode,jdbcType=VARCHAR}
            </if>
            <if test="params.shipmentLocatorCodes != null and params.shipmentLocatorCodes.size() > 0">
                and shipment_locator_code in
                <foreach collection="params.shipmentLocatorCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.req != null and params.req != ''">
                and req = #{params.req,jdbcType=VARCHAR}
            </if>
            <if test="params.lineNum != null and params.lineNum != ''">
                and line_num = #{params.lineNum,jdbcType=VARCHAR}
            </if>
            <if test="params.typeCoode != null and params.typeCoode != ''">
                and type_coode = #{params.typeCoode,jdbcType=VARCHAR}
            </if>
            <if test="params.creationDate != null">
                and creation_date = #{params.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.consigner != null and params.consigner != ''">
                and consigner = #{params.consigner,jdbcType=VARCHAR}
            </if>
            <if test="params.inWarehouseTime != null">
                and in_warehouse_time = #{params.inWarehouseTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.acreage != null">
                and acreage = #{params.acreage,jdbcType=VARCHAR}
            </if>
            <if test="params.acreageSum != null">
                and acreage_sum = #{params.acreageSum,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.kids != null and params.kids != ''">
                and kid in
                <foreach collection="params.kids" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.lotNumber != null and params.lotNumber != ''">
                and lot_number = #{params.lotNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.customerNumber != null and params.customerNumber != ''">
                and customer_number = #{params.customerNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.ebsSiteId != null and params.ebsSiteId != ''">
                and ebs_site_id = #{params.ebsSiteId,jdbcType=VARCHAR}
            </if>
            <if test="params.custPart != null and params.custPart != ''">
                and cust_part = #{params.custPart,jdbcType=VARCHAR}
            </if>
            <if test="params.custPo != null and params.custPo != ''">
                and cust_po = #{params.custPo,jdbcType=VARCHAR}
            </if>
            <if test="params.sourceType != null and params.sourceType != ''">
                and source_type = #{params.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.estimatedArrivePortTime != null">
                and estimated_arrive_port_time = #{params.estimatedArrivePortTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.actualArrivePortTime != null">
                and actual_arrive_port_time = #{params.actualArrivePortTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.estimatedCompletionTime != null">
                and estimated_completion_time = #{params.estimatedCompletionTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.actualCompletionTime != null">
                and actual_completion_time = #{params.actualCompletionTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.carNum != null and params.carNum != ''">
                and car_num = #{params.carNum,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and item_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.isReceiveNotEq != null and params.isReceiveNotEq != ''">
                and is_receive != #{params.isReceiveNotEq,jdbcType=VARCHAR}
            </if>
            <if test="params.beginDate != null and params.endDate != null">
                and DATE_FORMAT(creation_date, '%Y-%m-%d') BETWEEN #{params.beginDate, jdbcType=VARCHAR} AND #{params.endDate, jdbcType=VARCHAR}
            </if>
            <if test="params.shipmentLocatorCodes != null and params.shipmentLocatorCodes.size() > 0">
                and shipment_locator_code in
                <foreach collection="params.shipmentLocatorCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_warehouse_release_to_warehouse
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_warehouse_release_to_warehouse
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from fdp_warehouse_release_to_warehouse
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from fdp_warehouse_release_to_warehouse
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectInRoadWarehouse" resultMap="VOResultMap">
        SELECT
        wrr.item_code,
        wrr.sum_qty,
        wrr.creation_date,
        wrr.estimated_completion_time,
        wrr.actual_completion_time,
        wrr.shipment_locator_code,
        wrr.list_num
        FROM
        fdp_warehouse_release_to_warehouse wrr
        WHERE wrr.is_receive = 'N'
        <if test="productCodeList != null and productCodeList.size() > 0">
            AND wrr.item_code in
            <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="shipmentLocatorCodes != null and shipmentLocatorCodes.size() > 0">
            AND wrr.shipment_locator_code in
            <foreach collection="shipmentLocatorCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseToWarehousePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_warehouse_release_to_warehouse(
        id,
        plant_code,
        is_receive,
        list_num,
        req_num,
        bar_num,
        box_num,
        item_code,
        descriptions,
        sum_qty,
        container_num,
        bill_of_lading_num,
        ship_company,
        instock_source,
        attribute1,
        attribute2,
        shipment_warehouse_code,
        shipment_locator_code,
        req,
        line_num,
        type_coode,
        creation_date,
        consigner,
        in_warehouse_time,
        acreage,
        acreage_sum,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        kid,
        lot_number,
        customer_number,
        ebs_site_id,
        cust_part,
        cust_po,
        source_type,
        estimated_arrive_port_time,
        actual_arrive_port_time,
        estimated_completion_time,
        actual_completion_time,
        car_num)
        values (
        #{id,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{isReceive,jdbcType=VARCHAR},
        #{listNum,jdbcType=VARCHAR},
        #{reqNum,jdbcType=VARCHAR},
        #{barNum,jdbcType=VARCHAR},
        #{boxNum,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR},
        #{descriptions,jdbcType=VARCHAR},
        #{sumQty,jdbcType=VARCHAR},
        #{containerNum,jdbcType=VARCHAR},
        #{billOfLadingNum,jdbcType=VARCHAR},
        #{shipCompany,jdbcType=VARCHAR},
        #{instockSource,jdbcType=VARCHAR},
        #{attribute1,jdbcType=VARCHAR},
        #{attribute2,jdbcType=VARCHAR},
        #{shipmentWarehouseCode,jdbcType=VARCHAR},
        #{shipmentLocatorCode,jdbcType=VARCHAR},
        #{req,jdbcType=VARCHAR},
        #{lineNum,jdbcType=VARCHAR},
        #{typeCoode,jdbcType=VARCHAR},
        #{creationDate,jdbcType=TIMESTAMP},
        #{consigner,jdbcType=VARCHAR},
        #{inWarehouseTime,jdbcType=TIMESTAMP},
        #{acreage,jdbcType=VARCHAR},
        #{acreageSum,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{kid,jdbcType=VARCHAR},
        #{lotNumber,jdbcType=VARCHAR},
        #{customerNumber,jdbcType=VARCHAR},
        #{ebsSiteId,jdbcType=VARCHAR},
        #{custPart,jdbcType=VARCHAR},
        #{custPo,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{estimatedArrivePortTime,jdbcType=TIMESTAMP},
        #{actualArrivePortTime,jdbcType=TIMESTAMP},
        #{estimatedCompletionTime,jdbcType=TIMESTAMP},
        #{actualCompletionTime,jdbcType=TIMESTAMP},
        #{carNum,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseToWarehousePO">
        insert into fdp_warehouse_release_to_warehouse(
        id,
        plant_code,
        is_receive,
        list_num,
        req_num,
        bar_num,
        box_num,
        item_code,
        descriptions,
        sum_qty,
        container_num,
        bill_of_lading_num,
        ship_company,
        instock_source,
        attribute1,
        attribute2,
        shipment_warehouse_code,
        shipment_locator_code,
        req,
        line_num,
        type_coode,
        creation_date,
        consigner,
        in_warehouse_time,
        acreage,
        acreage_sum,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        kid,
        lot_number,
        customer_number,
        ebs_site_id,
        cust_part,
        cust_po,
        source_type,
        estimated_arrive_port_time,
        actual_arrive_port_time,
        estimated_completion_time,
        actual_completion_time,
        car_num)
        values (
        #{id,jdbcType=VARCHAR},
        #{plantCode,jdbcType=VARCHAR},
        #{isReceive,jdbcType=VARCHAR},
        #{listNum,jdbcType=VARCHAR},
        #{reqNum,jdbcType=VARCHAR},
        #{barNum,jdbcType=VARCHAR},
        #{boxNum,jdbcType=VARCHAR},
        #{itemCode,jdbcType=VARCHAR},
        #{descriptions,jdbcType=VARCHAR},
        #{sumQty,jdbcType=VARCHAR},
        #{containerNum,jdbcType=VARCHAR},
        #{billOfLadingNum,jdbcType=VARCHAR},
        #{shipCompany,jdbcType=VARCHAR},
        #{instockSource,jdbcType=VARCHAR},
        #{attribute1,jdbcType=VARCHAR},
        #{attribute2,jdbcType=VARCHAR},
        #{shipmentWarehouseCode,jdbcType=VARCHAR},
        #{shipmentLocatorCode,jdbcType=VARCHAR},
        #{req,jdbcType=VARCHAR},
        #{lineNum,jdbcType=VARCHAR},
        #{typeCoode,jdbcType=VARCHAR},
        #{creationDate,jdbcType=TIMESTAMP},
        #{consigner,jdbcType=VARCHAR},
        #{inWarehouseTime,jdbcType=TIMESTAMP},
        #{acreage,jdbcType=VARCHAR},
        #{acreageSum,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{kid,jdbcType=VARCHAR},
        #{lotNumber,jdbcType=VARCHAR},
        #{customerNumber,jdbcType=VARCHAR},
        #{ebsSiteId,jdbcType=VARCHAR},
        #{custPart,jdbcType=VARCHAR},
        #{custPo,jdbcType=VARCHAR},
        #{sourceType,jdbcType=VARCHAR},
        #{estimatedArrivePortTime,jdbcType=TIMESTAMP},
        #{actualArrivePortTime,jdbcType=TIMESTAMP},
        #{estimatedCompletionTime,jdbcType=TIMESTAMP},
        #{actualCompletionTime,jdbcType=TIMESTAMP},
        #{carNum,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_warehouse_release_to_warehouse(
        id,
        plant_code,
        is_receive,
        list_num,
        req_num,
        bar_num,
        box_num,
        item_code,
        descriptions,
        sum_qty,
        container_num,
        bill_of_lading_num,
        ship_company,
        instock_source,
        attribute1,
        attribute2,
        shipment_warehouse_code,
        shipment_locator_code,
        req,
        line_num,
        type_coode,
        creation_date,
        consigner,
        in_warehouse_time,
        acreage,
        acreage_sum,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        kid,
        lot_number,
        customer_number,
        ebs_site_id,
        cust_part,
        cust_po,
        source_type,
        estimated_arrive_port_time,
        actual_arrive_port_time,
        estimated_completion_time,
        actual_completion_time,
        car_num)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.isReceive,jdbcType=VARCHAR},
        #{entity.listNum,jdbcType=VARCHAR},
        #{entity.reqNum,jdbcType=VARCHAR},
        #{entity.barNum,jdbcType=VARCHAR},
        #{entity.boxNum,jdbcType=VARCHAR},
        #{entity.itemCode,jdbcType=VARCHAR},
        #{entity.descriptions,jdbcType=VARCHAR},
        #{entity.sumQty,jdbcType=VARCHAR},
        #{entity.containerNum,jdbcType=VARCHAR},
        #{entity.billOfLadingNum,jdbcType=VARCHAR},
        #{entity.shipCompany,jdbcType=VARCHAR},
        #{entity.instockSource,jdbcType=VARCHAR},
        #{entity.attribute1,jdbcType=VARCHAR},
        #{entity.attribute2,jdbcType=VARCHAR},
        #{entity.shipmentWarehouseCode,jdbcType=VARCHAR},
        #{entity.shipmentLocatorCode,jdbcType=VARCHAR},
        #{entity.req,jdbcType=VARCHAR},
        #{entity.lineNum,jdbcType=VARCHAR},
        #{entity.typeCoode,jdbcType=VARCHAR},
        #{entity.creationDate,jdbcType=TIMESTAMP},
        #{entity.consigner,jdbcType=VARCHAR},
        #{entity.inWarehouseTime,jdbcType=TIMESTAMP},
        #{entity.acreage,jdbcType=VARCHAR},
        #{entity.acreageSum,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.lotNumber,jdbcType=VARCHAR},
        #{entity.customerNumber,jdbcType=VARCHAR},
        #{entity.ebsSiteId,jdbcType=VARCHAR},
        #{entity.custPart,jdbcType=VARCHAR},
        #{entity.custPo,jdbcType=VARCHAR},
        #{entity.sourceType,jdbcType=VARCHAR},
        #{entity.estimatedArrivePortTime,jdbcType=TIMESTAMP},
        #{entity.actualArrivePortTime,jdbcType=TIMESTAMP},
        #{entity.estimatedCompletionTime,jdbcType=TIMESTAMP},
        #{entity.actualCompletionTime,jdbcType=TIMESTAMP},
        #{entity.carNum,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_warehouse_release_to_warehouse(
        id,
        plant_code,
        is_receive,
        list_num,
        req_num,
        bar_num,
        box_num,
        item_code,
        descriptions,
        sum_qty,
        container_num,
        bill_of_lading_num,
        ship_company,
        instock_source,
        attribute1,
        attribute2,
        shipment_warehouse_code,
        shipment_locator_code,
        req,
        line_num,
        type_coode,
        creation_date,
        consigner,
        in_warehouse_time,
        acreage,
        acreage_sum,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        kid,
        lot_number,
        customer_number,
        ebs_site_id,
        cust_part,
        cust_po,
        source_type,
        estimated_arrive_port_time,
        actual_arrive_port_time,
        estimated_completion_time,
        actual_completion_time,
        car_num)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.plantCode,jdbcType=VARCHAR},
        #{entity.isReceive,jdbcType=VARCHAR},
        #{entity.listNum,jdbcType=VARCHAR},
        #{entity.reqNum,jdbcType=VARCHAR},
        #{entity.barNum,jdbcType=VARCHAR},
        #{entity.boxNum,jdbcType=VARCHAR},
        #{entity.itemCode,jdbcType=VARCHAR},
        #{entity.descriptions,jdbcType=VARCHAR},
        #{entity.sumQty,jdbcType=VARCHAR},
        #{entity.containerNum,jdbcType=VARCHAR},
        #{entity.billOfLadingNum,jdbcType=VARCHAR},
        #{entity.shipCompany,jdbcType=VARCHAR},
        #{entity.instockSource,jdbcType=VARCHAR},
        #{entity.attribute1,jdbcType=VARCHAR},
        #{entity.attribute2,jdbcType=VARCHAR},
        #{entity.shipmentWarehouseCode,jdbcType=VARCHAR},
        #{entity.shipmentLocatorCode,jdbcType=VARCHAR},
        #{entity.req,jdbcType=VARCHAR},
        #{entity.lineNum,jdbcType=VARCHAR},
        #{entity.typeCoode,jdbcType=VARCHAR},
        #{entity.creationDate,jdbcType=TIMESTAMP},
        #{entity.consigner,jdbcType=VARCHAR},
        #{entity.inWarehouseTime,jdbcType=TIMESTAMP},
        #{entity.acreage,jdbcType=VARCHAR},
        #{entity.acreageSum,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.lotNumber,jdbcType=VARCHAR},
        #{entity.customerNumber,jdbcType=VARCHAR},
        #{entity.ebsSiteId,jdbcType=VARCHAR},
        #{entity.custPart,jdbcType=VARCHAR},
        #{entity.custPo,jdbcType=VARCHAR},
        #{entity.sourceType,jdbcType=VARCHAR},
        #{entity.estimatedArrivePortTime,jdbcType=TIMESTAMP},
        #{entity.actualArrivePortTime,jdbcType=TIMESTAMP},
        #{entity.estimatedCompletionTime,jdbcType=TIMESTAMP},
        #{entity.actualCompletionTime,jdbcType=TIMESTAMP},
        #{entity.carNum,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseToWarehousePO">
        update fdp_warehouse_release_to_warehouse set
        plant_code = #{plantCode,jdbcType=VARCHAR},
        is_receive = #{isReceive,jdbcType=VARCHAR},
        list_num = #{listNum,jdbcType=VARCHAR},
        req_num = #{reqNum,jdbcType=VARCHAR},
        bar_num = #{barNum,jdbcType=VARCHAR},
        box_num = #{boxNum,jdbcType=VARCHAR},
        item_code = #{itemCode,jdbcType=VARCHAR},
        descriptions = #{descriptions,jdbcType=VARCHAR},
        sum_qty = #{sumQty,jdbcType=VARCHAR},
        container_num = #{containerNum,jdbcType=VARCHAR},
        bill_of_lading_num = #{billOfLadingNum,jdbcType=VARCHAR},
        ship_company = #{shipCompany,jdbcType=VARCHAR},
        instock_source = #{instockSource,jdbcType=VARCHAR},
        attribute1 = #{attribute1,jdbcType=VARCHAR},
        attribute2 = #{attribute2,jdbcType=VARCHAR},
        shipment_warehouse_code = #{shipmentWarehouseCode,jdbcType=VARCHAR},
        shipment_locator_code = #{shipmentLocatorCode,jdbcType=VARCHAR},
        req = #{req,jdbcType=VARCHAR},
        line_num = #{lineNum,jdbcType=VARCHAR},
        type_coode = #{typeCoode,jdbcType=VARCHAR},
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
        consigner = #{consigner,jdbcType=VARCHAR},
        in_warehouse_time = #{inWarehouseTime,jdbcType=TIMESTAMP},
        acreage = #{acreage,jdbcType=VARCHAR},
        acreage_sum = #{acreageSum,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        kid = #{kid,jdbcType=VARCHAR},
        lot_number = #{lotNumber,jdbcType=VARCHAR},
        customer_number = #{customerNumber,jdbcType=VARCHAR},
        ebs_site_id = #{ebsSiteId,jdbcType=VARCHAR},
        cust_part = #{custPart,jdbcType=VARCHAR},
        cust_po = #{custPo,jdbcType=VARCHAR},
        source_type = #{sourceType,jdbcType=VARCHAR},
        estimated_arrive_port_time = #{estimatedArrivePortTime,jdbcType=TIMESTAMP},
        actual_arrive_port_time = #{actualArrivePortTime,jdbcType=TIMESTAMP},
        estimated_completion_time = #{estimatedCompletionTime,jdbcType=TIMESTAMP},
        actual_completion_time = #{actualCompletionTime,jdbcType=TIMESTAMP},
        car_num = #{carNum,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseToWarehousePO">
        update fdp_warehouse_release_to_warehouse
        <set>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.isReceive != null and item.isReceive != ''">
                is_receive = #{item.isReceive,jdbcType=VARCHAR},
            </if>
            <if test="item.listNum != null and item.listNum != ''">
                list_num = #{item.listNum,jdbcType=VARCHAR},
            </if>
            <if test="item.reqNum != null and item.reqNum != ''">
                req_num = #{item.reqNum,jdbcType=VARCHAR},
            </if>
            <if test="item.barNum != null and item.barNum != ''">
                bar_num = #{item.barNum,jdbcType=VARCHAR},
            </if>
            <if test="item.boxNum != null and item.boxNum != ''">
                box_num = #{item.boxNum,jdbcType=VARCHAR},
            </if>
            <if test="item.itemCode != null and item.itemCode != ''">
                item_code = #{item.itemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.descriptions != null and item.descriptions != ''">
                descriptions = #{item.descriptions,jdbcType=VARCHAR},
            </if>
            <if test="item.sumQty != null">
                sum_qty = #{item.sumQty,jdbcType=VARCHAR},
            </if>
            <if test="item.containerNum != null and item.containerNum != ''">
                container_num = #{item.containerNum,jdbcType=VARCHAR},
            </if>
            <if test="item.billOfLadingNum != null and item.billOfLadingNum != ''">
                bill_of_lading_num = #{item.billOfLadingNum,jdbcType=VARCHAR},
            </if>
            <if test="item.shipCompany != null and item.shipCompany != ''">
                ship_company = #{item.shipCompany,jdbcType=VARCHAR},
            </if>
            <if test="item.instockSource != null and item.instockSource != ''">
                instock_source = #{item.instockSource,jdbcType=VARCHAR},
            </if>
            <if test="item.attribute1 != null and item.attribute1 != ''">
                attribute1 = #{item.attribute1,jdbcType=VARCHAR},
            </if>
            <if test="item.attribute2 != null and item.attribute2 != ''">
                attribute2 = #{item.attribute2,jdbcType=VARCHAR},
            </if>
            <if test="item.shipmentWarehouseCode != null and item.shipmentWarehouseCode != ''">
                shipment_warehouse_code = #{item.shipmentWarehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="item.shipmentLocatorCode != null and item.shipmentLocatorCode != ''">
                shipment_locator_code = #{item.shipmentLocatorCode,jdbcType=VARCHAR},
            </if>
            <if test="item.req != null and item.req != ''">
                req = #{item.req,jdbcType=VARCHAR},
            </if>
            <if test="item.lineNum != null and item.lineNum != ''">
                line_num = #{item.lineNum,jdbcType=VARCHAR},
            </if>
            <if test="item.typeCoode != null and item.typeCoode != ''">
                type_coode = #{item.typeCoode,jdbcType=VARCHAR},
            </if>
            <if test="item.creationDate != null">
                creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.consigner != null and item.consigner != ''">
                consigner = #{item.consigner,jdbcType=VARCHAR},
            </if>
            <if test="item.inWarehouseTime != null">
                in_warehouse_time = #{item.inWarehouseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.acreage != null">
                acreage = #{item.acreage,jdbcType=VARCHAR},
            </if>
            <if test="item.acreageSum != null">
                acreage_sum = #{item.acreageSum,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.lotNumber != null and item.lotNumber != ''">
                lot_number = #{item.lotNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.customerNumber != null and item.customerNumber != ''">
                customer_number = #{item.customerNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsSiteId != null and item.ebsSiteId != ''">
                ebs_site_id = #{item.ebsSiteId,jdbcType=VARCHAR},
            </if>
            <if test="item.custPart != null and item.custPart != ''">
                cust_part = #{item.custPart,jdbcType=VARCHAR},
            </if>
            <if test="item.custPo != null and item.custPo != ''">
                cust_po = #{item.custPo,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.estimatedArrivePortTime != null">
                estimated_arrive_port_time = #{item.estimatedArrivePortTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.actualArrivePortTime != null">
                actual_arrive_port_time = #{item.actualArrivePortTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.estimatedCompletionTime != null">
                estimated_completion_time = #{item.estimatedCompletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.actualCompletionTime != null">
                actual_completion_time = #{item.actualCompletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.carNum != null and item.carNum != ''">
                car_num = #{item.carNum,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_warehouse_release_to_warehouse
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="plant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.plantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_receive = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.isReceive,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="list_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.listNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="req_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.reqNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bar_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.barNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="descriptions = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.descriptions,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sum_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sumQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="container_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.containerNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bill_of_lading_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.billOfLadingNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ship_company = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shipCompany,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="instock_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.instockSource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="attribute1 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.attribute1,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="attribute2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.attribute2,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shipment_warehouse_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shipmentWarehouseCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shipment_locator_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shipmentLocatorCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="req = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.req,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="line_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lineNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="type_coode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.typeCoode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creation_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creationDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="consigner = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.consigner,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="in_warehouse_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inWarehouseTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="acreage = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.acreage,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="acreage_sum = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.acreageSum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ebs_site_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ebsSiteId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cust_part = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.custPart,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cust_po = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.custPo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="estimated_arrive_port_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.estimatedArrivePortTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="actual_arrive_port_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.actualArrivePortTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="estimated_completion_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.estimatedCompletionTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="actual_completion_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.actualCompletionTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="car_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update fdp_warehouse_release_to_warehouse 
        <set>
            <if test="item.plantCode != null and item.plantCode != ''">
                plant_code = #{item.plantCode,jdbcType=VARCHAR},
            </if>
            <if test="item.isReceive != null and item.isReceive != ''">
                is_receive = #{item.isReceive,jdbcType=VARCHAR},
            </if>
            <if test="item.listNum != null and item.listNum != ''">
                list_num = #{item.listNum,jdbcType=VARCHAR},
            </if>
            <if test="item.reqNum != null and item.reqNum != ''">
                req_num = #{item.reqNum,jdbcType=VARCHAR},
            </if>
            <if test="item.barNum != null and item.barNum != ''">
                bar_num = #{item.barNum,jdbcType=VARCHAR},
            </if>
            <if test="item.boxNum != null and item.boxNum != ''">
                box_num = #{item.boxNum,jdbcType=VARCHAR},
            </if>
            <if test="item.itemCode != null and item.itemCode != ''">
                item_code = #{item.itemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.descriptions != null and item.descriptions != ''">
                descriptions = #{item.descriptions,jdbcType=VARCHAR},
            </if>
            <if test="item.sumQty != null">
                sum_qty = #{item.sumQty,jdbcType=VARCHAR},
            </if>
            <if test="item.containerNum != null and item.containerNum != ''">
                container_num = #{item.containerNum,jdbcType=VARCHAR},
            </if>
            <if test="item.billOfLadingNum != null and item.billOfLadingNum != ''">
                bill_of_lading_num = #{item.billOfLadingNum,jdbcType=VARCHAR},
            </if>
            <if test="item.shipCompany != null and item.shipCompany != ''">
                ship_company = #{item.shipCompany,jdbcType=VARCHAR},
            </if>
            <if test="item.instockSource != null and item.instockSource != ''">
                instock_source = #{item.instockSource,jdbcType=VARCHAR},
            </if>
            <if test="item.attribute1 != null and item.attribute1 != ''">
                attribute1 = #{item.attribute1,jdbcType=VARCHAR},
            </if>
            <if test="item.attribute2 != null and item.attribute2 != ''">
                attribute2 = #{item.attribute2,jdbcType=VARCHAR},
            </if>
            <if test="item.shipmentWarehouseCode != null and item.shipmentWarehouseCode != ''">
                shipment_warehouse_code = #{item.shipmentWarehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="item.shipmentLocatorCode != null and item.shipmentLocatorCode != ''">
                shipment_locator_code = #{item.shipmentLocatorCode,jdbcType=VARCHAR},
            </if>
            <if test="item.req != null and item.req != ''">
                req = #{item.req,jdbcType=VARCHAR},
            </if>
            <if test="item.lineNum != null and item.lineNum != ''">
                line_num = #{item.lineNum,jdbcType=VARCHAR},
            </if>
            <if test="item.typeCoode != null and item.typeCoode != ''">
                type_coode = #{item.typeCoode,jdbcType=VARCHAR},
            </if>
            <if test="item.creationDate != null">
                creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.consigner != null and item.consigner != ''">
                consigner = #{item.consigner,jdbcType=VARCHAR},
            </if>
            <if test="item.inWarehouseTime != null">
                in_warehouse_time = #{item.inWarehouseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.acreage != null">
                acreage = #{item.acreage,jdbcType=VARCHAR},
            </if>
            <if test="item.acreageSum != null">
                acreage_sum = #{item.acreageSum,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.lotNumber != null and item.lotNumber != ''">
                lot_number = #{item.lotNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.customerNumber != null and item.customerNumber != ''">
                customer_number = #{item.customerNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsSiteId != null and item.ebsSiteId != ''">
                ebs_site_id = #{item.ebsSiteId,jdbcType=VARCHAR},
            </if>
            <if test="item.custPart != null and item.custPart != ''">
                cust_part = #{item.custPart,jdbcType=VARCHAR},
            </if>
            <if test="item.custPo != null and item.custPo != ''">
                cust_po = #{item.custPo,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.estimatedArrivePortTime != null">
                estimated_arrive_port_time = #{item.estimatedArrivePortTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.actualArrivePortTime != null">
                actual_arrive_port_time = #{item.actualArrivePortTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.estimatedCompletionTime != null">
                estimated_completion_time = #{item.estimatedCompletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.actualCompletionTime != null">
                actual_completion_time = #{item.actualCompletionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.carNum != null and item.carNum != ''">
                car_num = #{item.carNum,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from fdp_warehouse_release_to_warehouse where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_warehouse_release_to_warehouse where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <select id="selectTargetStockLocation" resultType="java.lang.String">
        select distinct shipment_locator_code
        from fdp_warehouse_release_to_warehouse
        where shipment_locator_code is not null
        order by shipment_locator_code
    </select>
    
    <select id="selectMonthVOByParams" parameterType="java.util.Map"
            resultType="com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO">
        SELECT * FROM (
        SELECT
        m.oem_code AS oemCode,
        r.item_code AS itemCode,
        DATE_FORMAT(r.creation_date, '%Y-%m') AS yearMonth,
        SUM(r.sum_qty) AS sumQty
        FROM fdp_warehouse_release_to_warehouse r
        LEFT JOIN v_oem_target_stock_location_map m
            ON r.shipment_locator_code=m.location
        <where>
            r.sum_qty is not null
            <if test="params.oemCodes != null and params.oemCodes.size() > 0">
                and m.oem_code in
                <foreach collection="params.oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and r.item_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.beginDate != null and params.beginDate != '' and params.endDate != null and params.endDate != ''">
                and DATE_FORMAT(r.creation_date, '%Y-%m-%d') &gt;= #{params.beginDate,jdbcType=VARCHAR}
                and DATE_FORMAT(r.creation_date, '%Y-%m-%d') &lt; #{params.endDate,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY m.oem_code,r.item_code, DATE_FORMAT(r.creation_date, '%Y-%m')) t
    </select>
    
    <select id="selectDayVOByParams" parameterType="java.util.Map"
            resultType="com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseDayVO">
        SELECT * FROM (
        SELECT
        m.oem_code AS oemCode,
        r.item_code AS itemCode,
        DATE_FORMAT(r.creation_date, '%Y-%m-%d') AS monthDay,
        SUM(r.sum_qty) AS sumQty
        FROM fdp_warehouse_release_to_warehouse r
        LEFT JOIN v_oem_target_stock_location_map m
            ON r.shipment_locator_code=m.location
        <where>
            r.sum_qty is not null
            <if test="params.oemCodes != null and params.oemCodes.size() > 0">
                and m.oem_code in
                <foreach collection="params.oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and r.item_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.beginDate != null and params.beginDate != '' and params.endDate != null and params.endDate != ''">
                and DATE_FORMAT(r.creation_date, '%Y-%m-%d') &gt;= #{params.beginDate,jdbcType=VARCHAR}
                and DATE_FORMAT(r.creation_date, '%Y-%m-%d') &lt; #{params.endDate,jdbcType=VARCHAR}
            </if>
        </where>
        GROUP BY m.oem_code,r.item_code, DATE_FORMAT(r.creation_date, '%Y-%m-%d')) t
    </select>
    <select id="selectMonthVOByItemCodes"
            resultType="com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO">
        SELECT * FROM (
        SELECT
        m.oem_code AS oemCode,
        r.item_code AS itemCode,
        DATE_FORMAT(r.creation_date, '%Y%m') AS yearMonth,
        SUM(r.sum_qty) AS sumQty
        FROM fdp_warehouse_release_to_warehouse r
        LEFT JOIN v_oem_target_stock_location_map m
        ON r.shipment_locator_code=m.location
        <where>
            r.sum_qty is not null
            <if test="oemCodeList != null and oemCodeList.size() > 0">
                and m.oem_code in
                <foreach collection="oemCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productCodeList != null and productCodeList.size() > 0">
                and r.item_code in
                <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
        GROUP BY m.oem_code,r.item_code, DATE_FORMAT(r.creation_date, '%Y%m')) t
        WHERE t.yearMonth BETWEEN #{startYearMonth,jdbcType=VARCHAR} AND #{endYearMonth,jdbcType=VARCHAR}
    </select>
</mapper>
