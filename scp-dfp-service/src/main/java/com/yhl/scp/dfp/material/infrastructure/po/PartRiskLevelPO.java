package com.yhl.scp.dfp.material.infrastructure.po;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BasePO;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>PartRiskLevelPO</code>
 * <p>
 * 零件风险等级PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
public class PartRiskLevelPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 506762189043249847L;

    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    private String productCode;

    /**
     * 总评分
     */
    private BigDecimal totalScore;

    /**
     * 零件风险等级
     */
    private String materialRiskLevel;
    
    /**
     * 零件风险等级（计算后）
     */
    private String materialRiskLevelCalculate;
    
    /**
     * 零件风险等级（上月后）
     */
    private String materialRiskLevelMonth;
    
    /**
     * 修改原因
     */
    private String updateRemark;

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public BigDecimal getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(BigDecimal totalScore) {
        this.totalScore = totalScore;
    }

    public String getMaterialRiskLevel() {
        return materialRiskLevel;
    }

    public void setMaterialRiskLevel(String materialRiskLevel) {
        this.materialRiskLevel = materialRiskLevel;
    }

	public String getMaterialRiskLevelCalculate() {
		return materialRiskLevelCalculate;
	}

	public void setMaterialRiskLevelCalculate(String materialRiskLevelCalculate) {
		this.materialRiskLevelCalculate = materialRiskLevelCalculate;
	}

	public String getMaterialRiskLevelMonth() {
		return materialRiskLevelMonth;
	}

	public void setMaterialRiskLevelMonth(String materialRiskLevelMonth) {
		this.materialRiskLevelMonth = materialRiskLevelMonth;
	}

	public String getUpdateRemark() {
		return updateRemark;
	}

	public void setUpdateRemark(String updateRemark) {
		this.updateRemark = updateRemark;
	}
    
    
}
