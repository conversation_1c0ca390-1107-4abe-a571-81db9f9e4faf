package com.yhl.scp.dfp.loading.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionDetailPO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <code>LoadingDemandSubmissionDetailDao</code>
 * <p>
 * 装车需求提报详情DAO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 14:30:33
 */
public interface LoadingDemandSubmissionDetailDao extends BaseDao<LoadingDemandSubmissionDetailPO, LoadingDemandSubmissionDetailVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link LoadingDemandSubmissionDetailVO}
     */
    List<LoadingDemandSubmissionDetailVO> selectVOByParams(@Param("params") Map<String, Object> params);

    List<LoadingDemandSubmissionDetailPO> selectBySubmissionIds(@Param("submissionIds") List<String> submissionIds);

    /**
     * 根据版本批量删除
     *
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    int deleteByVersionIdAndSubmissionType(@Param("submissionId") String submissionId, @Param("contentType") String contentType);

    int updateQuantityById(@Param("id") String id, @Param("demandQuantity") BigDecimal demandQuantity);

    List<String> selectByOriginIdAndOemCode(@Param("originVersionId") String originVersionId, @Param("oemCode") String oemCode, @Param("contentType") String contentType);

    int deleteBySubmissionIds(@Param("submissionIds") List<String> submissionIds);

}
