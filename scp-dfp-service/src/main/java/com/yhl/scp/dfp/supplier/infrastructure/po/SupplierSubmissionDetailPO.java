package com.yhl.scp.dfp.supplier.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>SupplierSubmissionDetailPO</code>
 * <p>
 * 车型全供应商提报明细PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-10 09:32:34
 */
public class SupplierSubmissionDetailPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -38655693617345151L;

    /**
     * 主表id
     */
    private String supplierSubmissionId;
    /**
     * 预测日期
     */
    private Date forecastDate;
    /**
     * 预测数量
     */
    private Integer forecastQuantity;

    public String getSupplierSubmissionId() {
        return supplierSubmissionId;
    }

    public void setSupplierSubmissionId(String supplierSubmissionId) {
        this.supplierSubmissionId = supplierSubmissionId;
    }

    public Date getForecastDate() {
        return forecastDate;
    }

    public void setForecastDate(Date forecastDate) {
        this.forecastDate = forecastDate;
    }

    public Integer getForecastQuantity() {
        return forecastQuantity;
    }

    public void setForecastQuantity(Integer forecastQuantity) {
        this.forecastQuantity = forecastQuantity;
    }

}
