package com.yhl.scp.dfp.dispatch.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.BigDecimalUtils;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.clean.dto.CleanAlgorithmAnalysisDTO;
import com.yhl.scp.dfp.clean.dto.CleanAlgorithmDataDTO;
import com.yhl.scp.dfp.clean.service.CleanAlgorithmAnalysisService;
import com.yhl.scp.dfp.clean.service.CleanAlgorithmDataService;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastEstablishmentDao;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO;
import com.yhl.scp.dfp.dispatch.output.DfpAlgoOutput;
import com.yhl.scp.dfp.dispatch.output.ForecastFactorAnalysisOutputData;
import com.yhl.scp.dfp.dispatch.output.ForecastResultOutputData;
import com.yhl.scp.dfp.dispatch.service.DfpAlgoOutputService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelMapService;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class DfpAlgoOutputServiceImpl implements DfpAlgoOutputService {

    @Resource
    private CleanAlgorithmDataService cleanAlgorithmDataService;

    @Resource
    private CleanAlgorithmAnalysisService cleanAlgorithmAnalysisService;

    @Resource
    private OemVehicleModelMapService oemVehicleModelMapService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DemandForecastVersionDao demandForecastVersionDao;

    @Resource
    private DemandForecastEstablishmentDao demandForecastEstablishmentDao;

    /**
     * 调用算法没有处理需求版本
     * 输入最后一个集合文件数据未知
     * 解析逻辑未知
     */
    @Override
    public void doAnalysisAlgorithmOutputData(DfpAlgoOutput dfpAlgorithmOutput, AlgorithmLog algorithmLog) {
        String demandVersionId = algorithmLog.getKpiData();
        log.info("预测算法需求id：{}", demandVersionId);
        List<ForecastResultOutputData> forecastResultList = dfpAlgorithmOutput.getForecastResultList();
        log.info("算法预测结果行数：{}", forecastResultList.size());
        List<ForecastFactorAnalysisOutputData> forecastFactorAnalysisList = dfpAlgorithmOutput.getForecastFactorAnalysisList();
        log.info("预测因子分析行数：{}", forecastFactorAnalysisList.size());

        log.info("开始解析结果");
        analysisForecastResult(forecastResultList, demandVersionId, demandVersionId);
        analysisForecastFactorAnalysis(forecastFactorAnalysisList, demandVersionId);
        log.info("结束解析结果");
    }

    private void analysisForecastResult(List<ForecastResultOutputData> forecastResultList, String demandVersionId, String versionId) {
        String mdsScenario = getMdsScenario();
        List<String> productCodes = forecastResultList.stream()
        		.map(e -> e.getItemCode().split("&")[1]).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(mdsScenario, productCodes);
        Map<String, List<NewProductStockPointVO>> productStockPointVOMap = newProductStockPointVOS.stream()
                .collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode));
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String stockCode = scenarioBusinessRange.getData().getRangeData();
        List<CleanAlgorithmDataDTO> cleanAlgorithmDataDTOList = new ArrayList<>();
        for (ForecastResultOutputData forecastResultOutputData : forecastResultList) {
            String itemCode = forecastResultOutputData.getItemCode();
            String[] itemCodeSplit = itemCode.split("&");
            String oemCode = itemCodeSplit[0];
            itemCode = itemCodeSplit[1];
            CleanAlgorithmDataDTO cleanAlgorithmDataDTO = new CleanAlgorithmDataDTO();
            cleanAlgorithmDataDTO.setVersionId(demandVersionId);
            if (productStockPointVOMap.containsKey(itemCode)) {
                // 物品信息
                NewProductStockPointVO newProductStockPointVO;
                List<NewProductStockPointVO> productStockPointVOS = productStockPointVOMap.get(itemCode);
                if (productStockPointVOS.size() > 1) {
                    productStockPointVOS = productStockPointVOS.stream().filter(p ->
                            StrUtil.isNotEmpty(p.getStockPointCode()) && p.getStockPointCode().toUpperCase().equals(stockCode)).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(productStockPointVOS)) {
                        log.warn("算法解析itemCode无对应的物料信息：{}", itemCode);
                        continue;
                    }
                }
                newProductStockPointVO = productStockPointVOS.get(0);
                cleanAlgorithmDataDTO.setProductCode(newProductStockPointVO.getProductCode());
                cleanAlgorithmDataDTO.setVehicleModelCode(newProductStockPointVO.getVehicleModelCode());
                cleanAlgorithmDataDTO.setOemCode(oemCode);
            }
            cleanAlgorithmDataDTO.setForecastTime(DateUtils.stringToDate(forecastResultOutputData.getForecastDate()));
            cleanAlgorithmDataDTO.setForecastQuantity(BigDecimalUtils.toBigDecimal(forecastResultOutputData.getNewForecastValue()));
            cleanAlgorithmDataDTO.setVersionStatus("NOT_PUBLISH");
            cleanAlgorithmDataDTOList.add(cleanAlgorithmDataDTO);
        }
        log.info("创建预测算法数据结果行：{}", cleanAlgorithmDataDTOList.size());
        cleanAlgorithmDataService.doCreateBatch(cleanAlgorithmDataDTOList);
        // 更新需求预测编制算法数据
        List<DemandForecastVersionPO> forecastVersionPOS = demandForecastVersionDao.selectByParams(ImmutableMap.of("algorithmVersionId", versionId));
        log.info("需求预测版本查询数据行：{}", forecastVersionPOS.size());
        if (CollectionUtils.isNotEmpty(forecastVersionPOS)) {
            DemandForecastVersionPO demandForecastVersionPO = forecastVersionPOS.get(0);
            String id = demandForecastVersionPO.getId();
            List<DemandForecastEstablishmentPO> demandForecastEstablishmentPOList = demandForecastEstablishmentDao
                    .selectByParams(ImmutableMap.of("forecastVersionId", id));
            List<DemandForecastEstablishmentPO> updateList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(demandForecastEstablishmentPOList)) {
                // 处理映射字段
                Map<String, Integer> valueMap = cleanAlgorithmDataDTOList.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getVehicleModelCode())
                                && StringUtils.isNotBlank(item.getProductCode())
                                && item.getForecastTime() != null
                                && item.getForecastQuantity() != null)
                        .collect(Collectors.groupingBy(
                                item -> String.join("#", item.getVehicleModelCode(),
                                        item.getProductCode(),
                                        DateUtils.dateToString(item.getForecastTime())),
                                Collectors.summingInt(item -> item.getForecastQuantity().intValue())
                        ));
                // 更新待修改数据
                Date date = new Date();
                demandForecastEstablishmentPOList.forEach(item -> {
                    String key = String.join("#", item.getVehicleModelCode(),
                            item.getProductCode(),
                            DateUtils.dateToString(item.getForecastTime()));
                    Integer value = valueMap.getOrDefault(key, null);
                    if (value != null) {
                        item.setAlgorithmForecast(BigDecimalUtils.toBigDecimal(value));
                        item.setModifyTime(date);
                        updateList.add(item);
                    }
                });
                Lists.partition(updateList, 500).forEach(item -> demandForecastEstablishmentDao.updateBatch(item));
            }
            log.info("需求预测编制更新数据行：{}", updateList.size());
        }
    }

    private void analysisForecastFactorAnalysis(List<ForecastFactorAnalysisOutputData> forecastFactorAnalysisList, String demandVersionId) {
        List<CleanAlgorithmAnalysisDTO> cleanAlgorithmAnalysisDTOList = new ArrayList<>();
        for (ForecastFactorAnalysisOutputData forecastFactorAnalysisOutputData : forecastFactorAnalysisList) {
            CleanAlgorithmAnalysisDTO cleanAlgorithmAnalysisDTO = new CleanAlgorithmAnalysisDTO();
            cleanAlgorithmAnalysisDTO.setVersionId(demandVersionId);
            cleanAlgorithmAnalysisDTO.setFactorCode(forecastFactorAnalysisOutputData.getFactorCode());
            cleanAlgorithmAnalysisDTO.setImportanceValue(forecastFactorAnalysisOutputData.getImportanceValue());
            cleanAlgorithmAnalysisDTO.setVersionStatus("NOT_PUBLISH");
            cleanAlgorithmAnalysisDTOList.add(cleanAlgorithmAnalysisDTO);
        }
        log.info("创建预测因子算法结果行：{}", cleanAlgorithmAnalysisDTOList.size());
        cleanAlgorithmAnalysisService.doCreateBatch(cleanAlgorithmAnalysisDTOList);
    }

    private String getMdsScenario() {
        BaseResponse<String> scenarioMds =
                SpringUtil.getBean(IpsNewFeign.class).getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(),
                        TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenarioMds.getSuccess(), scenarioMds.getMsg());
        return scenarioMds.getData();
    }

}
