package com.yhl.scp.dfp.oem.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;

/**
 * @ClassName OemProductLineMapBasicPO
 * @Description TODO
 * @Date 2024-09-25 11:44:26
 * <AUTHOR>
 * @Copyright 瑞之泽
 * @Version 1.0
 */
public class OemProductLineMapBasicPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -39402485056992673L;

    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 产线编码
     */
    private String lineCode;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 版本
     */
    private Integer versionValue;
    /**
     * 是否由福耀提供
     */
    private String providedByFuyao;

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

    public String getProvidedByFuyao() {
        return providedByFuyao;
    }

    public void setProvidedByFuyao(String providedByFuyao) {
        this.providedByFuyao = providedByFuyao;
    }

}
