package com.yhl.scp.dfp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>CustomerOemJob</code>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 16:22:11
 */
@Component
@Slf4j
public class CustomerDataJob {
    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DfpFeign dfpFeign;

    @XxlJob("customerDataJob")
    public ReturnT<String> customerDataJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在DFP模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的同步库存点job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());

            dfpFeign.synCustomer(scenario.getDataBaseName(),"324", scenario.getTenantId());

            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步产品与库存点job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
