package com.yhl.scp.dfp.loading.controller;

import com.alibaba.excel.util.StringUtils;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.GenerateTypeEnum;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionReqDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionUploadDTO;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.DemandParsedVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.system.entity.Scenario;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>LoadingDemandSubmissionController</code>
 * <p>
 * 装车需求提报控制器
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 10:12:19
 */
@Slf4j
@Api(tags = "装车需求提报控制器")
@RestController
@RequestMapping("loadingDemandSubmission")
public class LoadingDemandSubmissionController extends BaseController {

    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    private OriginDemandVersionService originDemandVersionService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<LoadingDemandSubmissionVO>> page(@RequestParam("originDemandVersionId") String originDemandVersionId) {
        String queryCriteriaParam = getQueryCriteriaParam();
        if (StringUtils.isBlank(queryCriteriaParam)) {
            queryCriteriaParam = " version_id = '" + originDemandVersionId + "'";
        } else {
            queryCriteriaParam = queryCriteriaParam + " and version_id = '" + originDemandVersionId + "'";
        }
        List<LoadingDemandSubmissionVO> loadingDemandSubmissionList = loadingDemandSubmissionService.selectByPage(getPagination(),
                getSortParam(), queryCriteriaParam);
        PageInfo<LoadingDemandSubmissionVO> pageInfo = new PageInfo<>(loadingDemandSubmissionList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "模板导出")
    @GetMapping(value = "exportTemplate")
    public void exportTemplate(HttpServletResponse response, @RequestParam("templateType") String templateType) {
        loadingDemandSubmissionService.exportTemplate(response, templateType);
    }

    @ApiOperation(value = "附件上传")
    @PostMapping(value = "upload")
    public BaseResponse<Void> uploadFiles(@RequestParam("originVersionId") String originVersionId,
                                          @RequestParam("oemCode") String oemCode,
                                          @RequestParam("contentType") String contentType,
                                          @RequestPart("originFile") MultipartFile originFile,
                                          @RequestPart("submissionFile") MultipartFile submissionFile) {
        return loadingDemandSubmissionService.uploadFiles(originVersionId, oemCode, contentType, originFile, submissionFile);
    }

    @ApiOperation(value = "需求数据上传")
    @PostMapping(value = "importDemand")
    @BusinessMonitorLog(businessCode = "装车需求提报", moduleCode = "DFP", businessFrequency = "DAY")
    public BaseResponse<Void> importDemand(@RequestParam("originVersionId") String originVersionId,
                                           @RequestParam("importType") String importType,
                                           @RequestParam("templateType") String templateType,
                                           @RequestParam("projectDemandType") String projectDemandType,
                                           @RequestParam("checkFlag") String checkFlag,
                                           @RequestPart("submissionFile") MultipartFile submissionFile) {

        //获取最新的原始需求版本，版本则不允许修改
        OriginDemandVersionVO originDemandVersionVO = originDemandVersionService.selectLastVersionByPlanPeriod(null);
        if (null != originDemandVersionVO && org.apache.commons.lang3.StringUtils.equals(originDemandVersionVO.getId(), originVersionId)) {
            return loadingDemandSubmissionService.importDemand(originVersionId, importType, templateType,
                    projectDemandType, checkFlag, submissionFile);
        }
        throw new BusinessException("您编辑的版本已不是最新版本，请刷新界面后选择最新版本操作");
    }

    @ApiOperation(value = "同步EDI接口")
    @GetMapping(value = "syncEdi")
    public BaseResponse<Void> syncEdi() {
        String tenantId = SystemHolder.getTenantId();
        String databaseName = SystemHolder.getScenario();
        Scenario scenario = new Scenario();
        scenario.setTenantId(tenantId);
        scenario.setDataBaseName(databaseName);
        return loadingDemandSubmissionService.syncEdi(scenario);
    }
    @ApiOperation(value = "同步需求")
    @PostMapping(value = "syncDemand")
    public BaseResponse<Void> syncDemand(@RequestBody LoadingDemandSubmissionReqDTO reqDTO) {
        return loadingDemandSubmissionService.syncDemand(reqDTO);
    }
    @ApiOperation(value = "需求提报")
    @PostMapping(value = "submission")
    public BaseResponse<Void> importData(@RequestBody LoadingDemandSubmissionReqDTO reqDTO) {
        return loadingDemandSubmissionService.submissionData(reqDTO);
    }

    @ApiOperation(value = "新建版本")
    @PostMapping(value = "createNewVersion")
    public BaseResponse<Void> createNewVersion() {
        try {
            loadingDemandSubmissionService.doCreateNewVersion(SystemHolder.getScenario(), GenerateTypeEnum.MANUAL.getCode());
        } catch (Exception e) {
            log.error("新建版本失败:", e);
            throw new BusinessException("创建版本失败");
        }
        return BaseResponse.success("新建版本成功");
    }

    @ApiOperation(value = "查看需求提报源文件")
    @GetMapping(value = "download")
    public void downloadFile(@RequestParam("originVersionId") String originVersionId,
                             @RequestParam("oemCode") String oemCode, HttpServletResponse response) {
        loadingDemandSubmissionService.downloadFile(originVersionId, oemCode, response);
    }

    @ApiOperation(value = "需求提报修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> updateSubmissionDetail(@RequestBody List<LoadingDemandSubmissionDetailDTO> loadingDemandSubmissionDetailDTOS) {
        //获取最新的原始需求版本，版本则不允许修改
        OriginDemandVersionVO originDemandVersionVO = originDemandVersionService.selectLastVersionByPlanPeriod(null);
        if (null != originDemandVersionVO && org.apache.commons.lang3.StringUtils.equals(originDemandVersionVO.getId(), loadingDemandSubmissionDetailDTOS.get(0).getVersionId())) {
            return loadingDemandSubmissionService.updateLoadingSubmissionDetail(loadingDemandSubmissionDetailDTOS);
        }
        throw new BusinessException("您编辑的版本已不是最新版本，请刷新界面后选择最新版本操作");
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        //获取最新的原始需求版本，版本则不允许删除
        OriginDemandVersionVO originDemandVersionVO = originDemandVersionService.selectLastVersionByPlanPeriod(null);
        if (null != originDemandVersionVO && org.apache.commons.lang3.StringUtils.equals(originDemandVersionVO.getId(), versionDTOList.get(0).getVersionId())) {
            loadingDemandSubmissionService.doDeleteByVersion(versionDTOList);
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        }
        throw new BusinessException("您编辑的版本已不是最新版本，请刷新界面后选择最新版本操作");
    }

    @ApiOperation(value = "数据导入(测试)")
    @PostMapping(value = "uploadTest")
    public BaseResponse<Void> uploadTest(LoadingDemandSubmissionUploadDTO loadingDemandSubmissionUploadDTO) {
        return null;
    }

    @ApiOperation(value = "获取当前人员需求类型")
    @GetMapping(value = "getDemandTypeEnum")
    public BaseResponse<Object> getDemandTypeEnum() {
        return loadingDemandSubmissionService.getDemandTypeEnum();
    }

    @ApiOperation(value = "解析客户原始需求文件")
    @PostMapping(value = "parseDemandFile")
    public BaseResponse<List<DemandParsedVO>> parseDemandFile(@RequestParam("oemId") String oemId,
                                                              @RequestPart("demandFiles") MultipartFile[] demandFiles) {
        return this.loadingDemandSubmissionService.parseDemandFile(oemId, demandFiles);
    }
}