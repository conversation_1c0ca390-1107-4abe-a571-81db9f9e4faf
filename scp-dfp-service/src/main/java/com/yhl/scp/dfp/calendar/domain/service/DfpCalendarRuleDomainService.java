package com.yhl.scp.dfp.calendar.domain.service;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dfp.calendar.convertor.CalendarRuleConvertor;
import com.yhl.scp.dfp.calendar.convertor.ShiftConvertor;
import com.yhl.scp.dfp.calendar.domain.entity.CalendarRuleDO;
import com.yhl.scp.dfp.calendar.domain.entity.RuleFilterParamDO;
import com.yhl.scp.dfp.calendar.domain.entity.ShiftDO;
import com.yhl.scp.dfp.calendar.enums.RepeatFrequencyEnum;
import com.yhl.scp.dfp.calendar.infrastructure.dao.DfpCalendarRuleDao;
import com.yhl.scp.dfp.calendar.infrastructure.dao.DfpShiftDao;
import com.yhl.scp.dfp.calendar.infrastructure.po.CalendarRulePO;
import com.yhl.scp.dfp.calendar.infrastructure.po.ShiftPO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemDao;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemProductLineDao;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemProductLineMapDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemProductLineMapPO;
import com.yhl.scp.dfp.oem.infrastructure.po.OemProductLinePO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.feign.IpsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CalendarRuleDomainService</code>
 * <p>
 * 日历规则领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 16:06:22
 */
@Service
@Slf4j
public class DfpCalendarRuleDomainService {

    @Resource
    private DfpCalendarRuleDao calendarRuleDao;

    @Resource
    private DfpShiftDao shiftDao;

    @Resource
    private OemDao oemDao;

    @Resource
    private OemProductLineDao oemProductLineDao;

    @Resource
    private OemProductLineMapDao oemProductLineMapDao;

//    @Resource
//    private PhysicalResourceDao physicalResourceDao;

//    @Resource
//    private ProductionOrganizationService productionOrganizationService;

//    @Resource
//    private HumanResourceGroupService humanResourceGroupService;
//
//    @Resource
//    private HumanResourceService humanResourceService;
//
//    @Resource
//    private PositionSkillService positionSkillService;

    @Resource
    IpsFeign ipsFeign;


    /**
     * 数据校验
     *
     * @param calendarRuleDO 领域对象
     */
    public void validation(CalendarRuleDO calendarRuleDO) {
        checkNotNull(calendarRuleDO);
        checkUniqueCode(calendarRuleDO);
        checkConflict(calendarRuleDO);
    }

    /**
     * 规则冲突校验
     *
     * @param calendarRuleDO
     */
    private void checkConflict(CalendarRuleDO calendarRuleDO) {
        Map<String, Object> params = new HashMap<>(4);
        /*
        首先判断当前规则是否具体到物理资源，如果具体到物理资源，找出系统中具体到同样物理资源的规则，执行冲突校验；
        如果当前规则没有具体到物理资源，再次判断当前规则是否具体到标准资源，是则找出系统中具体到同样标准资源，物理资源的规则，执行冲突校验;
        如果当前规则未具体到物理资源，也没有具体到标准资源，则找出相同生产组织，标准资源，物理资源的规则，执行冲突校验
         */
        params.put("calendarRuleType", calendarRuleDO.getCalendarRuleType());
        if (!StringUtils.equals("*", calendarRuleDO.getPhysicalResourceIds().get(0))) {
            params.put("physicalResourceIds", calendarRuleDO.getPhysicalResourceIds().get(0));
            params.put("standardResourceIds", calendarRuleDO.getStandardResourceIds().get(0));
        } else {
            params.put("physicalResourceIds", calendarRuleDO.getPhysicalResourceIds().get(0));
            if (!StringUtils.equals("*", calendarRuleDO.getStandardResourceIds().get(0))) {
                params.put("standardResourceIds", calendarRuleDO.getStandardResourceIds().get(0));
            } else {
                params.put("standardResourceIds", calendarRuleDO.getStandardResourceIds().get(0));
//                params.put("organizationId", calendarRuleDO.getOrganizationId());
            }
        }
        List<CollectionValueVO> shiftTypeList = ipsFeign.getByCollectionCode("SHIFT_TYPE");
        Map<String, String> shiftTypeMap = shiftTypeList.stream().collect(Collectors.toMap(CollectionValueVO::getCollectionValue, CollectionValueVO::getNotNullDescription));

        List<CalendarRulePO> list = calendarRuleDao.selectByParams(params);

        List<ShiftPO> shiftPOS = shiftDao.selectByParams(Maps.newHashMap());
        Map<String, ShiftPO> shiftPOMap = shiftPOS.stream().collect(Collectors.toMap(ShiftPO::getId, Function.identity()));
        // 获取日历规则对应的具体工作日
        List<Pair<Date, Date>> dateList = getCalendarRuleWorkDay(calendarRuleDO.getRepeatFrequency(),
                calendarRuleDO.getFrequencyPattern(), calendarRuleDO.getStartDate(), calendarRuleDO.getEndDate(),
                shiftPOMap.get(calendarRuleDO.getShiftIds().get(0)).getShiftPattern());

        if (CollectionUtils.isNotEmpty(list)) {
            for (CalendarRulePO calendarRulePO : list) {

                if (calendarRulePO.getId().equals(calendarRuleDO.getId())) {
                    continue;
                }
                if (calendarRuleDO.getEndDate().getTime() < calendarRulePO.getStartDate().getTime()) {
                    continue;
                }
                if (calendarRuleDO.getStartDate().getTime() > calendarRulePO.getEndDate().getTime()) {
                    continue;
                }
                if (!shiftTypeMap.get(shiftPOMap.get(calendarRuleDO.getShiftIds().get(0)).getShiftType())
                        .equals(shiftTypeMap.get(shiftPOMap.get(calendarRulePO.getShiftIds()).getShiftType()))) {
                    continue;
                }
                List<Pair<Date, Date>> dateListInDB = getCalendarRuleWorkDay(calendarRulePO.getRepeatFrequency(),
                        calendarRulePO.getFrequencyPattern(), calendarRulePO.getStartDate(), calendarRulePO.getEndDate(),
                        shiftPOMap.get(calendarRulePO.getShiftIds()).getShiftPattern());

                for (Pair<Date, Date> datePair : dateList) {
                    Date startTime1 = datePair.getFirst();
                    Date endTime1 = datePair.getSecond();
                    for (Pair<Date, Date> datePairInDB : dateListInDB) {
                        Date startTime2 = datePairInDB.getFirst();
                        Date endTime2 = datePairInDB.getSecond();
                        if (!endTime1.after(startTime2)) {
                            continue;
                        }
                        if (!startTime1.before(endTime2)) {
                            continue;
                        }
                        throw new BusinessException(MessageFormat.format("当前日历规则和规则:{0}时间段重复", calendarRulePO.getRuleName()));

                    }
                }
            }
        }

    }

    /**
     * 获取日历规则具体工作日
     *
     * @param repeatFrequency
     * @param frequencyPattern
     * @param startDate
     * @param endDate
     * @param shiftPattern
     * @return
     */
    public List<Pair<Date, Date>> getCalendarRuleWorkDay(String repeatFrequency, String frequencyPattern, Date startDate,
                                                         Date endDate, String shiftPattern) {
        List<Pair<Date, Date>> datePairList = new ArrayList<>();
        List<Integer> frequencyPatternList = Arrays.stream(frequencyPattern.split(",")).filter(StrUtil::isNotEmpty).map(t -> Integer.valueOf(t)).collect(Collectors.toList());
        for (int i = 0; i < Integer.MAX_VALUE; i++) {
            Date workDate = DateUtils.moveDay(startDate, i);
            if (workDate.after(endDate)) {
                break;
            }
            int dayIndex;
            if (RepeatFrequencyEnum.WEEKLY.getCode().equals(repeatFrequency)) {
                dayIndex = DateUtils.getDayOfWeek(workDate);
            } else if (RepeatFrequencyEnum.MONTHLY.getCode().equals(repeatFrequency)) {
                dayIndex = DateUtils.getDayOfMonth(workDate);
            } else {
                throw new BusinessException("日历规则重复模式不合法");
            }
            if (frequencyPatternList.contains(dayIndex)) {
                datePairList.add(getDateStartAndEnd(workDate, shiftPattern));
            }
        }
        return datePairList;
    }

    private Pair<Date, Date> getDateStartAndEnd(Date baseDate, String shiftPattern) {
        String baseDateStr = DateUtils.dateToString(baseDate, DateUtils.COMMON_DATE_STR3) + " ";
        String[] timeArr = shiftPattern.split("-");
        String startTimeStr = baseDateStr + timeArr[0];
        Date startTime = DateUtils.stringToDate(startTimeStr, DateUtils.COMMON_DATE_STR5);
        String endTimeStr = baseDateStr + timeArr[1];
        Date endTime = DateUtils.stringToDate(endTimeStr, DateUtils.COMMON_DATE_STR5);
        if (startTime.after(endTime)) {
            endTime = DateUtils.moveDay(endTime, 1);
        }
        return new Pair<>(startTime, endTime);
    }

    /**
     * 非空检验
     *
     * @param calendarRuleDO 领域对象
     */
    private void checkNotNull(CalendarRuleDO calendarRuleDO) {
        if (CollectionUtils.isEmpty(calendarRuleDO.getPhysicalResourceIds())) {
            throw new BusinessException("物理资源，不能为空");
        }
        if (StringUtils.isBlank(calendarRuleDO.getRuleName())) {
            throw new BusinessException("日历规则名称，不能为空");
        }
        if (calendarRuleDO.getStartDate() == null || calendarRuleDO.getEndDate() == null) {
            throw new BusinessException("日历规则开始日和结束日，不能为空,否则不能自动刷新资源日历");
        }
        if (calendarRuleDO.getStartDate().compareTo(calendarRuleDO.getEndDate()) > 0) {
            throw new BusinessException("开始时间应小于结束时间");
        }
    }

    /**
     * 唯一性校验
     *
     * @param calendarRuleDO 领域对象
     */
    private void checkUniqueCode(CalendarRuleDO calendarRuleDO) {
        Map<String, Object> params = new HashMap<>(4);
        params.put("ruleName", calendarRuleDO.getRuleName());
        params.put("calendarRuleType", calendarRuleDO.getCalendarRuleType());
        if (StringUtils.isBlank(calendarRuleDO.getId())) {
            List<CalendarRulePO> list = calendarRuleDao.selectByParams(params);
            if (CollectionUtils.isNotEmpty(list)) {
                throw new BusinessException("新增失败，日历规则名称已存在：" + calendarRuleDO.getRuleName());
            }
        } else {
            CalendarRulePO old = calendarRuleDao.selectByPrimaryKey(calendarRuleDO.getId());
            if (!calendarRuleDO.getRuleName().equals(old.getRuleName())) {
                List<CalendarRulePO> list = calendarRuleDao.selectByParams(params);
                if (CollectionUtils.isNotEmpty(list)) {
                    throw new BusinessException("修改失败，日历规则名称已存在：" + calendarRuleDO.getRuleName());
                }
            }
        }
    }

    /**
     * 获取指定资源下的开始时间和结束时间这一段时间内可用的日历规则
     *
     * @param organizationId      生产组织ID
     * @param standardResourceIds 标准资源ids
     * @param physicalResourceIds 物理资源ids
     * @param startDate           开始时间
     * @param endDate             结束时间
     * @param calendarRuleDOList  待筛选的日历规则集合
     * @return 满足3个param的日历规则集合
     */
    public List<CalendarRuleDO> getCalendarRuleDOs(
            String organizationId,
            List<String> standardResourceIds,
            List<String> physicalResourceIds,
            Date startDate,
            Date endDate,
            List<CalendarRuleDO> calendarRuleDOList) {

        List<CalendarRuleDO> calendarRuleDOS = calendarRuleDOList;
        if (CollectionUtils.isEmpty(calendarRuleDOList)) {
            // 如果calendarRuleDOList入参为空，获取当前所有的日历规则
            List<CalendarRulePO> calendarRulePOS = calendarRuleDao.selectByParams(new HashMap<>(2));
            calendarRuleDOS = CalendarRuleConvertor.INSTANCE.pos2Dos(calendarRulePOS);
        }

        // 组装过滤条件
        RuleFilterParamDO param = RuleFilterParamDO.builder()
//                .organizationId(organizationId)
                .standardResourceIds(standardResourceIds)
                .physicalResourceIds(physicalResourceIds)
                .startDate(startDate)
                .endDate(endDate).build();

        // 过滤符合条件的日历规则
        List<CalendarRuleDO> filteredRules = calendarRuleDOS.stream().filter(it -> it.isSatisfiesBy(param)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(filteredRules)) {
            return new ArrayList<>();
        }
        // 填充每个日历规则 填充内部的shiftDO
        List<String> shiftsIds = filteredRules.stream().flatMap(it -> it.getShiftIds().stream()).collect(Collectors.toList());
        Map<String, ShiftPO> shiftPOMap = shiftDao.selectByPrimaryKeys(shiftsIds).stream().collect(Collectors.toMap(BasePO::getId, it -> it));
        filteredRules.forEach(
                it -> {
                    List<ShiftDO> shiftDOS = new ArrayList<>();
                    for (String shiftId : it.getShiftIds()) {
                        ShiftDO shiftDO = ShiftConvertor.INSTANCE.po2Do(shiftPOMap.get(shiftId));
                        shiftDOS.add(shiftDO);
                    }
                    it.setShiftDOS(shiftDOS);
                }
        );
        return filteredRules;
    }

    /**
     * 获取指定资源下 当前计划日 可用的日历规则
     *
     * @param calendarRuleDOList 待筛选日历规则
     * @param planDate           当前计划日
     * @return 可运用到当天的优先级最高的日历规则
     */
    public List<CalendarRuleDO> getPlanDateRule(List<CalendarRuleDO> calendarRuleDOList, Date planDate) {
        // 过滤出可以用在当天的日历规则
        List<CalendarRuleDO> rules = calendarRuleDOList.stream().filter(it -> it.ifCanUsedByTheDate(planDate)).collect(Collectors.toList());
        // 按照日历类型分组，同一个日历类型取优先级为1的
        // groupBy不支持key的null,需要先过滤掉CalendarType为null的
        Map<String, List<CalendarRuleDO>> calendarRuleMapGroupByCalendarType = rules.stream()
                .filter(it -> StringUtils.isNotEmpty(it.getCalendarType())).collect(Collectors.groupingBy(CalendarRuleDO::getCalendarType));

        //处理CalendarType为null的情况
        List<CalendarRuleDO> rulesWithNoCalendarType = rules.stream().filter(it -> StringUtils.isEmpty(it.getCalendarType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rulesWithNoCalendarType)) {
            calendarRuleMapGroupByCalendarType.put(null, rules.stream().filter(it -> StringUtils.isEmpty(it.getCalendarType())).collect(Collectors.toList()));
        }

        // 相同日历类型的，取优先级最高的
        List<CalendarRuleDO> res = new ArrayList<>();
        for (Map.Entry<String, List<CalendarRuleDO>> entry : calendarRuleMapGroupByCalendarType.entrySet()) {
            CalendarRuleDO calendarRuleDO = entry.getValue().stream()
                    .max(Comparator.comparing(CalendarRuleDO::getPriority, Comparator.nullsLast(Integer::compareTo)))
                    .orElse(null);
            Integer priority = calendarRuleDO.getPriority();
            List<CalendarRuleDO> ruleDOList = entry.getValue().stream().filter(it -> it.getPriority().equals(priority)).collect(Collectors.toList());
            res.addAll(ruleDOList);
        }
        return res;
    }


    /**
     * 获取指定资源下的开始时间和结束时间这一段时间内可用的日历规则
     *
     * @param organizationId      生产组织
     * @param standardResourceIds 标准资源ids
     * @param physicalResourceIds 物理资源ids
     * @return 日历规则集合
     */
    public List<CalendarRuleDO> getCalendarRulesByResource(
            String organizationId,
            List<String> standardResourceIds,
            List<String> physicalResourceIds) {


        List<CalendarRulePO> calendarRulePOS = calendarRuleDao.selectByParams(new HashMap<>(2));

        // 如果查询条件都是*,则返回所有
        if (

                standardResourceIds.contains("*")
                        && physicalResourceIds.contains("*")
        ) {
            return CalendarRuleConvertor.INSTANCE.pos2Dos(calendarRulePOS);
        }

        List<CalendarRulePO> filtered = calendarRulePOS.stream().filter(it -> {
                    // 如果该日历规则的三个属性都为*,则符合所有
                    if (

                            ("*").equals(it.getStandardResourceIds())
                                    && ("*").equals(it.getPhysicalResourceIds())
                    ) {
                        return true;
                    }
                    return
                            it.getPhysicalResourceIds().contains(StringUtils.join(physicalResourceIds, ","))
                                    &&
                                    it.getStandardResourceIds().contains(StringUtils.join(standardResourceIds, ","));
                }

        ).collect(Collectors.toList());
        return CalendarRuleConvertor.INSTANCE.pos2Dos(filtered);
    }

    /**
     * 格式化数据
     *
     * @param calendarRuleDO
     */
    public void formatData(CalendarRuleDO calendarRuleDO) {
        calendarRuleDO.setStartDate(DateUtils.truncateTimeOfDate(calendarRuleDO.getStartDate()));
        calendarRuleDO.setEndDate(DateUtils.truncateTimeOfDate(calendarRuleDO.getEndDate()));
        if (CollectionUtils.isEmpty(calendarRuleDO.getStandardResourceIds())) {
            calendarRuleDO.setStandardResourceIds(Lists.newArrayList("*"));
        }
        if (CollectionUtils.isEmpty(calendarRuleDO.getPhysicalResourceIds())) {
            calendarRuleDO.setPhysicalResourceIds(Lists.newArrayList("*"));
        }
    }

    /**
     * 日历规则分组，并进行优先级计算
     * 优先级从高到低规则为：具体到物理资源的规则 > 具体到标准资源、未具体到物理资源的规则 >
     * 具体到生产组织，未具体到标准资源和物理资源的规则 > 未具体到生产组织、标准资源以及物理资源的规则
     *
     * @return
     */
    public Map<String, List<CalendarRuleDO>> groupAndSortCelendarRule() {
        List<CalendarRulePO> calendarRulePOList = calendarRuleDao.selectByParams(Maps.newHashMap());
        List<ShiftPO> shiftPOS = shiftDao.selectByParams(Maps.newHashMap());
        Map<String, ShiftPO> shiftPOMap = shiftPOS.stream().collect(Collectors.toMap(ShiftPO::getId, Function.identity()));
        List<CollectionValueVO> shiftType = ipsFeign.getByCollectionCode("SHIFT_TYPE");
        Map<String, CollectionValueVO> shiftTypeMap = shiftType.stream().collect(Collectors.toMap(CollectionValueVO::getCollectionValue, Function.identity()));

        Map<String, List<CalendarRuleDO>> result = new HashMap<>();
        List<CalendarRuleDO> normalCalendarRuleList = new ArrayList<>();
        List<CalendarRuleDO> abnormalCalendarRuleList = new ArrayList<>();

        for (CalendarRulePO calendarRulePO : calendarRulePOList) {
            CalendarRuleDO calendarRuleDO = CalendarRuleConvertor.INSTANCE.po2Do(calendarRulePO);
            if (!shiftPOMap.containsKey(calendarRulePO.getShiftIds())) {
                log.warn("日历规则名称：{}，无对应的班次数据", calendarRulePO.getRuleName());
                continue;
            }
            ShiftPO shiftPO = shiftPOMap.get(calendarRulePO.getShiftIds());
            ShiftDO shiftDO = ShiftConvertor.INSTANCE.po2Do(shiftPO);
            calendarRuleDO.setShiftDOS(Lists.newArrayList(shiftDO));
            calendarRuleDO.setDescription(shiftTypeMap.get(shiftPO.getShiftType()).getDescription());
            if (StringUtils.isNotBlank(calendarRulePO.getPhysicalResourceIds()) && !StringUtils.equals("*", calendarRulePO.getPhysicalResourceIds())) {
                calendarRuleDO.setPriority(1);//定义了单独的physicalId
                calendarRuleDO.setPhysicalResourceIds(Lists.newArrayList(calendarRulePO.getPhysicalResourceIds()));
            } else if ((StringUtils.isBlank(calendarRulePO.getPhysicalResourceIds()) || StringUtils.equals("*", calendarRulePO.getPhysicalResourceIds())) &&
                    StringUtils.isNotBlank(calendarRulePO.getStandardResourceIds()) && !StringUtils.equals("*", calendarRulePO.getStandardResourceIds())) {
                List<String> physicalResourceIdList;
                calendarRuleDO.setPriority(2);//定义了单独的StandardResourceId
                // List<OemProductLinePO> oemProductLinePOS = oemProductLineDao.selectByOemCodes(Lists.newArrayList(calendarRulePO.getStandardResourceIds()));
                // physicalResourceIdList = oemProductLinePOS.stream().map(OemProductLinePO::getLineCode).collect(Collectors.toList());
                List<OemProductLineMapPO> oemProductLineMapPOS =
                        oemProductLineMapDao.selectByLineCodes(Lists.newArrayList(calendarRulePO.getStandardResourceIds()));
                physicalResourceIdList =
                        oemProductLineMapPOS.stream().map(OemProductLineMapPO::getVehicleModelCode).distinct().collect(Collectors.toList());
                calendarRuleDO.setPhysicalResourceIds(physicalResourceIdList);
            } else if ((StringUtils.isBlank(calendarRulePO.getPhysicalResourceIds()) || StringUtils.equals("*",
                    calendarRulePO.getPhysicalResourceIds())) &&
                    (StringUtils.isBlank(calendarRulePO.getStandardResourceIds()) || StringUtils.equals("*",
                            calendarRulePO.getStandardResourceIds())) &&
                    StringUtils.isNotBlank(calendarRulePO.getOemCode()) && !StringUtils.equals("*",calendarRulePO.getOemCode())) {
                List<String> physicalResourceIdList;
                calendarRuleDO.setPriority(3);//定义了单独的oemCode
                // List<OemProductLinePO> oemProductLinePOS = oemProductLineDao.selectByOemCodes(Lists.newArrayList(calendarRulePO.getStandardResourceIds()));
                // physicalResourceIdList = oemProductLinePOS.stream().map(OemProductLinePO::getLineCode).collect(Collectors.toList());
                List<OemProductLineMapPO> oemProductLineMapPOS =
                        oemProductLineMapDao.selectByOemCodeList(Lists.newArrayList(calendarRulePO.getOemCode()));
                physicalResourceIdList =
                        oemProductLineMapPOS.stream().map(OemProductLineMapPO::getVehicleModelCode).distinct().collect(Collectors.toList());
                calendarRuleDO.setPhysicalResourceIds(physicalResourceIdList);
            }  else {
                calendarRuleDO.setPriority(4);
                List<String> physicalResourceIdList;
                List<OemProductLineMapPO> oemProductLineMapPOS = oemProductLineMapDao.selectAll();
                physicalResourceIdList =
                        oemProductLineMapPOS.stream().map(OemProductLineMapPO::getVehicleModelCode).distinct().collect(Collectors.toList());
                calendarRuleDO.setPhysicalResourceIds(physicalResourceIdList);
            }
            if ("正常".equals(shiftTypeMap.get(shiftPO.getShiftType()).getDescription())) {
                normalCalendarRuleList.add(calendarRuleDO);
            } else {
                abnormalCalendarRuleList.add(calendarRuleDO);
            }
            normalCalendarRuleList.sort(Comparator.comparing(CalendarRuleDO::getPriority));
            abnormalCalendarRuleList.sort(Comparator.comparing(CalendarRuleDO::getPriority));
            result.put("normal", normalCalendarRuleList);
            result.put("abnormal", abnormalCalendarRuleList);
        }
        return result;
    }
}
