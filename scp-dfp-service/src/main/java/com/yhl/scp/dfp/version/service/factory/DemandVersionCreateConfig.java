package com.yhl.scp.dfp.version.service.factory;

import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.release.enums.ReleaseDemandTypeEnum;
import com.yhl.scp.dfp.version.service.IDemandVersionCreate;
import com.yhl.scp.dfp.version.service.impl.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <code>VersionCreateConfig</code>
 * <p>
 * VersionCreateConfig
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-04 13:40:38
 */
public class DemandVersionCreateConfig {

    /**
     * 版本创建策略组
     */
    protected Map<String, IDemandVersionCreate> demandVersionCreateStrategyMap = new ConcurrentHashMap<>();

    @Resource
    private OriginDemandVersionCreate originDemandVersionCreate;

    @Resource
    private CleanDayVersionCreate cleanDayVersionCreate;

    @Resource
    private CleanForecastVersionCreate cleanForecastVersionCreate;

    @Resource
    private CleanAlgorithmVersionCreate cleanAlgorithmVersionCreate;

    @Resource
    private DemandForecastVersionCreate demandForecastVersionCreate;

    @Resource
    private ConsistenceDemandForecastVersionCreate consistenceDemandForecastVersionCreate;

    @Resource
    private DeliveryPlanVersionCreate deliveryPlanVersionCreate;

    @Resource
    private ProjectForecastVersionCreate projectForecastVersionCreate;

    @PostConstruct
    public void init() {
        demandVersionCreateStrategyMap.put(VersionTypeEnum.ORIGIN_DEMAND.getCode(), originDemandVersionCreate);
        demandVersionCreateStrategyMap.put(VersionTypeEnum.CLEAN_DEMAND.getCode(), cleanDayVersionCreate);
        demandVersionCreateStrategyMap.put(VersionTypeEnum.CLEAN_FORECAST.getCode(), cleanForecastVersionCreate);
        demandVersionCreateStrategyMap.put(VersionTypeEnum.CLEAN_ALGORITHM.getCode(), cleanAlgorithmVersionCreate);
        demandVersionCreateStrategyMap.put(VersionTypeEnum.DEMAND_FORECAST.getCode(), demandForecastVersionCreate);
        demandVersionCreateStrategyMap.put(VersionTypeEnum.CONSISTENCE_DEMAND_FORECAST.getCode(), consistenceDemandForecastVersionCreate);
        demandVersionCreateStrategyMap.put(VersionTypeEnum.DELIVERY_PLAN.getCode(), deliveryPlanVersionCreate);
        demandVersionCreateStrategyMap.put(VersionTypeEnum.PROJECT_FORECAST.getCode(), projectForecastVersionCreate);
        //用于映射 “业务预测发布” 中需求类型 与 项目预测和一致性预测
        demandVersionCreateStrategyMap.put(ReleaseDemandTypeEnum.LOADING_DEMAND.getCode(), consistenceDemandForecastVersionCreate);
        demandVersionCreateStrategyMap.put("PROJECT_FORECAST", projectForecastVersionCreate);

    }
}
