package com.yhl.scp.dfp.material.domain.entity;

import java.io.Serializable;

import com.yhl.platform.common.ddd.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <code>PartRiskLevelDO</code>
 * <p>
 * 零件风险等级DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PartRiskLevelDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -80418475714927212L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    private String productCode;
    
    /**
     * 零件风险等级（计算后）
     */
    private String materialRiskLevelCalculate;
    
    /**
     * 零件风险等级（上月后）
     */
    private String materialRiskLevelMonth;
    
    /**
     * 修改原因
     */
    private String updateRemark;
}
