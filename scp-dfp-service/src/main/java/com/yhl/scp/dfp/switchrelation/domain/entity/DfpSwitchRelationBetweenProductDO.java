package com.yhl.scp.dfp.switchrelation.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.util.Date;

/**
 * <code>DfpSwitchRelationBetweenProductDO</code>
 * <p>
 * 新旧产品工程变更关系DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-19 16:35:33
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DfpSwitchRelationBetweenProductDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 842778349806636439L;

/**
     * 主键id
     */
    private String id;
/**
     * 主机厂编码
     */
    private String oemCode;
/**
     * 旧产品编码
     */
    private String oldProductCode;
/**
     * 新产品编码
     */
    private String newProductCode;
/**
     * 成套切换编码
     */
    private String switchCodeGroup;
/**
     * 切换方式
     */
    private String switchMode;
/**
     * 切换数量
     */
    private Integer switchQuantity;
/**
     * 切换开始时间
     */
    private Date switchTime;

}
