package com.yhl.scp.dfp.loading.handler;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.alibaba.excel.util.StringUtils;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.TemplateDayPatternEnum;
import com.yhl.scp.dfp.common.enums.TemplateMonthPatternEnum;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.loading.vo.LoadingProductVO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.feign.common.NewMdsFeign;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>LoadingComponentHandler</code>
 * <p>
 * LoadingComponentHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-20 13:39:22
 */
@Component
@Slf4j
public class LoadingComponentHandler {

    @Resource
    private PartRelationMapService partRelationMapService;

    @Resource
    @Lazy
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private OemService oemService;
    
    @Resource
    @Lazy
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    private final Map<String, LoadingAbstractHandler> strategyMap = new HashMap<>();

    // 定义需要保留的重要列
    private final List<String> essentialColumns = Arrays.asList("主机厂编码", "产品编码", "客户零件号");

    /**
     * 注入所有继承了LoadingAbstractHandler接口的Bean
     *
     * @param strategyMap 策略集合
     */
    @Autowired
    public LoadingComponentHandler(Map<String, LoadingAbstractHandler> strategyMap) {
        strategyMap.forEach((k, v) -> this.strategyMap.put(v.getCommand(), v));
    }

    /**
     * 保存数据
     */
    public void handle(String contentType, Map<Integer, String> headers, List<Map<Integer, String>> data, Map<String, String> extMap) {
        List<String> headerValues = new ArrayList<>(headers.values());

        List<String> productCodeList = new ArrayList<>();
        List<String> joinKeyList = new ArrayList<>();
        for (Map<Integer, String> datum : data) {
            String joinKey = String.join("&", datum.get(0), datum.get(1), datum.get(2));
            //校验数据重复问题
            productCodeList.add(datum.get(1));
            if (joinKeyList.contains(joinKey)) {
                throw new BusinessException(joinKey + " ：导入数据重复，请检查数据");
            }
            joinKeyList.add(joinKey);
        }

        // 校验日期格式
        validateDateHeaders(headerValues,essentialColumns);

        List<String> dayPatternHeaders = headerValues.stream()
                .filter(t -> Pattern.matches(TemplateDayPatternEnum.YYYY_MM_DD.getPattern(), t))
                .collect(Collectors.toList());
        List<String> monthPatternHeaders = headerValues.stream()
                .filter(t -> Pattern.matches(TemplateMonthPatternEnum.YYYY_MM.getPattern(), t))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(dayPatternHeaders) || CollectionUtils.isNotEmpty(monthPatternHeaders)) {
            handleMixedPatterns(productCodeList, headers, data, extMap, dayPatternHeaders, monthPatternHeaders);
        }
    }

    public static void validateDateHeaders(List<String> headerValues,
                                           List<String> essentialColumns) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 计算30天后的日期
        LocalDate thirtyDaysLater = currentDate.plusDays(30);
        // 计算当前月份和下一年同一月份
        YearMonth currentYearMonth = YearMonth.from(currentDate);
        YearMonth nextYearSameMonth = currentYearMonth.plusYears(1);

        // 生成需要检查的日期列表
        List<String> requiredDates = new ArrayList<>();
        // 添加日维度的日期
        for (LocalDate date = currentDate;!date.isAfter(thirtyDaysLater); date = date.plusDays(1)) {
            requiredDates.add(date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        // 添加月维度的日期
        for (YearMonth yearMonth = currentYearMonth;!yearMonth.isAfter(nextYearSameMonth); yearMonth = yearMonth.plusMonths(1)) {
            requiredDates.add(yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        }

        // 检查日期是否在表头中
        for (String requiredDate : requiredDates) {
            if (!headerValues.contains(requiredDate)) {
                throw new BusinessException(String.format("日期: %s 必须存在于模板中，请检查模板是否完整", requiredDate));
            }
        }

        // 原有的日期格式校验逻辑
        headerValues.forEach(item -> {
            // 不属于固定格式就为日期，需要校验日期格式是否正确
            if (!essentialColumns.contains(item)) {
                // 日期不为yyyy-MM-dd 并且 不为 yyyy-MM 两个维度都无法匹配，抛出异常
                if (!Pattern.matches(TemplateDayPatternEnum.YYYY_MM_DD.getPattern(), item) &&
                        !Pattern.matches(TemplateMonthPatternEnum.YYYY_MM.getPattern(), item)) {
                    throw new BusinessException(String.format("日期: %s 不符合模板格式, 请修改格式为正确日期格式", item));
                }
            }
        });
    }

    private void handleMixedPatterns(List<String> productCodeList, Map<Integer, String> headers, List<Map<Integer, String>> data,
                                     Map<String, String> extMap, List<String> dayPatternHeaders, List<String> monthPatternHeaders) {

        String message = "";
        LoadingDataHolder holder = getInitData(productCodeList, extMap);
        // 处理日期模式数据
        if (CollectionUtils.isNotEmpty(dayPatternHeaders)) {
            Map<Integer, String> dayHeaders = filterHeaders(headers, dayPatternHeaders, essentialColumns);
            List<Map<Integer, String>> dayData = filterData(data, dayHeaders);
            String dayCommand = TemplateDayPatternEnum.getTemplateCodeByPattern(dayPatternHeaders);
            remapIndexes(dayHeaders, dayData);
            message = processData(productCodeList, dayCommand, dayHeaders, dayData, extMap, holder);
        }

        // 处理月份模式数据
        if (CollectionUtils.isNotEmpty(monthPatternHeaders)) {
            Map<Integer, String> monthHeaders = filterHeaders(headers, monthPatternHeaders, essentialColumns);
            List<Map<Integer, String>> monthData = filterData(data, monthHeaders);
            String monthCommand = TemplateMonthPatternEnum.getTemplateCodeByPattern(monthPatternHeaders);
            remapIndexes(monthHeaders, monthData);
            message = processData(productCodeList, monthCommand, monthHeaders, monthData, extMap, holder);
        }
        extMap.put("resultInfo", message);
        //Assert.isTrue(StringUtils.isBlank(message), message);
    }

    private Map<Integer, String> filterHeaders(Map<Integer, String> headers, List<String> patternHeaders, List<String> essentialColumns) {
        return headers.entrySet().stream().filter(entry -> patternHeaders.contains(entry.getValue())
                        || essentialColumns.contains(entry.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
    }

    private List<Map<Integer, String>> filterData(List<Map<Integer, String>> data, Map<Integer, String> filteredHeaders) {
        return data.stream().map(row -> {
            if (row == null) {
                return new LinkedHashMap<Integer, String>();
            }
            return row.entrySet().stream().filter(entry -> filteredHeaders.containsKey(entry.getKey())
                            && entry.getValue() != null)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
        }).filter(map -> !map.isEmpty()).collect(Collectors.toList());
    }

    private void remapIndexes(Map<Integer, String> headers, List<Map<Integer, String>> data) {
        List<Integer> sortedKeys = new ArrayList<>(headers.keySet());
        Collections.sort(sortedKeys);

        Map<Integer, Integer> indexMap = new HashMap<>();
        for (int i = 0; i < sortedKeys.size(); i++) {
            indexMap.put(sortedKeys.get(i), i + 1);
        }

        Map<Integer, String> remappedHeaders = new LinkedHashMap<>();
        for (Map.Entry<Integer, String> entry : headers.entrySet()) {
            remappedHeaders.put(indexMap.get(entry.getKey()), entry.getValue());
        }
        headers.clear();
        headers.putAll(remappedHeaders);

        for (Map<Integer, String> row : data) {
            Map<Integer, String> remappedRow = new LinkedHashMap<>();
            for (Map.Entry<Integer, String> entry : row.entrySet()) {
                remappedRow.put(indexMap.get(entry.getKey()), entry.getValue());
            }
            row.clear();
            row.putAll(remappedRow);
        }
    }

    private String processData(List<String> productCodeList, String command, Map<Integer, String> headers,
                               List<Map<Integer, String>> data, Map<String, String> extMap, LoadingDataHolder holder) {
        String message = "";
        if (StringUtils.isBlank(command) || !strategyMap.containsKey(command)) {
            log.debug("不支持处理的消息:{}", command);
            return message;
        }
        LoadingAbstractHandler loadingAbstractHandler = strategyMap.get(command);
        if (Objects.nonNull(loadingAbstractHandler)) {
            message = loadingAbstractHandler.handleFileImport(holder, headers, data, extMap);
        }
        return message;
    }

    protected LoadingDataHolder getInitData(List<String> productCodeList, Map<String, String> extMap) {
        // 获取零件映射关系
        Map<String, Object> partRelationParam = new HashMap<>();
        partRelationParam.put("enabled", YesOrNoEnum.YES.getCode());
        partRelationParam.put("productCodeList", productCodeList);
        List<PartRelationMapVO> relationMapVOS = partRelationMapService.selectByParams(partRelationParam);
        List<String> partRelationProductCodes = relationMapVOS.stream().filter( e-> StringUtils.isNotBlank(e.getProductCode()))
        	.map(PartRelationMapVO::getProductCode).distinct().collect(Collectors.toList());
        List<String> errProductCodes = productCodeList.stream()
                .filter(item -> !partRelationProductCodes.contains(item))
                .collect(Collectors.toList());
        List<OemVO> oemVOS = oemService.selectByParams(new HashMap<>(2));
        Assert.isTrue(CollectionUtils.isEmpty(errProductCodes), "产品编码："+ String.join(",", errProductCodes) +"零件映射关系为空");
        Assert.isTrue(CollectionUtils.isNotEmpty(oemVOS), "主机厂档案为空");
        // 转换本厂编码及零件号的映射关系
        Map<String, List<String>> productPartMap = relationMapVOS.stream()
                .filter(item -> StringUtils.isNotBlank(item.getPartNumber()))
                .collect(Collectors.groupingBy(PartRelationMapVO::getProductCode, Collectors
                        .mapping(PartRelationMapVO::getPartNumber, Collectors.toList())));
        Map<String, String> partEnabledMap = relationMapVOS.stream().collect(Collectors
                .toMap(PartRelationMapVO::getPartNumber, PartRelationMapVO::getEnabled, (v1, v2) -> v2));
        // 根据oemCode分组
        Map<String, List<OemVO>> oemMap = oemVOS.stream().collect(Collectors.groupingBy(OemVO::getOemCode));
        // 获取历史的装车需求提报信息
        String originVersionId = extMap.get("originVersionId");
        Map<String, Object> loadingParamMap = new HashMap<>();
        loadingParamMap.put("versionId", originVersionId);
        loadingParamMap.put("enabled", YesOrNoEnum.YES.getCode());
        List<LoadingDemandSubmissionVO> oldLodLoadingDemandSubmissionVOS = loadingDemandSubmissionService.selectByParams(loadingParamMap);
        Map<String, LoadingDemandSubmissionVO> submissionVOMap = CollectionUtils.isEmpty(oldLodLoadingDemandSubmissionVOS)
                ? new HashMap<>() : oldLodLoadingDemandSubmissionVOS.stream().collect(
                Collectors.toMap(x -> String.join("_",
                                x.getOemCode(), x.getProductCode()),
                        Function.identity(), (v1, v2) -> v1));
        
        //查询获取历史的装车需求详细信息（只差月份）
        Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailVOMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(oldLodLoadingDemandSubmissionVOS)) {
        	List<String> oldSubmissionIds = oldLodLoadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getId).collect(Collectors.toList());
        	List<LoadingDemandSubmissionDetailVO> oldLodLoadingDemandSubmissionDetailVOS = loadingDemandSubmissionDetailService
        			.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), 
        			"submissionType" , GranularityEnum.MONTH.getCode(),
        			"submissionIds" , oldSubmissionIds));
        	submissionDetailVOMap = oldLodLoadingDemandSubmissionDetailVOS
        			.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId));
        }
        
        // 获取主机厂库存信息
        List<OemStockPointMapVO> oemStockPointMapVOS = oemStockPointMapService.selectAll();
        log.info("LoadingHorizonYearMonthDayHandler-主机厂映射关系行数：{}", oemStockPointMapVOS.size());
        // 获取物料信息
        List<String> stockPointCodes = oemStockPointMapVOS.stream().map(OemStockPointMapVO::getStockPointCode)
                .distinct().collect(Collectors.toList());
        List<LoadingProductVO> productStockPointVOS = new ArrayList<>();
        Map<String, List<String>> productCodeVehicleCodeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(stockPointCodes)) {
            //查询组织类型为"销售组织"的库存点
            String scenario = SystemHolder.getScenario();
            if (StringUtils.isEmpty(scenario)) {
                scenario = extMap.get("scenario");
            }
            productStockPointVOS = newMdsFeign.selectProduct4LoadingDemandSubmission(scenario)
                    .stream().map(item -> LoadingProductVO.builder().productCode(item.getProductCode())
                            .vehicleModelCode(item.getVehicleModelCode()).orderPlanner(item.getOrderPlanner()).build())
                    .collect(Collectors.toList());
            List<String> vehicleModelCodeList = productStockPointVOS.stream()
                    .map(LoadingProductVO::getVehicleModelCode).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(vehicleModelCodeList)) {
                // 通过车型编码获取主机厂车型信息
                List<OemVehicleModelVO> oemVehicleModelVOS = oemVehicleModelService.selectOemByVehicleCode(vehicleModelCodeList);
                // 按照车型编码分组
                Map<String, List<OemVehicleModelVO>> collect = oemVehicleModelVOS.stream().collect(Collectors
                        .groupingBy(OemVehicleModelVO::getOemVehicleModelCode));
                // key为本厂编码，value为主机厂
                for (LoadingProductVO productStockPointVO : productStockPointVOS) {
                    String vehicleModelCode = productStockPointVO.getVehicleModelCode();
                    List<OemVehicleModelVO> oemVehicleModelVOS1 = collect.get(vehicleModelCode);
                    if (CollectionUtils.isNotEmpty(oemVehicleModelVOS1)) {
                        productCodeVehicleCodeMap.put(productStockPointVO.getProductCode(), oemVehicleModelVOS.stream()
                                .map(OemVehicleModelVO::getOemCode).distinct().collect(Collectors.toList()));
                    }
                }
            }
        }
        return LoadingDataHolder.builder().productPartMap(productPartMap).partEnabledMap(partEnabledMap).oemMap(oemMap)
                .submissionVOMap(submissionVOMap).oemStockPointMapVOS(oemStockPointMapVOS).
                productStockPointVOS(productStockPointVOS).productCodeVehicleCodeMap(productCodeVehicleCodeMap)
                .submissionDetailVOMap(submissionDetailVOMap).build();
    }

}