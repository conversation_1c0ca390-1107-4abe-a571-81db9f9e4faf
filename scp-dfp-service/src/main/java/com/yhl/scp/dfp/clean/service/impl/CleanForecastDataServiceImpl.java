package com.yhl.scp.dfp.clean.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.basic.forecast.enums.VersionStatusEnum;
import com.yhl.scp.dfp.clean.convertor.CleanForecastDataConvertor;
import com.yhl.scp.dfp.clean.domain.entity.CleanForecastDataDO;
import com.yhl.scp.dfp.clean.domain.service.CleanForecastDataDomainService;
import com.yhl.scp.dfp.clean.dto.CleanForecastDataDTO;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanForecastDataDao;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanForecastDataDetailDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanForecastDataDetailPO;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanForecastDataPO;
import com.yhl.scp.dfp.clean.service.CleanForecastDataDetailService;
import com.yhl.scp.dfp.clean.service.CleanForecastDataService;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.DemandTypeEnum;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.ShowLevelEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.common.vo.DynamicDataDetailVO;
import com.yhl.scp.dfp.demand.dto.DemandForecastReplayDTO;
import com.yhl.scp.dfp.demand.enums.RowTypeEnum;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandVersionPO;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.*;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.origin.infrastructure.dao.OriginDemandVersionDao;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CleanForecastDataServiceImpl</code>
 * <p>
 * 滚动预测数据应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Service
public class CleanForecastDataServiceImpl extends AbstractService implements CleanForecastDataService {

    public static final String YM_PATTERN = "yyyyMM";

    public static final String PARAM_VERSION_ID = "versionId";

    public static final String PARAM_VERSION_TYPE = "versionType";

    public static final String PARAM_PRODUCT_CODE_LIST = "productCodeList";

    @Resource
    private CleanForecastDataDao cleanForecastDataDao;

    @Resource
    private CleanForecastDataDetailDao cleanForecastDataDetailDao;

    @Resource
    private CleanForecastDataDetailService cleanForecastDataDetailService;

    @Resource
    private DemandVersionDao demandVersionDao;

    @Resource
    private OriginDemandVersionDao originDemandVersionDao;

    @Resource
    private CleanForecastDataDomainService cleanForecastDataDomainService;

    @Resource
    private DemandVersionService demandVersionService;

    @Resource
    private OemService oemService;

    @Resource
    private PartRelationMapService partRelationMapService;

    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    @Resource
    private NewMdsFeign newMdsFeign;

    public static final String YYYY_MM = "yyyyMM";

    @Override
    public BaseResponse<Void> doCreate(CleanForecastDataDTO cleanForecastDataDTO) {
        // 0.数据转换
        CleanForecastDataDO cleanForecastDataDO = CleanForecastDataConvertor.INSTANCE.dto2Do(cleanForecastDataDTO);
        CleanForecastDataPO cleanForecastDataPO = CleanForecastDataConvertor.INSTANCE.dto2Po(cleanForecastDataDTO);
        // 1.数据校验
        cleanForecastDataDomainService.validation(cleanForecastDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(cleanForecastDataPO);
        cleanForecastDataDao.insertWithPrimaryKey(cleanForecastDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(CleanForecastDataDTO cleanForecastDataDTO) {
        // 0.数据转换
        CleanForecastDataDO cleanForecastDataDO = CleanForecastDataConvertor.INSTANCE.dto2Do(cleanForecastDataDTO);
        CleanForecastDataPO cleanForecastDataPO = CleanForecastDataConvertor.INSTANCE.dto2Po(cleanForecastDataDTO);
        // 1.数据校验
        cleanForecastDataDomainService.validation(cleanForecastDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(cleanForecastDataPO);
        cleanForecastDataDao.update(cleanForecastDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CleanForecastDataDTO> list) {
        List<CleanForecastDataPO> newList = CleanForecastDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        cleanForecastDataDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<CleanForecastDataDTO> list) {
        List<CleanForecastDataPO> newList = CleanForecastDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        cleanForecastDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return cleanForecastDataDao.deleteBatch(idList);
        }
        return cleanForecastDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CleanForecastDataVO selectByPrimaryKey(String id) {
        CleanForecastDataPO po = cleanForecastDataDao.selectByPrimaryKey(id);
        return CleanForecastDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_clean_forecast_data")
    public List<CleanForecastDataVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_clean_forecast_data")
    public List<CleanForecastDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CleanForecastDataVO> dataList = cleanForecastDataDao.selectByCondition(sortParam, queryCriteriaParam);
        CleanForecastDataServiceImpl target = SpringBeanUtils.getBean(CleanForecastDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CleanForecastDataVO> selectByParams(Map<String, Object> params) {
        List<CleanForecastDataPO> list = cleanForecastDataDao.selectByParams(params);
        return CleanForecastDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CleanForecastDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CLEAN_FORECAST_DATA.getCode();
    }

    @Override
    public List<CleanForecastDataVO> invocation(List<CleanForecastDataVO> dataList, Map<String, Object> params,
                                                String invocation) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        // 设置明细数据
        List<String> forecastIds = dataList.stream().map(CleanForecastDataVO::getId).collect(Collectors.toList());
        List<CleanForecastDataDetailPO> cleanDemandDataDetailList =
                cleanForecastDataDetailDao.selectByCleanForecastDataIds(forecastIds);
        Map<String, List<CleanForecastDataDetailPO>> detailMap =
                cleanDemandDataDetailList.stream().collect(Collectors.groupingBy(CleanForecastDataDetailPO::getCleanDemandDataId));
        dataList.forEach(data -> {
            List<CleanForecastDataDetailPO> details = detailMap.get(data.getId());
            if (CollectionUtils.isNotEmpty(details)) {
                Map<String, String> detailList = details.stream().collect(Collectors.toMap(
                        x -> DateUtils.dateToString(x.getForecastTime(), YM_PATTERN),
                        x -> x.getForecastQuantity() == null ? "" : x.getForecastQuantity().toString(),
                        (t1, t2) -> t2));
                data.setDetailList(detailList);
            }
        });
        return dataList;
    }

    /**
     * 重新计算是在创建日需求版本之后，如果主机厂重新替换了原始需求，需要重新计算
     */
    @Override
    public void recalculate(String versionCode) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("versionCode", versionCode);
        params.put(PARAM_VERSION_TYPE, VersionTypeEnum.CLEAN_FORECAST.getCode());
        DemandVersionPO demandVersionPO = demandVersionDao.selectVersionInfoByParams(params);
        if (demandVersionPO == null) {
            throw new BusinessException("没有找到需要重新计算的滚动预测版本");
        }
        processDataByVersionId(demandVersionPO.getId());
    }

    // 自动计算，定时任务
    @Override
    public void autoCalculate() {
        // 获取最新原始版本号
        String originVersionId = originDemandVersionDao.selectLatestVersionId();
        // 获取最新版本号
        Map<String, Object> params = new HashMap<>(2);
        params.put("originVersionId", originVersionId);
        params.put(PARAM_VERSION_TYPE, VersionTypeEnum.CLEAN_FORECAST.getDesc());
        DemandVersionPO demandVersionPO = demandVersionDao.selectVersionInfoByParams(params);
        if (demandVersionPO == null) {
            throw new BusinessException("没有找到需要重新计算的滚动预测版本");
        }
        processDataByVersionId(demandVersionPO.getId());
    }

    /**
     * 根据版本号处理数据
     *
     * @param versionId 版本ID
     */
    public void processDataByVersionId(String versionId) {
        log.info("需求预测计算+++++++++++++++++++++");
        // 获取对应版本数据
        List<CleanForecastDataPO> cleanForecastList = cleanForecastDataDao.selectByParams(ImmutableMap
                .of(PARAM_VERSION_ID, versionId));

        Map<String,List<CleanForecastDataPO>> cleanForecastDataDemandMap =
                cleanForecastList.stream().collect(Collectors.groupingBy(CleanForecastDataPO::getDemandCategory));

        Map<String, List<CleanForecastDataDetailPO>> cleanForecastDataDetailPOMapOfDataId = new HashMap<>();


        // 获取对应版本数据
        DemandVersionPO demandVersionPO = demandVersionDao.selectByPrimaryKey(versionId);

        //获取原始需求数据
        String originVersionId = demandVersionPO.getOriginVersionId();
        //查询装车需求提报数据
        List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOList = loadingDemandSubmissionService
                .selectByParams(ImmutableMap.of(PARAM_VERSION_ID, originVersionId));
        if (CollectionUtils.isEmpty(loadingDemandSubmissionVOList)) {
            throw new BusinessException("没有找到对应版本装车需求提报数据");
        }
        List<String> submissionIds = loadingDemandSubmissionVOList.stream().map(LoadingDemandSubmissionVO::getId)
                .collect(Collectors.toList());
        Map<String, Object> detailParams = new HashMap<>();
        detailParams.put("submissionIds", submissionIds);
        detailParams.put("submissionType", GranularityEnum.MONTH.getCode());
        List<LoadingDemandSubmissionDetailVO> submissionDetailVOS = loadingDemandSubmissionDetailService
                .selectByParams(detailParams);
        Map<String, List<LoadingDemandSubmissionDetailVO>> loadingDemandSubmissionDataDetailPOMap = submissionDetailVOS
                .stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId));

        //查询物品信息
        List<String> productCodeList = loadingDemandSubmissionVOList.stream()
                .map(LoadingDemandSubmissionVO::getProductCode).distinct().collect(Collectors.toList());
        Map<String, Object> productParams = new HashMap<>();
        productParams.put(PARAM_PRODUCT_CODE_LIST, productCodeList);
        Map<String, String> productVehicleModelMap = newMdsFeign
                .selectProductVehicleModel(SystemHolder.getScenario(), productParams);
        Map<String,List<LoadingDemandSubmissionVO>> loadingDemandSubmissionDemandMap =
                loadingDemandSubmissionVOList.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionVO::getDemandCategory));
        for (Map.Entry<String, List<LoadingDemandSubmissionVO>> entry : loadingDemandSubmissionDemandMap.entrySet()) {
            String demandCategory = entry.getKey();
            List<LoadingDemandSubmissionVO> list = entry.getValue();
            List<CleanForecastDataPO> cleanForecastDataPODemandList = cleanForecastDataDemandMap.remove(demandCategory);
            Map<String, CleanForecastDataPO> cleanForecastCollectOfJoinKey = new HashMap<>();
            if (CollectionUtils.isNotEmpty(cleanForecastDataPODemandList)) {
                cleanForecastCollectOfJoinKey = cleanForecastDataPODemandList.stream().collect(Collectors
                        .toMap(item -> String.join("#", item.getOemCode(), item.getProductCode(),
                                item.getPartName()), Function.identity(), (k1, k2) -> k2));

                List<String> forecastIds = cleanForecastDataPODemandList.stream().map(CleanForecastDataPO::getId)
                        .collect(Collectors.toList());
                List<CleanForecastDataDetailPO> cleanForecastDataDetailPOList = cleanForecastDataDetailDao
                        .selectByCleanForecastDataIds(forecastIds).stream()
                        .filter(item -> item.getForecastTime() != null)
                        .collect(Collectors.toList());
                cleanForecastDataDetailPOMapOfDataId = cleanForecastDataDetailPOList.stream().collect(Collectors
                        .groupingBy(CleanForecastDataDetailPO::getCleanForecastDataId));
            }
            processDataByDemandCategory(list,productVehicleModelMap,
                    cleanForecastCollectOfJoinKey,cleanForecastDataDetailPOMapOfDataId,
                    loadingDemandSubmissionDataDetailPOMap,versionId,demandVersionPO);
        }

        if (!cleanForecastDataDemandMap.isEmpty()) {
            for (List<CleanForecastDataPO> list : cleanForecastDataDemandMap.values()) {
                cleanForecastDataDao.deleteBatch(list.stream().map(CleanForecastDataPO::getId).collect(Collectors.toList()));
            }
        }
    }

    public void processDataByDemandCategory(List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOList,
                                            Map<String, String> productVehicleModelMap,
                                            Map<String, CleanForecastDataPO> cleanForecastCollectOfJoinKey,
                                            Map<String, List<CleanForecastDataDetailPO>> cleanForecastDataDetailPOMapOfDataId,
                                            Map<String, List<LoadingDemandSubmissionDetailVO>> loadingDemandSubmissionDataDetailPOMap,
                                            String versionId,DemandVersionPO demandVersionPO) {
        List<CleanForecastDataPO> insertCleanForecastDataPOList = new ArrayList<>();
        List<CleanForecastDataDetailPO> insertList = new ArrayList<>();
        List<CleanForecastDataDetailPO> updateList = new ArrayList<>();
        for (LoadingDemandSubmissionVO loadingDemandSubmissionVO : loadingDemandSubmissionVOList) {
            String joinKey = String.join("#", loadingDemandSubmissionVO.getOemCode(),
                    loadingDemandSubmissionVO.getProductCode(), loadingDemandSubmissionVO.getPartNumber());
            CleanForecastDataPO cleanForecastDataPO = cleanForecastCollectOfJoinKey.get(joinKey);
            //获取详情数据
            List<LoadingDemandSubmissionDetailVO> detailVOList = loadingDemandSubmissionDataDetailPOMap
                    .get(loadingDemandSubmissionVO.getId());
            if (null == cleanForecastDataPO) {
                //新增
                String uuid = UUIDUtil.getUUID();
                CleanForecastDataPO insertCleanForecastDataPO = new CleanForecastDataPO();
                insertCleanForecastDataPO.setVersionId(versionId);
                insertCleanForecastDataPO.setDemandCategory(loadingDemandSubmissionVO.getDemandCategory());
                insertCleanForecastDataPO.setOemCode(loadingDemandSubmissionVO.getOemCode());
                insertCleanForecastDataPO.setVehicleModelCode(productVehicleModelMap.get(loadingDemandSubmissionVO
                        .getProductCode()));
                insertCleanForecastDataPO.setProductCode(loadingDemandSubmissionVO.getProductCode());
                insertCleanForecastDataPO.setPartName(loadingDemandSubmissionVO.getPartNumber());
                insertCleanForecastDataPO.setDemandType(DemandTypeEnum.LOADING_DEMAND.getCode());
                insertCleanForecastDataPO.setVersionStatus(demandVersionPO.getVersionStatus());
                insertCleanForecastDataPO.setId(uuid);
                BasePOUtils.insertFiller(insertCleanForecastDataPO);
                insertCleanForecastDataPOList.add(insertCleanForecastDataPO);
                if (CollectionUtils.isNotEmpty(detailVOList)) {
                    for (LoadingDemandSubmissionDetailVO x : detailVOList) {
                        CleanForecastDataDetailPO cleanForecastDataDetailPO = new CleanForecastDataDetailPO();
                        cleanForecastDataDetailPO.setCleanForecastDataId(uuid);
                        cleanForecastDataDetailPO.setForecastTime(DateUtils.stringToDate(x.getDemandTime(), DateUtils.YEAR_MONTH));
                        cleanForecastDataDetailPO.setForecastQuantity(x.getDemandQuantity());
                        cleanForecastDataDetailPO.setId(UUIDUtil.getUUID());
                        BasePOUtils.insertFiller(cleanForecastDataDetailPO);
                        insertList.add(cleanForecastDataDetailPO);
                    }
                }
            } else {
//                //修改,注释原因：当前滚动预测数据预测的需求类型和装车需求提报的需求类型一定是一样的，没必要再次更新数据
//                cleanForecastDataPO.setDemandCategory(loadingDemandSubmissionVO.getDemandCategory());
//                cleanForecastDataDao.update(cleanForecastDataPO);
                //获取详情数据
                List<CleanForecastDataDetailPO> cleanForecastDataDetailPOS = cleanForecastDataDetailPOMapOfDataId
                        .get(cleanForecastDataPO.getId());
                Map<String, CleanForecastDataDetailPO> cleanForecastDataDetailPOMapOfTime = new HashMap<>();
                if (CollectionUtils.isNotEmpty(cleanForecastDataDetailPOS)) {
                    cleanForecastDataDetailPOMapOfTime = cleanForecastDataDetailPOS.stream().collect(Collectors
                            .toMap(item -> DateUtils.dateToString(item.getForecastTime(), DateUtils.YEAR_MONTH),
                                    Function.identity()));
                }

                if (CollectionUtils.isNotEmpty(detailVOList)) {
                    for (LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO : detailVOList) {
                        CleanForecastDataDetailPO cleanForecastDataDetailPO = cleanForecastDataDetailPOMapOfTime
                                .get(loadingDemandSubmissionDetailVO.getDemandTime());
                        if (null == cleanForecastDataDetailPO) {
                            //新增
                            CleanForecastDataDetailPO insertCleanForecastDataDetailPO = new CleanForecastDataDetailPO();
                            insertCleanForecastDataDetailPO.setCleanForecastDataId(cleanForecastDataPO.getId());
                            insertCleanForecastDataDetailPO.setForecastTime(DateUtils
                                    .stringToDate(loadingDemandSubmissionDetailVO.getDemandTime(), DateUtils.YEAR_MONTH));
                            insertCleanForecastDataDetailPO.setForecastQuantity(loadingDemandSubmissionDetailVO.getDemandQuantity());
                            insertCleanForecastDataDetailPO.setId(UUIDUtil.getUUID());
                            BasePOUtils.insertFiller(insertCleanForecastDataDetailPO);
                            insertList.add(insertCleanForecastDataDetailPO);
                        } else {
                            //修改
                            cleanForecastDataDetailPO.setForecastQuantity(loadingDemandSubmissionDetailVO.getDemandQuantity());
                            BasePOUtils.updateFiller(cleanForecastDataDetailPO);
                            updateList.add(cleanForecastDataDetailPO);
                        }
                    }
                }
            }
        }
        // 需要对比数据、判断是否需要删除的
        List<String> deleteCleanForecastDataIdList = new ArrayList<>();
        List<String> deleteCleanForecastDataDetailIdList = new ArrayList<>();

        Map<String, LoadingDemandSubmissionVO> loadingDemandSubmissionVOMap = loadingDemandSubmissionVOList.stream()
                .collect(Collectors.toMap(item -> String
                                .join("#", item.getOemCode(), item.getProductCode(), item.getPartNumber()),
                        Function.identity(), (k1, k2) -> k2));
        for (Map.Entry<String, CleanForecastDataPO> entry : cleanForecastCollectOfJoinKey.entrySet()) {
            if (!loadingDemandSubmissionVOMap.containsKey(entry.getKey())) {
                // 删除滚动需求数据
                deleteCleanForecastDataIdList.add(entry.getValue().getId());

                List<CleanForecastDataDetailPO> cleanForecastDataDetailPOS = cleanForecastDataDetailPOMapOfDataId
                        .get(entry.getValue().getId());
                if (CollectionUtils.isNotEmpty(cleanForecastDataDetailPOS)) {
                    deleteCleanForecastDataDetailIdList.addAll(cleanForecastDataDetailPOS.stream().map(BasePO::getId)
                            .collect(Collectors.toList()));
                }
            }
        }
        String scenario = SystemHolder.getScenario();
        // 删除数据
        CompletableFuture<Void> deleteDataFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            if (CollectionUtils.isNotEmpty(deleteCleanForecastDataIdList)) {
                Lists.partition(deleteCleanForecastDataIdList, 2000).forEach(subList ->
                        cleanForecastDataDao.deleteBatch(subList));
            }
            DynamicDataSourceContextHolder.clearDataSource();
        });
        CompletableFuture<Void> deleteDetailFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            if (CollectionUtils.isNotEmpty(deleteCleanForecastDataDetailIdList)) {
                Lists.partition(deleteCleanForecastDataDetailIdList, 2000).forEach(subList ->
                        cleanForecastDataDetailDao.deleteBatch(subList));
            }
            DynamicDataSourceContextHolder.clearDataSource();
        });
        CompletableFuture<Void> deleteFutures = CompletableFuture.allOf(deleteDataFuture, deleteDetailFuture);
        deleteFutures.join();
        // 插入数据
        CompletableFuture<Void> insertDataFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            if (CollectionUtils.isNotEmpty(insertCleanForecastDataPOList)) {
                Lists.partition(insertCleanForecastDataPOList, 2000).forEach(subList ->
                        cleanForecastDataDao.insertBatchWithPrimaryKey(subList));
            }
            DynamicDataSourceContextHolder.clearDataSource();
        });
        CompletableFuture<Void> insertDetailFuture = CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            if (CollectionUtils.isNotEmpty(insertList)) {
                Lists.partition(insertList, 2000).forEach(subList ->
                        cleanForecastDataDetailDao.insertBatchWithPrimaryKey(subList));
            }
            DynamicDataSourceContextHolder.clearDataSource();
        });
        CompletableFuture<Void> insertFutures = CompletableFuture.allOf(insertDataFuture, insertDetailFuture);
        insertFutures.join();
        // 更新数据
        if (CollectionUtils.isNotEmpty(updateList)) {
            Lists.partition(updateList, 2000).forEach(subList ->
                    cleanForecastDataDetailDao.updateBatch(subList));
        }
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return cleanForecastDataDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<DemandForecastReplayDetailVO> selectDataByVersionList(List<DemandVersionVO> versionList,
                                                                      String showLevel,
                                                                      DemandForecastReplayDTO replayDTO,
                                                                      List<String> vehicleCodeList,
                                                                      List<String> dateListSort) {
        List<DemandForecastReplayDetailVO> replayDetailVOList = new ArrayList<>();
        // 获取滚动数据
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("versionIdList", versionList.stream().map(DemandVersionVO::getId).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(replayDTO.getOemCodeList())
                && ShowLevelEnum.OEM.getCode().equals(replayDTO.getShowLevel())) {
            queryMap.put("oemCode", replayDTO.getOemCodeList());
        }
        if (CollectionUtils.isNotEmpty(replayDTO.getVehicleCodeList())
                && ShowLevelEnum.VEHICLE_MODEL.getCode().equals(replayDTO.getShowLevel())) {
            queryMap.put("vehicleCodeList", vehicleCodeList);
        }
        if (CollectionUtils.isNotEmpty(replayDTO.getProductCodeList())
                && ShowLevelEnum.PART.getCode().equals(replayDTO.getShowLevel())) {
            queryMap.put(PARAM_PRODUCT_CODE_LIST, replayDTO.getProductCodeList());
        }
        List<CleanForecastDataVO> cleanForecastDataVOS = this.selectByParams(queryMap);
        // 滚动预测父id集合
        List<String> parentIdList =
                cleanForecastDataVOS.stream().map(CleanForecastDataVO::getId).collect(Collectors.toList());


        // 获取滚动数据详情
        List<CleanForecastDataDetailVO> forecastDataDetailVOS =
                cleanForecastDataDetailService.selectByParams(ImmutableMap.of("cleanForecastDataIdList", parentIdList));
        // 计划版本id，周期map
        Map<String, String> versionIdMap = versionList.stream().collect(Collectors.toMap(BaseVO::getId,
                DemandVersionVO::getPlanPeriod));
        // 详情根据主表id分组
        Map<String, List<CleanForecastDataDetailVO>> detailGroup =
                forecastDataDetailVOS.stream().collect(Collectors.groupingBy(CleanForecastDataDetailVO::getCleanForecastDataId));
        // 分组统计返回
        for (DemandVersionVO demandVersionVO : versionList) {
            List<CleanForecastDataVO> dataVOList = cleanForecastDataVOS.stream()
                    .filter(k -> k.getVersionId().equals(demandVersionVO.getId())).collect(Collectors.toList());
            // 根据评级+版本分组
            Map<String, List<String>> levelGroup = new HashMap<>();
            if (ShowLevelEnum.OEM.getCode().equals(showLevel)) {
                levelGroup = dataVOList.stream().filter(k -> StringUtils.isNotEmpty(k.getOemCode()))
                        .collect(Collectors.groupingBy(k -> k.getOemCode() + Constants.DELIMITER + k.getVersionId(),
                                Collectors.mapping(CleanForecastDataVO::getId, Collectors.toList())));
            }
            if (ShowLevelEnum.VEHICLE_MODEL.getCode().equals(showLevel)) {
                levelGroup = dataVOList.stream().filter(k -> StringUtils.isNotEmpty(k.getVehicleModelCode()))
                        .collect(Collectors.groupingBy(k -> k.getVehicleModelCode()
                                + Constants.DELIMITER + k.getVersionId(),
                                Collectors.mapping(CleanForecastDataVO::getId, Collectors.toList())));
            }
            if (ShowLevelEnum.PART.getCode().equals(showLevel)) {
                levelGroup = dataVOList.stream().filter(k -> StringUtils.isNotEmpty(k.getProductCode()))
                        .collect(Collectors.groupingBy(k -> k.getProductCode() + Constants.DELIMITER
                                + k.getVersionId(), Collectors.mapping(CleanForecastDataVO::getId, Collectors.toList())));
            }
            for (Map.Entry<String, List<String>> entry : levelGroup.entrySet()) {
                String key = entry.getKey();
                List<String> ids = entry.getValue();
                List<CleanForecastDataDetailVO> dataDetailVOList = new ArrayList<>();
                // 获取对应周期
                String planPeriod = versionIdMap.get(key.split(Constants.DELIMITER)[1]);
                // 收集组内所有详情数据
                for (String id : ids) {
                    if (detailGroup.containsKey(id)) {
                        dataDetailVOList.addAll(detailGroup.get(id));
                    }
                }
                // 根据年月分组统计数量
                Map<String, Integer> monthQuantityMap = dataDetailVOList.stream().collect(Collectors
                        .groupingBy(k -> DateUtils.dateToString(k.getForecastTime(), YM_PATTERN),
                                Collectors.summingInt(CleanForecastDataDetailVO::getForecastQuantity)));
                // 转为动态列信息
                DemandForecastReplayDetailVO detailVO = new DemandForecastReplayDetailVO();
                List<DynamicDataDetailVO> details = new ArrayList<>();
                detailVO.setForecastVersion(key);
                for (String date : dateListSort) {
                    DynamicDataDetailVO dataDetailVO = new DynamicDataDetailVO();
                    dataDetailVO.setId(UUIDUtil.getUUID());
                    dataDetailVO.setSaleDate(date);
                    // 如果在计划周期前，不展示
                    if (planPeriod.compareTo(date) >= 0) {
                        dataDetailVO.setQuantity(null);
                    } else if (monthQuantityMap.containsKey(date)) {
                        dataDetailVO.setQuantity(BigDecimal.valueOf(monthQuantityMap.get(date)));
                    } else {
                        dataDetailVO.setQuantity(BigDecimal.ZERO);
                    }
                    details.add(dataDetailVO);
                }
                detailVO.setDetails(details);
                replayDetailVOList.add(detailVO);
            }
        }
        return replayDetailVOList;
    }

    @Override
    public List<DemandForecastReplayVO> selectForecastReplay(Map<String, Object> basicParams, String showLevel,
                                                             String planPeriod, List<String> planPeriods) {
        Map<String, Object> params = new HashMap<>();
        params.put("versionStatus", VersionStatusEnum.PUBLISHED.getCode());
        params.put(PARAM_VERSION_TYPE, VersionTypeEnum.CLEAN_FORECAST.getCode());
        params.put("planPeriods", planPeriods);
        List<DemandVersionVO> list = demandVersionService.selectMaxVersionByParams(params);
        list = list.stream().filter(p -> p.getVersionType()
                .equals(VersionTypeEnum.CLEAN_FORECAST.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> versionIds = list.stream().map(DemandVersionVO::getId).collect(Collectors.toList());
        Map<String, DemandVersionVO> versionMap = list.stream().collect(Collectors.toMap(DemandVersionVO::getId,
                Function.identity(), (t1, t2) -> t2));
        basicParams.put("versionIdList", versionIds);
        List<CleanForecastDataPO> dataList = cleanForecastDataDao.selectByParams(basicParams);

        List<String> productCodes = dataList.stream().map(CleanForecastDataPO::getProductCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(dataList)) {
            Map<String, String> partMap = partRelationMapService.selectByParams(ImmutableMap
                            .of(PARAM_PRODUCT_CODE_LIST, productCodes)).stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getProductCode())
                            && StringUtils.isNotEmpty(item.getPartName())).collect(Collectors
                            .toMap(PartRelationMapVO::getProductCode, PartRelationMapVO::getPartName,
                                    (t1, t2) -> t2));
            Map<String, String> oemMap = oemService.selectAll().stream()
                    .filter(item -> StringUtils.isNotEmpty(item.getOemCode())
                            && StringUtils.isNotEmpty(item.getOemName())).collect(Collectors
                            .toMap(OemVO::getOemCode, OemVO::getOemName, (t1, t2) -> t2));
            Map<String, List<CleanForecastDataPO>> dataGroup;
            if (ShowLevelEnum.OEM.getCode().equals(showLevel)) {
                dataGroup = dataList.stream().collect(Collectors.groupingBy(oemShowLevelFunction()));
            } else if (ShowLevelEnum.VEHICLE_MODEL.getCode().equals(showLevel)) {
                dataGroup =
                        dataList.stream().collect(Collectors.groupingBy(vehicleShowLevelFunction()));
            } else {
                dataGroup = dataList.stream().collect(Collectors.groupingBy(productShowLevelFunction()));
            }

            List<String> cleanForecastDataIdList =
                    dataList.stream().map(CleanForecastDataPO::getId).collect(Collectors.toList());
            Map<String, List<CleanForecastDataDetailVO>> detailGroup =
                    cleanForecastDataDetailService.selectByParams(ImmutableMap.of("cleanForecastDataIdList",
                            cleanForecastDataIdList)).stream().collect(Collectors
                            .groupingBy(CleanForecastDataDetailVO::getCleanForecastDataId));
            List<DemandForecastReplayVO> replayList = new ArrayList<>();
            for (Map.Entry<String, List<CleanForecastDataPO>> entry : dataGroup.entrySet()) {
                String key = entry.getKey();
                List<CleanForecastDataPO> subList = entry.getValue();
                String[] split = key.split(Constants.DELIMITER);
                String oemCode = split[0];
                String vehicleModelCode = (ShowLevelEnum.VEHICLE_MODEL.getCode().equals(showLevel)
                        || ShowLevelEnum.PART.getCode().equals(showLevel)) ? split[1] : null;
                String productCode = ShowLevelEnum.PART.getCode().equals(showLevel) ? split[2] : null;

                Map<String, Map<String, BigDecimal>> collect = new HashMap<>(16);
                Map<String, List<CleanForecastDataPO>> versionGroup =
                        subList.stream().collect(Collectors.groupingBy(CleanForecastDataPO::getVersionId));
                for (Map.Entry<String, List<CleanForecastDataPO>> versionEntry : versionGroup.entrySet()) {
                    String versionId = versionEntry.getKey();
                    List<String> dataIds =
                            versionEntry.getValue().stream().map(CleanForecastDataPO::getId).collect(Collectors.toList());
                    List<CleanForecastDataDetailVO> tempList = new ArrayList<>();
                    for (String dataId : dataIds) {
                        if (detailGroup.containsKey(dataId)) {
                            tempList.addAll(detailGroup.get(dataId));
                        }
                    }
                    collect.put(versionId, tempList.stream()
                            .filter(Objects::nonNull) // 过滤掉 null 的 item
                            .filter(item -> item.getForecastTime() != null) // 过滤掉 forecastTime 为 null 的 item
                            .filter(item -> item.getForecastQuantity() != null) // 过滤掉 forecastQuantity 为 null 的 item
                            .collect(Collectors.groupingBy(
                                    item -> DateUtils.dateToString(item.getForecastTime(), YYYY_MM),
                                    Collectors.reducing(BigDecimal.ZERO,
                                            item -> new BigDecimal(item.getForecastQuantity()), BigDecimal::add)
                            )));
                }

                DemandForecastReplayVO replayVO = DemandForecastReplayVO.builder().id(UUIDUtil.getUUID())
                        .oemCode(oemCode).oemName(oemMap.getOrDefault(oemCode, ""))
                        .vehicleModelCode(vehicleModelCode).productCode(productCode)
                        .partName(partMap.getOrDefault(productCode, ""))
                        .demandType(VersionTypeEnum.CLEAN_FORECAST.getCode()).build();
                List<ForecastReplayDetailVO> detailList = new ArrayList<>();
                for (Map.Entry<String, Map<String, BigDecimal>> entry1 : collect.entrySet()) {
                    String versionId = entry1.getKey();
                    DemandVersionVO versionVO = versionMap.get(versionId);
                    ForecastReplayDetailVO detail = ForecastReplayDetailVO.builder().versionId(versionId)
                            .versionCode(versionVO.getVersionCode()).planPeriod(versionVO.getPlanPeriod())
                            .rowType(RowTypeEnum.DEMAND_FORECAST).build();
                    Map<String, BigDecimal> dynamicMap = entry1.getValue();
                    List<DynamicCellVO> cellList = new ArrayList<>();
                    for (Map.Entry<String, BigDecimal> entry2 : dynamicMap.entrySet()) {
                        DynamicCellVO cell =
                                DynamicCellVO.builder().planPeriod(entry2.getKey()).quantity(entry2.getValue()).build();
                        cellList.add(cell);
                    }
                    cellList.sort(Comparator.comparing(DynamicCellVO::getPlanPeriod));
                    detail.setCells(cellList);
                    detailList.add(detail);
                }
                detailList.sort(Comparator.comparing(ForecastReplayDetailVO::getVersionCode));
                replayVO.setDetailList(detailList);
                replayList.add(replayVO);
            }
            return replayList;
        }
        return new ArrayList<>();
    }

    private static Function<CleanForecastDataPO, String> productShowLevelFunction() {
        return item -> String.join(Constants.DELIMITER, item.getOemCode(), item.getVehicleModelCode(),
                item.getProductCode());
    }

    private static Function<CleanForecastDataPO, String> vehicleShowLevelFunction() {
        return item -> String.join(Constants.DELIMITER, item.getOemCode(), item.getVehicleModelCode());
    }

    private static Function<CleanForecastDataPO, String> oemShowLevelFunction() {
        return item -> String.join(Constants.DELIMITER, item.getOemCode());
    }

}