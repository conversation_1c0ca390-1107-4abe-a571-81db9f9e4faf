package com.yhl.scp.dfp.newProduct.domain.factory;

import com.yhl.scp.dfp.newProduct.domain.entity.NewProductTrialSubmissionDO;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionDTO;
import com.yhl.scp.dfp.newProduct.infrastructure.dao.NewProductTrialSubmissionDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>NewProductTrialSubmissionFactory</code>
 * <p>
 * 新品试制提报领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Component
public class NewProductTrialSubmissionFactory {

    @Resource
    private NewProductTrialSubmissionDao newProductTrialSubmissionDao;

    NewProductTrialSubmissionDO create(NewProductTrialSubmissionDTO dto) {
        // TODO
        return null;
    }

}
