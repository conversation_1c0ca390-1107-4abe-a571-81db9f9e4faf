package com.yhl.scp.dfp.delivery.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.github.pagehelper.page.PageMethod;
import io.seata.common.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanDetailConvertor;
import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanDetailDO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanDetailDomainService;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDetailDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanDetailDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanDetailPO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanDetailService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanDetailVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.oem.enums.OemBusinessTypeEnum;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>DeliveryPlanDetailServiceImpl</code>
 * <p>
 * 发货计划明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:52:07
 */
@Slf4j
@Service
public class DeliveryPlanDetailServiceImpl extends AbstractService implements DeliveryPlanDetailService {

    @Resource
    private DeliveryPlanDetailDao deliveryPlanDetailDao;

    @Resource
    private DeliveryPlanDetailDomainService deliveryPlanDetailDomainService;
    
    @Resource
    private DeliveryPlanService deliveryPlanService;
    
    @Resource
    private DeliveryPlanVersionService deliveryPlanVersionService;

    @Override
    public BaseResponse<Void> doCreate(DeliveryPlanDetailDTO deliveryPlanDetailDTO) {
        // 0.数据转换
        DeliveryPlanDetailDO deliveryPlanDetailDO = DeliveryPlanDetailConvertor.INSTANCE.dto2Do(deliveryPlanDetailDTO);
        DeliveryPlanDetailPO deliveryPlanDetailPO = DeliveryPlanDetailConvertor.INSTANCE.dto2Po(deliveryPlanDetailDTO);
        // 1.数据校验
        deliveryPlanDetailDomainService.validation(deliveryPlanDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryPlanDetailPO);
        deliveryPlanDetailDao.insert(deliveryPlanDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DeliveryPlanDetailDTO deliveryPlanDetailDTO) {
        // 0.数据转换
        DeliveryPlanDetailDO deliveryPlanDetailDO = DeliveryPlanDetailConvertor.INSTANCE.dto2Do(deliveryPlanDetailDTO);
        DeliveryPlanDetailPO deliveryPlanDetailPO = DeliveryPlanDetailConvertor.INSTANCE.dto2Po(deliveryPlanDetailDTO);
        // 1.数据校验
        deliveryPlanDetailDomainService.validation(deliveryPlanDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryPlanDetailPO);
        deliveryPlanDetailDao.update(deliveryPlanDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryPlanDetailDTO> list) {
        List<DeliveryPlanDetailPO> newList = DeliveryPlanDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryPlanDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryPlanDetailDTO> list) {
        List<DeliveryPlanDetailPO> newList = DeliveryPlanDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryPlanDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return deliveryPlanDetailDao.deleteBatch(idList);
        }
        return deliveryPlanDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DeliveryPlanDetailVO selectByPrimaryKey(String id) {
        DeliveryPlanDetailPO po = deliveryPlanDetailDao.selectByPrimaryKey(id);
        return DeliveryPlanDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_DETAIL")
    public List<DeliveryPlanDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageMethod.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_DETAIL")
    public List<DeliveryPlanDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryPlanDetailVO> dataList = deliveryPlanDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryPlanDetailServiceImpl target = SpringBeanUtils.getBean(DeliveryPlanDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryPlanDetailVO> selectByParams(Map<String, Object> params) {
        if (params == null) {
            return Lists.newArrayList();
        }
        return deliveryPlanDetailDao.selectVOByParams(params);
    }

    @Override
    public List<DeliveryPlanDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DELIVERY_PLAN_DETAIL.getCode();
    }

    @Override
    public List<DeliveryPlanDetailVO> invocation(List<DeliveryPlanDetailVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return deliveryPlanDetailDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<DeliveryPlanDetailVO> selectByPlanDetailIdList(List<String> deliveryPlanIdList) {
        List<DeliveryPlanDetailPO> deliveryPlanDetailPOS = deliveryPlanDetailDao.selectByDeliveryPlanIds(deliveryPlanIdList);
        if (CollectionUtils.isNotEmpty(deliveryPlanDetailPOS)){
            return DeliveryPlanDetailConvertor.INSTANCE.po2Vos(deliveryPlanDetailPOS);
        }
        return Collections.emptyList();
    }

	@Override
	public void doRemoveEditSign(List<String> deliveryPlanDataIds) {
		if (CollectionUtils.isEmpty(deliveryPlanDataIds)) {
			throw new BusinessException("请选则需要去除修改标识数据!");
        }
		deliveryPlanDetailDao.updateSavedSignByDeliveryPlanIds(deliveryPlanDataIds);
	}

	@Override
	public void doUpdateDeliveryPlanDetail(List<DeliveryPlanPublishedDTO> publishedList) {
        // 1.校验发货计划版本是否唯一且最新
        DeliveryPlanVersionVO deliveryPlanVersionVO =
                deliveryPlanVersionService.selectLatestVersionByParams(new HashMap<>());
        String versionId = deliveryPlanVersionVO.getId();
        // 2.获取对应的发货计划数据及明细数据（不存在的直接异常提醒）
        List<String> oemCodes = publishedList.stream().map(DeliveryPlanPublishedDTO::getOemCode)
                .distinct().collect(Collectors.toList());
        List<String> productCodes = publishedList.stream().map(DeliveryPlanPublishedDTO::getProductCode)
                .distinct().collect(Collectors.toList());
        List<DeliveryPlanVO> deliveryPlans = deliveryPlanService.selectByParams(ImmutableMap
                .of("enabled", YesOrNoEnum.YES.getCode(), "versionId", versionId,
                        // "supplyType", OemBusinessTypeEnum.MTS.getCode(),
                        "oemCodes", oemCodes, "productCodes", productCodes));
        Map<String, DeliveryPlanVO> dpUnKeyMap = deliveryPlans.stream().collect(Collectors
                .toMap(e -> String.join(Constants.DELIMITER, e.getDemandCategory(), e.getOemCode(),
                        e.getProductCode()), Function.identity(), (v1, v2) -> v1));
        List<String> errorMsg = new ArrayList<>();
        List<String> deliveryPlanIds = new ArrayList<>();
        for (DeliveryPlanPublishedDTO publishedDTO : publishedList) {
            String dpUnKey = String.join(Constants.DELIMITER, publishedDTO.getDemandCategory(),
                    publishedDTO.getOemCode(), publishedDTO.getProductCode());
            if (!dpUnKeyMap.containsKey(dpUnKey)) {
                errorMsg.add(String.format("主机厂【%s】产品编码【%s】", publishedDTO.getOemCode(), publishedDTO.getProductCode()));
                continue;
            }
            deliveryPlanIds.add(dpUnKeyMap.get(dpUnKey).getId());
        }
        if (CollectionUtils.isNotEmpty(errorMsg)) {
            throw new BusinessException(String.join(",", errorMsg) + "未匹配到对应的发货计划!");
        }
        // 处理明细
        List<DeliveryPlanDetailVO> detailVOs = this.selectByParams(ImmutableMap
                .of("enabled", YesOrNoEnum.YES.getCode(), "deliveryPlanDataIds", deliveryPlanIds));
        Map<String, DeliveryPlanDetailVO> detailVOMap = detailVOs.stream().collect(Collectors
                .toMap(e -> String.join(Constants.DELIMITER, e.getDeliveryPlanDataId(),
                        DateUtils.dateToString(e.getDemandTime())), Function.identity(), (v1, v2) -> v1));

        List<DeliveryPlanDTO> saveDeliveryPlans = new ArrayList<>();
        for (DeliveryPlanPublishedDTO publishedDTO : publishedList) {
            String dpUnKey = String.join(Constants.DELIMITER, publishedDTO.getDemandCategory(),
                    publishedDTO.getOemCode(), publishedDTO.getProductCode());
            DeliveryPlanDTO saveDeliveryPlan = new DeliveryPlanDTO();
            saveDeliveryPlan.setId(dpUnKeyMap.get(dpUnKey).getId());
            saveDeliveryPlan.setDemandCategory(dpUnKeyMap.get(dpUnKey).getDemandCategory());
            saveDeliveryPlan.setOemCode(publishedDTO.getOemCode());
            saveDeliveryPlan.setProductCode(publishedDTO.getProductCode());
            saveDeliveryPlan.setVersionId(versionId);
            saveDeliveryPlans.add(saveDeliveryPlan);
            List<DeliveryPlanDetailDTO> detailList = publishedDTO.getDetailList();
            for (DeliveryPlanDetailDTO detail : detailList) {
                DeliveryPlanDetailVO oldDetail = detailVOMap.get(String.join(Constants.DELIMITER,
                        saveDeliveryPlan.getId(), DateUtils.dateToString(detail.getDemandTime())));
                if (oldDetail == null) {
                    errorMsg.add(DateUtils.dateToString(detail.getDemandTime()));
                    continue;
                }
                detail.setDeliveryPlanDataId(saveDeliveryPlan.getId());
                detail.setId(oldDetail.getId());
            }
            saveDeliveryPlan.setDetailList(detailList);
        }
        if (CollectionUtils.isNotEmpty(errorMsg)) {
            throw new BusinessException("发货时间：" + String.join(",", errorMsg) + "不在当前发货计划时间范围内!");
        }
        // 3.调用发货计划保存接口
        deliveryPlanService.doDeliveryPlanSave(saveDeliveryPlans, YesOrNoEnum.YES.getCode());
	}

}
