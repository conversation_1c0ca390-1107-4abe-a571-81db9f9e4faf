package com.yhl.scp.dfp.loading.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionDetailConvertor;
import com.yhl.scp.dfp.loading.domain.entity.LoadingDemandSubmissionDetailDO;
import com.yhl.scp.dfp.loading.domain.service.LoadingDemandSubmissionDetailDomainService;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailDTO;
import com.yhl.scp.dfp.loading.infrastructure.dao.LoadingDemandSubmissionDetailDao;
import com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionDetailPO;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>LoadingDemandSubmissionDetailServiceImpl</code>
 * <p>
 * 装车需求提报详情应用实现
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 14:30:34
 */
@Slf4j
@Service
public class LoadingDemandSubmissionDetailServiceImpl extends AbstractService implements LoadingDemandSubmissionDetailService {

    @Resource
    private LoadingDemandSubmissionDetailDao loadingDemandSubmissionDetailDao;

    @Resource
    private LoadingDemandSubmissionDetailDomainService loadingDemandSubmissionDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(LoadingDemandSubmissionDetailDTO loadingDemandSubmissionDetailDTO) {
        // 0.数据转换
        LoadingDemandSubmissionDetailDO loadingDemandSubmissionDetailDO = LoadingDemandSubmissionDetailConvertor.INSTANCE.dto2Do(loadingDemandSubmissionDetailDTO);
        LoadingDemandSubmissionDetailPO loadingDemandSubmissionDetailPO = LoadingDemandSubmissionDetailConvertor.INSTANCE.dto2Po(loadingDemandSubmissionDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        loadingDemandSubmissionDetailDomainService.validation(loadingDemandSubmissionDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(loadingDemandSubmissionDetailPO);
        loadingDemandSubmissionDetailDao.insert(loadingDemandSubmissionDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(LoadingDemandSubmissionDetailDTO loadingDemandSubmissionDetailDTO) {
        // 0.数据转换
        LoadingDemandSubmissionDetailDO loadingDemandSubmissionDetailDO = LoadingDemandSubmissionDetailConvertor.INSTANCE.dto2Do(loadingDemandSubmissionDetailDTO);
        LoadingDemandSubmissionDetailPO loadingDemandSubmissionDetailPO = LoadingDemandSubmissionDetailConvertor.INSTANCE.dto2Po(loadingDemandSubmissionDetailDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        loadingDemandSubmissionDetailDomainService.validation(loadingDemandSubmissionDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(loadingDemandSubmissionDetailPO);
        loadingDemandSubmissionDetailDao.update(loadingDemandSubmissionDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<LoadingDemandSubmissionDetailDTO> list) {
        List<LoadingDemandSubmissionDetailPO> newList = LoadingDemandSubmissionDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        loadingDemandSubmissionDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<LoadingDemandSubmissionDetailDTO> list) {
        List<LoadingDemandSubmissionDetailPO> newList = LoadingDemandSubmissionDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        loadingDemandSubmissionDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        int result = 0;
        if (CollectionUtils.isEmpty(idList)) {
            return result;
        }
        List<List<String>> partition = Lists.partition(idList, 100);
        for (List<String> ids : partition) {
            result += loadingDemandSubmissionDetailDao.deleteBatch(ids);
        }
        return result;
    }

    @Override
    public LoadingDemandSubmissionDetailVO selectByPrimaryKey(String id) {
        LoadingDemandSubmissionDetailPO po = loadingDemandSubmissionDetailDao.selectByPrimaryKey(id);
        return LoadingDemandSubmissionDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "LOADING_DEMAND_SUBMISSION_DETAIL")
    public List<LoadingDemandSubmissionDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "LOADING_DEMAND_SUBMISSION_DETAIL")
    public List<LoadingDemandSubmissionDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<LoadingDemandSubmissionDetailVO> dataList = loadingDemandSubmissionDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        LoadingDemandSubmissionDetailServiceImpl target = springBeanUtils.getBean(LoadingDemandSubmissionDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<LoadingDemandSubmissionDetailVO> selectByParams(Map<String, Object> params) {
        List<LoadingDemandSubmissionDetailPO> list = loadingDemandSubmissionDetailDao.selectByParams(params);
        return LoadingDemandSubmissionDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<LoadingDemandSubmissionDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.LOADING_DEMAND_SUBMISSION_DETAIL.getCode();
    }

    @Override
    public List<LoadingDemandSubmissionDetailVO> invocation(List<LoadingDemandSubmissionDetailVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return loadingDemandSubmissionDetailDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    /**
     * @param submissionId
     * @param contentType
     * @return
     */
    @Override
    public int deleteByVersionIdAndSubmissionType(String submissionId, String contentType) {
        return loadingDemandSubmissionDetailDao.deleteByVersionIdAndSubmissionType(submissionId, contentType);
    }

    @Override
    public void updateQuantityById(String id, BigDecimal demandQuantity) {
        loadingDemandSubmissionDetailDao.updateQuantityById(id, demandQuantity);
    }

    /**
     * 根据原始需求版本和主机厂编码获取数据
     *
     * @param originVersionId
     * @param oemCode
     * @return
     */
    @Override
    public List<String> selectByOriginIdAndOemCode(String originVersionId, String oemCode, String contentType) {
        return loadingDemandSubmissionDetailDao.selectByOriginIdAndOemCode(originVersionId, oemCode, contentType);
    }

    @Override
    public int deleteBySubmissionIds(List<String> submissionIds) {
        if (CollectionUtils.isEmpty(submissionIds)) {
            return 0;
        }
        return loadingDemandSubmissionDetailDao.deleteBySubmissionIds(submissionIds);
    }

	@Override
	public List<LoadingDemandSubmissionDetailVO> selectVOByParams(Map<String, Object> param) {
		return loadingDemandSubmissionDetailDao.selectVOByParams(param);
	}
}
