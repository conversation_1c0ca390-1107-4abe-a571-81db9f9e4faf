package com.yhl.scp.dfp.demand.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastOemDao;
import com.yhl.scp.dfp.demand.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.util.BizUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.calendar.service.DfpResourceCalendarService;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanAlgorithmDataDao;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanForecastDataDao;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanForecastDataDetailDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanAlgorithmDataPO;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanForecastDataDetailPO;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanForecastDataPO;
import com.yhl.scp.dfp.common.enums.CategoryEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.common.vo.DynamicDataDetailVO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedMonthVO;
import com.yhl.scp.dfp.demand.dto.DemandForecastReviewDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastReviewUpdateDTO;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastEstablishmentDao;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO;
import com.yhl.scp.dfp.demand.service.ComprehensiveEvaluationCoefficientService;
import com.yhl.scp.dfp.demand.service.DemandForecastReviewService;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.service.ProjectForecastVersionService;
import com.yhl.scp.dfp.forecast.service.HistoryForecastDataService;
import com.yhl.scp.dfp.forecast.vo.HistoryForecastDataVO;
import com.yhl.scp.dfp.market.infrastructure.dao.MarketShareDao;
import com.yhl.scp.dfp.market.infrastructure.dao.MarketShareDetailDao;
import com.yhl.scp.dfp.market.infrastructure.po.MarketShareDetailPO;
import com.yhl.scp.dfp.market.infrastructure.po.MarketSharePO;
import com.yhl.scp.dfp.market.service.MarketInformationService;
import com.yhl.scp.dfp.market.vo.MarketInformationVO;
import com.yhl.scp.dfp.material.infrastructure.dao.PartRiskLevelDao;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemDao;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemProductLineDao;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemProductLineMapDao;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemRiskLevelDao;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemVehicleModelDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemPO;
import com.yhl.scp.dfp.oem.infrastructure.po.OemProductLineMapPO;
import com.yhl.scp.dfp.oem.infrastructure.po.OemProductLinePO;
import com.yhl.scp.dfp.oem.infrastructure.po.OemRiskLevelPO;
import com.yhl.scp.dfp.oem.infrastructure.po.OemVehicleModelPO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemProductLineMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.passenger.infrastructure.dao.PassengerCarSaleDao;
import com.yhl.scp.dfp.passenger.infrastructure.po.PassengerCarSalePO;
import com.yhl.scp.dfp.policy.service.PolicyInformationService;
import com.yhl.scp.dfp.policy.vo.PolicyInformationVO;
import com.yhl.scp.dfp.switchrelation.service.DfpSwitchRelationBetweenProductService;
import com.yhl.scp.dfp.switchrelation.vo.SwitchRelationVO;
import com.yhl.scp.dfp.utils.DfpDateUtils;
import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseRecordDao;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductRiskLevelVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>DemandForecastReviewService</code>
 * <p>
 * DemandForecastReviewService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-13 09:59:26
 */
@Slf4j
@Service
public class DemandForecastReviewServiceImpl implements DemandForecastReviewService {

    public static final String YYYY_MM = "yyyyMM";
    public static final String DEMAND_FORECAST_VERSION_NOT_FOUND = "业务预测版本不存在";
    public static final String DEMAND_FORECAST_DATA_NOT_FOUND = "业务预测数据不存在";

    public static final String PARAM_FORECAST_VERSION_ID = "forecastVersionId";
    public static final String PARAM_OEM_CODE = "oemCode";
    public static final String PARAM_VEHICLE_MODEL_CODE_LIST = "vehicleModelCodeList";
    public static final String PARAM_PRODUCT_CODE_LIST = "productCodeList";
    public static final String PARAM_DEMAND_CATEGORY = "demandCategory";
    @Resource
    private OemService oemService;
    @Resource
    private OemDao oemDao;
    @Resource
    private DemandForecastVersionService demandForecastVersionService;
    @Resource
    private DemandForecastVersionDao demandForecastVersionDao;
    @Resource
    private CleanForecastDataDao cleanForecastDataDao;
    @Resource
    private CleanForecastDataDetailDao cleanForecastDataDetailDao;
    @Resource
    private CleanAlgorithmDataDao cleanAlgorithmDataDao;
    @Resource
    private DemandForecastEstablishmentDao demandForecastEstablishmentDao;
    @Resource
    private OemProductLineDao oemProductLineDao;
    @Resource
    private OemProductLineMapDao oemProductLineMapDao;
    @Resource
    private OemVehicleModelDao oemVehicleModelDao;
    @Resource
    private MarketShareDao marketShareDao;
    @Resource
    private MarketShareDetailDao marketShareDetailDao;
    @Resource
    private OemRiskLevelDao oemRiskLevelDao;
    @Resource
    private PartRiskLevelDao partRiskLevelDao;
    @Resource
    private PassengerCarSaleDao passengerCarSaleDao;
    @Resource
    private WarehouseReleaseRecordDao warehouseReleaseRecordDao;
    @Resource
    private ProjectForecastVersionService projectForecastVersionService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private DfpResourceCalendarService dfpResourceCalendarService;
    @Resource
    private ComprehensiveEvaluationCoefficientService comprehensiveEvaluationCoefficientService;
    @Resource
    private DfpSwitchRelationBetweenProductService dfpSwitchRelationBetweenProductService;
    @Resource
    private HistoryForecastDataService historyForecastDataService;
    @Resource
    private DeliveryPlanPublishedDao deliveryPlanPublishedDao;
    @Resource
    private PolicyInformationService policyInformationService;
    @Resource
    private MarketInformationService marketInformationService;
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;
    @Resource
    private DemandForecastOemDao demandForecastOemDao;

    /**
     * 生成平均值
     *
     * @param detailList 详情数据列表
     */
    private static void getAverageData(List<DynamicDataDetailVO> detailList) {
        if (!detailList.isEmpty()) {
            // 过滤掉 null 值
            List<BigDecimal> saleQuantities =
                    detailList.stream().map(DynamicDataDetailVO::getSaleQuantity).filter(Objects::nonNull)
                            .map(BigDecimal::new).collect(Collectors.toList());

            if (!saleQuantities.isEmpty()) {
                BigDecimal sum = saleQuantities.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal average = sum.divide(new BigDecimal(saleQuantities.size()), 2, RoundingMode.DOWN);
                detailList.add(DynamicDataDetailVO.builder().saleDate("YTD AVG").saleQuantity(average.toString()).build());
            }
        }
    }

    @Override
    public List<LabelValue<String>> queryVersionInfoByDemandType(String demandType) {
        if (StringUtils.equals(demandType, VersionTypeEnum.PROJECT_FORECAST.getCode())) {
            List<ProjectForecastVersionVO> projectForecastVersionVOS =
                    projectForecastVersionService.selectProjectForecastVersion();
            return projectForecastVersionVOS.stream().map(x -> new LabelValue<>(x.getId(), x.getVersionCode()))
                    .collect(Collectors.toList());
        } else {
            List<DemandForecastVersionPO> demandForecastVersionPOS =
                    demandForecastVersionDao.selectDemandForecastVersion(demandType);
            return demandForecastVersionPOS.stream().map(x -> new LabelValue<>(x.getId(), x.getVersionCode()))
                    .collect(Collectors.toList());
        }
    }

    @Override
    public List<LabelValue<String>> queryOemInfoByVersionId(String versionId, String demandType) {
        List<DemandForecastOemVO> list = demandForecastOemDao.selectOemDropdown(versionId,
                        demandType)
                .stream().filter(item -> StringUtils.isNotBlank(item.getOemCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }

        List<LabelValue<String>> result = new ArrayList<>();
        list.stream().collect(TreeMap::new,
                        (k, v) -> k.put(v.getOemCode(),
                                StringUtils.isEmpty(v.getOemName()) ? v.getOemCode() : v.getOemName()), TreeMap::putAll)
                .forEach((k, v) -> result.add(new LabelValue<>(v + "(" + k + ")", (String) k)));
        return result;
    }

    @Override
    public List<LabelValue<String>> queryProductionLineInfoByOemCode(String oemCode, String versionId) {
        List<OemProductLinePO> oemProductLineList = oemProductLineDao.selectByParams(ImmutableMap.of(PARAM_OEM_CODE,
                oemCode));
        if (StringUtils.isNotEmpty(versionId)) {
            // 1.通过版本号查询对应的需求预测编辑数据，然后根据需求预测编制里面的本厂编码获取他所有的车型信息（物料信息有车型信息），
            List<String> vehicleModelCodeList = getVehicleModelCodeList(versionId, oemCode);
            if (CollectionUtils.isEmpty(vehicleModelCodeList)) {
                return new ArrayList<>();
            }
            // 通过车型把所有的产线查出来（通过产线映射关系表mds_oem_product_line_map）获取所有的产线数据
            List<OemProductLineMapPO> oemProductLineMapPOS = oemProductLineMapDao.selectByParams(ImmutableMap.of(
                    PARAM_OEM_CODE, oemCode, PARAM_VEHICLE_MODEL_CODE_LIST, vehicleModelCodeList));
            if (CollectionUtils.isEmpty(oemProductLineMapPOS)) {
                return new ArrayList<>();
            }
            List<String> lineCodeList = oemProductLineMapPOS.stream().map(OemProductLineMapPO::getLineCode)
                    .distinct().collect(Collectors.toList());
            oemProductLineList = oemProductLineList.stream().filter(e -> lineCodeList.contains(e.getLineCode()))
                    .collect(Collectors.toList());
        }
        return oemProductLineList.stream().map(oemProductLinePo -> new LabelValue<>(oemProductLinePo.getLineCode(),
                oemProductLinePo.getLineName())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> queryVehicleInfoByProductionLineCode(String productionLineCode,
                                                                         String versionId,
                                                                         String oemCode) {
        List<String> productionLineCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(productionLineCode)) {
            String[] split = productionLineCode.split(",");
            productionLineCodeList.addAll(Arrays.asList(split));
        }
        List<OemProductLineMapPO> oemProductLineMapPOList =
                oemProductLineMapDao.selectByLineCodes(productionLineCodeList);
        // 根据车型编码去重
        oemProductLineMapPOList =
                oemProductLineMapPOList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(()
                                -> new TreeSet<>(Comparator.comparing(OemProductLineMapPO::getVehicleModelCode))),
                        ArrayList::new));
        if (StringUtils.isNotEmpty(versionId)) {
            // 车型数据：先通过产线在产线映射关系中找到对应的车型，然后获取需求预测编辑数据里所有的本厂编码对应的车型数据，取交集
            List<String> vehicleModelCodeList = getVehicleModelCodeList(versionId, oemCode);
            if (CollectionUtils.isEmpty(vehicleModelCodeList)) {
                return new ArrayList<>();
            }
            // 数据过滤
            oemProductLineMapPOList = oemProductLineMapPOList.stream()
                    .filter(e -> vehicleModelCodeList.contains(e.getVehicleModelCode()))
                    .collect(Collectors.toList());
        }
        return oemProductLineMapPOList.stream()
                .map(oemProductLineMapPo -> new LabelValue<>(oemProductLineMapPo.getLineCode(),
                        oemProductLineMapPo.getVehicleModelCode()))
                .distinct().collect(Collectors.toList());
    }

    /**
     * 通过版本id查询对应的需求预测编辑数据，然后根据需求预测编制里面的本厂编码获取他所有的车型信息
     *
     * @param versionId 版本ID
     * @param oemCode   主机厂代码
     * @return java.util.List<java.lang.String>
     */
    private List<String> getVehicleModelCodeList(String versionId, String oemCode) {
        if (StringUtils.isBlank(versionId)) {
            return new ArrayList<>();
        }
        List<DemandForecastEstablishmentPO> demandForecastEstablishments =
                demandForecastEstablishmentDao.selectByParams(ImmutableMap.of(PARAM_FORECAST_VERSION_ID, versionId,
                        PARAM_OEM_CODE, oemCode));
        if (CollectionUtils.isEmpty(demandForecastEstablishments)) {
            return new ArrayList<>();
        }
        List<String> productCodeList =
                demandForecastEstablishments.stream().map(DemandForecastEstablishmentPO::getProductCode)
                        .distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productList = newMdsFeign.selectByProductCode(SystemHolder.getScenario(),
                productCodeList);
        if (CollectionUtils.isEmpty(productList)) {
            return new ArrayList<>();
        }
        // 获取所有的车型信息
        return productList.stream().map(NewProductStockPointVO::getVehicleModelCode)
                .distinct().collect(Collectors.toList());
    }

    @Override
    public DemandForecastReviewVO queryOemSummaryData(DemandForecastReviewDTO demandForecastReviewDTO) {
        String scenario = SystemHolder.getScenario();
        String oemCode = demandForecastReviewDTO.getOemCode();
        String demandType = demandForecastReviewDTO.getDemandType();

        List<String> timeSeriesList = getTimeSeries();
        // 获取业务预测版本数据
        DemandForecastVersionVO demandForecastVersionVO =
                demandForecastVersionService.selectForecastVersion(demandForecastReviewDTO.getVersionCode());
        if (Objects.isNull(demandForecastVersionVO)) {
            throw new BusinessException(DEMAND_FORECAST_VERSION_NOT_FOUND);
        }

        // 获取业务预测数据
        String versionId = demandForecastVersionVO.getId();
        Map<String, Map<String, BigDecimal>> demandForecastDataMap = getDemandForecastDataMap(versionId, demandType,
                oemCode);
        // 业务预测数据中所有的零件编码
        List<String> productCodeList = new ArrayList<>(demandForecastDataMap.keySet());

        // 获取滚动预测数据
        Map<String, Map<String, BigDecimal>> cleanForecastDataMap =
                getCleanForecastDataMap(demandForecastVersionVO.getRollingVersionId(), demandType, oemCode,
                        productCodeList);
        // 获取算法预测数据
        Map<String, Map<String, BigDecimal>> cleanAlgorithmDataMap =
                getCleanAlgorithmDataMap(demandForecastVersionVO.getAlgorithmVersionId(), oemCode, productCodeList);

        // 查询历史预测数据
        Map<String, Map<String, HistoryForecastDataVO>> historyForecastMap = getHistoryForecastMap(oemCode,
                productCodeList, demandType, timeSeriesList);
        Map<String, Map<String, BigDecimal>> historyCustomerQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getCustomerForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyAlgorithmQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getAlgorithmForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyDemandForecastQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getForecastQuantity);
        // 获取主机厂车型信息
        List<OemVehicleModelPO> vehicleModelPOList = oemVehicleModelDao.selectByParams(ImmutableMap.of(PARAM_OEM_CODE,
                oemCode));
        List<String> vehicleModelCodeList = vehicleModelPOList.stream().map(OemVehicleModelPO::getOemVehicleModelCode)
                .distinct().collect(Collectors.toList());

        // 获取物料主数据
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByVehicleModelCode(scenario,
                vehicleModelCodeList);

        // 获取主机厂下有哪些零件编码
        Map<String, List<String>> oem2ProductAccessPositionMap = assembleAccessPositionMap(newProductStockPointVOS,
                vehicleModelPOList, OemVehicleModelPO::getOemCode);
        // 获取全球汽车销量
        OemPO oemPO = oemDao.selectByOemCode(oemCode);
        // 仓库收发货数据
        List<String> yearMonthRange = getYoyTimeSeries();
        SwitchRelationVO switchRelation =
                dfpSwitchRelationBetweenProductService.getSwitchRelation(Lists.newArrayList(oemCode), productCodeList);
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = warehouseReleaseRecordDao
                .selectMonthVOByItemCodes(Lists.newArrayList(oemCode), allProductCodes, yearMonthRange.get(0),
                        yearMonthRange.get(yearMonthRange.size() - 1));
        warehouseReleaseRecordMonthList.forEach(item -> {
            String itemCode = item.getItemCode();
            item.setVehicleCountFlag(false);
            if (oem2ProductAccessPositionMap.containsKey(oemCode)
                    && oem2ProductAccessPositionMap.getOrDefault(oemCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });
        Map<String, Map<String, BigDecimal>> warehouseReleaseRecordMap =
                getWarehouseReleaseRecordMonthly(warehouseReleaseRecordMonthList, true,
                        WarehouseReleaseRecordMonthVO::getOemCode);
        Map<String, Map<String, BigDecimal>> lastYearWarehouseReleaseRecordByMonthMap =
                getWarehouseReleaseRecordMonthly(warehouseReleaseRecordMonthList, false,
                        WarehouseReleaseRecordMonthVO::getOemCode);
        // 全球汽车销量
        Map<String, Map<String, BigDecimal>> globalCarSaleMonthMap = getGlobalCarSaleMonthMap(vehicleModelCodeList,
                true);
        // 市场占有率
        Map<String, Map<String, BigDecimal>> marketShareMonthMap = getMarketShareMonthMap(oemPO, vehicleModelCodeList);

        // 获取主机厂厂线信息
        List<OemProductLineMapVO> oemProductLineMapVOS = oemProductLineMapDao.selectVOByParams(ImmutableMap
                .of(PARAM_OEM_CODE, oemCode));
        List<String> productLineList =
                oemProductLineMapVOS.stream().map(OemProductLineMapVO::getLineCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productLineList)) {
            throw new BusinessException("主机厂产线映射缺失");
        }
        List<OemProductLinePO> oemProductLinePOList = oemProductLineDao.selectByLineCodes(productLineList);

        // 获取当月以后的数据
        Date firstDayOfMonth = DfpDateUtils.getFirstDayOfMonth(DateUtils.moveMonth(new Date(), 1));
        // 获取装车日历
        List<ResourceCalendarVO> resourceCalendarVOList = dfpResourceCalendarService.getResourceByOem(oemCode).stream()
                .filter(resourceCalendarVO -> resourceCalendarVO.getWorkDay().after(firstDayOfMonth))
                .collect(Collectors.toList());
        Map<String, Map<String, BigDecimal>> workHoursMap = getWorkHoursMap(resourceCalendarVOList,
                oemProductLinePOList);

        DemandForecastReviewVO demandForecastReviewVO = new DemandForecastReviewVO();
        demandForecastReviewVO.setCode(oemCode);
        List<String> productCodes = oem2ProductAccessPositionMap.getOrDefault(oemCode, new ArrayList<>());
        Map<String, BigDecimal> productCleanForecastDataMap = new HashMap<>(16);
        Map<String, BigDecimal> productCleanAlgorithmDataMap = new HashMap<>(16);
        Map<String, BigDecimal> productDemandForecastEstablishmentMap = new HashMap<>(16);
        Map<String, BigDecimal> warehouseDataMap = new HashMap<>(16);
        Map<String, BigDecimal> lastYearWarehouseDataMap = new HashMap<>(16);
        Map<String, BigDecimal> futurePlanQuantityMap;
        if (CollectionUtils.isNotEmpty(productCodes)) {
            List<String> needCalculateProductCodes =
                    productCodeList.stream().filter(productCodes::contains).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(needCalculateProductCodes)) {
                // 获取零件编码的所需数据
                productCleanForecastDataMap = getSummaryData(needCalculateProductCodes, cleanForecastDataMap,
                        historyCustomerQtyMap);
                productCleanAlgorithmDataMap = getSummaryData(needCalculateProductCodes, cleanAlgorithmDataMap,
                        historyAlgorithmQtyMap);
                productDemandForecastEstablishmentMap = getSummaryData(needCalculateProductCodes,
                        demandForecastDataMap, historyDemandForecastQtyMap);
                warehouseDataMap = warehouseReleaseRecordMap.getOrDefault(oemCode, new HashMap<>());
                lastYearWarehouseDataMap = lastYearWarehouseReleaseRecordByMonthMap.getOrDefault(oemCode,
                        new HashMap<>());
            }
        }
        // 按照厂线汇总
        futurePlanQuantityMap = getSummaryData(productLineList, workHoursMap, null);

        Map<String, BigDecimal> globalCarSaleMap = getSummaryData(vehicleModelCodeList, globalCarSaleMonthMap, null);
        Map<String, BigDecimal> marketShareMap = getSummaryData(vehicleModelCodeList, marketShareMonthMap, null);
        // 获取主机厂下所有零件的折线图数据
        getDemandForecastReviewTrendVO(productCleanForecastDataMap, productCleanAlgorithmDataMap,
                productDemandForecastEstablishmentMap, lastYearWarehouseDataMap, globalCarSaleMap, demandForecastReviewVO,
                timeSeriesList);
        assembleDemandForecastReviewCurrentVO(globalCarSaleMap, marketShareMap, warehouseDataMap,
                lastYearWarehouseDataMap, demandForecastReviewVO);
        List<OemRiskLevelPO> oemRiskLevelPOS = oemRiskLevelDao.selectByParams(ImmutableMap
                .of(PARAM_OEM_CODE, oemCode, "estimateTime", DateUtils.dateToString(new Date(), YYYY_MM)));
        demandForecastReviewVO.getDemandForecastReviewCurrentVO().setOemRiskLevel(CollectionUtils.isEmpty(oemRiskLevelPOS)
                ? "低" : oemRiskLevelPOS.get(0).getRiskLevel());
        // 获取主机厂下所有零件的预测数据
        getDemandForecastReviewForecastVO(productCleanForecastDataMap, productCleanAlgorithmDataMap,
                productDemandForecastEstablishmentMap, lastYearWarehouseDataMap, demandForecastReviewVO);

        // 获取厂线未来产量
        BigDecimal futurePlanQuantity = BigDecimal.ZERO;
        if (MapUtils.isNotEmpty(futurePlanQuantityMap)) {
            futurePlanQuantity = futurePlanQuantityMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        demandForecastReviewVO.getDemandForecastReviewForecastVO().setFuturePlanQuantity(futurePlanQuantity.intValue());
        return demandForecastReviewVO;
    }

    @Override
    public List<DemandForecastReviewVO> queryProductionLineSummaryData(DemandForecastReviewDTO demandForecastReviewDTO) {
        String scenario = SystemHolder.getScenario();
        String oemCode = demandForecastReviewDTO.getOemCode();
        String demandType = demandForecastReviewDTO.getDemandType();
        List<String> productionLineCodeList = demandForecastReviewDTO.getProductionLineCodeList();

        List<String> timeSeriesList = getTimeSeries();
        // 获取业务预测版本数据
        DemandForecastVersionVO demandForecastVersionVO =
                demandForecastVersionService.selectForecastVersion(demandForecastReviewDTO.getVersionCode());
        if (Objects.isNull(demandForecastVersionVO)) {
            throw new BusinessException(DEMAND_FORECAST_VERSION_NOT_FOUND);
        }

        // 获取业务预测数据
        String versionId = demandForecastVersionVO.getId();
        Map<String, Map<String, BigDecimal>> demandForecastDataMap =
                getDemandForecastDataMap(versionId, demandType, oemCode);
        // 业务预测数据中所有的零件编码
        List<String> productCodeList = new ArrayList<>(demandForecastDataMap.keySet());

        // 获取滚动预测数据
        Map<String, Map<String, BigDecimal>> cleanForecastDataMap =
                getCleanForecastDataMap(demandForecastVersionVO.getRollingVersionId(), demandType, oemCode,
                        productCodeList);
        // 获取算法预测数据
        Map<String, Map<String, BigDecimal>> cleanAlgorithmDataMap =
                getCleanAlgorithmDataMap(demandForecastVersionVO.getAlgorithmVersionId(), oemCode, productCodeList);

        // 查询历史预测数据
        Map<String, Map<String, HistoryForecastDataVO>> historyForecastMap = getHistoryForecastMap(oemCode,
                productCodeList, demandType, timeSeriesList);
        Map<String, Map<String, BigDecimal>> historyCustomerQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getCustomerForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyAlgorithmQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getAlgorithmForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyDemandForecastQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getForecastQuantity);
        // 获取产线映射
        List<OemProductLineMapPO> oemProductLineMapPOS = oemProductLineMapDao.selectByLineCodes(productionLineCodeList);
        List<String> vehicleModelCodeList = oemProductLineMapPOS.stream().map(OemProductLineMapPO::getVehicleModelCode)
                .distinct().collect(Collectors.toList());

        // 获取物料主数据
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByVehicleModelCode(scenario,
                vehicleModelCodeList);
        // 获取主机厂车型信息
        List<OemVehicleModelPO> vehicleModelPOList =
                oemVehicleModelDao.selectByVehicleModelCode(oemCode, vehicleModelCodeList);

        // 获取车型下有哪些零件编码
        Map<String, List<String>> vehicle2ProductAccessPositionMap = assembleAccessPositionMap(newProductStockPointVOS,
                vehicleModelPOList, OemVehicleModelPO::getOemVehicleModelCode);
        // 获取产线下有哪些零件编码
        Map<String, List<String>> productionLine2ProductMap =
                assembleProductionLine2ProductMap(vehicle2ProductAccessPositionMap, oemProductLineMapPOS);

        // 获取全球汽车销量
        OemPO oemPO = oemDao.selectByOemCode(oemCode);
        // 仓库收发货数据
        List<String> yearMonthRange = getYoyTimeSeries();
        SwitchRelationVO switchRelation =
                dfpSwitchRelationBetweenProductService.getSwitchRelation(Lists.newArrayList(oemCode), productCodeList);
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = warehouseReleaseRecordDao
                .selectMonthVOByItemCodes(Lists.newArrayList(oemCode), allProductCodes, yearMonthRange.get(0),
                        yearMonthRange.get(yearMonthRange.size() - 1));
        // 汇总产线销量数据
        Map<String, Object> productParams = new HashMap<>();
        productParams.put(PARAM_PRODUCT_CODE_LIST, productCodeList);
        Map<String, String> productVehicleModelMap = newMdsFeign.selectProductVehicleModel(SystemHolder.getScenario(),
                productParams);
        Map<String, String> vehicleModelLineMap = oemProductLineMapDao.selectByParams(ImmutableMap
                        .of(PARAM_OEM_CODE, oemCode))
                .stream().collect(Collectors.toMap(OemProductLineMapPO::getVehicleModelCode,
                        OemProductLineMapPO::getLineCode, (t1, t2) -> t2));
        warehouseReleaseRecordMonthList.forEach(item -> {
            String itemCode = item.getItemCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
            String productionLineCode = vehicleModelLineMap.getOrDefault(vehicleModelCode, vehicleModelCode);
            item.setProductionLineCode(productionLineCode);
            item.setVehicleCountFlag(false);
            if (productionLine2ProductMap.containsKey(productionLineCode)
                    && productionLine2ProductMap.getOrDefault(productionLineCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });

        Map<String, Map<String, BigDecimal>> warehouseReleaseRecordMap =
                getWarehouseReleaseRecordMonthly(warehouseReleaseRecordMonthList, true,
                        WarehouseReleaseRecordMonthVO::getProductionLineCode);
        Map<String, Map<String, BigDecimal>> lastYearWarehouseReleaseRecordByMonthMap =
                getWarehouseReleaseRecordMonthly(warehouseReleaseRecordMonthList, false,
                        WarehouseReleaseRecordMonthVO::getProductionLineCode);
        // 全球汽车销量
        Map<String, Map<String, BigDecimal>> globalCarSaleMonthMap = getGlobalCarSaleMonthMap(vehicleModelCodeList,
                true);
        // 市场占有率
        Map<String, Map<String, BigDecimal>> marketShareMonthMap = getMarketShareMonthMap(oemPO, vehicleModelCodeList);

        // 获取产线信息
        List<OemProductLinePO> oemProductLinePOList = oemProductLineDao.selectByLineCodes(productionLineCodeList);
        // 获取装车日历
        List<ResourceCalendarVO> resourceCalendarVOList =
                dfpResourceCalendarService.selectResourceByStandardResourceIds(productionLineCodeList);
        // 获取当月以后的数据
        Date firstDayOfMonth = DfpDateUtils.getFirstDayOfMonth(DateUtils.moveMonth(new Date(), 1));
        resourceCalendarVOList = resourceCalendarVOList.stream()
                .filter(resourceCalendarVO -> resourceCalendarVO.getWorkDay().after(firstDayOfMonth))
                .collect(Collectors.toList());
        Map<String, Map<String, BigDecimal>> workHoursMap = getWorkHoursMap(resourceCalendarVOList,
                oemProductLinePOList);

        List<DemandForecastReviewVO> demandForecastReviewVOList = Lists.newArrayList();
        for (String productionLineCode : productionLineCodeList) {
            List<String> productCodes = productionLine2ProductMap.getOrDefault(productionLineCode, new ArrayList<>());
            if (CollectionUtils.isEmpty(productCodes)) {
                continue;
            }
            DemandForecastReviewVO demandForecastReviewVO = new DemandForecastReviewVO();
            demandForecastReviewVO.setCode(productionLineCode);
            List<String> needCalculateProductCodes =
                    productCodeList.stream().filter(productCodes::contains).collect(Collectors.toList());
            // 获取零件编码的所需数据
            Map<String, BigDecimal> productCleanForecastDataMap = getSummaryData(needCalculateProductCodes,
                    cleanForecastDataMap, historyCustomerQtyMap);
            Map<String, BigDecimal> productCleanAlgorithmDataMap = getSummaryData(needCalculateProductCodes,
                    cleanAlgorithmDataMap, historyAlgorithmQtyMap);
            Map<String, BigDecimal> productDemandForecastEstablishmentMap = getSummaryData(needCalculateProductCodes,
                    demandForecastDataMap, historyDemandForecastQtyMap);
            Map<String, BigDecimal> warehouseDataMap = warehouseReleaseRecordMap.getOrDefault(productionLineCode,
                    new HashMap<>());
            Map<String, BigDecimal> lastYearWarehouseDataMap = lastYearWarehouseReleaseRecordByMonthMap
                    .getOrDefault(productionLineCode, new HashMap<>());
            Map<String, BigDecimal> globalCarSaleMap = getSummaryData(vehicleModelCodeList, globalCarSaleMonthMap,
                    null);
            Map<String, BigDecimal> marketShareMap = getSummaryData(vehicleModelCodeList, marketShareMonthMap, null);
            Map<String, BigDecimal> futurePlanQuantityMap = getSummaryData(Lists.newArrayList(productionLineCode),
                    workHoursMap, null);

            // 获取主机厂下所有零件的折线图数据
            getDemandForecastReviewTrendVO(productCleanForecastDataMap, productCleanAlgorithmDataMap,
                    productDemandForecastEstablishmentMap, lastYearWarehouseDataMap, globalCarSaleMap,
                    demandForecastReviewVO, timeSeriesList);

            assembleDemandForecastReviewCurrentVO(globalCarSaleMap, marketShareMap, warehouseDataMap,
                    lastYearWarehouseDataMap, demandForecastReviewVO);
            // 获取主机厂下所有零件的预测数据
            getDemandForecastReviewForecastVO(productCleanForecastDataMap, productCleanAlgorithmDataMap,
                    productDemandForecastEstablishmentMap, lastYearWarehouseDataMap, demandForecastReviewVO);
            // 获取厂线未来产量
            BigDecimal futurePlanQuantity = BigDecimal.ZERO;
            if (MapUtils.isNotEmpty(futurePlanQuantityMap)) {
                futurePlanQuantity = futurePlanQuantityMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            demandForecastReviewVO.getDemandForecastReviewForecastVO().setFuturePlanQuantity(futurePlanQuantity.intValue());
            demandForecastReviewVOList.add(demandForecastReviewVO);
        }
        return demandForecastReviewVOList;
    }

    /**
     * 获取预测数据
     *
     * @param productCleanForecastDataDetailPOMap     产品滚动预测数据MAP
     * @param productCleanAlgorithmDataPOMap          产品算法预测数据MAP
     * @param productDemandForecastEstablishmentPOMap 产品业务预测数据MAP
     * @param lastYearWarehouseDataMap                上一年数据
     * @param demandForecastReviewVO                  业务预测评审VO
     */
    private void getDemandForecastReviewForecastVO(Map<String, BigDecimal> productCleanForecastDataDetailPOMap,
                                                   Map<String, BigDecimal> productCleanAlgorithmDataPOMap,
                                                   Map<String, BigDecimal> productDemandForecastEstablishmentPOMap,
                                                   Map<String, BigDecimal> lastYearWarehouseDataMap,
                                                   DemandForecastReviewVO demandForecastReviewVO) {
        DemandForecastReviewForecastVO demandForecastReviewForecastVO = new DemandForecastReviewForecastVO();
        // 获取当年预测值

        // 客户预测发货量
        BigDecimal customerForecast = productCleanForecastDataDetailPOMap.values().stream().reduce(BigDecimal.ZERO,
                BigDecimal::add);
        demandForecastReviewForecastVO.setCustomerForecastQuantity(customerForecast.intValue());

        // 算法预测发货量
        BigDecimal algorithmForecast = productCleanAlgorithmDataPOMap.values().stream().reduce(BigDecimal.ZERO,
                BigDecimal::add);
        demandForecastReviewForecastVO.setAlgorithmForecastQuantity(algorithmForecast.intValue());

        // 业务预测发货量
        BigDecimal demandForecast = productDemandForecastEstablishmentPOMap.values().stream().reduce(BigDecimal.ZERO,
                BigDecimal::add);
        demandForecastReviewForecastVO.setDemandForecastQuantity(demandForecast.intValue());

        BigDecimal lastYearWarehouseData = lastYearWarehouseDataMap.values().stream().reduce(BigDecimal.ZERO,
                BigDecimal::add);
        // 客户预测未来呈
        // 当年客户预测平均值
        int averageCustomerForecast =
                customerForecast.divide(new BigDecimal(productCleanForecastDataDetailPOMap.isEmpty() ? 1 :
                        productCleanForecastDataDetailPOMap.size()), RoundingMode.CEILING).intValue();
        // 去年发货月平均值
        int lastYearAverageWarehouse =
                lastYearWarehouseData.divide(new BigDecimal(lastYearWarehouseDataMap.isEmpty() ? 1 :
                        lastYearWarehouseDataMap.size()), RoundingMode.CEILING).intValue();
        // 客户/算法预测未来呈取客户/算法预测发货量与去年同期发货总量的比较，规则与“较去年总量”一致；
        if (averageCustomerForecast > lastYearAverageWarehouse * 1.05)
            demandForecastReviewForecastVO.setCustomerForecastFuture("上升");
        if (averageCustomerForecast <= lastYearAverageWarehouse * 1.05
                && averageCustomerForecast >= lastYearAverageWarehouse * 0.95)
            demandForecastReviewForecastVO.setCustomerForecastFuture("平稳");
        if (averageCustomerForecast < lastYearAverageWarehouse * 0.95)
            demandForecastReviewForecastVO.setCustomerForecastFuture("下降");
        // 算法预测未来呈
        // 当年客户预测平均值
        int averageAlgorithmForecast =
                algorithmForecast.divide(new BigDecimal(productCleanAlgorithmDataPOMap.isEmpty() ? 1 :
                        productCleanAlgorithmDataPOMap.size()), RoundingMode.CEILING).intValue();
        // 客户/算法预测未来呈取客户/算法预测发货量与去年同期发货总量的比较，规则与“较去年总量”一致；
        if (averageAlgorithmForecast > lastYearAverageWarehouse * 1.05)
            demandForecastReviewForecastVO.setAlgorithmForecastFuture("上升");
        if (averageAlgorithmForecast <= lastYearAverageWarehouse * 1.05
                && averageAlgorithmForecast >= lastYearAverageWarehouse * 0.95)
            demandForecastReviewForecastVO.setAlgorithmForecastFuture("平稳");
        if (averageAlgorithmForecast < lastYearAverageWarehouse * 0.95)
            demandForecastReviewForecastVO.setAlgorithmForecastFuture("下降");
        // 最佳预测
        BigDecimal averageCustomerPrecision = calculationPrecision(customerForecast, lastYearWarehouseData);
        BigDecimal averageAlgorithmPrecision = calculationPrecision(algorithmForecast, lastYearWarehouseData);
        BigDecimal averageForecastPrecision = calculationPrecision(demandForecast, lastYearWarehouseData);
        Map<String, BigDecimal> precisionMap = new HashMap<>(16);
        precisionMap.put(CategoryEnum.CUSTOMER_FORECAST.getDesc(), averageCustomerPrecision);
        precisionMap.put(CategoryEnum.ALGORITHMIC_FORECAST.getDesc(), averageAlgorithmPrecision);
        precisionMap.put(CategoryEnum.DEMAND_FORECAST.getDesc(), averageForecastPrecision);
        Optional<String> optimalPrediction =
                precisionMap.entrySet().stream().max(Map.Entry.comparingByValue()).map(Map.Entry::getKey);
        optimalPrediction.ifPresent(demandForecastReviewForecastVO::setBestPrediction);
        demandForecastReviewVO.setDemandForecastReviewForecastVO(demandForecastReviewForecastVO);
    }

    @Override
    public List<DemandForecastReviewVO> queryVehicleSummaryData(DemandForecastReviewDTO demandForecastReviewDTO) {
        String scenario = SystemHolder.getScenario();
        String demandType = demandForecastReviewDTO.getDemandType();
        String oemCode = demandForecastReviewDTO.getOemCode();
        List<String> vehicleModelCodeList = demandForecastReviewDTO.getVehicleModelCodeList();

        List<String> timeSeriesList = getTimeSeries();
        // 获取业务预测版本数据
        DemandForecastVersionVO demandForecastVersionVO =
                demandForecastVersionService.selectForecastVersion(demandForecastReviewDTO.getVersionCode());
        if (Objects.isNull(demandForecastVersionVO)) {
            throw new BusinessException(DEMAND_FORECAST_VERSION_NOT_FOUND);
        }


        // 获取业务预测数据
        String versionId = demandForecastVersionVO.getId();
        Map<String, Map<String, BigDecimal>> demandForecastDataMap =
                getDemandForecastDataMap(versionId, demandType, oemCode);
        // 业务预测数据中所有的零件编码
        List<String> productCodeList = new ArrayList<>(demandForecastDataMap.keySet());

        // 获取滚动预测数据
        Map<String, Map<String, BigDecimal>> cleanForecastDataMap =
                getCleanForecastDataMap(demandForecastVersionVO.getRollingVersionId(), demandType, oemCode,
                        productCodeList);
        // 获取算法预测数据
        Map<String, Map<String, BigDecimal>> cleanAlgorithmDataMap =
                getCleanAlgorithmDataMap(demandForecastVersionVO.getAlgorithmVersionId(), oemCode, productCodeList);

        // 查询历史预测数据
        Map<String, Map<String, HistoryForecastDataVO>> historyForecastMap = getHistoryForecastMap(oemCode,
                productCodeList, demandType, timeSeriesList);
        Map<String, Map<String, BigDecimal>> historyCustomerQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getCustomerForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyAlgorithmQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getAlgorithmForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyDemandForecastQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getForecastQuantity);
        // 获取物料主数据
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByVehicleModelCode(scenario,
                vehicleModelCodeList);
        // 获取主机厂车型信息
        List<OemVehicleModelPO> vehicleModelPOList = oemVehicleModelDao.selectByVehicleModelCode(oemCode,
                vehicleModelCodeList);

        // 获取车型下有哪些零件编码
        Map<String, List<String>> vehicle2ProductAccessPositionMap = assembleAccessPositionMap(newProductStockPointVOS,
                vehicleModelPOList, OemVehicleModelPO::getOemVehicleModelCode);
        // 获取全球汽车销量
        OemPO oemPO = oemDao.selectByOemCode(oemCode);
        // 仓库收发货数据
        List<String> yearMonthRange = getYoyTimeSeries();
        SwitchRelationVO switchRelation =
                dfpSwitchRelationBetweenProductService.getSwitchRelation(Lists.newArrayList(oemCode), productCodeList);
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = warehouseReleaseRecordDao
                .selectMonthVOByItemCodes(Lists.newArrayList(oemCode), allProductCodes, yearMonthRange.get(0),
                        yearMonthRange.get(yearMonthRange.size() - 1));

        // 汇总车型下的零件编码数据
        Map<String, Object> productParams = new HashMap<>();
        productParams.put(PARAM_PRODUCT_CODE_LIST, productCodeList);
        Map<String, String> productVehicleModelMap = newMdsFeign.selectProductVehicleModel(SystemHolder.getScenario(),
                productParams);
        warehouseReleaseRecordMonthList.forEach(item -> {
            String itemCode = item.getItemCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
            item.setVehicleModelCode(vehicleModelCode);
            item.setVehicleCountFlag(false);
            if (vehicle2ProductAccessPositionMap.containsKey(vehicleModelCode)
                    && vehicle2ProductAccessPositionMap.getOrDefault(vehicleModelCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });
        Map<String, Map<String, BigDecimal>> warehouseReleaseRecordMap =
                getWarehouseReleaseRecordMonthly(warehouseReleaseRecordMonthList, true,
                        WarehouseReleaseRecordMonthVO::getVehicleModelCode);
        Map<String, Map<String, BigDecimal>> lastYearWarehouseReleaseRecordByMonthMap =
                getWarehouseReleaseRecordMonthly(warehouseReleaseRecordMonthList, false,
                        WarehouseReleaseRecordMonthVO::getVehicleModelCode);
        // 全球汽车销量
        Map<String, Map<String, BigDecimal>> globalCarSaleMonthMap = getGlobalCarSaleMonthMap(vehicleModelCodeList,
                true);
        // 市场占有率
        Map<String, Map<String, BigDecimal>> marketShareMonthMap = getMarketShareMonthMap(oemPO,
                vehicleModelCodeList);

        List<DemandForecastReviewVO> demandForecastReviewVOList = Lists.newArrayList();
        for (String vehicleModelCode : vehicleModelCodeList) {
            List<String> productCodes = (null == vehicle2ProductAccessPositionMap) ? new ArrayList<>() :
                    vehicle2ProductAccessPositionMap.getOrDefault(vehicleModelCode, new ArrayList<>());
            if (CollectionUtils.isEmpty(productCodes)) {
                continue;
            }
            DemandForecastReviewVO demandForecastReviewVO = new DemandForecastReviewVO();
            demandForecastReviewVO.setCode(vehicleModelCode);
            List<String> needCalculateProductCodes =
                    productCodeList.stream().filter(productCodes::contains).collect(Collectors.toList());
            // 获取零件编码的所需数据
            Map<String, BigDecimal> productCleanForecastDataMap = getSummaryData(needCalculateProductCodes,
                    cleanForecastDataMap, historyCustomerQtyMap);
            Map<String, BigDecimal> productCleanAlgorithmDataMap = getSummaryData(needCalculateProductCodes,
                    cleanAlgorithmDataMap, historyAlgorithmQtyMap);
            Map<String, BigDecimal> productDemandForecastEstablishmentMap = getSummaryData(needCalculateProductCodes,
                    demandForecastDataMap, historyDemandForecastQtyMap);
            Map<String, BigDecimal> warehouseDataMap = warehouseReleaseRecordMap.getOrDefault(vehicleModelCode,
                    new HashMap<>());
            Map<String, BigDecimal> lastYearWarehouseDataMap = lastYearWarehouseReleaseRecordByMonthMap
                    .getOrDefault(vehicleModelCode, new HashMap<>());
            Map<String, BigDecimal> globalCarSaleMap = globalCarSaleMonthMap.getOrDefault(vehicleModelCode,
                    new HashMap<>());
            Map<String, BigDecimal> marketShareMap = marketShareMonthMap.getOrDefault(vehicleModelCode,
                    new HashMap<>());

            // 获取主机厂下所有零件的折线图数据
            getDemandForecastReviewTrendVO(productCleanForecastDataMap, productCleanAlgorithmDataMap,
                    productDemandForecastEstablishmentMap, lastYearWarehouseDataMap, globalCarSaleMap,
                    demandForecastReviewVO, timeSeriesList);

            assembleDemandForecastReviewCurrentVO(globalCarSaleMap, marketShareMap, warehouseDataMap,
                    lastYearWarehouseDataMap, demandForecastReviewVO);
            // TODO 待确认车型生命周期具体值
            Optional<OemVehicleModelPO> oemVehicleModelOptional = vehicleModelPOList.stream()
                    .filter(x -> vehicleModelCode.equals(x.getOemVehicleModelCode())).findAny();
            oemVehicleModelOptional.ifPresent(x -> demandForecastReviewVO
                    .getDemandForecastReviewCurrentVO().setVehicleLifeCycle(x.getEop() == null ? "SOP" : "EOP"));
            // 获取主机厂下所有零件的预测数据
            getDemandForecastReviewForecastVO(productCleanForecastDataMap, productCleanAlgorithmDataMap,
                    productDemandForecastEstablishmentMap, lastYearWarehouseDataMap, demandForecastReviewVO);
            demandForecastReviewVOList.add(demandForecastReviewVO);
        }
        return demandForecastReviewVOList;
    }

    /**
     * 获取现状数据
     *
     * @param globalCarSaleMap                  乘用车销量MAP
     * @param marketShareMap                    市场占有率MAP
     * @param warehouseReleaseRecordMap         仓库发货数据MAP
     * @param lastYearWarehouseReleaseRecordMap 上一年发货数据MAP
     * @param demandForecastReviewVO            业务预测评审VO
     */
    private void assembleDemandForecastReviewCurrentVO(Map<String, BigDecimal> globalCarSaleMap,
                                                       Map<String, BigDecimal> marketShareMap,
                                                       Map<String, BigDecimal> warehouseReleaseRecordMap,
                                                       Map<String, BigDecimal> lastYearWarehouseReleaseRecordMap,
                                                       DemandForecastReviewVO demandForecastReviewVO) {
        DemandForecastReviewCurrentVO demandForecastReviewCurrentVO = new DemandForecastReviewCurrentVO();

        // 全年累计销量取本年度所有月实际销量(实际销量 = 销量* 市场占有率)数据总和
        BigDecimal totalSaleQuantity = (null == globalCarSaleMap || null == marketShareMap) ? BigDecimal.ZERO :
                globalCarSaleMap.entrySet().stream().filter(x -> marketShareMap
                                .containsKey(x.getKey())).map(x -> x.getValue()
                                .multiply(marketShareMap.get(x.getKey()).divide(new BigDecimal("100"), 2,
                                        RoundingMode.HALF_UP)))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

        demandForecastReviewCurrentVO.setTotalSaleQuantity(totalSaleQuantity.intValue());

        // 全年累计发货量
        BigDecimal totalDeliveryQuantity = warehouseReleaseRecordMap.values().stream().reduce(BigDecimal.ZERO,
                BigDecimal::add);
        demandForecastReviewCurrentVO.setTotalDeliveryQuantity(totalDeliveryQuantity.intValue());

        // 全年月均发货量
        int averageDeliveryQuantity =
                totalDeliveryQuantity.divide(new BigDecimal(warehouseReleaseRecordMap.isEmpty() ? 1 :
                        warehouseReleaseRecordMap.size()), 0, RoundingMode.CEILING).intValue();
        demandForecastReviewCurrentVO.setAverageDeliveryQuantity(averageDeliveryQuantity);

        BigDecimal lastYearTotalDeliveryQuantity =
                lastYearWarehouseReleaseRecordMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);

        // 去年月均发货量
        int lastYearAverageDeliveryQuantity =
                lastYearTotalDeliveryQuantity.divide(new BigDecimal(lastYearWarehouseReleaseRecordMap.isEmpty() ? 1 :
                        lastYearWarehouseReleaseRecordMap.size()), 0, RoundingMode.CEILING).intValue();
        /*
         * 全年月均发货量∈(去年月均发货量*1.05, ∞)，显示上升（趋势），
         * ∈[去年月均发货量*0.95, 去年月均发货量*1.05]，显示平稳（趋势），
         * ∈[0, 去年月均发货量*0.95)，显示下降（趋势）；
         */
        if (averageDeliveryQuantity > lastYearAverageDeliveryQuantity * 1.05)
            demandForecastReviewCurrentVO.setComparedToLastYearsTotalVolume("上升");
        if (averageDeliveryQuantity <= lastYearAverageDeliveryQuantity * 1.05
                && averageDeliveryQuantity >= lastYearAverageDeliveryQuantity * 0.95)
            demandForecastReviewCurrentVO.setComparedToLastYearsTotalVolume("平稳");
        if (averageDeliveryQuantity < lastYearAverageDeliveryQuantity * 0.95)
            demandForecastReviewCurrentVO.setComparedToLastYearsTotalVolume("下降");

        // 获取当年最小的发货量月份
        Optional<String> minDate =
                warehouseReleaseRecordMap.entrySet().stream().min(Map.Entry.comparingByValue()).map(Map.Entry::getKey);
        minDate.ifPresent(demandForecastReviewCurrentVO::setDeliveryLowerMonth);

        // 获取当年最大的发货量月份
        Optional<String> maxDate =
                warehouseReleaseRecordMap.entrySet().stream().max(Map.Entry.comparingByValue()).map(Map.Entry::getKey);
        maxDate.ifPresent(demandForecastReviewCurrentVO::setDeliveryHeightMonth);
        demandForecastReviewVO.setDemandForecastReviewCurrentVO(demandForecastReviewCurrentVO);
    }

    /**
     * 根据主机厂编码和车型获取市场占有率
     *
     * @param oemPO                主机厂
     * @param vehicleModelCodeList 车型代码列表
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.math.BigDecimal>>
     */
    private Map<String, Map<String, BigDecimal>> getMarketShareMonthMap(OemPO oemPO,
                                                                        List<String> vehicleModelCodeList) {
        // 根据车型编码获取市场占有率
        if (oemPO == null || CollectionUtils.isEmpty(vehicleModelCodeList)) {
            return new HashMap<>(2);
        }
        List<MarketSharePO> marketSharePOList = marketShareDao.selectByVehicleModelCodes(oemPO.getOemCode(),
                vehicleModelCodeList);
        if (CollectionUtils.isEmpty(marketSharePOList)) {
            return new HashMap<>(2);
        }

        List<String> marketShareIds = marketSharePOList.stream().map(MarketSharePO::getId).collect(Collectors.toList());
        List<MarketShareDetailPO> marketShareDetailPOList = marketShareDetailDao.selectByMarketShareIds(marketShareIds);
        Map<String, Map<String, BigDecimal>> detailMap =
                marketShareDetailPOList.stream().filter(x -> x.getSaleTime() != null
                        && StringUtils.isNotBlank(x.getShareRate())).filter(x -> {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(x.getSaleTime());
                    return calendar.get(Calendar.YEAR) == LocalDate.now().getYear()
                            || calendar.get(Calendar.YEAR) == LocalDate.now().getYear() - 1;
                }).collect(Collectors.groupingBy(MarketShareDetailPO::getMarketShareId,
                        Collectors.toMap(x -> DateUtils.dateToString(x.getSaleTime(), YYYY_MM),
                                v -> new BigDecimal(v.getShareRate().replace("%", "")),
                                (k1, k2) -> k1)));
        return marketSharePOList.stream().collect(Collectors.toMap(MarketSharePO::getVehicleModelCode,
                x -> detailMap.getOrDefault(x.getId(), new HashMap<>(2))));
    }

    /**
     * 获取折线图数据
     *
     * @param cleanForecastMap         产品滚动预测数据MAP
     * @param cleanAlgorithmMap        产品算法预测数据MAP
     * @param demandForecastMap        产品业务预测数据MAP
     * @param lastYearWarehouseDataMap 历史出货数据MAP
     * @param globalCarSaleMap         乘用车销量MAP
     * @param demandForecastReviewVO   业务预测评审VO
     * @param timeSeriesList           时间序列
     */
    private void getDemandForecastReviewTrendVO(Map<String, BigDecimal> cleanForecastMap,
                                                Map<String, BigDecimal> cleanAlgorithmMap,
                                                Map<String, BigDecimal> demandForecastMap,
                                                Map<String, BigDecimal> lastYearWarehouseDataMap,
                                                Map<String, BigDecimal> globalCarSaleMap,
                                                DemandForecastReviewVO demandForecastReviewVO,
                                                List<String> timeSeriesList) {
        // 客户预测
        List<DynamicDataDetailVO> customerForecastDynamicDataDetailVOList = Lists.newArrayList();
        // 算法预测
        List<DynamicDataDetailVO> algorithmForecastDynamicDataDetailVOList = Lists.newArrayList();
        // 业务预测
        List<DynamicDataDetailVO> demandForecastDynamicDataDetailVOList = Lists.newArrayList();
        // 历史出货
        List<DynamicDataDetailVO> historicalPeriodDynamicDataDetailVOList = Lists.newArrayList();
        // 销量
        List<DynamicDataDetailVO> marketSalesDynamicDataDetailVOList = Lists.newArrayList();
        // 获取时间周期
        for (String yearMonth : timeSeriesList) {
            // 客户预测
            DynamicDataDetailVO dynamicDataDetail1 = new DynamicDataDetailVO();
            BigDecimal customerForecast = cleanForecastMap.getOrDefault(yearMonth, BigDecimal.ZERO);
            dynamicDataDetail1.setSaleDate(yearMonth);
            dynamicDataDetail1.setSaleQuantity(String.valueOf(customerForecast));
            customerForecastDynamicDataDetailVOList.add(dynamicDataDetail1);
            // 算法预测
            DynamicDataDetailVO dynamicDataDetail2 = new DynamicDataDetailVO();
            BigDecimal algorithmForecast = cleanAlgorithmMap.getOrDefault(yearMonth, BigDecimal.ZERO);
            dynamicDataDetail2.setSaleDate(yearMonth);
            dynamicDataDetail2.setSaleQuantity(String.valueOf(algorithmForecast));
            algorithmForecastDynamicDataDetailVOList.add(dynamicDataDetail2);
            // 业务预测
            DynamicDataDetailVO dynamicDataDetail3 = new DynamicDataDetailVO();
            BigDecimal demandForecast = demandForecastMap.getOrDefault(yearMonth, BigDecimal.ZERO);
            dynamicDataDetail3.setSaleDate(yearMonth);
            dynamicDataDetail3.setSaleQuantity(String.valueOf(demandForecast));
            demandForecastDynamicDataDetailVOList.add(dynamicDataDetail3);
            // 历史出货
            DynamicDataDetailVO dynamicDataDetail4 = new DynamicDataDetailVO();
            BigDecimal historyDelivery = lastYearWarehouseDataMap.getOrDefault(yearMonth, BigDecimal.ZERO);
            dynamicDataDetail4.setSaleDate(yearMonth);
            dynamicDataDetail4.setSaleQuantity(String.valueOf(historyDelivery));
            historicalPeriodDynamicDataDetailVOList.add(dynamicDataDetail4);
            // 销量
            DynamicDataDetailVO dynamicDataDetail5 = new DynamicDataDetailVO();
            BigDecimal marketSales = globalCarSaleMap.getOrDefault(yearMonth, BigDecimal.ZERO);
            dynamicDataDetail5.setSaleDate(yearMonth);
            dynamicDataDetail5.setSaleQuantity(String.valueOf(marketSales));
            marketSalesDynamicDataDetailVOList.add(dynamicDataDetail5);
        }

        List<DemandForecastReviewTrendVO> demandForecastReviewTrendVOList = Lists.newArrayList();
        demandForecastReviewTrendVOList.add(DemandForecastReviewTrendVO.builder()
                .category(CategoryEnum.CUSTOMER_FORECAST.getDesc())
                .dynamicDataDetailList(customerForecastDynamicDataDetailVOList).build());
        demandForecastReviewTrendVOList.add(DemandForecastReviewTrendVO.builder()
                .category(CategoryEnum.ALGORITHMIC_FORECAST.getDesc())
                .dynamicDataDetailList(algorithmForecastDynamicDataDetailVOList).build());
        demandForecastReviewTrendVOList.add(DemandForecastReviewTrendVO.builder()
                .category(CategoryEnum.DEMAND_FORECAST.getDesc())
                .dynamicDataDetailList(demandForecastDynamicDataDetailVOList).build());
        demandForecastReviewTrendVOList.add(DemandForecastReviewTrendVO.builder()
                .category(CategoryEnum.HISTORICAL_PERIOD.getDesc())
                .dynamicDataDetailList(historicalPeriodDynamicDataDetailVOList).build());
        demandForecastReviewTrendVOList.add(DemandForecastReviewTrendVO.builder()
                .category(CategoryEnum.MARKET_SALES.getDesc())
                .dynamicDataDetailList(marketSalesDynamicDataDetailVOList).build());
        // 趋势结果组装
        demandForecastReviewVO.setDemandForecastReviewTrendVO(demandForecastReviewTrendVOList);
    }

    /**
     * 根据主机厂编码和车型获取全球汽车销量
     *
     * @param vehicleModelCodeList 车型代码列表
     * @param isSummary 是否汇总
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.math.BigDecimal>>
     */
    private Map<String, Map<String, BigDecimal>> getGlobalCarSaleMonthMap(List<String> vehicleModelCodeList,
                                                                          boolean isSummary) {
        if (CollectionUtils.isEmpty(vehicleModelCodeList)) {
            return new HashMap<>(2);
        }
        // 销量：【市场属性】为“国内”的主机厂销量取乘用车市场信息表，【市场属性】为“出口”取全球汽车销量表
        List<PassengerCarSalePO> passengerCarSalePOList =
                passengerCarSaleDao.selectByVehicleModelCodes(vehicleModelCodeList).stream()
                        .filter(item -> item.getSaleTime() != null
                                && StringUtils.isNotBlank(item.getVehicleModelCode())
                                && item.getSaleQuantity() != null
                                && YesOrNoEnum.YES.getCode().equals(item.getEnabled())).filter(x -> {
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(x.getSaleTime());
                            /*if (isSummary) {
                                return calendar.get(Calendar.YEAR) == LocalDate.now().getYear();
                            }*/
                            return calendar.get(Calendar.YEAR) == LocalDate.now().getYear()
                                    || calendar.get(Calendar.YEAR) == LocalDate.now().getYear() - 1;
                        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(passengerCarSalePOList)) {
            return new HashMap<>(2);
        }
        return passengerCarSalePOList.stream().collect(Collectors.groupingBy(PassengerCarSalePO::getVehicleModelCode,
                Collectors.groupingBy(x -> DateUtils.dateToString(x.getSaleTime(), YYYY_MM),
                        Collectors.reducing(BigDecimal.ZERO, x -> BigDecimal.valueOf(x.getSaleQuantity()),
                                BigDecimal::add))));
    }

    @Override
    public List<ProductionLineLevelVO> queryProductionLineLevelDetailData(DemandForecastReviewDTO demandForecastReviewDTO) {
        String scenario = SystemHolder.getScenario();
        String oemCode = demandForecastReviewDTO.getOemCode();
        String demandType = demandForecastReviewDTO.getDemandType();
        List<String> productionLineCodeList = demandForecastReviewDTO.getProductionLineCodeList();

        List<String> timeSeriesList = getTimeSeries();
        // 获取业务预测版本数据
        DemandForecastVersionVO demandForecastVersionVO =
                demandForecastVersionService.selectForecastVersion(demandForecastReviewDTO.getVersionCode());
        if (Objects.isNull(demandForecastVersionVO)) {
            throw new BusinessException(DEMAND_FORECAST_VERSION_NOT_FOUND);
        }

        // 获取业务预测数据
        String versionId = demandForecastVersionVO.getId();
        Map<String, Map<String, BigDecimal>> demandForecastDataMap =
                getDemandForecastDataMap(versionId, demandType, oemCode);
        // 业务预测数据中所有的零件编码
        List<String> productCodeList = new ArrayList<>(demandForecastDataMap.keySet());

        // 获取滚动预测数据
        Map<String, Map<String, BigDecimal>> cleanForecastDataMap =
                getCleanForecastDataMap(demandForecastVersionVO.getRollingVersionId(), demandType, oemCode,
                        productCodeList);
        // 获取算法预测数据
        Map<String, Map<String, BigDecimal>> cleanAlgorithmDataMap =
                getCleanAlgorithmDataMap(demandForecastVersionVO.getAlgorithmVersionId(), oemCode, productCodeList);

        // 查询历史预测数据
        Map<String, Map<String, HistoryForecastDataVO>> historyForecastMap = getHistoryForecastMap(oemCode,
                productCodeList, demandType, timeSeriesList);
        Map<String, Map<String, BigDecimal>> historyCustomerQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getCustomerForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyAlgorithmQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getAlgorithmForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyDemandForecastQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getForecastQuantity);
        // 获取产线
        List<OemProductLineMapPO> oemProductLineMapPOS = oemProductLineMapDao.selectByLineCodes(productionLineCodeList);
        List<String> vehicleModelCodeList = oemProductLineMapPOS.stream().map(OemProductLineMapPO::getVehicleModelCode)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleModelCodeList)) {
            throw new BusinessException("产线未关联到车型信息");
        }
        // 获取物料主数据
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByVehicleModelCode(scenario,
                vehicleModelCodeList);
        // 获取主机厂车型信息
        List<OemVehicleModelPO> vehicleModelPOList = oemVehicleModelDao.selectByVehicleModelCode(oemCode,
                vehicleModelCodeList);

        //获取销售组织库存点
        String rangeData = getSaleOrganizationStockPointCode();
        Map<String, List<String>> vehicle2ProductAccessPositionMap = assembleAccessPositionMap(
        		newProductStockPointVOS.stream().filter(e -> rangeData.equals(e.getStockPointCode())).collect(Collectors.toList()),
                vehicleModelPOList, OemVehicleModelPO::getOemVehicleModelCode);

        // 获取产线下有哪些零件编码
        Map<String, List<String>> productionLineConfigMap =
                assembleProductionLine2ProductMap(vehicle2ProductAccessPositionMap, oemProductLineMapPOS);
        List<String> yearMonthRange = getHistoricalTimeSeries();
        SwitchRelationVO switchRelation =
                dfpSwitchRelationBetweenProductService.getSwitchRelation(Lists.newArrayList(oemCode), productCodeList);
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        //获取仓库发货记录
        String beginDate = DateUtils.dateToString(DateUtils.getMonthFirstDay(DateUtils.stringToDate(yearMonthRange.get(0), YYYY_MM)));
        String endDate = DateUtils.dateToString(DateUtils.moveCalendar(new Date(), Calendar.DAY_OF_YEAR, 1),
                DateUtils.COMMON_DATE_STR3);
        Map<String, Object> warehouseQueryMap = Maps.newHashMap();
        warehouseQueryMap.put("beginDate", beginDate);
        warehouseQueryMap.put("endDate", endDate);
        warehouseQueryMap.put("productCodes", allProductCodes);
        warehouseQueryMap.put("oemCodes", Lists.newArrayList(oemCode));
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = warehouseReleaseRecordDao
        		.selectMonthVOByParams(warehouseQueryMap);	
        //获取中转库发货记录
        List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService
        		.selectMonthVOByParams(warehouseQueryMap);
        
        
        // 汇总产线零件数据
        Map<String, Object> productParams = new HashMap<>();
        productParams.put(PARAM_PRODUCT_CODE_LIST, productCodeList);
        Map<String, String> productVehicleModelMap = newMdsFeign.selectProductVehicleModel(SystemHolder.getScenario(),
                productParams);
        Map<String, String> vehicleModelLineMap = oemProductLineMapDao.selectByParams(ImmutableMap
                .of(PARAM_OEM_CODE, oemCode)).stream().collect(Collectors
                .toMap(OemProductLineMapPO::getVehicleModelCode, OemProductLineMapPO::getLineCode,
                        (t1, t2) -> t2));
        warehouseReleaseRecordMonthList.forEach(item -> {
            String itemCode = item.getItemCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
            String productionLineCode = vehicleModelLineMap.getOrDefault(vehicleModelCode, vehicleModelCode);
            item.setProductionLineCode(productionLineCode);
            item.setVehicleCountFlag(false);
            if (productionLineConfigMap.containsKey(productionLineCode)
                    && productionLineConfigMap.getOrDefault(productionLineCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });

        Map<String, Map<String, BigDecimal>> warehouseReleaseRecordByMonthMap =
                getWarehouseReleaseRecordDetailMonthly(warehouseReleaseRecordMonthList,
                        WarehouseReleaseRecordMonthVO::getProductionLineCode);
        //处理中转库发货记录数据
        warehouseReleaseToWarehouses.forEach(item -> {
            String itemCode = item.getItemCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
            String productionLineCode = vehicleModelLineMap.getOrDefault(vehicleModelCode, vehicleModelCode);
            item.setProductionLineCode(productionLineCode);
            item.setVehicleCountFlag(false);
            if (productionLineConfigMap.containsKey(productionLineCode)
                    && productionLineConfigMap.getOrDefault(productionLineCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });
        Map<String, Map<String, BigDecimal>> warehouseReleaseToRecordByMonthMap =
        		getWarehouseReleaseToRecordDetailMonthly(warehouseReleaseToWarehouses,
        				WarehouseReleaseToWarehouseMonthVO::getProductionLineCode);
        //获取当月待发货数据
        List<DeliveryPlanPublishedMonthVO> deliveryPlanPublishedCurrentMonthList = getDeliveryPlanPublishedCurrentMonthList(oemCode, allProductCodes);
        deliveryPlanPublishedCurrentMonthList.forEach(item -> {
        	String productCode = item.getProductCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(productCode, productCode);
            String productionLineCode = vehicleModelLineMap.getOrDefault(vehicleModelCode, vehicleModelCode);
            item.setProductionLineCode(productionLineCode);
            item.setCountFlag(false);
            if (productionLineConfigMap.containsKey(productionLineCode)
                    && productionLineConfigMap.getOrDefault(productionLineCode, new ArrayList<>()).contains(productCode)) {
                item.setCountFlag(true);
            }
        });
        Map<String, BigDecimal> deliveryPlanPublishedCurrentMonthMap = getDeliveryPlanPublishedCurrentMonthly(oemCode,
        		switchRelation, DeliveryPlanPublishedMonthVO::getProductionLineCode, deliveryPlanPublishedCurrentMonthList);
        // 获取全球汽车销量
        Map<String, Map<String, BigDecimal>> globalCarSaleMonthMap = getGlobalCarSaleMonthMap(vehicleModelCodeList,
                false);

        List<ProductionLineLevelVO> productionLineLevelVOS = Lists.newArrayList();

        // 获取产线信息
        List<OemProductLinePO> oemProductLinePOList = oemProductLineDao.selectByLineCodes(productionLineCodeList);

        // 获取装车日历
        List<ResourceCalendarVO> resourceCalendarVOList =
                dfpResourceCalendarService.selectResourceByStandardResourceIds(productionLineCodeList);
        // 获取当月以后的数据
        Date firstDayOfMonth = DfpDateUtils.getFirstDayOfMonth(DateUtils.moveMonth(new Date(), 1));
        resourceCalendarVOList = resourceCalendarVOList.stream()
                .filter(resourceCalendarVO -> resourceCalendarVO.getWorkDay().after(firstDayOfMonth))
                .collect(Collectors.toList());
        Map<String, Map<String, BigDecimal>> workHoursMap = getWorkHoursMap(resourceCalendarVOList,
                oemProductLinePOList);

        for (String productionLineCode : productionLineCodeList) {
            List<String> productCodes = productionLineConfigMap.getOrDefault(productionLineCode, new ArrayList<>());
            if (CollectionUtils.isEmpty(productCodes)) {
                continue;
            }
            List<LevelDetailVO> detailVOList = Lists.newArrayList();
            List<String> needCalculateProductCodes =
                    productCodeList.stream().filter(productCodes::contains).collect(Collectors.toList());
            ProductionLineLevelVO productionLineLevelVO = new ProductionLineLevelVO();
            List<OemProductLinePO> oemProductLinePOS = oemProductLineDao.selectByParams(ImmutableMap
                    .of(PARAM_OEM_CODE, oemCode, "lineCode", productionLineCode));
            String lineName = !oemProductLinePOS.isEmpty() ?
                    (oemProductLinePOS.get(0).getLineName() + "(" + productionLineCode + ")") : productionLineCode;
            productionLineLevelVO.setProductionLineName(lineName);
            // 获取零件编码的所需数据
            Map<String, BigDecimal> productWarehouseReleaseRecordMap = warehouseReleaseRecordByMonthMap
                    .getOrDefault(productionLineCode, new HashMap<>());
            Map<String, BigDecimal> productWarehouseReleaseToRecordMap = warehouseReleaseToRecordByMonthMap
                    .getOrDefault(productionLineCode, new HashMap<>());
            Map<String, BigDecimal> productCleanForecastDataDetailPOMap = getSummaryData(needCalculateProductCodes,
                    cleanForecastDataMap, historyCustomerQtyMap);
            Map<String, BigDecimal> productCleanAlgorithmDataPOMap = getSummaryData(needCalculateProductCodes,
                    cleanAlgorithmDataMap, historyAlgorithmQtyMap);
            Map<String, BigDecimal> productDemandForecastEstablishmentPOMap = getSummaryData(needCalculateProductCodes,
                    demandForecastDataMap, historyDemandForecastQtyMap);
            Map<String, BigDecimal> globalCarSaleMap = getSummaryData(vehicleModelCodeList, globalCarSaleMonthMap,
                    null);
            Map<String, BigDecimal> futurePlanQuantityMap = workHoursMap.getOrDefault(productionLineCode,
                    new HashMap<>());
            BigDecimal currentDeliveryPlanPublishedQty = deliveryPlanPublishedCurrentMonthMap.getOrDefault(productionLineCode, BigDecimal.ZERO);
            // 生成类目数据
            for (String category : CategoryEnum.productionLineLevelCategorySet) {
                detailVOList.add(getLevelDetailData(category, productWarehouseReleaseRecordMap,
                        productCleanForecastDataDetailPOMap, productCleanAlgorithmDataPOMap,
                        productDemandForecastEstablishmentPOMap, new HashMap<>(),
                        globalCarSaleMap, futurePlanQuantityMap, null, null, timeSeriesList, new HashMap<>(), 
                        currentDeliveryPlanPublishedQty, productWarehouseReleaseToRecordMap));
            }
            productionLineLevelVO.setDetailVOList(detailVOList);
            // 汇总车型下的零件编码数据
            productionLineLevelVOS.add(productionLineLevelVO);
        }
        return productionLineLevelVOS;
    }

    /**
     * 获取产线下有哪些零件编码
     *
     * @param vehicleConfigurationMap 车型配置MAP
     * @param oemProductLineMapPOS    主机厂产线列表
     * @return java.util.Map<java.lang.String, java.util.List < java.lang.String>>
     */
    private Map<String, List<String>> assembleProductionLine2ProductMap(Map<String, List<String>> vehicleConfigurationMap,
                                                                        List<OemProductLineMapPO> oemProductLineMapPOS) {
        // 获取产线下的车型
        Map<String, List<String>> productionLineMap =
                oemProductLineMapPOS.stream().collect(Collectors.groupingBy(OemProductLineMapPO::getLineCode,
                        Collectors.mapping(OemProductLineMapPO::getVehicleModelCode, Collectors.toList())));

        return productionLineMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey,
                x -> x.getValue().stream().flatMap(y -> vehicleConfigurationMap
                                .getOrDefault(y, Collections.emptyList()).stream())
                        .collect(Collectors.toList()), (t1, t2) -> t1));
    }

    public static List<String> getHistoricalTimeSeries() {
        String current = DateUtils.dateToString(new Date(), YYYY_MM);
        return getTimeSeries().stream().filter(x -> x.compareTo(current) <= 0).collect(Collectors.toList());
    }

    public static List<String> getYoyTimeSeries() {
        String current = DateUtils.dateToString(new Date(), YYYY_MM);
        int year = LocalDate.now().getYear();
        int previousYear = year - 1;
        return Lists.newArrayList(previousYear + "01", current);
    }

    @Override
    public List<VehicleModelLevelVO> queryVehicleLevelDetailData(DemandForecastReviewDTO demandForecastReviewDTO) {
        String scenario = SystemHolder.getScenario();
        String demandType = demandForecastReviewDTO.getDemandType();
        String oemCode = demandForecastReviewDTO.getOemCode();
        List<String> vehicleModelCodeList = demandForecastReviewDTO.getVehicleModelCodeList();

        List<String> timeSeriesList = getTimeSeries();
        // 获取业务预测版本数据
        DemandForecastVersionVO demandForecastVersionVO =
                demandForecastVersionService.selectForecastVersion(demandForecastReviewDTO.getVersionCode());
        if (Objects.isNull(demandForecastVersionVO)) {
            throw new BusinessException(DEMAND_FORECAST_VERSION_NOT_FOUND);
        }

        // 获取业务预测数据
        String versionId = demandForecastVersionVO.getId();
        Map<String, Map<String, BigDecimal>> demandForecastDataMap =
                getDemandForecastDataMap(versionId, demandType, oemCode);
        // 业务预测数据中所有的零件编码
        List<String> productCodeList = new ArrayList<>(demandForecastDataMap.keySet());

        // 获取滚动预测数据
        Map<String, Map<String, BigDecimal>> cleanForecastDataMap =
                getCleanForecastDataMap(demandForecastVersionVO.getRollingVersionId(), demandType, oemCode,
                        productCodeList);
        // 获取算法预测数据
        Map<String, Map<String, BigDecimal>> cleanAlgorithmDataMap =
                getCleanAlgorithmDataMap(demandForecastVersionVO.getAlgorithmVersionId(), oemCode, productCodeList);

        // 查询历史预测数据
        Map<String, Map<String, HistoryForecastDataVO>> historyForecastMap = getHistoryForecastMap(oemCode,
                productCodeList, demandType, timeSeriesList);
        Map<String, Map<String, BigDecimal>> historyCustomerQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getCustomerForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyAlgorithmQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getAlgorithmForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyDemandForecastQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getForecastQuantity);
        // 获取物料主数据
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByVehicleModelCode(scenario,
                vehicleModelCodeList);
        // 获取主机厂车型信息
        List<OemVehicleModelPO> vehicleModelPOList = oemVehicleModelDao.selectByVehicleModelCode(oemCode,
                vehicleModelCodeList);
        //获取销售组织库存点
        String rangeData = getSaleOrganizationStockPointCode();

        // 获取车型下有哪些零件编码
        Map<String, List<String>> vehicle2ProductAccessPositionMap = assembleAccessPositionMap(
        		newProductStockPointVOS.stream().filter(e -> rangeData.equals(e.getStockPointCode())).collect(Collectors.toList()),
                vehicleModelPOList, OemVehicleModelPO::getOemVehicleModelCode);
        // 根据本厂编码查询仓库的收发货数据
        List<String> yearMonthRange = getHistoricalTimeSeries();
        SwitchRelationVO switchRelation =
                dfpSwitchRelationBetweenProductService.getSwitchRelation(Lists.newArrayList(oemCode), productCodeList);
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        //获取仓库发货记录
        String beginDate = DateUtils.dateToString(DateUtils.getMonthFirstDay(DateUtils.stringToDate(yearMonthRange.get(0), YYYY_MM)));
        String endDate = DateUtils.dateToString(DateUtils.moveCalendar(new Date(), Calendar.DAY_OF_YEAR, 1),
                DateUtils.COMMON_DATE_STR3);
        Map<String, Object> warehouseQueryMap = Maps.newHashMap();
        warehouseQueryMap.put("beginDate", beginDate);
        warehouseQueryMap.put("endDate", endDate);
        warehouseQueryMap.put("productCodes", allProductCodes);
        warehouseQueryMap.put("oemCodes", Lists.newArrayList(oemCode));
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = warehouseReleaseRecordDao
        		.selectMonthVOByParams(warehouseQueryMap);	
        //获取中转库发货记录
        List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService
        		.selectMonthVOByParams(warehouseQueryMap);
        
        // 汇总车型下的零件编码数据
        Map<String, Object> productParams = new HashMap<>();
        productParams.put(PARAM_PRODUCT_CODE_LIST, productCodeList);
        Map<String, String> productVehicleModelMap = newMdsFeign.selectProductVehicleModel(SystemHolder.getScenario(),
                productParams);
        warehouseReleaseRecordMonthList.forEach(item -> {
            String itemCode = item.getItemCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
            item.setVehicleModelCode(vehicleModelCode);
            item.setVehicleCountFlag(false);
            if (vehicle2ProductAccessPositionMap.containsKey(vehicleModelCode)
                    && vehicle2ProductAccessPositionMap.getOrDefault(vehicleModelCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });
        Map<String, Map<String, BigDecimal>> warehouseReleaseRecordByMonthMap =
                getWarehouseReleaseRecordDetailMonthly(warehouseReleaseRecordMonthList,
                        WarehouseReleaseRecordMonthVO::getVehicleModelCode);
        //处理中转库数据
        warehouseReleaseToWarehouses.forEach(item -> {
            String itemCode = item.getItemCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
            item.setVehicleModelCode(vehicleModelCode);
            item.setVehicleCountFlag(false);
            if (vehicle2ProductAccessPositionMap.containsKey(vehicleModelCode)
                    && vehicle2ProductAccessPositionMap.getOrDefault(vehicleModelCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });
        Map<String, Map<String, BigDecimal>> warehouseReleaseToRecordByMonthMap =
                getWarehouseReleaseToRecordDetailMonthly(warehouseReleaseToWarehouses,
                		WarehouseReleaseToWarehouseMonthVO::getVehicleModelCode);
        
        // 获取当月待发货数据
        List<DeliveryPlanPublishedMonthVO> deliveryPlanPublishedCurrentMonthList = getDeliveryPlanPublishedCurrentMonthList(oemCode, allProductCodes);
        deliveryPlanPublishedCurrentMonthList.forEach(item -> {
        	String productCode = item.getProductCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(productCode, productCode);
            item.setVehicleModelCode(vehicleModelCode);
            item.setCountFlag(false);
            if (vehicle2ProductAccessPositionMap.containsKey(vehicleModelCode)
                    && vehicle2ProductAccessPositionMap.getOrDefault(vehicleModelCode, new ArrayList<>()).contains(productCode)) {
                item.setCountFlag(true);
            }
        });
        Map<String, BigDecimal> deliveryPlanPublishedCurrentMonthMap = getDeliveryPlanPublishedCurrentMonthly(oemCode,
        		switchRelation, DeliveryPlanPublishedMonthVO::getVehicleModelCode, deliveryPlanPublishedCurrentMonthList);
        
        // 获取全球汽车销量
        Map<String, Map<String, BigDecimal>> globalCarSaleMonthMap = getGlobalCarSaleMonthMap(vehicleModelCodeList,
                false);

        // 获取版本需求预测评审产线综评系数
        Map<String, Map<String, ComprehensiveEvaluationCoefficientVO>> coefficientMap = getCoefficientMap(
                demandForecastReviewDTO, demandForecastVersionVO);
        // 获取车型对应的物料EOP信息
        Map<String, List<NewProductStockPointVO>> productEopMap = newProductStockPointVOS.stream()
                .filter(e -> rangeData.equals(e.getStockPointCode())
                        && productCodeList.contains(e.getProductCode()))
                .collect(Collectors.groupingBy(NewProductStockPointVO::getVehicleModelCode));

        List<VehicleModelLevelVO> vehicleModelLevels = Lists.newArrayList();
        for (String vehicleModelCode : vehicleModelCodeList) {
            List<String> productCodes = vehicle2ProductAccessPositionMap.getOrDefault(vehicleModelCode,
                    new ArrayList<>());
            if (CollectionUtils.isEmpty(productCodes)) {
                continue;
            }
            List<String> needCalculateProductCodes =
                    productCodeList.stream().filter(productCodes::contains).collect(Collectors.toList());
            VehicleModelLevelVO vehicleModelLevelVO = new VehicleModelLevelVO();
            vehicleModelLevelVO.setVehicleModelName(vehicleModelCode);
            vehicleModelLevelVO.setVehicleLifeCycle("未EOP");
            // 维护车型下的物料EOP数据
            List<NewProductStockPointVO> productEopList = productEopMap.getOrDefault(vehicleModelCode,
                    new ArrayList<>());
            List<ProductStockPointEopVO> eopDetailList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(productEopList)) {
                Date now = new Date();
                List<String> productList =
                        productEopList.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
                List<String> interSection = com.yhl.platform.common.utils.CollectionUtils.getInterSection(productList,
                        needCalculateProductCodes);
                if (CollectionUtils.isNotEmpty(interSection)) {
                    boolean present = interSection.stream().anyMatch(x -> CollectionUtils
                            .isNotEmpty(productEopList.stream().filter(y -> x.equals(y.getProductCode())
                                    && y.getProductEop() != null && y.getProductEop().compareTo(now) <= 0).collect(Collectors.toList())));
                    if (present) {
                        vehicleModelLevelVO.setVehicleLifeCycle("EOP");
                    }
                }
                for (NewProductStockPointVO newProductStockPointVO : productEopList) {
                    Date productEop = newProductStockPointVO.getProductEop();
                    ProductStockPointEopVO eopDetail = new ProductStockPointEopVO();
                    BeanUtils.copyProperties(newProductStockPointVO, eopDetail);
                    if (productEop != null && productEop.compareTo(now) <= 0) {
                        eopDetail.setRedFlag(YesOrNoEnum.YES.getCode());
                    }
                    eopDetailList.add(eopDetail);
                }
                // 按照EOP时间排序
                List<ProductStockPointEopVO> hasEopDateDetailList =
                        eopDetailList.stream().filter(e -> e.getProductEop() != null)
                                .sorted(Comparator.comparing(ProductStockPointEopVO::getProductEop)).collect(Collectors.toList());
                hasEopDateDetailList.addAll(eopDetailList.stream().filter(e -> e.getProductEop() == null).collect(Collectors.toList()));
                eopDetailList = hasEopDateDetailList;
            }
            vehicleModelLevelVO.setEopDetailList(eopDetailList);
            // 获取零件编码的所需数据
            Map<String, BigDecimal> productWarehouseReleaseRecordMap = warehouseReleaseRecordByMonthMap
                    .getOrDefault(vehicleModelCode, new HashMap<>());
            //获取中转库数据
            Map<String, BigDecimal> productWarehouseReleaseToRecordMap = warehouseReleaseToRecordByMonthMap
                    .getOrDefault(vehicleModelCode, new HashMap<>());
            Map<String, BigDecimal> productCleanForecastDataDetailPOMap = getSummaryData(needCalculateProductCodes,
                    cleanForecastDataMap, historyCustomerQtyMap);
            Map<String, BigDecimal> productCleanAlgorithmDataPOMap = getSummaryData(needCalculateProductCodes,
                    cleanAlgorithmDataMap, historyAlgorithmQtyMap);
            Map<String, BigDecimal> productDemandForecastEstablishmentPOMap = getSummaryData(needCalculateProductCodes,
                    demandForecastDataMap, historyDemandForecastQtyMap);
            Map<String, BigDecimal> globalCarSaleMap = globalCarSaleMonthMap.getOrDefault(vehicleModelCode,
                    new HashMap<>());
            BigDecimal currentDeliveryPlanPublishedQty = deliveryPlanPublishedCurrentMonthMap.getOrDefault(vehicleModelCode, BigDecimal.ZERO);

            Map<String, Object> policyParams = new HashMap<>();
            policyParams.put("customerCode", oemCode.substring(0, oemCode.lastIndexOf("_")));
            policyParams.put("fromDate", DateUtils.stringToDate(timeSeriesList.get(0), YYYY_MM));
            policyParams.put("toDate", DateUtils.getMonthLastDay(DateUtils.stringToDate(timeSeriesList
                    .get(timeSeriesList.size() - 1), YYYY_MM)));
            List<PolicyInformationVO> policies = policyInformationService.selectByParams(policyParams);
            Map<String, String> policyInfoMap = getPolicyInfo(policies, vehicleModelCode, timeSeriesList);
            List<MarketInformationVO> markets = marketInformationService.selectByParams(policyParams);
            Map<String, String> marketInfoMap = getMarketInfo(markets, vehicleModelCode, timeSeriesList);
            // 获取综评系数所需数据
            Map<String, ComprehensiveEvaluationCoefficientVO> vehicleCoefficientMap =
                    coefficientMap.getOrDefault(vehicleModelCode, new HashMap<>());
            // 生成类目数据
            List<LevelDetailVO> detailVOList = Lists.newArrayList();
            for (String category : CategoryEnum.vehicleModelLevelCategorySet) {
                detailVOList.add(getLevelDetailData(category, productWarehouseReleaseRecordMap,
                        productCleanForecastDataDetailPOMap, productCleanAlgorithmDataPOMap,
                        productDemandForecastEstablishmentPOMap, new HashMap<>(), globalCarSaleMap,
                        new HashMap<>(), marketInfoMap, policyInfoMap, timeSeriesList, vehicleCoefficientMap, 
                        currentDeliveryPlanPublishedQty, productWarehouseReleaseToRecordMap));
            }
            vehicleModelLevelVO.setDetailVOList(detailVOList);
            // 汇总车型下的零件编码数据
            vehicleModelLevels.add(vehicleModelLevelVO);
        }
        return vehicleModelLevels;
    }

    /**
     * 获取销售组织库存点
     * @return java.lang.String
     */
	private String getSaleOrganizationStockPointCode() {
		BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        return scenarioBusinessRange.getData().getRangeData();
	}

    /**
     * //获取版本需求预测评审产线综评系数
     *
     * @param demandForecastReviewDTO 评审请求DTO
     * @param demandForecastVersionVO 版本VO
    return java.util.Map<java.lang.String, java.util.Map < java.lang.String, com.yhl.scp.dfp.demand.vo
    .ComprehensiveEvaluationCoefficientVO>>
     */
    private Map<String, Map<String, ComprehensiveEvaluationCoefficientVO>> getCoefficientMap(DemandForecastReviewDTO demandForecastReviewDTO,
                                                                                             DemandForecastVersionVO demandForecastVersionVO) {
        List<ComprehensiveEvaluationCoefficientVO> coefficientList =
                comprehensiveEvaluationCoefficientService.selectByParams(
                        ImmutableMap.of("vehicleModelCodes", demandForecastReviewDTO.getVehicleModelCodeList(),
                                PARAM_FORECAST_VERSION_ID, demandForecastVersionVO.getId()));
        return coefficientList.stream().collect(Collectors.groupingBy(ComprehensiveEvaluationCoefficientVO::getVehicleModelCode,
                Collectors.toMap(ComprehensiveEvaluationCoefficientVO::getCoefficientMonth, Function.identity(), (t1,
                                                                                                                  t2) -> t2)));
    }

    /**
     * 根据物料主数据和主机厂车型获取车型下的零件编码
     * @param newProductStockPointVOS 库存点物品列表
     * @param vehicleModelPOList      主机厂车型列表
     * @param function                主机厂车型函数
     * @return java.util.Map<java.lang.String, java.util.List < java.lang.String>>
     */
    private Map<String, List<String>> assembleAccessPositionMap(List<NewProductStockPointVO> newProductStockPointVOS,
                                                                List<OemVehicleModelPO> vehicleModelPOList,
                                                                Function<OemVehicleModelPO, String> function) {
        return vehicleModelPOList.stream().filter(x -> StringUtils.isNotBlank(x.getAccessPosition()))
                .collect(Collectors.toMap(function, x -> newProductStockPointVOS.stream()
                                .filter(y -> y.getVehicleModelCode().equals(x.getOemVehicleModelCode())
                                        && StringUtils.equals(x.getAccessPosition(), y.getLoadingPositionSub()))
                                .map(NewProductStockPointVO::getProductCode).collect(Collectors.toList()),
                        // 如果有重复的key,合并新旧值
                        (oldValue, newValue) -> {
                            oldValue.addAll(newValue);
                            return oldValue;
                        }));
    }

    private Map<String, Map<String, BigDecimal>> getWarehouseReleaseRecordMonthly(List<WarehouseReleaseRecordMonthVO> records,
                                                                                  boolean isCurrentYear,
                                                                                  Function<WarehouseReleaseRecordMonthVO, String> function) {
        records = records.stream().filter(WarehouseReleaseRecordMonthVO::getVehicleCountFlag)
                .collect(Collectors.toList());
        // 获取每个月的发货量数据
        return records.parallelStream()
                .filter(x -> x != null && x.getYearMonth() != null
                        && StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null)
                .filter(x -> {
                    Calendar calendar = Calendar.getInstance();
                    Date date = DateUtils.stringToDate(x.getYearMonth() + "01", "yyyyMMdd");
                    calendar.setTime(date);
                    int year;
                    if (isCurrentYear) {
                        year = LocalDate.now().getYear();
                    } else {
                        year = LocalDate.now().getYear() - 1;
                    }
                    return calendar.get(Calendar.YEAR) == year;
                }).collect(Collectors.groupingBy(function,
                        Collectors.groupingBy(WarehouseReleaseRecordMonthVO::getYearMonth,
                                Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseRecordMonthVO::getSumQty,
                                        BigDecimal::add))));
    }

    private Map<String, Map<String, BigDecimal>> getWarehouseReleaseRecordDetailMonthly(List<WarehouseReleaseRecordMonthVO> records,
                                                                                        Function<WarehouseReleaseRecordMonthVO, String> function) {
        // 获取每个月的发货量数据
        return records.parallelStream()
                .filter(x -> x != null && x.getYearMonth() != null
                        && StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null)
                .filter(WarehouseReleaseRecordMonthVO::getVehicleCountFlag)
                .collect(Collectors.groupingBy(function,
                        Collectors.groupingBy(e -> e.getYearMonth().replace("-", ""),
                                Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseRecordMonthVO::getSumQty,
                                        BigDecimal::add))));
    }
    
    private Map<String, Map<String, BigDecimal>> getWarehouseReleaseToRecordDetailMonthly(List<WarehouseReleaseToWarehouseMonthVO> records,
            Function<WarehouseReleaseToWarehouseMonthVO, String> function) {
		// 获取每个月的发货量数据
		return records.parallelStream()
				.filter(x -> x != null && x.getYearMonth() != null
					&& StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null)
				.filter(WarehouseReleaseToWarehouseMonthVO::getVehicleCountFlag)
				.collect(Collectors.groupingBy(function,
						Collectors.groupingBy(e -> e.getYearMonth().replace("-", ""),
								Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseMonthVO::getSumQty,
										BigDecimal::add))));
	}


    /**
     * 根据车型或零件编码获取汇总数据
     *
     * @param needCalculateCodes 需要计算的产品编码
     * @param monthDataMap       月度数据MAP
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     */
    private Map<String, BigDecimal> getSummaryData(List<String> needCalculateCodes,
                                                   Map<String, Map<String, BigDecimal>> monthDataMap,
                                                   Map<String, Map<String, BigDecimal>> historyDataMap) {
        List<Map<String, BigDecimal>> tempList = new ArrayList<>();
        for (String needCalculateProductCode : needCalculateCodes) {
            if (monthDataMap.containsKey(needCalculateProductCode)) {
                tempList.add(monthDataMap.get(needCalculateProductCode));
            }
        }
        // 使用 TreeMap 自动对键进行排序
        Map<String, BigDecimal> result =
                tempList.stream().flatMap(x -> x.entrySet().stream()).collect(Collectors
                        .toMap(Map.Entry::getKey, Map.Entry::getValue, BigDecimal::add));

        if (MapUtils.isNotEmpty(historyDataMap)) {
            List<Map<String, BigDecimal>> tempList1 = new ArrayList<>();
            for (String needCalculateProductCode : needCalculateCodes) {
                if (historyDataMap.containsKey(needCalculateProductCode)) {
                    tempList1.add(historyDataMap.get(needCalculateProductCode));
                }
            }

            // 使用 TreeMap 自动对键进行排序
            Map<String, BigDecimal> result1 =
                    tempList1.stream().flatMap(x -> x.entrySet().stream()).collect(Collectors
                            .toMap(Map.Entry::getKey, Map.Entry::getValue, BigDecimal::add));
            result.putAll(result1);
        }
        // 确保结果是排序的
        return new TreeMap<>(result);
    }

    @Override
    public List<OemLevelVO> queryOemLevelDetailData(DemandForecastReviewDTO demandForecastReviewDTO) {
        String scenario = SystemHolder.getScenario();
        String oemCode = demandForecastReviewDTO.getOemCode();
        String demandType = demandForecastReviewDTO.getDemandType();

        List<String> timeSeriesList = getTimeSeries();
        // 获取业务预测版本数据
        DemandForecastVersionVO demandForecastVersionVO =
                demandForecastVersionService.selectForecastVersion(demandForecastReviewDTO.getVersionCode());
        if (Objects.isNull(demandForecastVersionVO)) {
            throw new BusinessException(DEMAND_FORECAST_VERSION_NOT_FOUND);
        }

        // 获取业务预测数据
        String versionId = demandForecastVersionVO.getId();
        Map<String, Map<String, BigDecimal>> demandForecastDataMap = getDemandForecastDataMap(versionId, demandType,
                oemCode);
        // 业务预测数据中所有的零件编码
        List<String> productCodeList = new ArrayList<>(demandForecastDataMap.keySet());

        // 获取滚动预测数据
        Map<String, Map<String, BigDecimal>> cleanForecastDataMap =
                getCleanForecastDataMap(demandForecastVersionVO.getRollingVersionId(), demandType, oemCode,
                        productCodeList);
        // 获取算法预测数据
        Map<String, Map<String, BigDecimal>> cleanAlgorithmDataMap =
                getCleanAlgorithmDataMap(demandForecastVersionVO.getAlgorithmVersionId(), oemCode, productCodeList);

        // 查询历史预测数据
        Map<String, Map<String, HistoryForecastDataVO>> historyForecastMap = getHistoryForecastMap(oemCode,
                productCodeList, demandType, timeSeriesList);
        Map<String, Map<String, BigDecimal>> historyCustomerQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getCustomerForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyAlgorithmQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getAlgorithmForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyDemandForecastQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getForecastQuantity);
        // 获取主机厂车型信息
        List<OemVehicleModelPO> vehicleModelPOList = oemVehicleModelDao.selectByParams(ImmutableMap
                .of(PARAM_OEM_CODE, oemCode));
        List<String> vehicleModelCodeList = vehicleModelPOList.stream().map(OemVehicleModelPO::getOemVehicleModelCode)
                .distinct().collect(Collectors.toList());
        // 获取物料主数据
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByVehicleModelCode(scenario,
                vehicleModelCodeList);
        //获取销售组织库存点
        String rangeData = getSaleOrganizationStockPointCode();
        // 获取主机厂下有哪些零件编码
        Map<String, List<String>> oem2ProductAccessPositionMap = assembleAccessPositionMap(
        		newProductStockPointVOS.stream().filter(e -> rangeData.equals(e.getStockPointCode())).collect(Collectors.toList()),
                vehicleModelPOList, OemVehicleModelPO::getOemCode);

        // 获取全球汽车销量
        Map<String, Map<String, BigDecimal>> globalCarSaleMonthMap = getGlobalCarSaleMonthMap(vehicleModelCodeList,
                false);

        // 根据本厂编码查询仓库的收发货数据
        List<String> yearMonthRange = getHistoricalTimeSeries();
        SwitchRelationVO switchRelation =
                dfpSwitchRelationBetweenProductService.getSwitchRelation(Lists.newArrayList(oemCode), productCodeList);
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        // 汇总本厂的月度收发货数据
        String beginDate = DateUtils.dateToString(DateUtils.getMonthFirstDay(DateUtils.stringToDate(yearMonthRange.get(0), YYYY_MM)));
        String endDate = DateUtils.dateToString(DateUtils.moveCalendar(new Date(), Calendar.DAY_OF_YEAR, 1),
                DateUtils.COMMON_DATE_STR3);
        Map<String, Object> warehouseQueryMap = Maps.newHashMap();
        warehouseQueryMap.put("beginDate", beginDate);
        warehouseQueryMap.put("endDate", endDate);
        warehouseQueryMap.put("productCodes", allProductCodes);
        warehouseQueryMap.put("oemCodes", Lists.newArrayList(oemCode));
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = warehouseReleaseRecordDao
        		.selectMonthVOByParams(warehouseQueryMap);	
        //获取中转库发货记录
        List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService
        		.selectMonthVOByParams(warehouseQueryMap);
        
        warehouseReleaseRecordMonthList.forEach(item -> {
            String itemCode = item.getItemCode();
            item.setVehicleCountFlag(false);
            if (oem2ProductAccessPositionMap.containsKey(oemCode)
                    && oem2ProductAccessPositionMap.getOrDefault(oemCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });
        Map<String, Map<String, BigDecimal>> warehouseReleaseRecordByMonthMap =
                getWarehouseReleaseRecordDetailMonthly(warehouseReleaseRecordMonthList,
                        WarehouseReleaseRecordMonthVO::getOemCode);
        //处理中转库发货数据
        warehouseReleaseToWarehouses.forEach(item -> {
            String itemCode = item.getItemCode();
            item.setVehicleCountFlag(false);
            if (oem2ProductAccessPositionMap.containsKey(oemCode)
                    && oem2ProductAccessPositionMap.getOrDefault(oemCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });
        Map<String, Map<String, BigDecimal>> warehouseReleaseToRecordByMonthMap =
                getWarehouseReleaseToRecordDetailMonthly(warehouseReleaseToWarehouses,
                		WarehouseReleaseToWarehouseMonthVO::getOemCode);
        // 获取当月待发货数据
        List<DeliveryPlanPublishedMonthVO> deliveryPlanPublishedCurrentMonthList = getDeliveryPlanPublishedCurrentMonthList(oemCode, allProductCodes);
        deliveryPlanPublishedCurrentMonthList.forEach(item -> {
        	String productCode = item.getProductCode();
        	item.setCountFlag(false);
            if (oem2ProductAccessPositionMap.containsKey(oemCode)
                    && oem2ProductAccessPositionMap.getOrDefault(oemCode, new ArrayList<>()).contains(productCode)) {
                item.setCountFlag(true);
            }
        });
        Map<String, BigDecimal> deliveryPlanPublishedCurrentMonthMap = getDeliveryPlanPublishedCurrentMonthly(oemCode,
        		switchRelation, DeliveryPlanPublishedMonthVO::getOemCode, deliveryPlanPublishedCurrentMonthList);
        BigDecimal currentDeliveryPlanPublishedQty = deliveryPlanPublishedCurrentMonthMap.getOrDefault(oemCode, BigDecimal.ZERO);
        
        List<OemLevelVO> oemLevelVOS = Lists.newArrayList();
        List<LevelDetailVO> detailVOList = Lists.newArrayList();
        List<String> productCodes = oem2ProductAccessPositionMap.getOrDefault(oemCode, new ArrayList<>());

        List<String> needCalculateProductCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productCodes)) {
            needCalculateProductCodes =
                    productCodeList.stream().filter(productCodes::contains).collect(Collectors.toList());
        }
        OemLevelVO oemLevelVO = new OemLevelVO();
        List<OemVO> oemVOS = oemService.selectByParams(ImmutableMap.of(PARAM_OEM_CODE, oemCode));
        String name = !oemVOS.isEmpty() ? (oemVOS.get(0).getOemName() + "(" + oemCode + ")") : oemCode;
        oemLevelVO.setOemName(name);

        // 获取主机厂厂线信息
        List<OemProductLineMapVO> oemProductLineMapVOS = oemProductLineMapDao.selectVOByParams(ImmutableMap
                .of(PARAM_OEM_CODE, oemCode));
        if (CollectionUtils.isEmpty(oemProductLineMapVOS)) {
            throw new BusinessException("主机厂产线映射缺失");
        }

        List<OemProductLinePO> oemProductLinePOList = oemProductLineDao.selectByLineCodes(oemProductLineMapVOS.stream()
                .map(OemProductLineMapVO::getLineCode).distinct().collect(Collectors.toList()));
        List<String> productLineCodeList =
                oemProductLinePOList.stream().map(OemProductLinePO::getLineCode).collect(Collectors.toList());

        // 获取装车日历
        List<ResourceCalendarVO> resourceCalendarVOList = dfpResourceCalendarService.getResourceByOem(oemCode);
        // 获取当月以后的数据
        Date firstDayOfMonth = DfpDateUtils.getFirstDayOfMonth(DateUtils.moveMonth(new Date(), 1));
        resourceCalendarVOList = resourceCalendarVOList.stream().filter(x ->
                x.getWorkDay().after(firstDayOfMonth)).collect(Collectors.toList());
        Map<String, Map<String, BigDecimal>> workHoursMap = getWorkHoursMap(resourceCalendarVOList,
                oemProductLinePOList);

        // 获取零件编码的所需数据
        Map<String, BigDecimal> productWarehouseReleaseRecordMap =
                warehouseReleaseRecordByMonthMap.getOrDefault(oemCode, new HashMap<>());
        Map<String, BigDecimal> productWarehouseReleaseToRecordMap =
        		warehouseReleaseToRecordByMonthMap.getOrDefault(oemCode, new HashMap<>());
        Map<String, BigDecimal> productCleanForecastDataDetailPOMap = getSummaryData(needCalculateProductCodes,
                cleanForecastDataMap, historyCustomerQtyMap);
        Map<String, BigDecimal> productCleanAlgorithmDataPOMap = getSummaryData(needCalculateProductCodes,
                cleanAlgorithmDataMap, historyAlgorithmQtyMap);
        Map<String, BigDecimal> productDemandForecastEstablishmentPOMap = getSummaryData(needCalculateProductCodes,
                demandForecastDataMap, historyDemandForecastQtyMap);
        Map<String, BigDecimal> globalCarSaleMap = getSummaryData(vehicleModelCodeList, globalCarSaleMonthMap, null);
        Map<String, BigDecimal> futurePlanQuantityMap = getSummaryData(productLineCodeList, workHoursMap, null);
        Map<String, Object> policyParams = new HashMap<>();
        policyParams.put("customerCode", oemCode.substring(0, oemCode.lastIndexOf("_")));
        policyParams.put("fromDate", DateUtils.stringToDate(timeSeriesList.get(0), YYYY_MM));
        policyParams.put("toDate", DateUtils.getMonthLastDay(DateUtils.stringToDate(timeSeriesList
                .get(timeSeriesList.size() - 1), YYYY_MM)));
        List<PolicyInformationVO> policies = policyInformationService.selectByParams(policyParams);
        Map<String, String> policyInfoMap = getPolicyInfo(policies, null, timeSeriesList);
        List<MarketInformationVO> markets = marketInformationService.selectByParams(policyParams);
        Map<String, String> marketInfoMap = getMarketInfo(markets, null, timeSeriesList);

        List<OemRiskLevelPO> oemRiskLevelPOS = oemRiskLevelDao.selectByParams(ImmutableMap
                .of(PARAM_OEM_CODE, oemCode, "estimateTime", DateUtils.dateToString(new Date(), YYYY_MM)));
        oemLevelVO.setOemRiskLevel(CollectionUtils.isEmpty(oemRiskLevelPOS) ? "低" :
                oemRiskLevelPOS.get(0).getRiskLevel());
        // 生成类目数据
        for (String category : CategoryEnum.oemLevelCategorySet) {
            detailVOList.add(getLevelDetailData(category, productWarehouseReleaseRecordMap,
                    productCleanForecastDataDetailPOMap, productCleanAlgorithmDataPOMap,
                    productDemandForecastEstablishmentPOMap, new HashMap<>(), globalCarSaleMap,
                    futurePlanQuantityMap, marketInfoMap, policyInfoMap, timeSeriesList, new HashMap<>(), 
                    currentDeliveryPlanPublishedQty, productWarehouseReleaseToRecordMap));
        }
        oemLevelVO.setDetailVOList(detailVOList);
        // 汇总车型下的零件编码数据
        oemLevelVOS.add(oemLevelVO);
        return oemLevelVOS;
    }
    
    

    private Map<String, Map<String, BigDecimal>> getWarehouseReleaseRecordMonthly(List<WarehouseReleaseRecordMonthVO> records,
                                                                                  Map<String, String> newOldMap,
                                                                                  Map<String, String> oldNewMap) {
        List<WarehouseReleaseRecordMonthVO> filterList = records.parallelStream()
                .filter(x -> x != null && x.getYearMonth() != null
                        && StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null).collect(Collectors.toList());
        Map<String, BigDecimal> qtyMap = filterList.stream().collect(Collectors.groupingBy(item ->
                item.getItemCode() + Constants.DELIMITER + item.getYearMonth(), Collectors
                .reducing(BigDecimal.ZERO, WarehouseReleaseRecordMonthVO::getSumQty, BigDecimal::add)));

        for (WarehouseReleaseRecordMonthVO item : filterList) {
            BigDecimal sumQty = item.getSumQty();
            String itemCode = item.getItemCode();
            String yearMonth = item.getYearMonth();
            if (newOldMap.containsKey(itemCode)) {
                String xItemCode = newOldMap.get(itemCode);
                String xKey = String.join(Constants.DELIMITER, xItemCode, yearMonth);
                sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
            }
            if (oldNewMap.containsKey(itemCode)) {
                String yItemCode = oldNewMap.get(itemCode);
                String yKey = String.join(Constants.DELIMITER, yItemCode, yearMonth);
                sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
            }
            item.setSumQty(sumQty);
        }
        // 获取每个月的发货量数据
        return filterList.stream().collect(Collectors.groupingBy(WarehouseReleaseRecordMonthVO::getItemCode,
                Collectors.groupingBy(e -> e.getYearMonth().replace("-", ""),
                        Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseRecordMonthVO::getSumQty,
                                BigDecimal::add))));
    }
    
    private Map<String, Map<String, BigDecimal>> getWarehouseReleaseToRecordMonthly(List<WarehouseReleaseToWarehouseMonthVO> records,
		            Map<String, String> newOldMap,
		            Map<String, String> oldNewMap) {
		List<WarehouseReleaseToWarehouseMonthVO> filterList = records.parallelStream()
				.filter(x -> x != null && x.getYearMonth() != null
					&& StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null).collect(Collectors.toList());
		Map<String, BigDecimal> qtyMap = filterList.stream().collect(Collectors.groupingBy(item ->
				item.getItemCode() + Constants.DELIMITER + item.getYearMonth(), Collectors
				.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseMonthVO::getSumQty, BigDecimal::add)));
		
		for (WarehouseReleaseToWarehouseMonthVO item : filterList) {
			BigDecimal sumQty = item.getSumQty();
			String itemCode = item.getItemCode();
			String yearMonth = item.getYearMonth();
			if (newOldMap.containsKey(itemCode)) {
				String xItemCode = newOldMap.get(itemCode);
				String xKey = String.join(Constants.DELIMITER, xItemCode, yearMonth);
				sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
			}
			if (oldNewMap.containsKey(itemCode)) {
				String yItemCode = oldNewMap.get(itemCode);
				String yKey = String.join(Constants.DELIMITER, yItemCode, yearMonth);
				sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
			}
			item.setSumQty(sumQty);
		}
			// 获取每个月的发货量数据
		return filterList.stream().collect(Collectors.groupingBy(WarehouseReleaseToWarehouseMonthVO::getItemCode,
				Collectors.groupingBy(e -> e.getYearMonth().replace("-", ""),
						Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseMonthVO::getSumQty,
								BigDecimal::add))));
	}
    
    
    

    @Override
    public List<String> queryProductCodeList(String versionCode, String vehicleModelCode) {
        DemandForecastVersionVO demandForecastVersion =
                demandForecastVersionService.selectForecastVersion(versionCode);
        if (demandForecastVersion == null) {
            return Lists.newArrayList();
        }
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM_FORECAST_VERSION_ID, demandForecastVersion.getId());
        params.put("vehicleModelCode", vehicleModelCode);
        List<DemandForecastEstablishmentPO> demandForecastEstablishmentPOS =
                demandForecastEstablishmentDao.selectByParams(params);
        return demandForecastEstablishmentPOS.stream().map(DemandForecastEstablishmentPO::getProductCode)
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<PartLevelVO> queryPartLevelDetailData(DemandForecastReviewDTO demandForecastReviewDTO) {
        String scenario = SystemHolder.getScenario();
        String oemCode = demandForecastReviewDTO.getOemCode();
        String demandType = demandForecastReviewDTO.getDemandType();
        List<String> vehicleModelCodeList = demandForecastReviewDTO.getVehicleModelCodeList();

        List<String> timeSeriesList = getTimeSeries();
        // 获取业务预测版本数据
        DemandForecastVersionVO demandForecastVersionVO =
                demandForecastVersionService.selectForecastVersion(demandForecastReviewDTO.getVersionCode());
        if (Objects.isNull(demandForecastVersionVO)) {
            throw new BusinessException(DEMAND_FORECAST_VERSION_NOT_FOUND);
        }

        // 获取业务预测数据
        String versionId = demandForecastVersionVO.getId();
        Map<String, Map<String, DemandForecastEstablishmentPO>> demandForecastDataMap =
                getDemandForecastDataDetailMap(versionId, oemCode, demandForecastReviewDTO.getProductCodes());
        // 业务预测数据中所有的零件编码
        List<String> productCodeList = new ArrayList<>(demandForecastDataMap.keySet());

        // 获取滚动预测数据
        String rollingVersionId = demandForecastVersionVO.getRollingVersionId();
        Map<String, Map<String, BigDecimal>> cleanForecastDataMap =
                getCleanForecastDataMap(rollingVersionId, demandType, oemCode, productCodeList);
        // 获取算法预测数据
        String algorithmVersionId = demandForecastVersionVO.getAlgorithmVersionId();
        Map<String, Map<String, BigDecimal>> cleanAlgorithmDataMap =
                getCleanAlgorithmDataMap(algorithmVersionId, oemCode, productCodeList);

        // 查询历史预测数据
        Map<String, Map<String, HistoryForecastDataVO>> historyForecastMap = getHistoryForecastMap(oemCode,
                productCodeList, demandType, timeSeriesList);
        Map<String, Map<String, BigDecimal>> historyCustomerQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getCustomerForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyAlgorithmQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getAlgorithmForecastsQuantity);
        Map<String, Map<String, BigDecimal>> historyDemandForecastQtyMap = getHistoryForecastQtyMap(historyForecastMap,
                HistoryForecastDataVO::getForecastQuantity);
        // 获取零件风险等级
        Map<String, String> partRiskLevelMap = partRiskLevelDao.selectByProductCodeList(productCodeList, oemCode)
                .stream().collect(Collectors.toMap(PartRiskLevelVO::getProductCode,
                        PartRiskLevelVO::getMaterialRiskLevel, (t1, t2) -> t1));
        // 获取物料主数据
        List<NewProductStockPointVO> newProductStockPoints = newMdsFeign.selectByVehicleModelCode(scenario,
                vehicleModelCodeList);
        // k:productCode v:vehicleModelCode
        Map<String, String> productVehicleModelMap = newProductStockPoints.stream().collect(Collectors
                .toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode,
                        (t1, t2) -> t2));
        // k:productCode v:businessSpecial
        Map<String, String> productBusinessSpecialMap = newProductStockPoints.stream()
                .filter(item -> StringUtils.isNotBlank(item.getProductCode())
                        && StringUtils.isNotBlank(item.getBusinessSpecial())).collect(Collectors
                        .toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getBusinessSpecial,
                                (k1, k2) -> k1));
        // 获取车型下所有的物料
        List<String> productCodes = newProductStockPoints.stream().map(NewProductStockPointVO::getProductCode)
                .collect(Collectors.toList());
        /*
         * 根据本厂编码查询仓库的收发货数据
         * 获取每个月的发货量数据
         */
        List<String> yearMonthRange = getHistoricalTimeSeries();
        SwitchRelationVO switchRelation =
                dfpSwitchRelationBetweenProductService.getSwitchRelation(Lists.newArrayList(oemCode), productCodeList);
        //获取仓库发货记录
        Map<String, String> newOldMap = switchRelation.getNewOldMap();
        Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        String beginDate = DateUtils.dateToString(DateUtils.getMonthFirstDay(DateUtils.stringToDate(yearMonthRange.get(0), YYYY_MM)));
        String endDate = DateUtils.dateToString(DateUtils.moveCalendar(new Date(), Calendar.DAY_OF_YEAR, 1),
                DateUtils.COMMON_DATE_STR3);
        Map<String, Object> warehouseQueryMap = Maps.newHashMap();
        warehouseQueryMap.put("beginDate", beginDate);
        warehouseQueryMap.put("endDate", endDate);
		warehouseQueryMap.put("productCodes", allProductCodes);
		warehouseQueryMap.put("oemCodes", Lists.newArrayList(oemCode));
		List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = warehouseReleaseRecordDao
                .selectMonthVOByParams(warehouseQueryMap);	
        Map<String, Map<String, BigDecimal>> warehouseReleaseRecordByMonthMap =
                getWarehouseReleaseRecordMonthly(warehouseReleaseRecordMonthList, newOldMap, oldNewMap);
        //获取中转库发货记录
        List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService
        		.selectMonthVOByParams(warehouseQueryMap);
        Map<String, Map<String, BigDecimal>> warehouseReleaseToRecordByMonthMap =
                getWarehouseReleaseToRecordMonthly(warehouseReleaseToWarehouses, newOldMap, oldNewMap);

        //获取当月待发货数据
        List<DeliveryPlanPublishedMonthVO> deliveryPlanPublishedCurrentMonthList = getDeliveryPlanPublishedCurrentMonthList(
				oemCode, allProductCodes);
        deliveryPlanPublishedCurrentMonthList.forEach(item -> item.setCountFlag(true));
        Map<String, BigDecimal> deliveryPlanPublishedCurrentMonthMap = getDeliveryPlanPublishedCurrentMonthly(oemCode,
        		switchRelation, DeliveryPlanPublishedMonthVO::getProductCode, deliveryPlanPublishedCurrentMonthList);
        // 产品对应的物料风险等级数据
        Map<String, List<ProductRiskLevelVO>> productRiskLevelMap = newMdsFeign.selectProductRiskLevelMap(scenario,
                productCodeList);

        List<PartLevelVO> partLevels = Lists.newArrayList();
        for (String productCode : productCodeList) {
            if (!productCodes.contains(productCode)) {
                continue;
            }
            PartLevelVO partLevelVO = new PartLevelVO();
            partLevelVO.setProductCode(productCode);
            partLevelVO.setVehicleModelCode(productVehicleModelMap.get(productCode));
            partLevelVO.setBusinessSpecial(productBusinessSpecialMap.getOrDefault(productCode, "重点物料"));
            partLevelVO.setProductRiskLevel(partRiskLevelMap.getOrDefault(productCode, "低"));
            partLevelVO.setMaterialRiskLevel("无");
            partLevelVO.setMaterialRiskLevelList(Lists.newArrayList());

            List<ProductRiskLevelVO> riskDataList = MapUtils.isNotEmpty(productRiskLevelMap) ?
                    productRiskLevelMap.getOrDefault(productCode, Lists.newArrayList()) : Lists.newArrayList();
            List<ProductRiskLevelVO> collect = riskDataList.stream()
                    .filter(item -> "高".equals(item.getRiskLevel())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                partLevelVO.setMaterialRiskLevel("高");
                List<MaterialRiskLevelVO> newList = collect.stream().map(item -> MaterialRiskLevelVO.builder()
                                .productCode(item.getProductCode()).materialCode(item.getMaterialCode())
                                .materialName(item.getMaterialName()).riskLevel(item.getRiskLevel()).build())
                        .collect(Collectors.toList());
                partLevelVO.setMaterialRiskLevelList(newList);
            }
            List<LevelDetailVO> detailVOList = Lists.newArrayList();
            // 获取零件编码的所需数据
            Map<String, BigDecimal> productWarehouseReleaseRecordMap = warehouseReleaseRecordByMonthMap
                    .getOrDefault(productCode, new HashMap<>());
            //获取中转库库存
            Map<String, BigDecimal> productWarehouseReleaseToRecordMap = warehouseReleaseToRecordByMonthMap
                    .getOrDefault(productCode, new HashMap<>());
            // 获取零件编码当月的待发货数量
            BigDecimal currentDeliveryPlanPublishedQty = deliveryPlanPublishedCurrentMonthMap.getOrDefault(
                    productCode, BigDecimal.ZERO);
            Map<String, BigDecimal> productCleanForecastDataDetailPOMap =
                    cleanForecastDataMap.getOrDefault(productCode, new HashMap<>());
            Map<String, BigDecimal> historyCustomerQtyMapSub = historyCustomerQtyMap.getOrDefault(productCode,
                    new HashMap<>());
            if (MapUtils.isNotEmpty(historyCustomerQtyMapSub)) {
                productCleanForecastDataDetailPOMap.putAll(historyCustomerQtyMapSub);
            }
            Map<String, BigDecimal> productCleanAlgorithmDataPOMap = cleanAlgorithmDataMap.getOrDefault(productCode,
                    new HashMap<>());
            Map<String, BigDecimal> historyAlgorithmQtyMapSub = historyAlgorithmQtyMap.getOrDefault(productCode,
                    new HashMap<>());
            if (MapUtils.isNotEmpty(historyAlgorithmQtyMapSub)) {
                productCleanAlgorithmDataPOMap.putAll(historyAlgorithmQtyMapSub);
            }
            Map<String, DemandForecastEstablishmentPO> productDemandForecastEstablishmentPOMap =
                    demandForecastDataMap.getOrDefault(productCode, new HashMap<>());

            Map<String, BigDecimal> historyDemandForecastQtyMapSub =
                    historyDemandForecastQtyMap.getOrDefault(productCode, new HashMap<>());
            // 生成类目数据
            for (String category : CategoryEnum.partLevelCategorySet) {
                detailVOList.add(getLevelDetailData(category, productWarehouseReleaseRecordMap,
                        productCleanForecastDataDetailPOMap, productCleanAlgorithmDataPOMap,
                        historyDemandForecastQtyMapSub, productDemandForecastEstablishmentPOMap,
                        new HashMap<>(), new HashMap<>(), null, null, timeSeriesList, new HashMap<>(),
                        currentDeliveryPlanPublishedQty, productWarehouseReleaseToRecordMap));
            }
            partLevelVO.setDetailVOList(detailVOList);
            partLevels.add(partLevelVO);
        }
        com.yhl.platform.common.utils.CollectionUtils.sort(partLevels, "vehicleModelCode,productCode");
        return partLevels;
    }

    /**
     * 获取当月待发货数据
     *
     * @param oemCode         主机厂代码
     * @param allProductCodes 物品代码列表
     * @return java.util.List<com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedMonthVO>
     */
	private List<DeliveryPlanPublishedMonthVO> getDeliveryPlanPublishedCurrentMonthList(String oemCode,
			List<String> allProductCodes) {
		Date startDemandTime = DateUtils.getDayFirstTime(new Date());
        Date endDemandTime = DateUtils.getMonthLastDay(startDemandTime);
        return deliveryPlanPublishedDao
                .selectCurrentMonthVOByItemCodes(Lists.newArrayList(oemCode), allProductCodes,
                        startDemandTime, endDemandTime);
	}

    private Map<String, BigDecimal> getDeliveryPlanPublishedCurrentMonthly(String oemCode,
    		SwitchRelationVO switchRelation, Function<DeliveryPlanPublishedMonthVO, String> function,
    		List<DeliveryPlanPublishedMonthVO> deliveryPlanPublishedCurrentMonthList) {
    	Map<String, String> newOldMap = switchRelation.getNewOldMap();
    	Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        Map<String, BigDecimal> qtyMap = deliveryPlanPublishedCurrentMonthList.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedMonthVO::getProductCode,
                        Collectors.reducing(BigDecimal.ZERO, DeliveryPlanPublishedMonthVO::getSumDemandQuantity,
                                BigDecimal::add)));
        for (DeliveryPlanPublishedMonthVO item : deliveryPlanPublishedCurrentMonthList) {
            BigDecimal sumQty = item.getSumDemandQuantity();
            String itemCode = item.getProductCode();
            if (newOldMap.containsKey(itemCode)) {
                String xKey = newOldMap.get(itemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
            }
            if (oldNewMap.containsKey(itemCode)) {
                String yKey = oldNewMap.get(itemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
            }
            item.setSumDemandQuantity(sumQty);
        }
        return deliveryPlanPublishedCurrentMonthList
                .stream().filter(DeliveryPlanPublishedMonthVO::getCountFlag).collect(Collectors.groupingBy(function,
                        Collectors.reducing(BigDecimal.ZERO, DeliveryPlanPublishedMonthVO::getSumDemandQuantity,
                                BigDecimal::add)));
    }

    /**
     * 获取零件明细数据
     *
     * @param warehouseReleaseRecordByMonthMap 仓库发货月度量MAP
     * @param cleanForecastDataMap             滚动预测数据MAP
     * @param cleanAlgorithmDataMap            算法预测数据MAP
     * @param demandForecastDataMap            业务预测数据MAP
     * @param demandForecastDataDetailMap      业务预测明细数据MAP
     * @param globalCarSaleMap                 乘用车销量MAP
     * @param futurePlanQuantityMap            计划产量MAP
     * @param marketInfoMap                    市场信息MAP
     * @param policyInfoMap                    政策信息MAP
     * @param timeSeriesList                   日期跨度
     * @param vehicleCoefficientMap            综评系数
     * @param currentDeliveryPlanPublishedQty  当月待发货数量
     * @param productWarehouseReleaseToRecordMap 
     * @return com.yhl.scp.dfp.demand.vo.LevelDetailVO
     */
    private LevelDetailVO getLevelDetailData(String category,
                                             Map<String, BigDecimal> warehouseReleaseRecordByMonthMap,
                                             Map<String, BigDecimal> cleanForecastDataMap,
                                             Map<String, BigDecimal> cleanAlgorithmDataMap,
                                             Map<String, BigDecimal> demandForecastDataMap,
                                             Map<String, DemandForecastEstablishmentPO> demandForecastDataDetailMap,
                                             Map<String, BigDecimal> globalCarSaleMap,
                                             Map<String, BigDecimal> futurePlanQuantityMap,
                                             Map<String, String> marketInfoMap,
                                             Map<String, String> policyInfoMap,
                                             List<String> timeSeriesList,
                                             Map<String, ComprehensiveEvaluationCoefficientVO> vehicleCoefficientMap,
                                             BigDecimal currentDeliveryPlanPublishedQty, Map<String, BigDecimal> productWarehouseReleaseToRecordMap) {
        LevelDetailVO levelDetailVO = new LevelDetailVO();
        levelDetailVO.setCategory(category);
        List<DynamicDataDetailVO> detailList = Lists.newArrayList();
        List<String> demandForecastKeySet;
        String currentYearMonth = DateUtils.dateToString(new Date(), YYYY_MM);
        // 业务预测评审界面，希望时间保持一致
        // 应该展示当年历史月+计划周期月，比如，现在是九月，就是202401-202409的历史+202410-202509的计划周期
        demandForecastKeySet = timeSeriesList;
        switch (category) {
            case "实际出货":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    BigDecimal saleQuantity = BigDecimal.ZERO;
                    if(warehouseReleaseRecordByMonthMap != null) {
                    	saleQuantity = saleQuantity.add(warehouseReleaseRecordByMonthMap
                                .getOrDefault(k, BigDecimal.ZERO));
                    }
                    if(productWarehouseReleaseToRecordMap != null) {
                    	saleQuantity = saleQuantity.add(productWarehouseReleaseToRecordMap
                                .getOrDefault(k, BigDecimal.ZERO));
                    }
                    dynamicDataDetailVO.setSaleQuantity(formatBigDecimal(saleQuantity));
                    // 当月发货值= 已发货+待发货,展示已发：xxx，待发：xxx
                    if (!Objects.isNull(currentDeliveryPlanPublishedQty) && currentYearMonth.equals(k)) {
                        dynamicDataDetailVO.setCurrentMonthSaleQuantity("已发：" + dynamicDataDetailVO.getSaleQuantity() + ","
                                + " 待发：" + formatBigDecimal(currentDeliveryPlanPublishedQty));
                        dynamicDataDetailVO.setSaleQuantity(
                                formatBigDecimal(new BigDecimal(dynamicDataDetailVO.getSaleQuantity())
                                        .add(currentDeliveryPlanPublishedQty))
                        );
                    }
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                break;
            case "客户预测":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    dynamicDataDetailVO.setSaleQuantity(null == cleanForecastDataMap ? "0" :
                            formatBigDecimal(cleanForecastDataMap.getOrDefault(k, BigDecimal.ZERO)));
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                break;
            case "算法预测":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    dynamicDataDetailVO.setSaleQuantity(null == cleanAlgorithmDataMap ? "0" :
                            formatBigDecimal(cleanAlgorithmDataMap.getOrDefault(k, BigDecimal.ZERO)));
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                break;
            case "综评系数":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    if (vehicleCoefficientMap == null || vehicleCoefficientMap.get(k) == null) {
                        dynamicDataDetailVO.setSaleQuantity("1");
                    } else {
                        ComprehensiveEvaluationCoefficientVO coefficientVO = vehicleCoefficientMap.get(k);
                        dynamicDataDetailVO.setId(coefficientVO.getId());
                        dynamicDataDetailVO.setVersionCode(coefficientVO.getVersionValue());
                        dynamicDataDetailVO.setSaleQuantity(formatBigDecimal(coefficientVO.getComprehensiveEvaluationCoefficient()));
                    }
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                break;
            case "业务预测":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    DemandForecastEstablishmentPO demandForecastEstablishmentPO =
                            demandForecastDataDetailMap.get(k);
                    if (Objects.isNull(demandForecastEstablishmentPO)) {
                        dynamicDataDetailVO.setSaleQuantity(formatBigDecimal(demandForecastDataMap.getOrDefault(k,
                                BigDecimal.ZERO)));
                    } else {
                        dynamicDataDetailVO.setId(demandForecastEstablishmentPO.getId());
                        BigDecimal demandForecast = demandForecastEstablishmentPO.getDemandForecast();
                        dynamicDataDetailVO.setSaleQuantity(demandForecast != null ?
                                formatBigDecimal(demandForecast) : BigDecimal.ZERO.toString());
                        dynamicDataDetailVO.setVersionValue(demandForecastEstablishmentPO.getVersionValue());
                    }
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                break;
            case "客户准确率":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    //获取已发数量（仓库发货数量+中转库发货数量）
                    BigDecimal deliveryValue = BigDecimal.ZERO;
                    if(warehouseReleaseRecordByMonthMap != null) {
                    	deliveryValue = deliveryValue.add(warehouseReleaseRecordByMonthMap
                    			.getOrDefault(k, BigDecimal.ZERO));
                    }
                    if(productWarehouseReleaseToRecordMap != null) {
                    	deliveryValue = deliveryValue.add(productWarehouseReleaseToRecordMap
                    			.getOrDefault(k, BigDecimal.ZERO));
                    }
                    //如果是当月，发货数量 = 已发+待发
                    if (!Objects.isNull(currentDeliveryPlanPublishedQty) && currentYearMonth.equals(k)) {
                    	deliveryValue = deliveryValue.add(currentDeliveryPlanPublishedQty);
                    }
                    dynamicDataDetailVO.setSaleQuantity(calculationPrecision(null == cleanForecastDataMap ?
                                    BigDecimal.ZERO : cleanForecastDataMap.getOrDefault(k, BigDecimal.ZERO),
                            deliveryValue)
                            .stripTrailingZeros()
                            .toPlainString());
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                detailList.forEach(e -> e.setSaleQuantity(e.getSaleQuantity() + "%"));
                break;
            case "算法准确率":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    //获取已发数量（仓库发货数量+中转库发货数量）
                    BigDecimal deliveryValue = BigDecimal.ZERO;
                    if(warehouseReleaseRecordByMonthMap != null) {
                    	deliveryValue = deliveryValue.add(warehouseReleaseRecordByMonthMap
                    			.getOrDefault(k, BigDecimal.ZERO));
                    }
                    if(productWarehouseReleaseToRecordMap != null) {
                    	deliveryValue = deliveryValue.add(productWarehouseReleaseToRecordMap
                    			.getOrDefault(k, BigDecimal.ZERO));
                    }
                    //如果是当月，发货数量 = 已发+待发
                    if (!Objects.isNull(currentDeliveryPlanPublishedQty) && currentYearMonth.equals(k)) {
                    	deliveryValue = deliveryValue.add(currentDeliveryPlanPublishedQty);
                    }
                    dynamicDataDetailVO.setSaleQuantity(calculationPrecision(null == cleanAlgorithmDataMap ?
                                    BigDecimal.ZERO : cleanAlgorithmDataMap.getOrDefault(k, BigDecimal.ZERO),
                            deliveryValue)
                            .stripTrailingZeros()
                            .toPlainString());
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                detailList.forEach(e -> e.setSaleQuantity(e.getSaleQuantity() + "%"));
                break;
            case "预测准确率":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    BigDecimal forecastValue = BigDecimal.ZERO;
                    if (demandForecastDataMap == null) {
                        DemandForecastEstablishmentPO demandForecastEstablishmentPO =
                                demandForecastDataDetailMap.get(k);
                        if (Objects.nonNull(demandForecastEstablishmentPO))
                            forecastValue = demandForecastEstablishmentPO.getDemandForecast();
                    } else {
                        forecastValue = demandForecastDataMap.getOrDefault(k, BigDecimal.ZERO);
                    }
                    //获取已发数量（仓库发货数量+中转库发货数量）
                    BigDecimal deliveryValue = BigDecimal.ZERO;
                    if(warehouseReleaseRecordByMonthMap != null) {
                    	deliveryValue = deliveryValue.add(warehouseReleaseRecordByMonthMap
                    			.getOrDefault(k, BigDecimal.ZERO));
                    }
                    if(productWarehouseReleaseToRecordMap != null) {
                    	deliveryValue = deliveryValue.add(productWarehouseReleaseToRecordMap
                    			.getOrDefault(k, BigDecimal.ZERO));
                    }
                    //如果是当月，发货数量 = 已发+待发
                    if (!Objects.isNull(currentDeliveryPlanPublishedQty) && currentYearMonth.equals(k)) {
                    	deliveryValue = deliveryValue.add(currentDeliveryPlanPublishedQty);
                    }
                    dynamicDataDetailVO.setSaleQuantity(calculationPrecision(forecastValue, deliveryValue)
                            .stripTrailingZeros()
                            .toPlainString());
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                detailList.forEach(e -> e.setSaleQuantity(e.getSaleQuantity() + "%"));
                break;
            case "实际销量":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    if (null == globalCarSaleMap) {
                        dynamicDataDetailVO.setSaleQuantity("0");
                    } else {
                        dynamicDataDetailVO.setSaleQuantity(globalCarSaleMap.getOrDefault(k, BigDecimal.ZERO).toString());
                    }
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                break;
            case "计划产量":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    // TODO 设置计划产量 目前默认为零, 等生产计划模块上线提供接口再做设值处理
                    dynamicDataDetailVO.setSaleQuantity(null == futurePlanQuantityMap
                            || null == futurePlanQuantityMap.get(k) ? "0" : futurePlanQuantityMap.get(k).toString());
                    detailList.add(dynamicDataDetailVO);
                });
                getAverageData(detailList);
                break;
            case "备注":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    if (demandForecastDataDetailMap != null) {
                        DemandForecastEstablishmentPO demandForecastEstablishmentPO =
                                demandForecastDataDetailMap.get(k);
                        if (demandForecastEstablishmentPO != null) {
                            dynamicDataDetailVO.setId(demandForecastEstablishmentPO.getId());
                            dynamicDataDetailVO.setRemark(demandForecastEstablishmentPO.getRemark());
                            dynamicDataDetailVO.setVersionValue(demandForecastEstablishmentPO.getVersionValue());
                        }
                    }
                    detailList.add(dynamicDataDetailVO);
                });
                break;
            case "市场信息":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    if (marketInfoMap != null) {
                        String marketInfo = marketInfoMap.getOrDefault(k, null);
                        dynamicDataDetailVO.setId(UUIDUtil.getUUID());
                        dynamicDataDetailVO.setRemark(marketInfo);
                    }
                    detailList.add(dynamicDataDetailVO);
                });
                break;
            case "政策信息":
                demandForecastKeySet.forEach(k -> {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(k);
                    if (policyInfoMap != null) {
                        String policyInfo = policyInfoMap.getOrDefault(k, null);
                        dynamicDataDetailVO.setId(UUIDUtil.getUUID());
                        dynamicDataDetailVO.setRemark(policyInfo);
                    }
                    detailList.add(dynamicDataDetailVO);
                });
                break;
            default:
                break;
        }
        detailList.sort(Comparator.comparing(DynamicDataDetailVO::getSaleDate));
        levelDetailVO.setDetails(detailList);
        return levelDetailVO;
    }

    /**
     * 获取今年日期到未来一年日期（yyyyMM格式）
     *
     * @return java.util.List<java.lang.String>
     */
    public static List<String> getTimeSeries() {
        Set<String> monthSet = new LinkedHashSet<>();
        LocalDate currentDate = LocalDate.now();
        int year = currentDate.getYear();
        Month month = currentDate.getMonth();
        // 当前月份往前推六个月的第一天
        LocalDate startDate = currentDate.minusMonths(12).withDayOfMonth(1);
        LocalDate endDate = LocalDate.of(year + 1, month, 1);
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern(YYYY_MM);
        while (startDate.isBefore(endDate) || startDate.isEqual(endDate)) {
            String monthStr = startDate.format(timeFormatter);
            monthSet.add(monthStr);
            startDate = startDate.plusMonths(1);
        }
        return new ArrayList<>(monthSet);
    }

    public static Map<String, Map<String, BigDecimal>> getHistoryForecastQtyMap(
            Map<String, Map<String, HistoryForecastDataVO>> dataMap,
            Function<HistoryForecastDataVO, BigDecimal> function) {
        Map<String, Map<String, BigDecimal>> resultMap = new HashMap<>();
        dataMap.forEach((key, value) -> {
            Map<String, BigDecimal> subMap = new HashMap<>();
            value.forEach((k, v) -> subMap.put(k, function.apply(v)));
            resultMap.put(key, subMap);
        });
        return resultMap;
    }

    /**
     * （1-（abs（客户预测值-实际发货值）/客户预测值））*100%
     *
     * @param forecastValue 预测值
     * @param deliveryValue 发货值
     * @return java.math.BigDecimal
     */
    private BigDecimal calculationPrecision(BigDecimal forecastValue, BigDecimal deliveryValue) {
        if (Objects.isNull(deliveryValue) || BigDecimal.ZERO.compareTo(deliveryValue) == 0
                || Objects.isNull(forecastValue) || BigDecimal.ZERO.compareTo(forecastValue) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal divide = deliveryValue.divide(forecastValue, 3, RoundingMode.HALF_UP);
        return divide.multiply(new BigDecimal("100"));
    }

    /**
     * 根据业务预测版本获取业务预测数据
     *
     * @param versionId      版本ID
     * @param demandCategory 需求类型
     * @param oemCode        主机厂代码
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.math.BigDecimal>>
     */
    private Map<String, Map<String, BigDecimal>> getDemandForecastDataMap(String versionId, String demandCategory,
                                                                          String oemCode) {
        List<DemandForecastEstablishmentPO> demandForecastEstablishments =
                demandForecastEstablishmentDao.selectByParams4ForecastReview(ImmutableMap
                        .of(PARAM_FORECAST_VERSION_ID, versionId, PARAM_DEMAND_CATEGORY, demandCategory,
                                PARAM_OEM_CODE, oemCode));
        if (CollectionUtils.isEmpty(demandForecastEstablishments)) {
            throw new BusinessException(DEMAND_FORECAST_DATA_NOT_FOUND);
        }
        return demandForecastEstablishments.stream().filter(p -> StringUtils
                .isNotEmpty(p.getProductCode()) && p.getDemandForecast() != null).collect(Collectors
                .groupingBy(DemandForecastEstablishmentPO::getProductCode, Collectors
                        .toMap(x -> DateUtils.dateToString(x.getForecastTime(), YYYY_MM),
                                DemandForecastEstablishmentPO::getDemandForecast, (k1, k2) -> k1)));
    }

    /**
     * 未来产线计划量
     *
     * @param resourceCalendarVOList 资源日历列表
     * @param oemProductLinePOList   主机厂产线列表
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.math.BigDecimal>>
     */
    private Map<String, Map<String, BigDecimal>> getWorkHoursMap(List<ResourceCalendarVO> resourceCalendarVOList,
                                                                 List<OemProductLinePO> oemProductLinePOList) {
        if (CollectionUtils.isEmpty(resourceCalendarVOList)) {
            return new HashMap<>();
        }

        // 按照厂线进行分组
        Map<String, List<ResourceCalendarVO>> map =
                resourceCalendarVOList.stream().filter(p -> StringUtils.isNotEmpty(p.getStandardResourceId())
                        && p.getStartTime() != null).collect(Collectors.groupingBy(ResourceCalendarVO::getStandardResourceId));

        // 厂线信息组成key，value
        Map<String, OemProductLinePO> oemProductLinePOMapOfOemCode =
                oemProductLinePOList.stream().collect(Collectors.toMap(OemProductLinePO::getLineCode,
                        Function.identity(), (k1, k2) -> k2));

        Map<String, Map<String, BigDecimal>> result = new HashMap<>();
        for (Map.Entry<String, List<ResourceCalendarVO>> entry : map.entrySet()) {
            List<ResourceCalendarVO> value = entry.getValue();
            Map<String, List<ResourceCalendarVO>> resourceCalendarVOMapOfMonth =
                    value.stream().collect(Collectors.groupingBy(x -> DateUtils.dateToString(x.getStartTime(),
                            YYYY_MM)));
            Map<String, BigDecimal> decimalMap = new HashMap<>();
            for (Map.Entry<String, List<ResourceCalendarVO>> listEntry : resourceCalendarVOMapOfMonth.entrySet()) {
                String month = listEntry.getKey();
                List<ResourceCalendarVO> calendarVOS = listEntry.getValue();
                // 工作时长
                BigDecimal workingHours =
                        calendarVOS.stream().map(ResourceCalendarVO::getWorkHours).filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                // 获取厂线
                OemProductLinePO oemProductLinePO = oemProductLinePOMapOfOemCode.get(entry.getKey());
                // 节拍
                Integer lineBeat = oemProductLinePO.getLineBeat();
                if (lineBeat == null || lineBeat == 0) {
                    decimalMap.put(month, BigDecimal.ZERO);
                } else {
                    decimalMap.put(month,
                            workingHours.multiply(BigDecimal.valueOf(3600)).divide(new BigDecimal(lineBeat)
                                    , 0, RoundingMode.HALF_UP));
                }
            }
            result.put(entry.getKey(), decimalMap);
        }
        return result;
    }

    /**
     * 根据业务预测版本获取业务预测数据
     *
     * @param versionId    版本ID
     * @param oemCode    主机厂代码
     * @param productCodes 产品代码列表
     @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO>>
     */
    private Map<String, Map<String, DemandForecastEstablishmentPO>> getDemandForecastDataDetailMap(String versionId,
                                                                                                   String oemCode,
                                                                                                   List<String> productCodes) {
        productCodes = CollectionUtils.isEmpty(productCodes) ? new ArrayList<>() : productCodes;
        List<DemandForecastEstablishmentPO> demandForecastEstablishments =
                demandForecastEstablishmentDao.selectByParams(ImmutableMap
                        .of(PARAM_FORECAST_VERSION_ID, versionId, PARAM_OEM_CODE, oemCode, PARAM_PRODUCT_CODE_LIST,
                                productCodes));
        if (CollectionUtils.isEmpty(demandForecastEstablishments)) {
            throw new BusinessException(DEMAND_FORECAST_DATA_NOT_FOUND);
        }
        return demandForecastEstablishments.stream().filter(p -> StringUtils.isNotEmpty(p.getProductCode()))
                .collect(Collectors.groupingBy(DemandForecastEstablishmentPO::getProductCode, Collectors
                        .toMap(x -> DateUtils.dateToString(x.getForecastTime(), YYYY_MM),
                                Function.identity(), (k1, k2) -> k1)));
    }


    /**
     * 根据业务预测版本获取算法预测数据
     *
     * @param versionId      版本ID
     * @param oemCode 主机厂编码
     * @param productCodes 主机厂编码
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.math.BigDecimal>>
     */
    private Map<String, Map<String, BigDecimal>> getCleanAlgorithmDataMap(String versionId, String oemCode,
                                                                          List<String> productCodes) {
        if (StringUtils.isBlank(versionId)) {
            return Maps.newHashMap();
        }
        List<CleanAlgorithmDataPO> cleanAlgorithmDataPOList =
                cleanAlgorithmDataDao.selectByParams4ForecastReview(ImmutableMap
                        .of("versionId", versionId, PARAM_OEM_CODE, oemCode, PARAM_PRODUCT_CODE_LIST, productCodes));
        return CollectionUtils.isEmpty(cleanAlgorithmDataPOList) ? Maps.newHashMap() :
                cleanAlgorithmDataPOList.stream().filter(p -> StringUtils.isNotEmpty(p.getProductCode()))
                        .collect(Collectors.toList()).stream().collect(Collectors
                                .groupingBy(CleanAlgorithmDataPO::getProductCode, Collectors
                                        .toMap(x -> DateUtils.dateToString(x.getForecastTime(), YYYY_MM),
                                                CleanAlgorithmDataPO::getForecastQuantity, (k1, k2) -> k1)));
    }

    private Map<String, Map<String, HistoryForecastDataVO>> getHistoryForecastMap(String oemCode,
                                                                                  List<String> productCodes,
                                                                                  String demandCategory,
                                                                                  List<String> timeSeriesList) {
        List<String> histroyList = timeSeriesList.subList(0, 12);
        String beginYearMonth = histroyList.get(0);
        String endYearMonth = histroyList.get(histroyList.size() - 1);
        Map<String, Object> historyQueryParams = new HashMap<>();
        historyQueryParams.put(PARAM_DEMAND_CATEGORY, demandCategory);
        historyQueryParams.put(PARAM_OEM_CODE, oemCode);
        historyQueryParams.put("productCodes", productCodes);
        historyQueryParams.put("beginYearMonth", beginYearMonth);
        historyQueryParams.put("endYearMonth", endYearMonth);
        List<HistoryForecastDataVO> historyForecastDataList =
                historyForecastDataService.selectByParams(historyQueryParams);

        return historyForecastDataList.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getProductCode())
                        && Objects.nonNull(p.getForecastTime()))
                .collect(Collectors.groupingBy(HistoryForecastDataVO::getProductCode,
                        Collectors.toMap(p -> DateUtils.dateToString(p.getForecastTime(), YYYY_MM),
                                Function.identity(), (v1, v2) ->
                                        v1.getModifyTime().getTime() >= v2.getModifyTime().getTime() ? v1 : v2)));
    }

    /**
     * 根据业务预测版本获取滚动预测数据
     *
     * @param versionId      版本ID
     * @param demandCategory 需求类型
     * @param oemCode        主机厂代码
     * @param productCodes   物品代码列表
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.math.BigDecimal>>
     */
    private Map<String, Map<String, BigDecimal>> getCleanForecastDataMap(String versionId, String demandCategory,
                                                                         String oemCode, List<String> productCodes) {
        if (StringUtils.isBlank(versionId)) {
            return Maps.newHashMap();
        }
        List<CleanForecastDataPO> cleanForecastDataList = cleanForecastDataDao.selectByParams(ImmutableMap
                .of("versionId", versionId, PARAM_DEMAND_CATEGORY, demandCategory, PARAM_OEM_CODE, oemCode,
                        PARAM_PRODUCT_CODE_LIST, productCodes));
        if (CollectionUtils.isEmpty(cleanForecastDataList)) {
            throw new BusinessException("滚动预测数据不存在");
        }
        List<String> cleanForecastDataIds =
                cleanForecastDataList.stream().map(CleanForecastDataPO::getId).collect(Collectors.toList());
        List<CleanForecastDataDetailPO> cleanForecastDataDetails =
                cleanForecastDataDetailDao.selectByCleanForecastDataIds(cleanForecastDataIds);
        // 先根据滚动预测数据id分组
        Map<String, Map<String, BigDecimal>> detailMap =
                cleanForecastDataDetails.stream()
                        .filter(item -> item.getForecastQuantity() != null
                                && item.getForecastTime() != null
                                && StringUtils.isNotBlank(item.getCleanForecastDataId()))
                        .collect(Collectors.groupingBy(CleanForecastDataDetailPO::getCleanForecastDataId,
                                Collectors.toMap(x -> DateUtils.dateToString(x.getForecastTime(), YYYY_MM),
                                        CleanForecastDataDetailPO::getForecastQuantity, (k1, k2) -> k1)));
        return cleanForecastDataList.stream()
                .filter(p -> StringUtils.isNotEmpty(p.getProductCode()))
                .collect(Collectors.toMap(CleanForecastDataPO::getProductCode,
                        x -> detailMap.getOrDefault(x.getId(), new HashMap<>(2)),
                        (k1, k2) -> k2));
    }

    @Override
    public void saveDemandForecastReview(DemandForecastReviewDTO demandForecastReviewDTO) {
        OemPO oemPO = oemDao.selectByOemCode(demandForecastReviewDTO.getOemCode());
        oemPO.setRemark(demandForecastReviewDTO.getReviewSummary());
        BasePOUtils.updateFiller(oemPO);
        oemDao.update(oemPO);
    }

    @Override
    public void updateDemandForecast(DemandForecastReviewUpdateDTO demandForecastReviewUpdateDTO) {
        DemandForecastEstablishmentPO establishmentPO =
                demandForecastEstablishmentDao.selectByPrimaryKey(demandForecastReviewUpdateDTO.getDataId());
        if (establishmentPO != null) {
            establishmentPO.setDemandForecast((demandForecastReviewUpdateDTO.getQuantity()));
            establishmentPO.setRemark(demandForecastReviewUpdateDTO.getRemark());
            BasePOUtils.updateFiller(establishmentPO);
            demandForecastEstablishmentDao.update(establishmentPO);
        }
    }

    @Override
    public String queryDemandForecast(String oemCode) {
        OemPO oemPO = oemDao.selectByOemCode(oemCode);
        if (null == oemPO) {
            return "";
        }
        return oemPO.getRemark();
    }

    /**
     * 获取主机厂、产线、车型之间的映射关系
     *
     * @param oemCode 主机厂代码
     * @return java.util.Map<java.lang.String, java.util.List < java.lang.String>>
     */
    @SuppressWarnings("unused")
    private Map<String, List<String>> getRealationMap(String oemCode) {
        // 获取主机厂、产线、车型之间的映射关系
        List<OemProductLineMapPO> oemProductLineMapPOList = oemProductLineMapDao.selectByParams(new HashMap<>(2));
        Map<String, Map<String, List<String>>> relationMap =
                oemProductLineMapPOList.stream().collect(Collectors.groupingBy(OemProductLineMapPO::getOemCode,
                        Collectors.groupingBy(OemProductLineMapPO::getLineCode,
                                Collectors.mapping(OemProductLineMapPO::getVehicleModelCode, Collectors.toList()))));
        return relationMap.get(oemCode);
    }

    /**
     * 将BigDecimal转换为String，如果小数部分全为0，则返回整数形式。
     *
     * @param bd BigDecimal对象
     * @return 格式化后的字符串
     */
    private String formatBigDecimal(BigDecimal bd) {
        // stripTrailingZeros() 方法移除小数点后不必要的0
        // toPlainString() 返回一个没有科学记数法表示的字符串
        return bd.stripTrailingZeros().toPlainString();
    }

    @Override
    public List<String> queryProductCodeDown(DemandForecastReviewDTO demandForecastReviewDTO) {
        DemandForecastVersionVO demandForecastVersion =
                demandForecastVersionService.selectForecastVersion(demandForecastReviewDTO.getVersionCode());
        if (demandForecastVersion == null) {
            return Lists.newArrayList();
        }
        Map<String, Object> params = new HashMap<>();
        params.put(PARAM_FORECAST_VERSION_ID, demandForecastVersion.getId());
        params.put(PARAM_VEHICLE_MODEL_CODE_LIST, demandForecastReviewDTO.getVehicleModelCodeList());
        params.put(PARAM_DEMAND_CATEGORY, demandForecastReviewDTO.getDemandType());
        List<DemandForecastEstablishmentPO> demandForecastEstablishmentPOS =
                demandForecastEstablishmentDao.selectByParams(params);
        return demandForecastEstablishmentPOS.stream().map(DemandForecastEstablishmentPO::getProductCode)
                .distinct().collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> queryProductionLineInfoDown(String versionCode, String oemCode) {
        DemandForecastVersionVO demandForecastVersion =
                demandForecastVersionService.selectForecastVersion(versionCode);
        if (demandForecastVersion == null) {
            return Lists.newArrayList();
        }
        // 获取版本下的需求预测数据，
        List<String> productCodeList = demandForecastEstablishmentDao.selectVOByParams(ImmutableMap
                        .of(PARAM_FORECAST_VERSION_ID, demandForecastVersion.getId(), PARAM_OEM_CODE, oemCode))
                .stream().map(DemandForecastEstablishmentVO::getProductCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodeList)) {
            return Lists.newArrayList();
        }
        // 查询物料数据获取对应的车型信息
        String scenario = SystemHolder.getScenario();
        List<NewProductStockPointVO> productList = newMdsFeign.selectByProductCode(scenario, productCodeList);
        List<String> vehicleModelCodeList = productList.stream().map(NewProductStockPointVO::getVehicleModelCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        // 通过产线映射关系获取车型对应的产线数据
        List<OemProductLineMapPO> oemProductLineMapPOList = oemProductLineMapDao.selectByParams(ImmutableMap
                .of(PARAM_VEHICLE_MODEL_CODE_LIST, vehicleModelCodeList, PARAM_OEM_CODE, oemCode));
        List<String> lineCodeList = oemProductLineMapPOList.stream()
                .map(OemProductLineMapPO::getLineCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodeList)) {
            return Lists.newArrayList();
        }
        // 获取本厂编码下的产线信息
        List<OemProductLinePO> oemProductLineList = oemProductLineDao.selectByParams(ImmutableMap
                .of(PARAM_OEM_CODE, oemCode));
        return oemProductLineList.stream()
                .filter(item -> lineCodeList.contains(item.getLineCode()))
                .map(oemProductLinePo -> new LabelValue<>(oemProductLinePo.getLineCode(),
                        oemProductLinePo.getLineName())).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> queryVehicleInfoDown(String versionCode, String oemCode,
                                                         String productionLineCode) {
        DemandForecastVersionVO demandForecastVersion =
                demandForecastVersionService.selectForecastVersion(versionCode);
        if (demandForecastVersion == null) {
            return Lists.newArrayList();
        }
        // 获取版本下的需求预测数据，
        List<String> productCodeList = demandForecastEstablishmentDao.selectVOByParams(ImmutableMap
                        .of(PARAM_FORECAST_VERSION_ID, demandForecastVersion.getId(), PARAM_OEM_CODE, oemCode))
                .stream().map(DemandForecastEstablishmentVO::getProductCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodeList)) {
            return Lists.newArrayList();
        }
        // 查询物料数据获取对应的车型信息
        String scenario = SystemHolder.getScenario();
        List<NewProductStockPointVO> productList = newMdsFeign.selectByProductCode(scenario, productCodeList);
        List<String> vehicleModelCodeList = productList.stream().map(NewProductStockPointVO::getVehicleModelCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        // 通过产线映射关系获取车型对应的产线数据
        List<String> productionLineCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(productionLineCode)) {
            String[] split = productionLineCode.split(",");
            productionLineCodeList.addAll(Arrays.asList(split));
        }
        List<OemProductLineMapPO> oemProductLineMapPOList = oemProductLineMapDao.selectByParams(ImmutableMap
                .of(PARAM_VEHICLE_MODEL_CODE_LIST, vehicleModelCodeList,
                        PARAM_OEM_CODE, oemCode, "lineCodeList", productionLineCodeList));
        oemProductLineMapPOList = oemProductLineMapPOList.stream().collect(Collectors.collectingAndThen(Collectors
                        .toCollection(() -> new TreeSet<>(Comparator.comparing(OemProductLineMapPO::getVehicleModelCode))),
                ArrayList::new));
        return oemProductLineMapPOList.stream()
                .map(oemProductLineMapPo -> new LabelValue<>(oemProductLineMapPo.getLineCode(),
                        oemProductLineMapPo.getVehicleModelCode())).distinct().collect(Collectors.toList());
    }

    @Override
    public void addOrUpdateDemandForecast(List<DemandForecastReviewUpdateDTO> list) {
        // 1.获取新增数据物料数据信息
        List<String> productCodes = list.stream().filter(e -> StringUtils.isEmpty(e.getDataId()))
                .map(DemandForecastReviewUpdateDTO::getProductCode).distinct().collect(Collectors.toList());
        Map<String, NewProductStockPointVO> productMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productCodes)) {
            // 获取物理数据信息
            BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(),
                    SystemHolder.getTenantId());
            List<NewProductStockPointVO> productInfoList = newMdsFeign.selectByProductCode(scenario.getData(),
                    productCodes);
            productMap = productInfoList.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode,
                    each -> each, (v1, v2) -> v1));
        }
        List<DemandForecastEstablishmentPO> insertBatchList = new ArrayList<>();
        for (DemandForecastReviewUpdateDTO item : list) {
            if (StringUtils.isNotEmpty(item.getDataId())) {
                // 修改逻辑
                this.updateDemandForecast(item);
            } else {
                // 新增逻辑
                DemandForecastEstablishmentPO add = new DemandForecastEstablishmentPO();
                add.setForecastVersionId(item.getForecastVersionId());
                add.setOemCode(item.getOemCode());
                add.setProductCode(item.getProductCode());
                NewProductStockPointVO productInfo = productMap.get(item.getProductCode());
                if (productInfo == null || StringUtils.isEmpty(productInfo.getLoadingPositionSub())
                        || StringUtils.isEmpty(productInfo.getVehicleModelCode())) {
                    throw new BusinessException("本厂编码：" + item.getProductCode() + "信息缺失!");
                }
                add.setVehicleModelCode(productInfo.getVehicleModelCode());
                add.setAccessPosition(productInfo.getLoadingPositionSub());
                add.setForecastTime(DateUtils.stringToDate(item.getForecastTime(), YYYY_MM));
                add.setCustomerForecast(BigDecimal.ZERO);
                add.setAlgorithmForecast(BigDecimal.ZERO);
                add.setDemandForecast((item.getQuantity()));
                add.setRemark(item.getRemark());
                insertBatchList.add(add);

            }
        }
        if (CollectionUtils.isNotEmpty(insertBatchList)) {
            BasePOUtils.insertBatchFiller(insertBatchList);
            demandForecastEstablishmentDao.insertBatch(insertBatchList);
        }
    }

    private Map<String, String> getPolicyInfo(List<PolicyInformationVO> list, String vehicleCode,
                                              List<String> timeSeriesList) {
        List<PolicyInformationVO> policies = new ArrayList<>();
        for (PolicyInformationVO informationVO : list) {
            if (Objects.isNull(informationVO.getEndTime())) {
                informationVO.setEndTime(informationVO.getBeginTime());
            }
            policies.add(informationVO);
        }
        policies.sort(Comparator.comparing(PolicyInformationVO::getBeginTime));

        Map<String, String> result = new HashMap<>();
        List<PolicyInformationVO> oemPolicies =
                policies.stream().filter(x -> StringUtils.isBlank(x.getVehicleModelCode()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oemPolicies)) {
            return result;
        }
        for (String timeSeries : timeSeriesList) {
            Date fromDate = DateUtils.stringToDate(timeSeries, YYYY_MM);
            Date toDate = DateUtils.getMonthLastDay(fromDate);
            for (PolicyInformationVO policyInformationVO : oemPolicies) {
                if (BizUtils.isOverlapped(fromDate, toDate, policyInformationVO.getBeginTime(),
                        policyInformationVO.getEndTime())) {
                    result.put(timeSeries, policyInformationVO.getRemark());
                    break;
                }
            }
        }

        if (StringUtils.isBlank(vehicleCode)) {
            return result;
        }
        List<PolicyInformationVO> vehiclePolicies =
                policies.stream().filter(x -> vehicleCode.equals(x.getVehicleModelCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehiclePolicies)) {
            return result;
        }
        for (String timeSeries : timeSeriesList) {
            Date fromDate = DateUtils.stringToDate(timeSeries, YYYY_MM);
            Date toDate = DateUtils.getMonthLastDay(fromDate);
            for (PolicyInformationVO policyInformationVO : vehiclePolicies) {
                if (BizUtils.isOverlapped(fromDate, toDate, policyInformationVO.getBeginTime(),
                        policyInformationVO.getEndTime())) {
                    result.put(timeSeries, policyInformationVO.getRemark());
                    break;
                }
            }
        }
        return result;
    }

    private Map<String, String> getMarketInfo(List<MarketInformationVO> list, String vehicleCode,
                                              List<String> timeSeriesList) {
        List<MarketInformationVO> policies = new ArrayList<>();
        for (MarketInformationVO informationVO : list) {
            if (Objects.isNull(informationVO.getEndTime())) {
                informationVO.setEndTime(informationVO.getBeginTime());
            }
            policies.add(informationVO);
        }
        policies.sort(Comparator.comparing(MarketInformationVO::getBeginTime));

        Map<String, String> result = new HashMap<>();
        List<MarketInformationVO> oemMarkets =
                policies.stream().filter(x -> StringUtils.isBlank(x.getVehicleModelCode()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(oemMarkets)) {
            return result;
        }
        for (String timeSeries : timeSeriesList) {
            Date fromDate = DateUtils.stringToDate(timeSeries, YYYY_MM);
            Date toDate = DateUtils.getMonthLastDay(fromDate);
            for (MarketInformationVO marketInformationVO : oemMarkets) {
                if (BizUtils.isOverlapped(fromDate, toDate, marketInformationVO.getBeginTime(),
                        marketInformationVO.getEndTime())) {
                    result.put(timeSeries, marketInformationVO.getRemark());
                    break;
                }
            }
        }

        if (StringUtils.isBlank(vehicleCode)) {
            return result;
        }
        List<MarketInformationVO> vehicleMarkets =
                policies.stream().filter(x -> vehicleCode.equals(x.getVehicleModelCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(vehicleMarkets)) {
            return result;
        }
        for (String timeSeries : timeSeriesList) {
            Date fromDate = DateUtils.stringToDate(timeSeries, YYYY_MM);
            Date toDate = DateUtils.getMonthLastDay(fromDate);
            for (MarketInformationVO marketInformationVO : vehicleMarkets) {
                if (BizUtils.isOverlapped(fromDate, toDate, marketInformationVO.getBeginTime(),
                        marketInformationVO.getEndTime())) {
                    result.put(timeSeries, marketInformationVO.getRemark());
                    break;
                }
            }
        }
        return result;
    }

	@Override
	public String selectMaxDemandForecastVersionId() {
		return demandForecastVersionDao.selectMaxDemandForecastVersionId();
	}

}