package com.yhl.scp.dfp.origin.service.impl;

import cn.hutool.core.util.ZipUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.IOUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.constants.DfpConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionConvertor;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionDetailConvertor;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailDTO;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.origin.convertor.OriginDemandVersionConvertor;
import com.yhl.scp.dfp.origin.domain.entity.OriginDemandVersionDO;
import com.yhl.scp.dfp.origin.domain.service.OriginDemandVersionDomainService;
import com.yhl.scp.dfp.origin.dto.OriginDemandVersionDTO;
import com.yhl.scp.dfp.origin.dto.OriginalDemandCreateDTO;
import com.yhl.scp.dfp.origin.infrastructure.dao.OriginDemandVersionDao;
import com.yhl.scp.dfp.origin.infrastructure.po.OriginDemandVersionPO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO;
import com.yhl.scp.dfp.verison.dto.VersionCreateDTO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>OriginDemandVersionServiceImpl</code>
 * <p>
 * 原始需求版本应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:25
 */
@Slf4j
@Service
public class OriginDemandVersionServiceImpl extends AbstractService implements OriginDemandVersionService {

    public static final String PARAM_VERSION_CODE = "versionCode";

    @Resource
    private OriginDemandVersionDao originDemandVersionDao;

    @Resource
    private OriginDemandVersionDomainService originDemandVersionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private OemService oemService;

    @Resource
    @Lazy
    private LoadingDemandSubmissionService loadingDemandSubmissionService;

    @Resource
    @Lazy
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    private static final ReentrantLock LOCK = new ReentrantLock();

    private static final AtomicInteger SEQUENCE = new AtomicInteger(0);

    @Value("${loadSubmission.file.path:/usr/local/loadSubmission}")
    private String filePath;

    public static void copyFolder(Path source, Path target) throws IOException {
        Files.walkFileTree(source, new SimpleFileVisitor<Path>() {
            @Override
            public @Nonnull FileVisitResult visitFile(@Nonnull Path file, @Nonnull BasicFileAttributes attrs) throws IOException {
                Path targetFile = target.resolve(source.relativize(file));
                Files.copy(file, targetFile);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public @Nonnull FileVisitResult preVisitDirectory(@Nonnull Path dir, @Nonnull BasicFileAttributes attrs) throws IOException {
                Path targetDir = target.resolve(source.relativize(dir));
                Files.createDirectories(targetDir);
                return FileVisitResult.CONTINUE;
            }
        });
    }

    @Override
    public BaseResponse<Void> doCreate(OriginDemandVersionDTO originDemandVersionDTO) {
        // 0.数据转换
        OriginDemandVersionDO originDemandVersionDO =
                OriginDemandVersionConvertor.INSTANCE.dto2Do(originDemandVersionDTO);
        OriginDemandVersionPO originDemandVersionPO =
                OriginDemandVersionConvertor.INSTANCE.dto2Po(originDemandVersionDTO);
        // 1.数据校验
        originDemandVersionDomainService.validation(originDemandVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(originDemandVersionPO);
        originDemandVersionDao.insertWithPrimaryKey(originDemandVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OriginDemandVersionDTO originDemandVersionDTO) {
        // 0.数据转换
        OriginDemandVersionDO originDemandVersionDO =
                OriginDemandVersionConvertor.INSTANCE.dto2Do(originDemandVersionDTO);
        OriginDemandVersionPO originDemandVersionPO =
                OriginDemandVersionConvertor.INSTANCE.dto2Po(originDemandVersionDTO);
        // 1.数据校验
        originDemandVersionDomainService.validation(originDemandVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(originDemandVersionPO);
        originDemandVersionDao.update(originDemandVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OriginDemandVersionDTO> list) {
        List<OriginDemandVersionPO> newList = OriginDemandVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        originDemandVersionDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<OriginDemandVersionDTO> list) {
        List<OriginDemandVersionPO> newList = OriginDemandVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        originDemandVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return originDemandVersionDao.deleteBatch(idList);
        }
        return originDemandVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OriginDemandVersionVO selectByPrimaryKey(String id) {
        OriginDemandVersionPO po = originDemandVersionDao.selectByPrimaryKey(id);
        return OriginDemandVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_origin_demand_version")
    public List<OriginDemandVersionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_origin_demand_version")
    public List<OriginDemandVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OriginDemandVersionVO> dataList = originDemandVersionDao.selectByCondition(sortParam, queryCriteriaParam);
        OriginDemandVersionServiceImpl target = springBeanUtils.getBean(OriginDemandVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OriginDemandVersionVO> selectByParams(Map<String, Object> params) {
        List<OriginDemandVersionPO> list = originDemandVersionDao.selectByParams(params);
        return OriginDemandVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OriginDemandVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<LabelValue<String>> targetVersion(String planPeriod) {
        Map<String, Object> queryMap = new HashMap<>();
        if (StringUtils.isNotBlank(planPeriod)) {
            queryMap.put("planPeriod", planPeriod);
        }
        List<OriginDemandVersionVO> demandVersionVOS = this.selectByParams(queryMap);
        if (CollectionUtils.isEmpty(demandVersionVOS)) {
            return Lists.newArrayList();
        }
        return demandVersionVOS.stream()
                .filter(k -> StringUtils.isBlank(k.getOemCode()) && StringUtils.isNotBlank(k.getParentVersionId()))
                .sorted(Comparator.comparing(OriginDemandVersionVO::getVersionCode).reversed())
                .map(x -> new LabelValue<>(x.getVersionCode(), x.getId()))
                .collect(Collectors.toList());
    }

    @SuppressWarnings("unused")
    public List<String> getOemCodeByVersionCode(String versionCode) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("version_code", versionCode);
        queryMap.put("creator", SystemHolder.getUserId());
        List<OriginDemandVersionVO> demandVersionVOS = this.selectByParams(queryMap);
        if (CollectionUtils.isEmpty(demandVersionVOS)) {
            return Lists.newArrayList();
        }
        return demandVersionVOS.stream().filter(k -> StringUtils.isNotEmpty(k.getOemCode()))
                .map(OriginDemandVersionVO::getVersionCode).collect(Collectors.toList());
    }

    @Override
    public BaseResponse<String> createOriginalVersion(VersionCreateDTO demandCreateDTO) {
        LOCK.lock();
        String newVersionCode;
        String secondId;
        try {
            String targetOriginDemandVersionId = demandCreateDTO.getTargetOriginDemandVersionId();
            String planPeriod = demandCreateDTO.getPlanPeriod();
            List<String> excludeProductCodes = demandCreateDTO.getExcludeProductCodes();

            OriginDemandVersionVO targetDemandVersionVO = this.selectByPrimaryKey(targetOriginDemandVersionId);
            if (null == targetDemandVersionVO) {
                // 获取新建版本号
                newVersionCode = getVersionCode(null, planPeriod);
            } else {
                // 获取新建版本号
                newVersionCode = getVersionCode(targetDemandVersionVO.getVersionCode(), planPeriod);
            }

            // 生成新原始需求
            secondId = originalDemandCreate(newVersionCode, planPeriod, demandCreateDTO, targetDemandVersionVO, excludeProductCodes);
        } finally {
            LOCK.unlock();
        }
        return BaseResponse.<String>builder().data(newVersionCode + "," + secondId).msg("版本创建成功").success(true).build();
    }

    /**
     * 判断层级 目标版本没有 从第一级开始创建 不存在该版本号 从二级创建 存在该版本号 从三级创建
     *
     * @param demandCreateDTO 需求版本创建请求对象
     * @param newVersionCode 新生版本号
     * @return java.lang.Integer
     */
    @SuppressWarnings("unused")
    private Integer getLevel(OriginalDemandCreateDTO demandCreateDTO, String newVersionCode) {
        int level;
        if (StringUtils.isBlank(demandCreateDTO.getVersionCode())) {
            level = 1;
            return level;
        }
        List<OriginDemandVersionPO> demandVersionPOS = originDemandVersionDao.selectByParams(ImmutableMap
                .of(PARAM_VERSION_CODE, newVersionCode));
        if (CollectionUtils.isNotEmpty(demandVersionPOS)) {
            level = 3;
            return level;
        }
        level = 2;
        return level;
    }

    /**
     * 获取最新版本号
     *
     * @param targetVersionCode 原始需求版本
     * @param planPeriod        周期
     * @return java.lang.String
     */
    @SneakyThrows
    private String getVersionCode(String targetVersionCode, String planPeriod) {
        String newVersionCode;
        String prefix = DfpConstants.ORIGIN_DEMAND_VERSION_CODE_PRE + planPeriod;
        if (StringUtils.isBlank(targetVersionCode) || targetVersionCode.length() < 5) {
            SEQUENCE.set(1);
            newVersionCode = String.format("%05d", SEQUENCE.get());
            SEQUENCE.set(SEQUENCE.get() + 1);
        } else {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("planPeriod", planPeriod);
            queryMap.put("parentVersionId", "is not null");
            String latestVersionCode = originDemandVersionDao.selectLatestVersionCode(queryMap);
            if (StringUtils.isBlank(targetVersionCode) || StringUtils.isBlank(latestVersionCode)) {
                SEQUENCE.set(1);
                newVersionCode = String.format("%05d", SEQUENCE.get());
                SEQUENCE.set(SEQUENCE.get() + 1);
            } else {
                // 获取最新需求版本
                String lastFive = latestVersionCode.substring(latestVersionCode.length() - 5);
                int sequenceNumber = SEQUENCE.get();
                if (sequenceNumber == 0) {
                    //服务重启，SEQUENCE则被重置为0，需要获取数据库最大序列
                    sequenceNumber = Integer.parseInt(lastFive);
                    // 数字加1
                    sequenceNumber++;
                    // 格式化为五位数，不足五位补零
                }
                newVersionCode = String.format("%05d", sequenceNumber);
                SEQUENCE.set(sequenceNumber + 1);
            }
        }
        return prefix + newVersionCode;
    }

    /**
     * 生成新原始需求
     *
     * @param newVersionCode 版本编码
     * @param planPeriod     计划周期
     * @param demandCreateDTO     需求版本创建请求对象
     * @param targetDemandVersionVO     源版本
     * @return java.lang.String
     */
    private String originalDemandCreate(String newVersionCode, String planPeriod,
                                        VersionCreateDTO demandCreateDTO, OriginDemandVersionVO targetDemandVersionVO,
                                        List<String> excludeProductCodes) {
        // 一级版本ID
        String firstLevelId;
        if (null == targetDemandVersionVO) {
            // 一级版本生成
            OriginDemandVersionPO firstOriginDemandVersionPO = new OriginDemandVersionPO();
            firstOriginDemandVersionPO.setPlanPeriod(planPeriod);
            BasePOUtils.insertFiller(firstOriginDemandVersionPO);
            originDemandVersionDao.insertWithPrimaryKey(firstOriginDemandVersionPO);
            firstLevelId = firstOriginDemandVersionPO.getId();

            // 二级版本生成
            OriginDemandVersionPO nextOriginDemandVersionPO = new OriginDemandVersionPO();
            nextOriginDemandVersionPO.setPlanPeriod(planPeriod);
            nextOriginDemandVersionPO.setVersionCode(newVersionCode);
            nextOriginDemandVersionPO.setParentVersionId(firstLevelId);
            nextOriginDemandVersionPO.setGenerateType(demandCreateDTO.getGenerateType());
            BasePOUtils.insertFiller(nextOriginDemandVersionPO);
            originDemandVersionDao.insertWithPrimaryKey(nextOriginDemandVersionPO);
            return originDemandVersionDao.selectOriginalVersionIdByVersionCode(ImmutableMap.of(PARAM_VERSION_CODE, newVersionCode));
        }

        String targetVersionCode = targetDemandVersionVO.getVersionCode();
        String targetVersionId = targetDemandVersionVO.getId();
        if (StringUtils.isBlank(targetVersionCode)) {
            // 一级版本生成
            OriginDemandVersionPO firstOriginDemandVersionPO = new OriginDemandVersionPO();
            firstOriginDemandVersionPO.setPlanPeriod(planPeriod);
            BasePOUtils.insertFiller(firstOriginDemandVersionPO);
            originDemandVersionDao.insertWithPrimaryKey(firstOriginDemandVersionPO);
            firstLevelId = firstOriginDemandVersionPO.getId();
        } else {
            // 获取一级版本ID
            OriginDemandVersionPO originDemandVersionPO =
                    originDemandVersionDao.selectFirstVersionInfoByPlanPeriod(planPeriod);
            if (Objects.isNull(originDemandVersionPO)) {
                // 一级版本生成
                OriginDemandVersionPO firstOriginDemandVersionPO = new OriginDemandVersionPO();
                firstOriginDemandVersionPO.setPlanPeriod(planPeriod);
                BasePOUtils.insertFiller(firstOriginDemandVersionPO);
                originDemandVersionDao.insertWithPrimaryKey(firstOriginDemandVersionPO);
                firstLevelId = firstOriginDemandVersionPO.getId();
            } else {
                firstLevelId = originDemandVersionPO.getId();
            }
        }

        // 二级版本生成
        OriginDemandVersionPO nextOriginDemandVersionPO = new OriginDemandVersionPO();
        nextOriginDemandVersionPO.setPlanPeriod(planPeriod);
        nextOriginDemandVersionPO.setVersionCode(newVersionCode);
        nextOriginDemandVersionPO.setParentVersionId(firstLevelId);
        nextOriginDemandVersionPO.setGenerateType(demandCreateDTO.getGenerateType());
        BasePOUtils.insertFiller(nextOriginDemandVersionPO);
        originDemandVersionDao.insertWithPrimaryKey(nextOriginDemandVersionPO);
        // 当前版本的二级ID
        String secondId = originDemandVersionDao.selectOriginalVersionIdByVersionCode(ImmutableMap.of(PARAM_VERSION_CODE, newVersionCode));

        if (StringUtils.isBlank(targetVersionCode)) {
            return "";
        }

        log.info("装车业务预测数据拷贝");
        Map<String, Object> loadingQueryMap = new HashMap<>();
        loadingQueryMap.put("versionId", targetVersionId);
        // 查询上个版本的装车需求数据
        List<LoadingDemandSubmissionVO> targetLoadingSubmissions = loadingDemandSubmissionService.selectByParams(loadingQueryMap);
        if (CollectionUtils.isNotEmpty(excludeProductCodes)) {
            targetLoadingSubmissions = targetLoadingSubmissions.stream().filter(x ->
                    !excludeProductCodes.contains(x.getProductCode())).collect(Collectors.toList());
        }

        List<String> targetSubmissionIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(targetLoadingSubmissions)) {
            targetSubmissionIds.addAll(targetLoadingSubmissions.stream().map(LoadingDemandSubmissionVO::getId)
                    .distinct().collect(Collectors.toList()));
        }

        // 目标版本submission map
        Map<String, List<LoadingDemandSubmissionVO>> targetLoadingSubmissionMap =
                targetLoadingSubmissions.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionVO::getOemCode));

        Map<String, Object> loadingDetailQueryMap = new HashMap<>();
        loadingDetailQueryMap.put("submissionIds", targetSubmissionIds);
        // 上个版本装车需求明细数据
        List<LoadingDemandSubmissionDetailVO> targetLoadingSubmissionDetails =
                loadingDemandSubmissionDetailService.selectByParams(loadingDetailQueryMap);
        // 目标版本submission detail map
        Map<String, List<LoadingDemandSubmissionDetailVO>> targetLoadingSubmissionDetaiMap = targetLoadingSubmissionDetails.stream()
                .collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId));

        List<LoadingDemandSubmissionVO> newSubmissionVOs = Lists.newArrayList();
        List<LoadingDemandSubmissionDetailVO> newSubmissionDetailVOs = Lists.newArrayList();

        if (!targetLoadingSubmissionMap.isEmpty()) {
            for (Map.Entry<String, List<LoadingDemandSubmissionVO>> entry : targetLoadingSubmissionMap.entrySet()) {
                for (LoadingDemandSubmissionVO loadingDemandSubmissionVO : entry.getValue()) {
                    String oldSubmissionId = loadingDemandSubmissionVO.getId();
                    String newSubmissionId = UUIDUtil.getUUID();
                    loadingDemandSubmissionVO.setId(newSubmissionId);
                    loadingDemandSubmissionVO.setVersionId(nextOriginDemandVersionPO.getId());
                    newSubmissionVOs.add(loadingDemandSubmissionVO);
                    List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS =
                            targetLoadingSubmissionDetaiMap.get(oldSubmissionId);
                    if (CollectionUtils.isEmpty(loadingDemandSubmissionDetailVOS)) {
                        continue;
                    }
                    handleSubmissionDetailData(newSubmissionDetailVOs, loadingDemandSubmissionDetailVOS, newSubmissionId,
                            planPeriod);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(newSubmissionVOs)) {
            List<LoadingDemandSubmissionDTO> submissionList = LoadingDemandSubmissionConvertor.INSTANCE.vo2Dtos(newSubmissionVOs);
            Lists.partition(submissionList, 500)
                    .forEach(subList -> loadingDemandSubmissionService.doCreateBatch(subList));
        }
        if (CollectionUtils.isNotEmpty(newSubmissionDetailVOs)) {
            List<LoadingDemandSubmissionDetailDTO> detailList = LoadingDemandSubmissionDetailConvertor.INSTANCE.vo2Dtos(newSubmissionDetailVOs);
            Lists.partition(detailList, 500)
                    .forEach(subList -> loadingDemandSubmissionDetailService.doCreateBatch(subList));
        }
        log.info("装车业务预测数据拷贝完成");
        return secondId;
    }

    /**
     * 处理装车业务预测数据
     *
     * @param newSubmissionDetailVOs 新明细数据集合
     * @param loadingDemandSubmissionDetailVOS 旧明细数据
     * @param newSubmissionId 新提报ID
     * @param planPeriod 计划周期
     */
    private void handleSubmissionDetailData(List<LoadingDemandSubmissionDetailVO> newSubmissionDetailVOs,
                                            List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS,
                                            String newSubmissionId, String planPeriod) {
        Map<String, List<LoadingDemandSubmissionDetailVO>> submissionTypeMap = loadingDemandSubmissionDetailVOS.stream()
                .filter(item -> StringUtils.isNotBlank(item.getSubmissionType()))
                .collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionType));
        for (Map.Entry<String, List<LoadingDemandSubmissionDetailVO>> entry : submissionTypeMap.entrySet()) {
            String submissionType = entry.getKey();
            List<LoadingDemandSubmissionDetailVO> detailVOS = entry.getValue();
            Map<String, LoadingDemandSubmissionDetailVO> detailVOMap =
                    detailVOS.stream().collect(Collectors.toMap(LoadingDemandSubmissionDetailVO::getDemandTime,
                            Function.identity(), (v1, v2) -> v1));
            if (GranularityEnum.DAY.getCode().equals(submissionType)) {
                Date planDate = DateUtils.stringToDate(planPeriod, "yyyyMMdd");
                Date startDate = org.apache.commons.lang3.time.DateUtils.addDays(planDate, 0);
                Date endDate = org.apache.commons.lang3.time.DateUtils.addDays(planDate, 30);
                while (!startDate.after(endDate)) {
                    String dateKey = DateUtils.dateToString(startDate, DateUtils.COMMON_DATE_STR3);
                    if (detailVOMap.containsKey(dateKey)) {
                        LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO = detailVOMap.get(dateKey);
                        loadingDemandSubmissionDetailVO.setId(UUIDUtil.getUUID());
                        loadingDemandSubmissionDetailVO.setSubmissionId(newSubmissionId);
                        newSubmissionDetailVOs.add(loadingDemandSubmissionDetailVO);
                    } else {
                        LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO =
                                new LoadingDemandSubmissionDetailVO();
                        loadingDemandSubmissionDetailVO.setId(UUIDUtil.getUUID());
                        loadingDemandSubmissionDetailVO.setSubmissionId(newSubmissionId);
                        loadingDemandSubmissionDetailVO.setDemandTime(dateKey);
                        loadingDemandSubmissionDetailVO.setSubmissionType(submissionType);
                        newSubmissionDetailVOs.add(loadingDemandSubmissionDetailVO);
                    }
                    startDate = org.apache.commons.lang3.time.DateUtils.addDays(startDate, 1);
                }
            } else if (GranularityEnum.MONTH.getCode().equals(submissionType)) {
                Date planDate = DateUtils.stringToDate(planPeriod, "yyyyMMdd");
                Date startDate = org.apache.commons.lang3.time.DateUtils.addMonths(planDate, 0);
                Date endDate = org.apache.commons.lang3.time.DateUtils.addMonths(planDate, 12);
                while (!startDate.after(endDate)) {
                    String dateKey = DateUtils.dateToString(startDate, DateUtils.YEAR_MONTH);
                    if (detailVOMap.containsKey(dateKey)) {
                        LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO = detailVOMap.get(dateKey);
                        loadingDemandSubmissionDetailVO.setId(UUIDUtil.getUUID());
                        loadingDemandSubmissionDetailVO.setSubmissionId(newSubmissionId);
                        newSubmissionDetailVOs.add(loadingDemandSubmissionDetailVO);
                    } else {
                        LoadingDemandSubmissionDetailVO loadingDemandSubmissionDetailVO =
                                new LoadingDemandSubmissionDetailVO();
                        loadingDemandSubmissionDetailVO.setId(UUIDUtil.getUUID());
                        loadingDemandSubmissionDetailVO.setSubmissionId(newSubmissionId);
                        loadingDemandSubmissionDetailVO.setDemandTime(dateKey);
                        loadingDemandSubmissionDetailVO.setSubmissionType(submissionType);
                        newSubmissionDetailVOs.add(loadingDemandSubmissionDetailVO);
                    }
                    startDate = org.apache.commons.lang3.time.DateUtils.addMonths(startDate, 1);
                }
            }
        }
    }

    /**
     * 拷贝文件
     *
     * @param sourceVersionId 源版本ID
     * @param targetVersionId 目标版本ID
     * @param oemCodeResource 主机厂资源集合
     */
    @SneakyThrows
    @SuppressWarnings("unused")
    private void copyFile(String sourceVersionId, String targetVersionId, Set<String> oemCodeResource) {
        String sourceVersionPathStr = filePath + File.separator + sourceVersionId;
        File sourceVersionFile = new File(sourceVersionPathStr);
        if (!sourceVersionFile.exists()) {
            return;
        }
        Path sourceVersionPath = Paths.get(sourceVersionPathStr);
        String targetVersionPathStr = filePath + File.separator + targetVersionId;
        File targetVersionFile = new File(targetVersionPathStr);
        if (targetVersionFile.mkdirs()) {
            log.info("targetVersionId:{}创建目录成功", targetVersionId);
        } else {
            log.error("targetVersionId:{}创建目录失败或目录已存在", targetVersionId);
        }
        Path targetVersionPath = Paths.get(targetVersionPathStr);
        copyFolder(sourceVersionPath, targetVersionPath);
        for (String oemCode : oemCodeResource) {
            String oemFile = targetVersionPathStr + File.separator + oemCode;
            File targetOemFile = new File(oemFile);
            if (targetOemFile.exists()) {
                boolean delete = targetOemFile.delete();
                log.info("文件：{}，删除{}", oemFile, delete ? "成功" : "失败");
            }
        }
    }

    /**
     * 添加子节点
     *
     * @param childVersionGroup 子版本组
     * @param originDemandVersionVO 源版本
     */
    private void toDemandVersionTreeVO(Map<String, List<OriginDemandVersionVO>> childVersionGroup,
                                       OriginDemandVersionVO originDemandVersionVO) {
        if (childVersionGroup.containsKey(originDemandVersionVO.getId())) {
            List<OriginDemandVersionVO> demandVersionVOList = new ArrayList<>();
            for (OriginDemandVersionVO demandVersionVO : childVersionGroup.get(originDemandVersionVO.getId())) {
                toDemandVersionTreeVO(childVersionGroup, demandVersionVO);
                demandVersionVOList.add(demandVersionVO);
            }
            demandVersionVOList = demandVersionVOList.stream().sorted(Comparator
                    .comparing(OriginDemandVersionVO::getVersionCode)).collect(Collectors.toList());
            originDemandVersionVO.setDemandVersionVOList(demandVersionVOList);
        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.ORIGIN_DEMAND_VERSION.getCode();
    }

    @Override
    public List<OriginDemandVersionVO> invocation
            (List<OriginDemandVersionVO> dataList, Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        List<String> planPeriods =
                dataList.stream().map(OriginDemandVersionVO::getPlanPeriod).collect(Collectors.toList());
        List<OriginDemandVersionVO> excludeFirstData = originDemandVersionDao.selectDataExcludeFirstLevel(planPeriods);
        if (CollectionUtils.isEmpty(excludeFirstData)) {
            return dataList;
        }
        // 其他层根据父级版本号分组
        Map<String, List<OriginDemandVersionVO>> childVersionGroup = excludeFirstData.stream()
                .collect(Collectors.groupingBy(OriginDemandVersionVO::getParentVersionId));
        // 组装为树结构返回前端
        for (OriginDemandVersionVO originDemandVersionVO : dataList) {
            toDemandVersionTreeVO(childVersionGroup, originDemandVersionVO);
        }
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isEmpty(versionDTOList)) {
            return 0;
        }
        originDemandVersionDomainService.checkDelete(versionDTOList);
        return originDemandVersionDao.deleteBatchVersion(versionDTOList);
    }

    @SneakyThrows
    @Override
    public void downloadSubmissionFiles(String versionId, String oemCode, HttpServletResponse response) {
        String versionOemFilePath = filePath + File.separator + versionId + File.separator + oemCode;
        versionOemFilePath = IOUtils.cleanseDir(false, versionOemFilePath);
        if (versionOemFilePath.endsWith("/")) {
            versionOemFilePath = versionOemFilePath.substring(0, versionOemFilePath.length() - 1);
        }
        log.info("versionOemFilePath is {}", versionOemFilePath);
        File file = new File(versionOemFilePath);
        if (file.exists()) {
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=" + oemCode + ".zip");
            OutputStream os = response.getOutputStream();
            ZipUtil.zip(os, Charset.defaultCharset(), false, null, file);
        } else {
            log.error("文件目录不存在！");
            throw new BusinessException("文件目录不存在！");
        }
    }

    @Override
    public BaseResponse<List<LabelValue<String>>> dropDownOemCode(String targetVersionId) {
        List<LabelValue<String>> result = Lists.newArrayList();
        List<OemVO> oemVOS = oemService.selectAll();
        if (CollectionUtils.isEmpty(oemVOS)) {
            return BaseResponse.success(result);
        }
        List<String> existOemCodes = Lists.newArrayList();
        if (StringUtils.isNotBlank(targetVersionId)) {
            Map<String, Object> parentParams = new HashMap<>();
            parentParams.put("parentVersionId", targetVersionId);
            List<OriginDemandVersionVO> originDemandVersionVOS = this.selectByParams(parentParams);
            if (CollectionUtils.isNotEmpty(originDemandVersionVOS)) {
                existOemCodes.addAll(originDemandVersionVOS.stream().map(OriginDemandVersionVO::getOemCode)
                        .distinct().collect(Collectors.toList()));
            }
        }
        oemVOS.stream().filter(x -> existOemCodes.contains(x.getOemCode()))
                .forEach(x -> {
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setValue(x.getOemCode());
                    labelValue.setLabel(x.getOemName());
                    result.add(labelValue);
                });
        return BaseResponse.success(result);
    }

    @Override
    public int updateSubmissionType(String originVersionId, String oemCode, String contentType) {
        return originDemandVersionDao.updateSubmissionType(originVersionId, oemCode, contentType);
    }

    @Override
    public BaseResponse<List<LabelValue<String>>> getVersionByForecast(String planPeriod) {
        List<LabelValue<String>> result = Lists.newArrayList();
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("likePlanPeriod", planPeriod);
        List<OriginDemandVersionVO> demandVersionVOS = this.selectByParams(queryMap);
        if (CollectionUtils.isEmpty(demandVersionVOS)) {
            return BaseResponse.success(result);
        }
        result.addAll(demandVersionVOS.stream()
                .filter(k -> StringUtils.isBlank(k.getOemCode()) && StringUtils.isNotBlank(k.getParentVersionId()))
                .sorted(Comparator.comparing(OriginDemandVersionVO::getModifyTime).reversed())
                .map(x -> new LabelValue<>(x.getVersionCode(), x.getId()))
                .collect(Collectors.toList()));
        return BaseResponse.success(result);
    }

    @Override
    public String selectPreVersionId(String planPeriod) {
        return originDemandVersionDao.selectPreVersionId(planPeriod);
    }

    @Override
    public String selectPreVersionIdByLike(String planPeriod) {
        return originDemandVersionDao.selectPreVersionIdByLike(planPeriod);
    }

    @Override
    public String selectLatestVersionId() {
        return originDemandVersionDao.selectLatestVersionId();
    }

    @Override
    public OriginDemandVersionVO selectLastVersionByPlanPeriod(String planPeriod) {
        OriginDemandVersionPO originDemandVersionPO = originDemandVersionDao.selectLastVersionByPlanPeriod(planPeriod);
        if (null != originDemandVersionPO) {
            return OriginDemandVersionConvertor.INSTANCE.po2Vo(originDemandVersionPO);
        }
        return null;
    }

}