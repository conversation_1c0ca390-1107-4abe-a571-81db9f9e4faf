package com.yhl.scp.dfp.report.service.impl;

import com.github.pagehelper.page.PageMethod;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.utils.MiscellaneousUtils;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastVersionDao;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.report.infrastructure.dao.CustomerOrderWeeklyReportDao;
import com.yhl.scp.dfp.report.service.CustomerOrderWeeklyReportService;
import com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportSimpleVO;
import com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import io.seata.common.util.CollectionUtils;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.IsoFields;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <code>CustomerOrderWeeklyReportServiceImpl</code>
 * <p>
 * CustomerOrderWeeklyReportServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-12 19:23:52
 */
@Service
@Slf4j
public class CustomerOrderWeeklyReportServiceImpl implements CustomerOrderWeeklyReportService {

    @Resource
    private CustomerOrderWeeklyReportDao customerOrderWeeklyReportDao;

    @Resource
    private ConsistenceDemandForecastVersionDao consistenceDemandForecastVersionDao;

    @Resource
    private ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;

    @Resource
    private IpsFeign ipsFeign;

    @Override
    public List<LabelValue<String>> selectVersionDropdown() {
        return customerOrderWeeklyReportDao.selectVersionDropdown();
    }

    @Override
    public List<String> selectWeekSequence(String versionId) {
        ConsistenceDemandForecastVersionVO version = consistenceDemandForecastVersionService.selectByPrimaryKey(versionId);
        Date createTime = version.getCreateTime();
        List<String> result = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            Date date = DateUtils.moveCalendar(createTime, Calendar.DAY_OF_YEAR, 7 * i);
            int weekSequence = getWeekSequence(MiscellaneousUtils.date2LocalDate(date));
            if (weekSequence < 10) {
                result.add("W0" + weekSequence);
            } else {
                result.add("W" + weekSequence);
            }
        }
        return result;
    }

    @Override
    public List<CustomerOrderWeeklyReportVO> selectByVersionId(String versionId) {
        return customerOrderWeeklyReportDao.selectByVersionId(versionId);
    }

    @Override
    public List<CustomerOrderWeeklyReportVO> selectByPage(Pagination pagination, String sortParam,
                                                          String queryCriteriaParam, String versionId) {
        PageMethod.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam, versionId);
    }

    @Override
    public List<CustomerOrderWeeklyReportVO> selectByCondition(String sortParam, String queryCriteriaParam,
                                                               String versionId) {
        ConsistenceDemandForecastVersionVO version = consistenceDemandForecastVersionService.selectByPrimaryKey(versionId);
        String planPeriod = version.getPlanPeriod();
        Date planPeriodFirstDay = DateUtils.moveCalendar(DateUtils
                .stringToDate(planPeriod + "01", "yyyyMMdd"), Calendar.MONTH, 1);
        Date planPeriodLastDay = DateUtils.getMonthLastDay(planPeriodFirstDay);
        String firstDay = DateUtils.dateToString(planPeriodFirstDay);
        String lastDay = DateUtils.dateToString(planPeriodLastDay);
        Date createTime = version.getCreateTime();
        String scenario = SystemHolder.getScenario();
        List<CustomerOrderWeeklyReportVO> dataList = customerOrderWeeklyReportDao.selectByCondition(sortParam,
                queryCriteriaParam, firstDay, lastDay);
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        // 分组聚合
        Map<String, List<CustomerOrderWeeklyReportVO>> weeklyGroup = dataList.stream().collect(Collectors
                .groupingBy(x -> String.join(Constants.DELIMITER, x.getOemCode(),
                        x.getOemName(), x.getCustomerCode(), x.getVehicleModelCode(), x.getAccessPosition())));
        List<CustomerOrderWeeklyReportVO> newDataList = new ArrayList<>();
        for (Map.Entry<String, List<CustomerOrderWeeklyReportVO>> entry : weeklyGroup.entrySet()) {
            String key = entry.getKey();
            String[] keyArray = key.split(Constants.DELIMITER);
            List<CustomerOrderWeeklyReportVO> value = entry.getValue();
            BigDecimal customerForecastQuantity =
                    value.stream().map(CustomerOrderWeeklyReportVO::getCustomerForecastQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal demandForecastQuantity =
                    value.stream().map(CustomerOrderWeeklyReportVO::getDemandForecastQuantity)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
            CustomerOrderWeeklyReportVO build = CustomerOrderWeeklyReportVO.builder()
                    .oemCode(keyArray[0]).oemName(keyArray[1]).customerCode(keyArray[2]).vehicleModelCode(keyArray[3])
                    .accessPosition(keyArray[4]).customerForecastQuantity(customerForecastQuantity)
                    .demandForecastQuantity(demandForecastQuantity).build();
            newDataList.add(build);
        }

        List<String> oemCodes = newDataList.stream().map(CustomerOrderWeeklyReportVO::getOemCode)
                .distinct().collect(Collectors.toList());
        List<String> vehicleModelCodes = newDataList.stream().map(CustomerOrderWeeklyReportVO::getVehicleModelCode)
                .distinct().collect(Collectors.toList());
        // 查询版本数据
        String weekDay = ipsFeign.getByCollectionCode("CUSTOMER_ORDER_WEEKLY_REPORT_WEEK_DAY").stream()
                .map(CollectionValueVO::getCollectionValue).findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置CUSTOMER_ORDER_WEEKLY_REPORT_WEEK_DAY"));
        List<Date> timeSeries = getTimeSeries(createTime, Integer.parseInt(weekDay));
        List<String> dateList = timeSeries.stream().map(DateUtils::dateToString)
                .distinct().sorted().collect(Collectors.toList());
        Map<String, String> date2VersionIdMap = consistenceDemandForecastVersionDao.selectVersionByDateList(dateList)
                        .stream().collect(Collectors.toMap(x ->
                        DateUtils.dateToString(x.getCreateTime()), ConsistenceDemandForecastVersionVO::getId,
                        (v1, v2) -> v1));

        List<Date> dateItemList = convert2MondayList(timeSeries);
        Date firstMonday = dateItemList.get(0);
        Date lastMonday = dateItemList.get(3);
        Date lastSunday = DateUtils.moveCalendar(lastMonday, Calendar.DAY_OF_YEAR, 6);
        Date minTime = planPeriodFirstDay.getTime() <= firstMonday.getTime() ?
                new Date(planPeriodFirstDay.getTime()) : new Date(firstMonday.getTime());
        Date maxTime = planPeriodLastDay.getTime() <= lastSunday.getTime() ?
                new Date(lastSunday.getTime()) : new Date(planPeriodLastDay.getTime());
        ExecutorService executor = Executors.newFixedThreadPool(8);
        try {
            // 查询明细数据
            CompletableFuture<Map<String, BigDecimal>> week1Future = getWeekFuture(timeSeries.get(0), date2VersionIdMap,
                    firstDay, lastDay, scenario, executor);
            CompletableFuture<Map<String, BigDecimal>> week2Future = getWeekFuture(timeSeries.get(1), date2VersionIdMap,
                    firstDay, lastDay, scenario, executor);
            CompletableFuture<Map<String, BigDecimal>> week3Future = getWeekFuture(timeSeries.get(2), date2VersionIdMap,
                    firstDay, lastDay, scenario, executor);
            CompletableFuture<Map<String, BigDecimal>> week4Future = getWeekFuture(timeSeries.get(3), date2VersionIdMap,
                    firstDay, lastDay, scenario, executor);
            CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> warehouseReleaseFuture =
                    getWarehouseReleaseFuture(minTime, maxTime, oemCodes, vehicleModelCodes, scenario, executor);
            CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> warehouseToWarehouseFuture =
                    getWarehouseToWarehouseFuture(minTime, maxTime, oemCodes, vehicleModelCodes, scenario, executor);
            CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> deliveryPlanPublishedFuture =
                    getDeliveryPlanPublishedFuture(minTime, maxTime, oemCodes, vehicleModelCodes, scenario, executor);

            CompletableFuture.allOf(week1Future, week2Future, week3Future, week4Future, warehouseReleaseFuture,
                    warehouseToWarehouseFuture, deliveryPlanPublishedFuture);
            Map<String, BigDecimal> week1Map = week1Future.join();
            Map<String, BigDecimal> week2Map = week2Future.join();
            Map<String, BigDecimal> week3Map = week3Future.join();
            Map<String, BigDecimal> week4Map = week4Future.join();
            List<CustomerOrderWeeklyReportSimpleVO> warehouseReleaseList = warehouseReleaseFuture.join();
            List<CustomerOrderWeeklyReportSimpleVO> warehouseToWarehouseList = warehouseToWarehouseFuture.join();
            List<CustomerOrderWeeklyReportSimpleVO> deliveryPlanPublishedList = deliveryPlanPublishedFuture.join();
            // 月发货
            Map<String, BigDecimal> warehouseReleaseMonthlyQtyMap = getQtyMapByTimePeriod(warehouseReleaseList,
                    planPeriodFirstDay, planPeriodLastDay);
            Map<String, BigDecimal> warehouseToWarehouseMonthlyQtyMap = getQtyMapByTimePeriod(warehouseToWarehouseList,
                    planPeriodFirstDay, planPeriodLastDay);
            Map<String, BigDecimal> deliveryMonthlyQtyMap = getQtyMapByTimePeriod(deliveryPlanPublishedList,
                    planPeriodFirstDay, planPeriodLastDay);
            // 周发货
            List<Map<String, BigDecimal>> warehouseReleaseWeeklyQtyMapList = new ArrayList<>();
            List<Map<String, BigDecimal>> warehouseToWarehouseWeeklyQtyMapList = new ArrayList<>();
            List<Map<String, BigDecimal>> deliveryWeeklyQtyMapList = new ArrayList<>();
            for (Date weekStartDate : dateItemList) {
                Date weekEndDate = DateUtils.moveCalendar(weekStartDate, Calendar.DAY_OF_YEAR, 6);
                Map<String, BigDecimal> warehouseReleaseWeeklyQtyMap =
                        getQtyMapByTimePeriod(warehouseReleaseList, weekStartDate, weekEndDate);
                warehouseReleaseWeeklyQtyMapList.add(warehouseReleaseWeeklyQtyMap);
                Map<String, BigDecimal> warehouseToWarehouseWeeklyQtyMap =
                        getQtyMapByTimePeriod(warehouseToWarehouseList, weekStartDate, weekEndDate);
                warehouseToWarehouseWeeklyQtyMapList.add(warehouseToWarehouseWeeklyQtyMap);
                Map<String, BigDecimal> deliveryWeeklyQtyMap =
                        getQtyMapByTimePeriod(deliveryPlanPublishedList, weekStartDate, weekEndDate);
                deliveryWeeklyQtyMapList.add(deliveryWeeklyQtyMap);
            }

            // 核心逻辑
            for (CustomerOrderWeeklyReportVO item : newDataList) {
                String oemCode = item.getOemCode();
                String vehicleModelCode = item.getVehicleModelCode();
                BigDecimal customerForecastQuantity = item.getCustomerForecastQuantity();
                String unionKey = String.join(Constants.DELIMITER, oemCode, vehicleModelCode);

                BigDecimal week1ForecastQuantity = week1Map.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setWeek1ForecastQuantity(week1ForecastQuantity);
                item.setWeek1ForecastDeviation(null);
                BigDecimal week2ForecastQuantity = week2Map.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setWeek2ForecastQuantity(week2ForecastQuantity);
                item.setWeek2ForecastDeviation(null);
                BigDecimal week3ForecastQuantity = week3Map.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setWeek3ForecastQuantity(week3ForecastQuantity);
                item.setWeek3ForecastDeviation(null);
                BigDecimal week4ForecastQuantity = week4Map.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setWeek4ForecastQuantity(week4ForecastQuantity);
                item.setWeek4ForecastDeviation(null);

                BigDecimal warehouseReleaseQty = warehouseReleaseMonthlyQtyMap.getOrDefault(unionKey, BigDecimal.ZERO);
                BigDecimal warehouseToWarehouseQty = warehouseToWarehouseMonthlyQtyMap.getOrDefault(unionKey, BigDecimal.ZERO);
                BigDecimal monthlyShippedQty = warehouseReleaseQty.add(warehouseToWarehouseQty);
                item.setMonthlyShippedQuantity(monthlyShippedQty);
                BigDecimal monthlyDeliveryQty = deliveryMonthlyQtyMap.getOrDefault(unionKey, BigDecimal.ZERO);
                item.setMonthlyDeliveryQuantity(monthlyDeliveryQty);

                processWeekData(0, unionKey, warehouseReleaseWeeklyQtyMapList, warehouseToWarehouseWeeklyQtyMapList,
                        deliveryWeeklyQtyMapList, item, CustomerOrderWeeklyReportVO::setWeek1ShippedQuantity,
                        CustomerOrderWeeklyReportVO::setWeek1DeliveryQuantity);
                processWeekData(1, unionKey, warehouseReleaseWeeklyQtyMapList, warehouseToWarehouseWeeklyQtyMapList,
                        deliveryWeeklyQtyMapList, item, CustomerOrderWeeklyReportVO::setWeek2ShippedQuantity,
                        CustomerOrderWeeklyReportVO::setWeek2DeliveryQuantity);
                processWeekData(2, unionKey, warehouseReleaseWeeklyQtyMapList, warehouseToWarehouseWeeklyQtyMapList,
                        deliveryWeeklyQtyMapList, item, CustomerOrderWeeklyReportVO::setWeek3ShippedQuantity,
                        CustomerOrderWeeklyReportVO::setWeek3DeliveryQuantity);
                processWeekData(3, unionKey, warehouseReleaseWeeklyQtyMapList, warehouseToWarehouseWeeklyQtyMapList,
                        deliveryWeeklyQtyMapList, item, CustomerOrderWeeklyReportVO::setWeek4ShippedQuantity,
                        CustomerOrderWeeklyReportVO::setWeek4DeliveryQuantity);

                if (customerForecastQuantity != null && customerForecastQuantity.compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal week1ForecastDeviation = week1ForecastQuantity.subtract(customerForecastQuantity)
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setWeek1ForecastDeviation(week1ForecastDeviation);
                    BigDecimal week2ForecastDeviation = week2ForecastQuantity.subtract(customerForecastQuantity)
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setWeek2ForecastDeviation(week2ForecastDeviation);
                    BigDecimal week3ForecastDeviation = week3ForecastQuantity.subtract(customerForecastQuantity)
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setWeek3ForecastDeviation(week3ForecastDeviation);
                    BigDecimal week4ForecastDeviation = week4ForecastQuantity.subtract(customerForecastQuantity)
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setWeek4ForecastDeviation(week4ForecastDeviation);
                    BigDecimal forecastAchievementRate = item.getMonthlyDeliveryQuantity()
                            .add(item.getMonthlyShippedQuantity())
                            .divide(customerForecastQuantity, 2, RoundingMode.HALF_UP);
                    item.setForecastAchievementRate(forecastAchievementRate);
                }
            }
            // 增加合计行
            return groupAndAggregation(newDataList);
        } finally {
            shutdownExecutorService(executor);
        }
    }

    private void processWeekData(
            int index, String unionKey,
            List<Map<String, BigDecimal>> warehouseReleaseWeeklyQtyMapList,
            List<Map<String, BigDecimal>> warehouseToWarehouseWeeklyQtyMapList,
            List<Map<String, BigDecimal>> deliveryWeeklyQtyMapList,
            CustomerOrderWeeklyReportVO item,
            BiConsumer<CustomerOrderWeeklyReportVO, BigDecimal> shippedSetter,
            BiConsumer<CustomerOrderWeeklyReportVO, BigDecimal> deliverySetter
    ) {
        BigDecimal releaseQty = warehouseReleaseWeeklyQtyMapList.get(index).getOrDefault(unionKey, BigDecimal.ZERO);
        BigDecimal toWarehouseQty = warehouseToWarehouseWeeklyQtyMapList.get(index).getOrDefault(unionKey, BigDecimal.ZERO);
        BigDecimal shippedQty = releaseQty.add(toWarehouseQty);
        BigDecimal deliveryQty = deliveryWeeklyQtyMapList.get(index).getOrDefault(unionKey, BigDecimal.ZERO);

        shippedSetter.accept(item, shippedQty);
        deliverySetter.accept(item, deliveryQty);
    }


    public static Map<String, BigDecimal> getQtyMapByTimePeriod(
            List<CustomerOrderWeeklyReportSimpleVO> simpleList, Date startTime, Date endTime) {
        return simpleList.stream()
                .filter(x -> StringUtils.isNotBlank(x.getOemCode())
                        && StringUtils.isNotBlank(x.getVehicleModelCode())
                        && Objects.nonNull(x.getQuantity())
                        && startTime.getTime() <= x.getTimePoint().getTime()
                        && x.getTimePoint().getTime() <= endTime.getTime()).collect(Collectors
                        .groupingBy(x -> String.join(Constants.DELIMITER,
                                x.getOemCode(), x.getVehicleModelCode()), Collectors.reducing(BigDecimal.ZERO,
                                CustomerOrderWeeklyReportSimpleVO::getQuantity, BigDecimal::add)));
    }

    public static List<Date> convert2MondayList(List<Date> timeSeries) {
        return timeSeries.stream().map(x ->
                MiscellaneousUtils.localDate2Date(MiscellaneousUtils.date2LocalDate(x).with(DayOfWeek.MONDAY)))
                .collect(Collectors.toList());
    }

    private CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> getWarehouseReleaseFuture(Date shippedStartDate,
                                                                                                 Date shippedEndDate,
                                                                                                 List<String> oemCodes,
                                                                                                 List<String> vehicleModelCodes,
                                                                                                 String scenario,
                                                                                                 ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<CustomerOrderWeeklyReportSimpleVO> dataList =
                    customerOrderWeeklyReportDao.selectWarehouseReleaseRecordList(oemCodes, vehicleModelCodes,
                            DateUtils.dateToString(shippedStartDate), DateUtils.dateToString(shippedEndDate));
            DynamicDataSourceContextHolder.clearDataSource();
            return dataList;
        }, executor);
    }

    private CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> getWarehouseToWarehouseFuture(Date shippedStartDate,
                                                                                                     Date shippedEndDate,
                                                                                                     List<String> oemCodes,
                                                                                                     List<String> vehicleModelCodes,
                                                                                                     String scenario,
                                                                                                     ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<CustomerOrderWeeklyReportSimpleVO> dataList =
                    customerOrderWeeklyReportDao.selectWarehouseToWarehouseRecordList(oemCodes, vehicleModelCodes,
                            DateUtils.dateToString(shippedStartDate), DateUtils.dateToString(shippedEndDate));
            DynamicDataSourceContextHolder.clearDataSource();
            return dataList;
        }, executor);
    }

    private CompletableFuture<List<CustomerOrderWeeklyReportSimpleVO>> getDeliveryPlanPublishedFuture(Date shippedStartDate,
                                                                                                     Date shippedEndDate,
                                                                                                     List<String> oemCodes,
                                                                                                     List<String> vehicleModelCodes,
                                                                                                     String scenario,
                                                                                                     ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<CustomerOrderWeeklyReportSimpleVO> dataList =
                    customerOrderWeeklyReportDao.selectDeliveryPlanPublishedList(oemCodes, vehicleModelCodes,
                            DateUtils.dateToString(shippedStartDate), DateUtils.dateToString(shippedEndDate));
            DynamicDataSourceContextHolder.clearDataSource();
            return dataList;
        }, executor);
    }

    /**
     * 分组聚合增加合计行
     *
     * @param dataList 原始数据
     * @return java.util.List<com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportVO>
     */
    private static List<CustomerOrderWeeklyReportVO> groupAndAggregation(List<CustomerOrderWeeklyReportVO> dataList) {
        // 1. 按类别分组并计算每组总和
        Map<String, BigDecimal> categorySums = dataList.stream()
                .collect(Collectors.groupingBy(CustomerOrderWeeklyReportVO::getCustomerCode,
                        Collectors.reducing(BigDecimal.ZERO, CustomerOrderWeeklyReportVO::getCustomerForecastQuantity,
                                BigDecimal::add)));
        Map<String, List<CustomerOrderWeeklyReportVO>> collect = dataList.stream().collect(Collectors
                .groupingBy(CustomerOrderWeeklyReportVO::getCustomerCode));
        // 2. 按类别排序（可自定义排序规则）
        List<Map.Entry<String, BigDecimal>> sortedCategories = new ArrayList<>(categorySums.entrySet());
        sortedCategories.sort(Map.Entry.<String, BigDecimal>comparingByValue().reversed());
        // 3. 构建结果列表：包含原始数据和每组的合计
        List<CustomerOrderWeeklyReportVO> result = new ArrayList<>();
        for (Map.Entry<String, BigDecimal> entry : sortedCategories) {
            String category = entry.getKey();
            BigDecimal sumQty = entry.getValue();
            List<CustomerOrderWeeklyReportVO> items = collect.get(category);
            items.sort(Comparator.comparing(CustomerOrderWeeklyReportVO::getCustomerForecastQuantity).reversed());
            // 添加该类别的原始数据（按原始顺序）
            result.addAll(items);
            CustomerOrderWeeklyReportVO summary = new CustomerOrderWeeklyReportVO();
            summary.setCustomerCode(category + "合计");
            summary.setCustomerForecastQuantity(sumQty);
            summary.setStatisticsRow(true);
            // 添加该类别的合计项（标记为合计）
            result.add(summary);
        }
        return result;
    }

    public static final String IO_EXECUTOR = "ioExecutor";
    /**
     * 关闭线程池
     *
     * @param executor 多线程执行器
     */
    private void shutdownExecutorService(ExecutorService executor) {
        // Helper method to shut down executor services (same as before)
        if (executor != null && !executor.isShutdown()) {
            try {
                log.info("Shutting down {}...", IO_EXECUTOR);
                executor.shutdown();
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    log.warn("{} did not terminate in 10 seconds, forcing shutdown.", IO_EXECUTOR);
                    executor.shutdownNow();
                    if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                        log.error("{} did not terminate even after shutdownNow.", IO_EXECUTOR);
                    }
                }
                log.info("{} shut down successfully.", IO_EXECUTOR);
            } catch (InterruptedException ie) {
                log.error("Shutdown of {} interrupted.", IO_EXECUTOR, ie);
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 获取Future
     *
     * @param dateItem           日期
     * @param date2VersionIdMap  日期版本ID映射
     * @param planPeriodFirstDay 计划周期第一天
     * @param planPeriodLastDay  计划周期最后一天
     * @param scenario           场景名
     * @param executor           执行器
     * @return java.util.concurrent.CompletableFuture<java.util.Map < java.lang.String, java.math.BigDecimal>>
     */
    private CompletableFuture<Map<String, BigDecimal>> getWeekFuture(Date dateItem, Map<String, String> date2VersionIdMap,
                                                                     String planPeriodFirstDay,
                                                                     String planPeriodLastDay,
                                                                     String scenario, ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            String date = DateUtils.dateToString(dateItem);
            if (!date2VersionIdMap.containsKey(date)) {
                return new HashMap<>();
            }
            DynamicDataSourceContextHolder.setDataSource(scenario);
            String versionId = date2VersionIdMap.get(date);
            Map<String, BigDecimal> dataMap = customerOrderWeeklyReportDao.selectByCondition(null,
                    MiscellaneousUtils.assembleEqCondition("versionId", versionId), planPeriodFirstDay,
                            planPeriodLastDay).stream().collect(Collectors.groupingBy(x ->
                            String.join(Constants.DELIMITER, x.getOemCode(), x.getVehicleModelCode()),
                            Collectors.reducing(BigDecimal.ZERO,
                                    CustomerOrderWeeklyReportVO::getCustomerForecastQuantity, BigDecimal::add)));
            DynamicDataSourceContextHolder.clearDataSource();
            return dataMap;
        }, executor);
    }

    /**
     * 获取周次
     *
     * @param localDate 日期
     * @return int 周次
     */
    public static int getWeekSequence(LocalDate localDate) {
        if (Objects.isNull(localDate)) {
            localDate = LocalDate.now();
        }
        return localDate.get(IsoFields.WEEK_OF_WEEK_BASED_YEAR);
    }

    /**
     * 根据周几，输出后四周周一日期
     *
     * @param fromDate 起始日期
     * @param weekDay  周几
     * @return java.util.List<java.util.Date>
     */
    public static List<Date> getTimeSeries(Date fromDate, int weekDay) {
        // 转换为DayOfWeek枚举
        DayOfWeek targetDayOfWeek = DayOfWeek.of(weekDay);
        // 获取当前日期
        LocalDate currentDate = MiscellaneousUtils.date2LocalDate(fromDate);
        // 获取当前周指定星期的日期
        LocalDate currentWeekTargetDay = currentDate.with(targetDayOfWeek);
        // 如果当前日期已经过了指定的星期，则获取下一周的日期
        currentWeekTargetDay = currentWeekTargetDay.plusWeeks(1);
        // 创建日期列表
        List<Date> dateList = new ArrayList<>();
        // 添加当前周的日期
        dateList.add(Date.from(currentWeekTargetDay.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        // 添加接下来三周的相同星期的日期
        for (int i = 1; i <= 3; i++) {
            LocalDate localDate = currentWeekTargetDay.plusWeeks(i);
            dateList.add(Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        }
        return dateList;
    }

    public static void main(String[] args) {
        LocalDate localDate = MiscellaneousUtils.date2LocalDate(DateUtils.stringToDate("2025-05-05"));
        System.out.println(getWeekSequence(localDate));
    }

}