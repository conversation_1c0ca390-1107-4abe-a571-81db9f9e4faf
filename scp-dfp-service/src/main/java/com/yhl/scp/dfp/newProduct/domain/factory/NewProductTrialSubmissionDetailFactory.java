package com.yhl.scp.dfp.newProduct.domain.factory;

import com.yhl.scp.dfp.newProduct.domain.entity.NewProductTrialSubmissionDetailDO;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionDetailDTO;
import com.yhl.scp.dfp.newProduct.infrastructure.dao.NewProductTrialSubmissionDetailDao;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>NewProductTrialSubmissionDetailFactory</code>
 * <p>
 * 新品试制提报详情领域工厂
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:25
 */
@Component
public class NewProductTrialSubmissionDetailFactory {

    @Resource
    private NewProductTrialSubmissionDetailDao newProductTrialSubmissionDetailDao;

    NewProductTrialSubmissionDetailDO create(NewProductTrialSubmissionDetailDTO dto) {
        // TODO
        return null;
    }

}
