<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.report.infrastructure.dao.CustomerOrderWeeklyReportDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportVO">
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="access_position" jdbcType="DECIMAL" property="accessPosition"/>
        <result column="forecast_time" jdbcType="TIMESTAMP" property="forecastTime"/>
        <result column="customer_forecast_quantity" jdbcType="DECIMAL" property="customerForecastQuantity"/>
        <result column="demand_forecast_quantity" jdbcType="DECIMAL" property="demandForecastQuantity"/>
    </resultMap>
    <sql id="Base_Column_List">
        version_id,oem_code,oem_name,customer_code,vehicle_model_code,access_position,
        customer_forecast_quantity,demand_forecast_quantity
    </sql>
    <select id="selectVersionDropdown" resultType="com.yhl.platform.common.LabelValue">
        select
        date_format(date_add(str_to_date(concat(cdfv.plan_period,'01'), '%Y%m%d'), interval 1 month),'%Y%m') as `label`,
        cdfv.id                                                                                              as `value`
        from fdp_consistence_demand_forecast_version cdfv
                 inner join (
            select max(version_code) as version_code from fdp_consistence_demand_forecast_version
            where review_version_flag ='YES'
              and version_code is not null
            group by plan_period) cdfv1 on cdfv.version_code=cdfv1.version_code
        order by date_format(date_add(str_to_date(concat(cdfv.plan_period,'01'), '%Y%m%d'), interval 1 month),'%Y%m') desc
    </select>
    <select id="selectByVersionId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from v_fdp_customer_order_weekly_report
        where version_id = #{versionId,jdbcType=VARCHAR}
    </select>
    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from v_fdp_customer_order_weekly_report
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
            and date_format(forecast_time,'%Y-%m-%d') between #{planPeriodFirstDay,jdbcType=VARCHAR} and
            #{planPeriodLastDay,jdbcType=VARCHAR}
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <select id="selectWarehouseReleaseRecordList"
            resultType="com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportSimpleVO">
        with `cte_product_stock_point` as (
        select distinct
        `ovm`.`oem_code` as `oem_code`,
        `ovm`.`oem_vehicle_model_code` as `vehicle_model_code`,
        `psp`.`product_code` as `product_code`,
        `ovm`.`access_position` as `access_position`
        from `mds_product_stock_point` `psp`
        left join `mds_stock_point` `sp` on `psp`.stock_point_code = `sp`.stock_point_code
        join `mds_oem_vehicle_model` `ovm` on `psp`.`loading_position_sub` = `ovm`.`access_position`
        and `psp`.`vehicle_model_code` = `ovm`.`oem_vehicle_model_code`
        where `sp`.organize_type = 'SALE_ORGANIZATION')
        select
        m.oem_code as oemCode,
        psp.vehicle_model_code as vehicleModelCode,
        r.creation_date as timePoint,
        r.sum_qty as quantity
        from fdp_warehouse_release_to_warehouse r
        left join v_oem_target_stock_location_map m on r.shipment_locator_code = m.location
        inner join cte_product_stock_point psp on r.item_code = psp.product_code and m.oem_code = psp.oem_code
        where date(r.creation_date) between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
        and m.oem_code in
        <foreach collection="oemCodes" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and psp.vehicle_model_code in
        <foreach collection="vehicleModelCodes" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectWarehouseToWarehouseRecordList"
            resultType="com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportSimpleVO">
        with `cte_product_stock_point` as (
        select distinct
        `ovm`.`oem_code` as `oem_code`,
        `ovm`.`oem_vehicle_model_code` as `vehicle_model_code`,
        `psp`.`product_code` as `product_code`,
        `ovm`.`access_position` as `access_position`
        from `mds_product_stock_point` `psp`
        left join `mds_stock_point` `sp` on `psp`.stock_point_code = `sp`.stock_point_code
        join `mds_oem_vehicle_model` `ovm` on `psp`.`loading_position_sub` = `ovm`.`access_position`
        and `psp`.`vehicle_model_code` = `ovm`.`oem_vehicle_model_code`
        where `sp`.organize_type = 'SALE_ORGANIZATION')
        select
        m.oem_code as oemCode,
        psp.vehicle_model_code as vehicleModelCode,
        r.creation_date as timePoint,
        r.sum_qty as quantity
        from fdp_warehouse_release_record r
        left join v_oem_target_stock_location_map m on r.shipment_locator_code = m.location
        inner join cte_product_stock_point psp on r.item_code = psp.product_code and m.oem_code = psp.oem_code
        where date(r.creation_date) between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
        and m.oem_code in
        <foreach collection="oemCodes" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and psp.vehicle_model_code in
        <foreach collection="vehicleModelCodes" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectDeliveryPlanPublishedList"
            resultType="com.yhl.scp.dfp.report.vo.CustomerOrderWeeklyReportSimpleVO">
        with `cte_product_stock_point` as (
        select distinct
        `ovm`.`oem_code` as `oem_code`,
        `ovm`.`oem_vehicle_model_code` as `vehicle_model_code`,
        `psp`.`product_code` as `product_code`,
        `ovm`.`access_position` as `access_position`
        from `mds_product_stock_point` `psp`
        left join `mds_stock_point` `sp` on `psp`.stock_point_code = `sp`.stock_point_code
        join `mds_oem_vehicle_model` `ovm` on `psp`.`loading_position_sub` = `ovm`.`access_position`
        and `psp`.`vehicle_model_code` = `ovm`.`oem_vehicle_model_code`
        where `sp`.organize_type = 'SALE_ORGANIZATION')
        select
        r.oem_code as oemCode,
        psp.vehicle_model_code as vehicleModelMode,
        r.demand_time as timePoint,
        r.demand_quantity as quantity
        from fdp_delivery_plan_published r
        inner join cte_product_stock_point psp on r.product_code = psp.product_code and r.oem_code = psp.oem_code
        where date(r.demand_time) between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
        and r.oem_code in
        <foreach collection="oemCodes" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and psp.vehicle_model_code in
        <foreach collection="vehicleModelCodes" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>