package com.yhl.scp.dfp.demand.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.DateTypeEnum;
import com.yhl.scp.dfp.common.enums.ForecastMethodEnum;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.config.CustomCellWriteHandler;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastVersionDao;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedDayVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedMonthVO;
import com.yhl.scp.dfp.demand.convertor.DemandForecastEstablishmentConvertor;
import com.yhl.scp.dfp.demand.domain.entity.DemandForecastEstablishmentDO;
import com.yhl.scp.dfp.demand.domain.service.DemandForecastEstablishmentDomainService;
import com.yhl.scp.dfp.demand.dto.DeliveryForecastCompareDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastDetailModifyVehicleDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastDetailProductDetailDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastDetailQueryDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastDetailUseOtherModifyDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastEstablishmentDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastModifyDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastModifyDetailDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastOemQueryDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastOverViewQueryDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastQueryDTO;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastEstablishmentDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO;
import com.yhl.scp.dfp.demand.service.DemandForecastEstablishmentService;
import com.yhl.scp.dfp.demand.service.DemandForecastReviewService;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.vo.DeliveryForecastCompareDetailVO;
import com.yhl.scp.dfp.demand.vo.DeliveryForecastCompareVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentProductVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVehicleVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastOverViewVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.EstablishmentDynamicDataDetailVO;
import com.yhl.scp.dfp.forecast.service.HistoryForecastDataService;
import com.yhl.scp.dfp.forecast.vo.HistoryForecastDataVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionFutureVO;
import com.yhl.scp.dfp.material.service.PartRiskLevelService;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.dto.OemVehicleModelDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemVehicleModelDao;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.release.dto.ReleaseLineChartDTO;
import com.yhl.scp.dfp.release.dto.ReleaseProductItemDTO;
import com.yhl.scp.dfp.release.vo.ReleaseLineChartMonthVO;
import com.yhl.scp.dfp.release.vo.ReleaseVO;
import com.yhl.scp.dfp.switchrelation.service.DfpSwitchRelationBetweenProductService;
import com.yhl.scp.dfp.switchrelation.vo.SwitchRelationVO;
import com.yhl.scp.dfp.warehouse.infrastructure.dao.WarehouseReleaseRecordDao;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordDayVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseDayVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>DemandForecastEstablishmentServiceImpl</code>
 * <p>
 * 业务预测编制应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-14 09:35:29
 */
@Slf4j
@Service
public class DemandForecastEstablishmentServiceImpl extends AbstractService implements DemandForecastEstablishmentService {

    @Resource
    private DemandForecastEstablishmentDao demandForecastEstablishmentDao;

    @Resource
    private DemandForecastEstablishmentDomainService demandForecastEstablishmentDomainService;

    @Resource
    private OemService oemService;

    @Resource
    private DemandForecastVersionService demandForecastVersionService;

    @Resource
    private OemVehicleModelDao vehicleModelDao;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private PartRiskLevelService partRiskLevelService;

    @Resource
    private OemStockPointMapService oemStockPointMapService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Resource
    private DfpSwitchRelationBetweenProductService dfpSwitchRelationBetweenProductService;

    @Resource
    private HistoryForecastDataService historyForecastDataService;
    
    @Resource
    DemandForecastReviewService demandForecastReviewService;
    
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;
    
    @Resource
    private IpsNewFeign ipsNewFeign;
    
    @Resource
    private ConsistenceDemandForecastVersionDao consistenceDemandForecastVersionDao;
    
    @Resource
    private ConsistenceDemandForecastDataService consistenceDemandForecastDataService;
    
    @Resource
    private WarehouseReleaseRecordDao warehouseReleaseRecordDao;
    
    @Resource
    private DeliveryPlanPublishedDao deliveryPlanPublishedDao;
    
    public static final String YYYY_MM = "yyyyMM";
    public static final String PARAM_DEMAND_CATEGORY = "demandCategory";
    public static final String PARAM_OEM_CODE = "oemCode";
    public static final String PARAM_PRODUCT_CODES = "productCodes";
    public static final String PARAM_FORECAST_VERSION_ID = "forecastVersionId";
    public static final String PARAM_PRODUCT_CODE_LIST = "productCodeList";
    public static final String PARAM_VEHICLE_MODEL_CODE_LIST = "vehicleModelCodeList";

    @Override
    public BaseResponse<Void> doCreate(DemandForecastEstablishmentDTO demandForecastEstablishmentDTO) {
        // 0.数据转换
        DemandForecastEstablishmentDO demandForecastEstablishmentDO =
                DemandForecastEstablishmentConvertor.INSTANCE.dto2Do(demandForecastEstablishmentDTO);
        DemandForecastEstablishmentPO demandForecastEstablishmentPO =
                DemandForecastEstablishmentConvertor.INSTANCE.dto2Po(demandForecastEstablishmentDTO);
        // 1.数据校验
        demandForecastEstablishmentDomainService.validation(demandForecastEstablishmentDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(demandForecastEstablishmentPO);
        demandForecastEstablishmentDao.insert(demandForecastEstablishmentPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DemandForecastEstablishmentDTO demandForecastEstablishmentDTO) {
        // 0.数据转换
        DemandForecastEstablishmentDO demandForecastEstablishmentDO =
                DemandForecastEstablishmentConvertor.INSTANCE.dto2Do(demandForecastEstablishmentDTO);
        DemandForecastEstablishmentPO demandForecastEstablishmentPO =
                DemandForecastEstablishmentConvertor.INSTANCE.dto2Po(demandForecastEstablishmentDTO);
        // 1.数据校验
        demandForecastEstablishmentDomainService.validation(demandForecastEstablishmentDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(demandForecastEstablishmentPO);
        demandForecastEstablishmentDao.update(demandForecastEstablishmentPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DemandForecastEstablishmentDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<DemandForecastEstablishmentPO> newList = DemandForecastEstablishmentConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        demandForecastEstablishmentDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DemandForecastEstablishmentDTO> list) {
        List<DemandForecastEstablishmentPO> newList = DemandForecastEstablishmentConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        demandForecastEstablishmentDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return demandForecastEstablishmentDao.deleteBatch(idList);
        }
        return demandForecastEstablishmentDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DemandForecastEstablishmentVO selectByPrimaryKey(String id) {
        DemandForecastEstablishmentPO po = demandForecastEstablishmentDao.selectByPrimaryKey(id);
        return DemandForecastEstablishmentConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DEMAND_FORECAST_ESTABLISHMENT")
    public List<DemandForecastEstablishmentVO> selectByPage(Pagination pagination, String sortParam,
                                                            String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DEMAND_FORECAST_ESTABLISHMENT")
    public List<DemandForecastEstablishmentVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DemandForecastEstablishmentVO> dataList = demandForecastEstablishmentDao.selectByCondition(sortParam,
                queryCriteriaParam);
        DemandForecastEstablishmentServiceImpl target =
                SpringBeanUtils.getBean(DemandForecastEstablishmentServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DemandForecastEstablishmentVO> selectByParams(Map<String, Object> params) {
        List<DemandForecastEstablishmentPO> list = demandForecastEstablishmentDao.selectByParams(params);
        return DemandForecastEstablishmentConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DemandForecastEstablishmentVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return demandForecastEstablishmentDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    /**
     * 获取主机厂的总览查询
     */
    @Override
    public List<DemandForecastOverViewVO> selectDemandOverView(Pagination pagination,
                                                               DemandForecastOverViewQueryDTO queryDTO) {
        String demandType = queryDTO.getDemandType();
        String versionId = queryDTO.getVersionId();
        List<DemandForecastOverViewVO> resultVOList = new ArrayList<>();
        DemandForecastVersionVO demandForecastVersionVO =
                demandForecastVersionService.selectByPrimaryKey(versionId);
        if (Objects.isNull(demandForecastVersionVO)) {
            return resultVOList;
        }
        String versionCode = demandForecastVersionVO.getVersionCode();
        String planPeriod = demandForecastVersionVO.getPlanPeriod();
        queryDTO.setPlanPeriod(planPeriod);
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        resultVOList.addAll(demandForecastEstablishmentDao.selectOemWithRisk(queryDTO));
        if (CollectionUtils.isEmpty(resultVOList)) {
            return resultVOList;
        }
        Set<String> oemSets =
                resultVOList.stream().map(DemandForecastOverViewVO::getOemCode).collect(Collectors.toSet());
        // 获取车型信息
        List<OemVehicleModelVO> vehicleModelVOList = vehicleModelDao.selectVOByParams(ImmutableMap.of("oemCodeList",
                oemSets));
        // 计算主机厂企划达成率
        Map<String, Double> oemRateMap = vehicleModelVOList.stream()
                .filter(k -> Objects.nonNull(k.getProjectRate()))
                .collect(Collectors.groupingBy(OemVehicleModelVO::getOemCode,
                        Collectors.averagingDouble(x -> x.getProjectRate().doubleValue())));
        Map<String, BigDecimal> forecastRateMap = getForecastRate(demandType, versionId, new ArrayList<>(oemSets));
        // 拼接返回数据
        for (DemandForecastOverViewVO demandForecastOverViewVO : resultVOList) {
            demandForecastOverViewVO.setVersionCode(versionCode);
            Double projectRate = oemRateMap.get(demandForecastOverViewVO.getOemCode());
            if (Objects.nonNull(projectRate)) {
                demandForecastOverViewVO.setProjectRate(BigDecimal.valueOf(projectRate).setScale(2,
                        RoundingMode.HALF_UP));
            }
            demandForecastOverViewVO.setForecastRate(forecastRateMap
                    .getOrDefault(demandForecastOverViewVO.getOemCode(), BigDecimal.ZERO));
        }
        return resultVOList;
    }

    /**
     * 计算预测达成率
     *
     * @param demandType 需求类型
     * @param versionId  版本ID
     * @param oemCodes  主机厂列表
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     */
    private Map<String, BigDecimal> getForecastRate(String demandType, String versionId, List<String> oemCodes) {
        Map<String, BigDecimal> result = Maps.newHashMap();
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put(PARAM_DEMAND_CATEGORY, demandType);
        queryMap.put(PARAM_FORECAST_VERSION_ID, versionId);
        
        String assIgnFieldStr = "forecast_time ,product_code , oem_code, customer_forecast";
        List<DemandForecastEstablishmentVO> establishments = demandForecastEstablishmentDao
        		.selectAssIgnFieldByByParams(assIgnFieldStr, queryMap);
        if (CollectionUtils.isEmpty(establishments)) {
            return result;
        }
        List<String> dayRangeList = establishments.stream().filter(item -> Objects.nonNull(item.getForecastTime()))
                .map(item -> DateUtils.dateToString(item.getForecastTime(), DateUtils.COMMON_DATE_STR3))
                .distinct().sorted(Comparator.comparing(Function.identity())).collect(Collectors.toList());
        List<String> productCodes = establishments.stream().map(DemandForecastEstablishmentVO::getProductCode)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dayRangeList) || CollectionUtils.isEmpty(productCodes)) {
            return result;
        }
        Map<String, Object> warehouseQueryMap = Maps.newHashMap();
        warehouseQueryMap.put("oemCodes", oemCodes);

        SwitchRelationVO switchRelation = dfpSwitchRelationBetweenProductService.getSwitchRelation(oemCodes,
                productCodes);
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        warehouseQueryMap.put(PARAM_PRODUCT_CODES, allProductCodes);
        warehouseQueryMap.put("beginDate", dayRangeList.get(0));
        String lastDayPlusOne = DateUtils.dateToString(DateUtils.moveDay(DateUtils
                .stringToDate(dayRangeList.get(dayRangeList.size() - 1)), 1));
        warehouseQueryMap.put("endDate", lastDayPlusOne);

        Map<String, Integer> warehouseReleaseMap =
                warehouseReleaseRecordService.selectMonthVOByParamsGroupOem(warehouseQueryMap)
                        .stream().collect(Collectors.toMap(WarehouseReleaseRecordMonthVO::getOemCode,
                                x -> Objects.isNull(x.getSumQty())
                                        ? 0 : x.getSumQty().intValue()));
        establishments.stream().collect(Collectors.groupingBy(DemandForecastEstablishmentVO::getOemCode))
                .forEach((x, y) -> {
                    BigDecimal customerQuantity = y.stream().map(item -> Objects
                                    .nonNull(item.getCustomerForecast()) ? item.getCustomerForecast() : BigDecimal.ZERO)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    int warehouseQuantity = warehouseReleaseMap.getOrDefault(x, 0);
                    if (0 == warehouseQuantity) {
                        result.put(x, BigDecimal.ZERO);
                    } else {
                        result.put(x, customerQuantity.divide(new BigDecimal(warehouseQuantity), 2,
                                RoundingMode.HALF_UP));
                    }
                });
        return result;
    }

    /**
     * 业务预测编制更新
     */
    @Override
    public BaseResponse<Void> saveForecastVersion(DemandForecastModifyDTO demandForecastModifyDTO) {
        List<DemandForecastModifyDetailDTO> detailDTOS = demandForecastModifyDTO.getDetailDTOS();
        BaseResponse<Void> validResponse = this.checkUpdateDemand(detailDTOS);
        if (Boolean.FALSE.equals(validResponse.getSuccess())) {
            return validResponse;
        }
        this.updateForecastValueByIds(detailDTOS);
        handleCreateVersion(demandForecastModifyDTO);
        return BaseResponse.success();
    }

    @Override
    public List<LabelValue<String>> selectVersionCode(DemandForecastQueryDTO queryDTO) {
        List<LabelValue<String>> result = Lists.newArrayList();
        String versionId = queryDTO.getVersionId();
        DemandForecastVersionVO forecastVersionVO = demandForecastVersionService.selectByPrimaryKey(versionId);
        String planPeriod = forecastVersionVO.getPlanPeriod();
        Date endDate = DateUtils.stringToDate(planPeriod, YYYY_MM);
        List<String> planPeriods = Lists.newArrayList();
        for (int i = 0; i <= 3; i++) {
            Date currentDate = DateUtils.moveCalendar(endDate, Calendar.MONTH, (-i));
            planPeriods.add(DateUtils.dateToString(currentDate, YYYY_MM));
        }
        result.addAll(demandForecastVersionService.selectLastVersionCodes(planPeriods));
        return result;
    }

    @Override
    public int updateByVersionCondition(List<ReleaseProductItemDTO> items) {
        if (CollectionUtils.isEmpty(items)) {
            return 0;
        }
        int result = 0;
        for (ReleaseProductItemDTO item : items) {
            result = result + demandForecastEstablishmentDao.updateByVersionCondition(item);
        }
        return result;
    }

    @Override
    public List<ReleaseVO> selectByVersionAndOem(String versionId, String oemCode) {
        return demandForecastEstablishmentDao.selectByVersionAndOem(versionId, oemCode);
    }

    @Override
    public List<DemandForecastEstablishmentVehicleVO> selectForecastDemandDetail(Pagination pagination,
                                                                                 DemandForecastDetailQueryDTO queryDTO) {
        log.info("查询业务预测编制结构查询");
        List<DemandForecastEstablishmentVehicleVO> result = Lists.newArrayList();
        String demandType = queryDTO.getDemandType();
        String versionId = queryDTO.getVersionId();
        String oemCode = queryDTO.getOemCode();
        List<String> vehicleModelCodes = queryDTO.getVehicleModelCodes();

        List<String> productCodes = queryDTO.getProductCodes();
        if (CollectionUtils.isEmpty(productCodes)) {
            DemandForecastOemQueryDTO demandForecastOemQueryDTO = new DemandForecastOemQueryDTO();
            demandForecastOemQueryDTO.setDemandType(demandType);
            demandForecastOemQueryDTO.setVersionId(versionId);
            demandForecastOemQueryDTO.setOemCode(oemCode);
            demandForecastOemQueryDTO.setVehicleModelCodes(vehicleModelCodes);
            productCodes = this.queryProductCondition(demandForecastOemQueryDTO).getData().stream()
                    .map(LabelValue::getValue).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productCodes)) {
                return result;
            }
        }
        Map<String, Object> queryMap = new HashMap<>();
        DemandForecastVersionVO forecastVersionVO = demandForecastVersionService.selectByPrimaryKey(versionId);
        String planPeriod = forecastVersionVO.getPlanPeriod();
        queryMap.put("versionId", versionId);
        queryMap.put(PARAM_OEM_CODE, oemCode);
        queryMap.put(PARAM_PRODUCT_CODE_LIST, productCodes);
        queryMap.put(PARAM_VEHICLE_MODEL_CODE_LIST, vehicleModelCodes);
        List<DemandForecastEstablishmentVehicleVO> dataList = this.selectDataByCondition(queryMap);
        if (CollectionUtils.isEmpty(dataList)) {
            return result;
        }
        Map<String, Object> totalQueryMap = Maps.newHashMap();
        totalQueryMap.put(PARAM_FORECAST_VERSION_ID, versionId);
        totalQueryMap.put(PARAM_OEM_CODE, oemCode);
        totalQueryMap.put(PARAM_PRODUCT_CODE_LIST, productCodes);
        totalQueryMap.put(PARAM_VEHICLE_MODEL_CODE_LIST, vehicleModelCodes);
        totalQueryMap.put(PARAM_DEMAND_CATEGORY, demandType);
        List<DemandForecastEstablishmentVO> establishments = this.selectByParams(totalQueryMap);
        if (CollectionUtils.isEmpty(establishments)) {
            return result;
        }
        handleDemandForecastData(demandType, planPeriod, oemCode, dataList, establishments, productCodes);
        result.addAll(dataList);
        return result;
    }

    /**
     * 处理组装数据
     *
     * @param demandType                        需求类型
     * @param planPeriod                        计划周期
     * @param oemCode                           主机厂代码
     * @param demandForecastEstablishmentVOList 需求预测数据列表
     * @param totalEstablishments               需求预测数据列表
     * @param filterProductCodes                过滤物品代码列表
     */
    private void handleDemandForecastData(String demandType, String planPeriod, String oemCode,
                                          List<DemandForecastEstablishmentVehicleVO> demandForecastEstablishmentVOList,
                                          List<DemandForecastEstablishmentVO> totalEstablishments,
                                          List<String> filterProductCodes) {
        Map<String, List<String>> allPlanPeriodMap = getBeforePlanPeriodsByCurrentPlanPeriod(planPeriod);
        // 当前预测12个月
        List<String> currentMonths = allPlanPeriodMap.get(planPeriod);

        // 查询主机厂车型数据
        List<OemVehicleModelVO> vehicleModelVOList =
                oemVehicleModelService.selectByParams(ImmutableMap.of(PARAM_OEM_CODE,
                        oemCode));
        // 按照主机厂+车型编码分组
        Map<String, OemVehicleModelVO> oemVehicleModelVOMap = vehicleModelVOList.stream().collect(Collectors
                .toMap(s -> String.join(Constants.DELIMITER, s.getOemCode(),
                        s.getOemVehicleModelCode()), Function.identity(), (k1, k2) -> k2));

        // 获取零件风险等级
        Map<String, Object> materialQueryMap = Maps.newHashMap();
        materialQueryMap.put(PARAM_OEM_CODE, oemCode);
        List<PartRiskLevelVO> partRiskLevelVOS = partRiskLevelService.selectByParams(materialQueryMap);
        Map<String, String> materialRiskLevelMap = CollectionUtils.isEmpty(partRiskLevelVOS) ?
                Maps.newHashMap() :
                partRiskLevelVOS.stream().collect(Collectors.toMap(x ->
                                (StringUtils.isBlank(x.getProductCode()) ? "" : x.getProductCode()) + Constants.DELIMITER
                                        + (StringUtils.isBlank(x.getVehicleModelCode()) ? "" : x.getVehicleModelCode()),
                        PartRiskLevelVO::getMaterialRiskLevel, (v1, v2) -> v1));

        // 获取实际出货数据
        List<String> productCodes = totalEstablishments.stream().map(DemandForecastEstablishmentVO::getProductCode)
                .distinct().collect(Collectors.toList());
        // key:本厂编码+年月份,value:实际出货数量
        Map<String, BigDecimal> warehouseReleaseMap = Maps.newHashMap();
        CompletableFuture<Void> warehouseFuture = getWareHouseReleaseMap(oemCode, productCodes, warehouseReleaseMap);
        //获取中转库发货记录
        Map<String, BigDecimal> warehouseReleaseToMap = Maps.newHashMap();
        CompletableFuture<Void> warehouseToFuture = getWareHouseReleaseToMap(oemCode, productCodes, warehouseReleaseToMap);
        
        // 预测根据车型编码、本厂编码、年月分组
        Map<String, Map<String, Map<String, DemandForecastEstablishmentVO>>> currentEstablishmentMap =
                Maps.newHashMap();
        CompletableFuture<Void> currentFuture = CompletableFuture.runAsync(() -> currentEstablishmentMap
                .putAll(totalEstablishments.stream().collect(Collectors.groupingBy(
                        x -> StringUtils.isBlank(x.getVehicleModelCode()) ? "" : x.getVehicleModelCode(),
                        Collectors.groupingBy(DemandForecastEstablishmentVO::getProductCode,
                                Collectors.toMap(x -> DateUtils.dateToString(x.getForecastTime(), DateUtils.YEAR_MONTH),
                                        Function.identity(), (v1, v2) -> v1))))));
        // 删除当前业务预测计划周期
        allPlanPeriodMap.remove(planPeriod);
        // 历史根据车型编码、本厂编码、年月分组
        String scenario = SystemHolder.getScenario();
        Map<String, Map<String, Map<String, HistoryForecastDataVO>>> historyForecastMap =
                Maps.newHashMap();
        CompletableFuture<Void> historyFuture = CompletableFuture.runAsync(() -> historyForecastMap
                .putAll(getHistoryForecastMap(demandType, allPlanPeriodMap, oemCode, productCodes, scenario)));
        CompletableFuture<Void> future = CompletableFuture.allOf(warehouseFuture, currentFuture, historyFuture, warehouseToFuture);
        future.join();

        // 获取物料数据信息，根据物料获取对应的装车位置小类
        List<NewProductStockPointVO> productInfoList = newMdsFeign.selectByProductCode(scenario, productCodes);
        Map<String, String> loadingPositionSubMap = new HashMap<>();
        productInfoList.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getProductCode()) && StringUtils.isNotEmpty(e.getLoadingPositionSub())) {
                loadingPositionSubMap.put(e.getProductCode(), e.getLoadingPositionSub());
            }
        });

        // 车型信息循环
        for (DemandForecastEstablishmentVehicleVO vehicleVO : demandForecastEstablishmentVOList) {
            // 车型动态列Map
            Map<String, EstablishmentDynamicDataDetailVO> establishmentVehicleVOMap = Maps.newHashMap();
            // 车型动态列
            List<EstablishmentDynamicDataDetailVO> establishmentVehicleVOs = Lists.newArrayList();
            // 车型编码
            String vehicleModelCode = vehicleVO.getVehicleModelCode();
            // 装车位置
            String accessPosition = vehicleVO.getAccessPosition();
            // 车型下有效取数位置的
            List<EstablishmentDynamicDataDetailVO> vehicleProductDetailVOList = Lists.newArrayList();
            // 本厂编码动态列
            List<DemandForecastEstablishmentProductVO> establishmentProductVOS = Lists.newArrayList();
            // 获取当前车型下的所有的物品数据
            Map<String, Map<String, DemandForecastEstablishmentVO>> currentVehicleMap =
                    currentEstablishmentMap.getOrDefault(vehicleModelCode, Maps.newHashMap());
            // 获取历史车型下的所有的物品数据
            Map<String, Map<String, HistoryForecastDataVO>> historyVehicleMap =
                    historyForecastMap.getOrDefault(vehicleModelCode, Maps.newHashMap());

            // 物品信息循环
            for (Map.Entry<String, Map<String, DemandForecastEstablishmentVO>> entry : currentVehicleMap.entrySet()) {
                DemandForecastEstablishmentProductVO productVO = new DemandForecastEstablishmentProductVO();
                // 处理当前业务预测的数据
                String productCode = entry.getKey();
                productVO.setDemandType(demandType);
                productVO.setProductCode(productCode);
                productVO.setRiskLevel(materialRiskLevelMap.get((StringUtils.isBlank(productCode) ? "" : productCode) +
                        Constants.DELIMITER +
                        (StringUtils.isBlank(vehicleModelCode) ? "" : vehicleModelCode)));
                Map<String, DemandForecastEstablishmentVO> yearMonthData = entry.getValue();
                List<EstablishmentDynamicDataDetailVO> productDetailVOList = Lists.newArrayList();
                currentMonths.forEach(month -> {
                    String joinKey = String.join(Constants.DELIMITER, oemCode, productCode, month);
                    EstablishmentDynamicDataDetailVO dataDetailVO = new EstablishmentDynamicDataDetailVO();
                    dataDetailVO.setColumnName(month);
                    dataDetailVO.setDataType(DateTypeEnum.FORECAST.getCode());
                    BigDecimal deliveryNum = warehouseReleaseMap.getOrDefault(joinKey, BigDecimal.ZERO);
                    //已发数量加上中转库数量
                    deliveryNum = deliveryNum.add(warehouseReleaseToMap.getOrDefault(joinKey, BigDecimal.ZERO));
                    dataDetailVO.setDeliveryNum(deliveryNum);
                    DemandForecastEstablishmentVO currentMonthData = yearMonthData.get(month);
                    if (Objects.nonNull(currentMonthData)) {
                        dataDetailVO.setColumnName(month);
                        String productAccessPosition = currentMonthData.getAccessPosition();
                        dataDetailVO.setId(currentMonthData.getId());
                        dataDetailVO.setVersionValue(currentMonthData.getVersionValue());
                        dataDetailVO.setCustomerForecast(currentMonthData.getCustomerForecast());
                        dataDetailVO.setAlgorithmForecast(currentMonthData.getAlgorithmForecast());
                        dataDetailVO.setDemandForecast(currentMonthData.getDemandForecast() == null ?
                                BigDecimal.ZERO : currentMonthData.getDemandForecast());
                        // 车型取数位置数据
                        if (StringUtils.isBlank(accessPosition) || accessPosition.equals(productAccessPosition)) {
                            vehicleProductDetailVOList.add(dataDetailVO);
                        }
                    } else {
                        dataDetailVO.setCustomerForecast(BigDecimal.ZERO);
                        dataDetailVO.setAlgorithmForecast(BigDecimal.ZERO);
                        dataDetailVO.setDemandForecast(BigDecimal.ZERO);
                    }
                    if (CollectionUtils.isEmpty(filterProductCodes)) {
                        productDetailVOList.add(dataDetailVO);
                    } else {
                        if (filterProductCodes.contains(productCode)) {
                            productDetailVOList.add(dataDetailVO);
                        }
                    }
                });
                Map<String, HistoryForecastDataVO> historyYearMonthData =
                        historyVehicleMap.getOrDefault(productCode, Maps.newHashMap());
                // 处理历史需求预测的数据
                allPlanPeriodMap.values().forEach(history -> history.forEach(month -> {
                    String joinKey = String.join(Constants.DELIMITER, oemCode, productCode, month);

                    EstablishmentDynamicDataDetailVO dataDetailVO = new EstablishmentDynamicDataDetailVO();
                    dataDetailVO.setColumnName(month);
                    dataDetailVO.setDataType(DateTypeEnum.HISTORY.getCode());
                    BigDecimal deliveryNum = warehouseReleaseMap.getOrDefault(joinKey, BigDecimal.ZERO);
                    //已发数量加上中转库数量
                    deliveryNum = deliveryNum.add(warehouseReleaseToMap.getOrDefault(joinKey, BigDecimal.ZERO));
                    dataDetailVO.setDeliveryNum(deliveryNum);
                    String loadingPositionSub = loadingPositionSubMap.get(productCode);
                    HistoryForecastDataVO currentMonthData = historyYearMonthData.get(month);
                    if (Objects.nonNull(currentMonthData)) {
                        dataDetailVO.setColumnName(month);
                        String productAccessPosition = currentMonthData.getAccessPosition();
                        dataDetailVO.setId(currentMonthData.getId());
                        dataDetailVO.setVersionValue(currentMonthData.getVersionValue());
                        dataDetailVO.setCustomerForecast(currentMonthData.getCustomerForecastsQuantity());
                        dataDetailVO.setAlgorithmForecast(currentMonthData.getAlgorithmForecastsQuantity());
                        dataDetailVO.setDemandForecast(currentMonthData.getForecastQuantity());
                        // 车型取数位置数据
                        if (StringUtils.isBlank(accessPosition)
                                || accessPosition.equals(productAccessPosition)
                                || (warehouseReleaseMap.containsKey(joinKey) && accessPosition.equals(loadingPositionSub))
                                || (warehouseReleaseToMap.containsKey(joinKey) && accessPosition.equals(loadingPositionSub))) {
                            vehicleProductDetailVOList.add(dataDetailVO);
                        }
                    } else {
                        dataDetailVO.setCustomerForecast(BigDecimal.ZERO);
                        dataDetailVO.setAlgorithmForecast(BigDecimal.ZERO);
                        dataDetailVO.setDemandForecast(BigDecimal.ZERO);
                        if (StringUtils.isBlank(accessPosition)
                                || (warehouseReleaseMap.containsKey(joinKey)
                                && accessPosition.equals(loadingPositionSub))
                                || (warehouseReleaseToMap.containsKey(joinKey)
                                        && accessPosition.equals(loadingPositionSub))) {
                            vehicleProductDetailVOList.add(dataDetailVO);
                        }
                    }
                    if (CollectionUtils.isEmpty(filterProductCodes)) {
                        productDetailVOList.add(dataDetailVO);
                    } else {
                        if (filterProductCodes.contains(productCode)) {
                            productDetailVOList.add(dataDetailVO);
                        }
                    }
                }));
                productVO.setDetailVOList(productDetailVOList.stream().sorted(Comparator
                        .comparing(EstablishmentDynamicDataDetailVO::getColumnName)).collect(Collectors.toList()));
                if (CollectionUtils.isEmpty(filterProductCodes)) {
                    establishmentProductVOS.add(productVO);
                } else {
                    if (filterProductCodes.contains(productCode)) {
                        establishmentProductVOS.add(productVO);
                    }
                }
            }
            // 综合系数
            String join = String.join(Constants.DELIMITER, oemCode, vehicleModelCode);
            vehicleVO.setComprehensiveCoefficient(oemVehicleModelVOMap.containsKey(join) ?
                    oemVehicleModelVOMap.get(join).getComprehensiveCoefficient() : null);
            // 物品信息
            vehicleVO.setProductVOList(establishmentProductVOS);
            // 处理当前车型业务预测的数据
            vehicleProductDetailVOList.stream().collect(Collectors.groupingBy(EstablishmentDynamicDataDetailVO::getColumnName))
                    .forEach((x, y) -> {
                        EstablishmentDynamicDataDetailVO vehicleDetail = new EstablishmentDynamicDataDetailVO();
                        vehicleDetail.setColumnName(x);
                        BigDecimal customerForecast = BigDecimal.ZERO;
                        BigDecimal algorithmForecast = BigDecimal.ZERO;
                        BigDecimal demandForecast = BigDecimal.ZERO;
                        BigDecimal deliveryNum = BigDecimal.ZERO;
                        if (CollectionUtils.isNotEmpty(y)) {
                            for (EstablishmentDynamicDataDetailVO dataDetailVO : y) {
                                vehicleDetail.setDataType(dataDetailVO.getDataType());
                                customerForecast =
                                        customerForecast.add(Objects.isNull(dataDetailVO.getCustomerForecast()) ?
                                                BigDecimal.ZERO : dataDetailVO.getCustomerForecast());
                                algorithmForecast =
                                        algorithmForecast.add(Objects.isNull(dataDetailVO.getAlgorithmForecast()) ?
                                                BigDecimal.ZERO : dataDetailVO.getAlgorithmForecast());
                                demandForecast = demandForecast.add(Objects.isNull(dataDetailVO.getDemandForecast()) ?
                                        BigDecimal.ZERO : dataDetailVO.getDemandForecast());
                                deliveryNum = deliveryNum.add(Objects.isNull(dataDetailVO.getDeliveryNum()) ?
                                        BigDecimal.ZERO : dataDetailVO.getDeliveryNum());
                            }
                        }
                        vehicleDetail.setCustomerForecast(customerForecast);
                        vehicleDetail.setAlgorithmForecast(algorithmForecast);
                        vehicleDetail.setDemandForecast(demandForecast);
                        vehicleDetail.setDeliveryNum(deliveryNum);
                        establishmentVehicleVOMap.put(x, vehicleDetail);
                    });
            // 处理历史车型动态列数据
            List<String> allValues = allPlanPeriodMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            for (String historyMonth : allValues) {
                EstablishmentDynamicDataDetailVO dataDetailVO = establishmentVehicleVOMap.get(historyMonth);
                if (Objects.nonNull(dataDetailVO)) {
                    establishmentVehicleVOs.add(dataDetailVO);
                    continue;
                }
                EstablishmentDynamicDataDetailVO defaultData = getDefaultData(historyMonth,
                        DateTypeEnum.HISTORY.getCode());
                establishmentVehicleVOs.add(defaultData);
            }
            // 处理预测车型动态列数据
            for (String currentMonth : currentMonths) {
                EstablishmentDynamicDataDetailVO dataDetailVO = establishmentVehicleVOMap.get(currentMonth);
                if (Objects.nonNull(dataDetailVO)) {
                    establishmentVehicleVOs.add(dataDetailVO);
                    continue;
                }
                EstablishmentDynamicDataDetailVO defaultData = getDefaultData(currentMonth,
                        DateTypeEnum.FORECAST.getCode());
                establishmentVehicleVOs.add(defaultData);
            }
            establishmentVehicleVOs.forEach(establishmentVehicleVO -> {
                BigDecimal customerPrecision = establishmentVehicleVO.getCustomerPrecision();
                BigDecimal algorithmPrecision = establishmentVehicleVO.getAlgorithmPrecision();
                BigDecimal forecastPrecision = establishmentVehicleVO.getForecastPrecision();
                establishmentVehicleVO.setCustomerPrecision(customerPrecision);
                establishmentVehicleVO.setAlgorithmPrecision(algorithmPrecision);
                establishmentVehicleVO.setForecastPrecision(forecastPrecision);
            });
            // 车型动态列
            vehicleVO.setDetailVOList(establishmentVehicleVOs.stream().sorted(Comparator
                    .comparing(EstablishmentDynamicDataDetailVO::getColumnName)).collect(Collectors.toList()));
        }
    }

    /**
     * 获取仓库发货记录
     * @param oemCode
     * @param productCodes
     * @param warehouseReleaseMap
     * @return
     */
	private CompletableFuture<Void> getWareHouseReleaseMap(String oemCode, List<String> productCodes,
			Map<String, BigDecimal> warehouseReleaseMap) {
		CompletableFuture<Void> warehouseFuture = CompletableFuture.runAsync(() -> {
            Date currentDate = new Date();
            String beginDate = DateUtils.dateToString(DateUtils.moveCalendar(DateUtils.getMonthFirstDay(currentDate),
                    Calendar.MONTH, -12));
            String endDate = DateUtils.dateToString(DateUtils.moveCalendar(new Date(), Calendar.DAY_OF_YEAR, 1),
                    DateUtils.COMMON_DATE_STR3);
            Map<String, Object> warehouseQueryMap = Maps.newHashMap();
            warehouseQueryMap.put("beginDate", beginDate);
            warehouseQueryMap.put("endDate", endDate);
            SwitchRelationVO switchRelation = dfpSwitchRelationBetweenProductService.getSwitchRelation(Lists
                    .newArrayList(oemCode), productCodes);
            Map<String, String> newOldMap = switchRelation.getNewOldMap();
            Map<String, String> oldNewMap = switchRelation.getOldNewMap();
            List<String> allProductCodes = switchRelation.getAllProductCodes();
            warehouseQueryMap.put(PARAM_PRODUCT_CODES, allProductCodes);
            List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecords = warehouseReleaseRecordService
                    .selectMonthVOByParams(warehouseQueryMap);
            if (CollectionUtils.isEmpty(warehouseReleaseRecords)) {
                return;
            }
            Map<String, BigDecimal> sumQtyMap = new HashMap<>();
            Map<String, BigDecimal> collect = warehouseReleaseRecords.stream().collect(Collectors
                    .toMap(x -> String.join(Constants.DELIMITER,
                                    x.getOemCode(), x.getItemCode(), x.getYearMonth()),
                            WarehouseReleaseRecordMonthVO::getSumQty, (t1, t2) -> t1));
            for (WarehouseReleaseRecordMonthVO warehouseReleaseRecord : warehouseReleaseRecords) {
                BigDecimal sumQty = warehouseReleaseRecord.getSumQty();
                String itemCode = warehouseReleaseRecord.getItemCode();
                String yearMonth = warehouseReleaseRecord.getYearMonth();
                String key = String.join(Constants.DELIMITER, oemCode, itemCode, yearMonth);
                if (newOldMap.containsKey(itemCode)) {
                    String xItemCode = newOldMap.get(itemCode);
                    String xKey = String.join(Constants.DELIMITER, oemCode, xItemCode, yearMonth);
                    sumQty = sumQty.add(collect.getOrDefault(xKey, BigDecimal.ZERO));
                }
                if (oldNewMap.containsKey(itemCode)) {
                    String yItemCode = oldNewMap.get(itemCode);
                    String yKey = String.join(Constants.DELIMITER, oemCode, yItemCode, yearMonth);
                    sumQty = sumQty.add(collect.getOrDefault(yKey, BigDecimal.ZERO));
                }
                sumQtyMap.put(key, sumQty);
            }
            warehouseReleaseMap.putAll(sumQtyMap);
        });
		return warehouseFuture;
	}
	
	/**
     * 获取中转库发货记录
     * @param oemCode
     * @param productCodes
     * @param warehouseReleaseToMap
     * @return
     */
	private CompletableFuture<Void> getWareHouseReleaseToMap(String oemCode, List<String> productCodes,
			Map<String, BigDecimal> warehouseReleaseToMap) {
		CompletableFuture<Void> warehouseFuture = CompletableFuture.runAsync(() -> {
            Date currentDate = new Date();
            String beginDate = DateUtils.dateToString(DateUtils.moveCalendar(DateUtils.getMonthFirstDay(currentDate),
                    Calendar.MONTH, -12));
            String endDate = DateUtils.dateToString(DateUtils.moveCalendar(new Date(), Calendar.DAY_OF_YEAR, 1),
                    DateUtils.COMMON_DATE_STR3);
            Map<String, Object> warehouseQueryMap = Maps.newHashMap();
            warehouseQueryMap.put("beginDate", beginDate);
            warehouseQueryMap.put("endDate", endDate);
            SwitchRelationVO switchRelation = dfpSwitchRelationBetweenProductService.getSwitchRelation(Lists
                    .newArrayList(oemCode), productCodes);
            Map<String, String> newOldMap = switchRelation.getNewOldMap();
            Map<String, String> oldNewMap = switchRelation.getOldNewMap();
            List<String> allProductCodes = switchRelation.getAllProductCodes();
            warehouseQueryMap.put(PARAM_PRODUCT_CODES, allProductCodes);
            List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService
            		.selectMonthVOByParams(warehouseQueryMap);
            if (CollectionUtils.isEmpty(warehouseReleaseToWarehouses)) {
                return;
            }
            Map<String, BigDecimal> sumQtyMap = new HashMap<>();
            Map<String, BigDecimal> collect = warehouseReleaseToWarehouses.stream().collect(Collectors
                    .toMap(x -> String.join(Constants.DELIMITER,
                                    x.getOemCode(), x.getItemCode(), x.getYearMonth()),
                    		WarehouseReleaseToWarehouseMonthVO::getSumQty, (t1, t2) -> t1));
            for (WarehouseReleaseToWarehouseMonthVO warehouseReleaseRecord : warehouseReleaseToWarehouses) {
                BigDecimal sumQty = warehouseReleaseRecord.getSumQty();
                String itemCode = warehouseReleaseRecord.getItemCode();
                String yearMonth = warehouseReleaseRecord.getYearMonth();
                String key = String.join(Constants.DELIMITER, oemCode, itemCode, yearMonth);
                if (newOldMap.containsKey(itemCode)) {
                    String xItemCode = newOldMap.get(itemCode);
                    String xKey = String.join(Constants.DELIMITER, oemCode, xItemCode, yearMonth);
                    sumQty = sumQty.add(collect.getOrDefault(xKey, BigDecimal.ZERO));
                }
                if (oldNewMap.containsKey(itemCode)) {
                    String yItemCode = oldNewMap.get(itemCode);
                    String yKey = String.join(Constants.DELIMITER, oemCode, yItemCode, yearMonth);
                    sumQty = sumQty.add(collect.getOrDefault(yKey, BigDecimal.ZERO));
                }
                sumQtyMap.put(key, sumQty);
            }
            warehouseReleaseToMap.putAll(sumQtyMap);
        });
		return warehouseFuture;
	}

    private EstablishmentDynamicDataDetailVO getDefaultData(String columnName, String dataType) {
        EstablishmentDynamicDataDetailVO detailVO = new EstablishmentDynamicDataDetailVO();
        detailVO.setColumnName(columnName);
        detailVO.setDataType(dataType);
        detailVO.setDeliveryNum(BigDecimal.ZERO);
        detailVO.setCustomerForecast(BigDecimal.ZERO);
        detailVO.setAlgorithmForecast(BigDecimal.ZERO);
        detailVO.setForecastPrecision(BigDecimal.ZERO);
        detailVO.setDemandForecast(BigDecimal.ZERO);
        return detailVO;
    }

    /**
     * 使用上次版本数据
     *
     * @param demandType         需求类型
     * @param preVersionId       前版本ID
     * @param oemCode            主机厂代码
     * @param adjustProductCodes 调整物品代码列表
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.math.BigDecimal>>
     */
    private Map<String, Map<String, BigDecimal>> getPreForecastData(String demandType, String preVersionId,
                                                                    String oemCode, List<String> adjustProductCodes) {
        Map<String, Object> totalQueryMap = Maps.newHashMap();
        totalQueryMap.put(PARAM_FORECAST_VERSION_ID, preVersionId);
        totalQueryMap.put(PARAM_OEM_CODE, oemCode);
        totalQueryMap.put(PARAM_PRODUCT_CODE_LIST, adjustProductCodes);
        totalQueryMap.put(PARAM_DEMAND_CATEGORY, demandType);
        List<DemandForecastEstablishmentVO> totalEstablishmentVOS = this.selectByParams(totalQueryMap);
        if (CollectionUtils.isEmpty(totalEstablishmentVOS)) {
            return Maps.newHashMap();
        }
        return totalEstablishmentVOS.stream().collect(Collectors.groupingBy(DemandForecastEstablishmentVO::getProductCode,
                Collectors.toMap(x -> DateUtils.dateToString(x.getForecastTime(), DateUtils.YEAR_MONTH),
                        DemandForecastEstablishmentVO::getDemandForecast, (v1, v2) -> v1)));
    }

    /**
     * 获取历史需求数据
     *
     * @param demandType     需求类型
     * @param allPlanPeriods 计划周期列表
     * @param oemCode        主机厂代码
     * @param productCodes   物品代码列表
     * @param scenario   物品代码列表
     * @return java.util.Map<java.lang.String, java.util.Map < java.lang.String, java.util.Map < java.lang.String, com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO>>>
     */
    private Map<String, Map<String, Map<String, HistoryForecastDataVO>>> getHistoryForecastMap(
            String demandType, Map<String, List<String>> allPlanPeriods, String oemCode, List<String> productCodes,
            String scenario) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return new HashMap<>();
        }

        if (CollectionUtils.isEmpty(productCodes)) {
            return new HashMap<>();
        }
        // 获取主机厂库存信息
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put(PARAM_OEM_CODE, oemCode);
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemStockPointMapVO> oemStockPointMapVOS = oemStockPointMapService.selectByParams(queryMap);
        // 获取物料信息
        List<String> stockPointCodes =
                oemStockPointMapVOS.stream().map(OemStockPointMapVO::getStockPointCode).distinct().collect(Collectors.toList());
        Map<String, Object> productParams = Maps.newHashMap();
        productParams.put("enabled", YesOrNoEnum.YES.getCode());
        if (CollectionUtils.isNotEmpty(stockPointCodes) && stockPointCodes.size() == 1) {
            productParams.put("stockPointCode", stockPointCodes.get(0));
        } else {
            productParams.put("stockPointCodes", stockPointCodes);
        }

        if (CollectionUtils.isNotEmpty(productCodes) && productCodes.size() == 1) {
            productParams.put("productCode", productCodes.get(0));
        } else {
            productParams.put(PARAM_PRODUCT_CODES, productCodes);
        }
        Map<String, String> productVehicleMap = newMdsFeign.selectProductStockPointByParams(scenario, productParams)
                .stream().collect(Collectors.toMap(item -> item.getVehicleModelCode()
                                + Constants.DELIMITER + item.getProductCode(),
                        NewProductStockPointVO::getLoadingPositionSub, (t1, t2) -> t2));

        List<String> historyPlanPeriods = allPlanPeriods.values().stream().filter(Objects::nonNull)
                .flatMap(List::stream).distinct().sorted().collect(Collectors.toList());
        String beginYearMonth = historyPlanPeriods.get(0).replace("-", "");
        String endYearMonth = historyPlanPeriods.get(historyPlanPeriods.size() - 1).replace("-", "");
        Map<String, Object> historyQueryParams = new HashMap<>();
        historyQueryParams.put(PARAM_DEMAND_CATEGORY, demandType);
        historyQueryParams.put(PARAM_OEM_CODE, oemCode);
        historyQueryParams.put(PARAM_PRODUCT_CODES, productCodes);
        historyQueryParams.put("beginYearMonth", beginYearMonth);
        historyQueryParams.put("endYearMonth", endYearMonth);
        List<HistoryForecastDataVO> historyForecastDataList =
                historyForecastDataService.selectByParams(historyQueryParams);
        if (CollectionUtils.isEmpty(historyForecastDataList)) {
            return Maps.newHashMap();
        }

        // 设置装车位置
        historyForecastDataList.forEach(x -> x.setAccessPosition(productVehicleMap
                .get(x.getVehicleModelCode() + Constants.DELIMITER + x.getProductCode())));
        return historyForecastDataList.stream().collect(Collectors.groupingBy(HistoryForecastDataVO::getVehicleModelCode,
                Collectors.groupingBy(HistoryForecastDataVO::getProductCode,
                        Collectors.toMap(x -> DateUtils.dateToString(x.getForecastTime(),
                                        DateUtils.YEAR_MONTH),
                                Function.identity(), (v1, v2) ->
                                        v1.getModifyTime().getTime() >= v2.getModifyTime().getTime() ? v1 : v2))));
    }

    /**
     * 根据计划周期获取当年历史月的计划周期
     *
     * @param planPeriod 计划周期
     * @return java.util.Map<java.lang.String, java.util.List < java.lang.String>>
     */
    public static Map<String, List<String>> getBeforePlanPeriodsByCurrentPlanPeriod(String planPeriod) {
        Map<String, List<String>> result = Maps.newHashMap();
        Date currentDate = DateUtils.stringToDate(planPeriod, YYYY_MM);
        List<String> currentDateList = Lists.newArrayList();
        // 当前预测+当前月
        for (int i = 0; i <= 12; i++) {
            currentDateList.add(DateUtils.dateToString(DateUtils.moveCalendar(currentDate, Calendar.MONTH, i),
                    DateUtils.YEAR_MONTH));
            result.put(planPeriod, currentDateList);
        }
        // 历史预测需要去除当前月数据
        for (int i = 1; i <= 12; i++) {
            Date date = DateUtils.moveCalendar(currentDate, Calendar.MONTH, -i);
            String historyPlanPeriod = DateUtils.dateToString(date, YYYY_MM);
            List<String> historyPlanPeriods = Lists.newArrayList(DateUtils.dateToString(date, DateUtils.YEAR_MONTH));
            result.put(historyPlanPeriod, historyPlanPeriods);
        }
        return result;
    }

    /**
     * 根据需求类型查询主机厂信息
     */
    @Override
    public BaseResponse<List<LabelValue<String>>> queryOemCondition(DemandForecastQueryDTO queryDTO) {
        String demandType = queryDTO.getDemandType();
        String versionId = queryDTO.getVersionId();
        Set<String> oemCodes = Sets.newHashSet();
        List<LabelValue<String>> result = Lists.newArrayList();
        Map<String, Object> establishQueryMap = Maps.newHashMap();
        establishQueryMap.put(PARAM_FORECAST_VERSION_ID, versionId);
        establishQueryMap.put(PARAM_DEMAND_CATEGORY, demandType);
        List<DemandForecastEstablishmentPO> establishmentPOS =
                demandForecastEstablishmentDao.selectDataPermissionByParams(establishQueryMap);
        if (CollectionUtils.isNotEmpty(establishmentPOS)) {
            for (DemandForecastEstablishmentPO establishmentPO : establishmentPOS) {
                oemCodes.add(establishmentPO.getOemCode());
            }
        }
        if (CollectionUtils.isEmpty(oemCodes)) {
            return BaseResponse.success(result);
        }
        Map<String, Object> oemQueryMap = Maps.newHashMap();
        oemQueryMap.put("oemCodeList", oemCodes);
        // 支持权限查询
        List<OemVO> oemVOS = oemService.selectByParams(oemQueryMap);
        if (CollectionUtils.isEmpty(oemVOS)) {
            return BaseResponse.success();
        }
        result.addAll(oemVOS.stream().map(x -> {
            LabelValue<String> labelValue = new LabelValue<>();
            labelValue.setLabel(x.getOemName() + "(" + x.getOemCode() + ")");
            labelValue.setValue(x.getOemCode());
            return labelValue;
        }).sorted(Comparator.comparing(LabelValue::getValue)).collect(Collectors.toList()));
        return BaseResponse.success(result);
    }

    @Override
    public List<DemandForecastEstablishmentVehicleVO> selectDataByCondition(Map<String, Object> queryMap) {
        return demandForecastEstablishmentDao.selectDataByCondition(queryMap);
    }

    @Override
    public BaseResponse<List<LabelValue<String>>> queryVehicleCondition(DemandForecastOemQueryDTO queryDTO) {
        String demandType = queryDTO.getDemandType();
        String versionId = queryDTO.getVersionId();
        String oemCode = queryDTO.getOemCode();
        Map<String, Object> params = new HashMap<>();
        params.put("orderPlanner", SystemHolder.getUserId());
        List<DemandForecastEstablishmentPO> demandForecastEstablishments =
                demandForecastEstablishmentDao.selectByParams(ImmutableMap.of(PARAM_FORECAST_VERSION_ID, versionId,
                        PARAM_OEM_CODE, oemCode, PARAM_DEMAND_CATEGORY, demandType));
        if (CollectionUtils.isEmpty(demandForecastEstablishments)) {
            return BaseResponse.success(new ArrayList<>());
        }
        List<String> productCodeList =
                demandForecastEstablishments.stream().map(DemandForecastEstablishmentPO::getProductCode)
                        .distinct().collect(Collectors.toList());
        params.put(PARAM_PRODUCT_CODES, productCodeList);
        FeignDynamicParam dynamicParams = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("vehicle_model_code")).queryParam(params).build();
        List<String> list = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), dynamicParams).stream()
                .filter(e -> !Objects.isNull(e))
                .map(NewProductStockPointVO::getVehicleModelCode)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success(Lists.newArrayList());
        }
        // 获取主机厂车型配置信息
        List<OemVehicleModelVO> vehicleModelVOList = vehicleModelDao.selectVOByParams(ImmutableMap.of(PARAM_OEM_CODE,
                oemCode));
        if (CollUtil.isEmpty(vehicleModelVOList)) {
            return BaseResponse.success(Lists.newArrayList());
        }
        List<String> oemVehicleModelCodeList =
                vehicleModelVOList.stream().map(OemVehicleModelVO::getOemVehicleModelCode)
                        .collect(Collectors.toList());
        List<LabelValue<String>> dataList = list.stream().filter(oemVehicleModelCodeList::contains)
                .map(x -> new LabelValue<>(x, x)).collect(Collectors.toList());
        return BaseResponse.success(dataList);
    }

    @Override
    public BaseResponse<List<LabelValue<String>>> queryProductCondition(DemandForecastOemQueryDTO queryDTO) {
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("versionId", queryDTO.getVersionId());
        queryMap.put(PARAM_OEM_CODE, queryDTO.getOemCode());
        queryMap.put(PARAM_VEHICLE_MODEL_CODE_LIST, queryDTO.getVehicleModelCodes());
        queryMap.put(PARAM_DEMAND_CATEGORY, queryDTO.getDemandType());
        List<DemandForecastEstablishmentVehicleVO> establishmentVOList = this.selectProductDataByCondition(queryMap);
        if (CollectionUtils.isEmpty(establishmentVOList)) {
            return BaseResponse.success(new ArrayList<>());
        }
        List<String> productCodes = establishmentVOList.stream()
                .map(DemandForecastEstablishmentVehicleVO::getProductCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        Map<String, Object> params = new HashMap<>();
        params.put("orderPlanner", SystemHolder.getUserId());
        params.put(PARAM_PRODUCT_CODES, productCodes);
        String scenario = SystemHolder.getScenario();
        List<LabelValue<String>> dataList = newMdsFeign.selectProductStockPointLabelValueByParams(scenario, params);
        Map<String, LabelValue<String>> dataMap = dataList.stream().collect(Collectors.toMap(LabelValue::getValue,
                each -> each, (value1, value2) -> value1));
        return BaseResponse.success(new ArrayList<>(dataMap.values()));
    }

    @Override
    public BaseResponse<Void> checkUpdateDemand(List<DemandForecastModifyDetailDTO> detailDTOS) {
        if (CollectionUtils.isEmpty(detailDTOS)) {
            return BaseResponse.success();
        }

        List<DemandForecastModifyDetailDTO> data = detailDTOS.stream()
                .filter(p -> p.getId() != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(data)) {
            return BaseResponse.success();
        }

        // 构建 detailMap 和 demandIds
        Map<String, Integer> detailMap = data.stream().filter(p -> p.getId() != null)
                .collect(Collectors.toMap(
                        DemandForecastModifyDetailDTO::getId,
                        DemandForecastModifyDetailDTO::getVersionValue,
                        (v1, v2) -> v1
                ));

        List<String> demandIds = data.stream()
                .map(DemandForecastModifyDetailDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<DemandForecastEstablishmentPO> establishmentPOS =
                demandForecastEstablishmentDao.selectByPrimaryKeys(demandIds);

        if (CollectionUtils.isEmpty(establishmentPOS)) {
            return BaseResponse.error("数据不存在");
        }
        for (DemandForecastEstablishmentPO establishmentPO : establishmentPOS) {
            if (!establishmentPO.getVersionValue().equals(detailMap.get(establishmentPO.getId()))) {
                return BaseResponse.error("修改失败，数据已被修改，请刷新后重试");
            }
        }
        return BaseResponse.success();
    }

    @Override
    public int updateForecastValueByIds(List<DemandForecastModifyDetailDTO> detailDTOS) {
        int result = 0;
        if (CollectionUtils.isEmpty(detailDTOS)) {
            return result;
        }
        Date date = new Date();
        for (DemandForecastModifyDetailDTO detailDTO : detailDTOS) {
            detailDTO.setModifyTime(date);
            result += demandForecastEstablishmentDao.updateForecastValueById(detailDTO);
        }
        return result;
    }

    private void handleCreateVersion(DemandForecastModifyDTO demandForecastModifyDTO) {
        List<DemandForecastModifyDetailDTO> detailDTOS = demandForecastModifyDTO.getDetailDTOS();
        List<DemandForecastModifyDetailDTO> insertData = detailDTOS.stream()
                .filter(p -> null == p.getId()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(insertData)) {
            log.info("需求预测编制无数据新增");
            return;
        }

        List<DemandForecastEstablishmentDTO> insert = new ArrayList<>();
        String versionId = demandForecastModifyDTO.getVersionId();
        for (DemandForecastModifyDetailDTO data : insertData) {
            DemandForecastEstablishmentDTO demandForecastEstablishmentDO = new DemandForecastEstablishmentDTO();
            demandForecastEstablishmentDO.setId(UUIDUtil.getUUID());
            demandForecastEstablishmentDO.setForecastVersionId(versionId);
            demandForecastEstablishmentDO.setOemCode(data.getOemCode());
            demandForecastEstablishmentDO.setVehicleModelCode(data.getVehicleCode());
            demandForecastEstablishmentDO.setAccessPosition(data.getAccessPosition());
            demandForecastEstablishmentDO.setProductCode(data.getProductCode());
            demandForecastEstablishmentDO.setForecastTime(DateUtils.stringToDate(data.getForecastTime()));
            demandForecastEstablishmentDO.setDemandForecast(data.getForecastValue());
            demandForecastEstablishmentDO.setCustomerForecast(BigDecimal.ZERO);
            demandForecastEstablishmentDO.setAlgorithmForecast(BigDecimal.ZERO);
            insert.add(demandForecastEstablishmentDO);
        }
        this.doCreateBatch(insert);
        log.info("新增需求预测编制行数：{}", insert.size());
    }

    @Override
    public List<DemandForecastEstablishmentVehicleVO> selectProductDataByCondition(Map<String, Object> queryMap) {
        return demandForecastEstablishmentDao.selectProductDataByCondition(queryMap);
    }

    /**
     * 使用客户数据、算法预测、上版预测
     */
    @Override
    public BaseResponse<Void> modifyDataByOtherInfo(DemandForecastDetailUseOtherModifyDTO modifyDTO) {
        List<DemandForecastDetailProductDetailDTO> adjustProductCodes = modifyDTO.getAdjustProductCodes();
        if (CollectionUtils.isEmpty(adjustProductCodes)) {
            return BaseResponse.error("请至少选择一条数据");
        }
        List<String> detailIds = Lists.newArrayList();
        adjustProductCodes.forEach(x -> {
            List<String> xDetailIds = x.getDetailIds();
            if (CollectionUtils.isEmpty(xDetailIds)) {
                return;
            }
            for (String xDetailId : xDetailIds) {
                if (StringUtils.isBlank(xDetailId)) {
                    continue;
                }
                detailIds.add(xDetailId);
            }
        });
        if (CollectionUtils.isEmpty(detailIds)) {
            return BaseResponse.success("保存成功");
        }
        String demandType = modifyDTO.getDemandType();
        String forecastMethod = modifyDTO.getForecastMethod();
        String preVersionId = modifyDTO.getPreVersionId();
        String oemCode = modifyDTO.getOemCode();
        Map<String, Map<String, BigDecimal>> preForecastDataMap = Maps.newHashMap();
        if (ForecastMethodEnum.PRE_FORECAST.getCode().equals(forecastMethod)) {
            preForecastDataMap.putAll(getPreForecastData(demandType, preVersionId, oemCode,
                    adjustProductCodes.stream().map(DemandForecastDetailProductDetailDTO::getProductCode)
                            .distinct().collect(Collectors.toList())));
        }
        List<String> deleteIds = new ArrayList<>();
        List<DemandForecastEstablishmentPO> createList = new ArrayList<>();
        List<DemandForecastEstablishmentPO> establishmentPOS =
                demandForecastEstablishmentDao.selectByPrimaryKeys(detailIds);
        if (CollectionUtils.isEmpty(establishmentPOS)) {
            return BaseResponse.success("保存成功");
        }
        if (ForecastMethodEnum.CUSTOMER_FORECAST.getCode().equals(forecastMethod)) {
            for (DemandForecastEstablishmentPO establishmentPO : establishmentPOS) {
                establishmentPO.setDemandForecast(establishmentPO.getCustomerForecast());
            }
        } else if (ForecastMethodEnum.ALGORITHMIC_FORECAST.getCode().equals(forecastMethod)) {
            for (DemandForecastEstablishmentPO establishmentPO : establishmentPOS) {
                establishmentPO.setDemandForecast(establishmentPO.getAlgorithmForecast());
            }
        } else if (ForecastMethodEnum.PRE_FORECAST.getCode().equals(forecastMethod)) {
            // establishmentPOS根据productCode分组
            Map<String, List<DemandForecastEstablishmentPO>> productCodeGroup = establishmentPOS.stream()
                    .collect(Collectors.groupingBy(DemandForecastEstablishmentPO::getProductCode));

            for (DemandForecastEstablishmentPO establishmentPO : establishmentPOS) {
                Map<String, BigDecimal> decimalMap = preForecastDataMap.get(establishmentPO.getProductCode());
                if (MapUtils.isEmpty(decimalMap)) {
                    deleteIds.add(establishmentPO.getId());
                    continue;
                }
                BigDecimal decimal = decimalMap.get(DateUtils.dateToString(establishmentPO.getForecastTime(),
                        DateUtils.YEAR_MONTH));
                if (Objects.isNull(decimal)) {
                    deleteIds.add(establishmentPO.getId());
                    continue;
                }
                establishmentPO.setDemandForecast(decimal);
            }
            // 如果上版计划有数据，这版计划没有，需要新增
            preForecastDataMap.forEach((productCode, decimalMap) -> {
                List<DemandForecastEstablishmentPO> productEstablishmentPOS = productCodeGroup.get(productCode);
                // productEstablishmentPOS根据forecastTime转成map
                Map<String, DemandForecastEstablishmentPO> forecastTimeMap = productEstablishmentPOS.stream()
                        .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getForecastTime(),
                                        DateUtils.YEAR_MONTH),
                                Function.identity()));
                decimalMap.forEach((forecastTime, decimal) -> {
                    if (!forecastTimeMap.containsKey(forecastTime)) {
                        DemandForecastEstablishmentPO demandForecastEstablishmentPO =
                                new DemandForecastEstablishmentPO();
                        DemandForecastEstablishmentPO establishmentItem = productEstablishmentPOS.get(0);
                        demandForecastEstablishmentPO.setDemandCategory(establishmentItem.getDemandCategory());
                        demandForecastEstablishmentPO.setOemCode(establishmentItem.getOemCode());
                        demandForecastEstablishmentPO.setVehicleModelCode(establishmentItem.getVehicleModelCode());
                        demandForecastEstablishmentPO.setProductCode(establishmentItem.getProductCode());
                        demandForecastEstablishmentPO.setPartName(establishmentItem.getPartName());
                        demandForecastEstablishmentPO.setForecastVersionId(establishmentItem.getForecastVersionId());
                        demandForecastEstablishmentPO.setForecastTime(DateUtils.stringToDate(forecastTime + "-01"));
                        demandForecastEstablishmentPO.setDemandForecast(decimal);
                        createList.add(demandForecastEstablishmentPO);
                    }
                });
            });
        } else {
            return BaseResponse.error("使用预测类型不存在");
        }
        if (!CollectionUtils.isEmpty(deleteIds)) {
            demandForecastEstablishmentDao.deleteBatch(deleteIds);
        }
        if (!CollectionUtils.isEmpty(createList)) {
            BasePOUtils.insertBatchFiller(createList, new Date());
            demandForecastEstablishmentDao.insertBatch(createList);
        }
        this.doUpdateBatch(DemandForecastEstablishmentConvertor.INSTANCE.po2Dtos(establishmentPOS));
        return BaseResponse.success("保存成功");
    }

    @Override
    public List<ReleaseLineChartMonthVO> selectLineChartByYearAndOem(int calcYear, String oemCode) {
        List<ReleaseLineChartMonthVO> result = Lists.newArrayList();
        List<ReleaseLineChartDTO> releaseLineCharts =
                demandForecastEstablishmentDao.selectLineChartByYearAndOem(calcYear, oemCode);
        if (CollectionUtils.isEmpty(releaseLineCharts)) {
            return result;
        }
        releaseLineCharts.stream().collect(Collectors.groupingBy(ReleaseLineChartDTO::getForecastTime,
                        Collectors.mapping(ReleaseLineChartDTO::getDemandForecast,
                                Collectors.summingInt(Integer::intValue))))
                .forEach((key, value) -> {
                    ReleaseLineChartMonthVO chartMonthVO = new ReleaseLineChartMonthVO();
                    chartMonthVO.setColumn(key);
                    chartMonthVO.setQuantity(value);
                    result.add(chartMonthVO);
                });
        return result;
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DEMAND_FORECAST_ESTABLISHMENT.getCode();
    }

    @Override
    public List<DemandForecastEstablishmentVO> invocation(List<DemandForecastEstablishmentVO> dataList, Map<String,
            Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> doUpdateDemandForecastByComprehensiveCoefficient(List<DemandForecastDetailModifyVehicleDTO> modifyDTOList) {
        if (CollectionUtils.isEmpty(modifyDTOList)) {
            return BaseResponse.success("更新成功");
        }
        List<DemandForecastEstablishmentDTO> updateList = new ArrayList<>();
        for (DemandForecastDetailModifyVehicleDTO demandForecastDetailModifyVehicleDTO : modifyDTOList) {
            // 综评系数
            BigDecimal comprehensiveCoefficient = demandForecastDetailModifyVehicleDTO.getComprehensiveCoefficient();
            // 如果comprehensiveCoefficient为null，则赋默认值1
            if (Objects.isNull(comprehensiveCoefficient)) {
                comprehensiveCoefficient = BigDecimal.ONE;
            }
            // 需要调整的本厂编码明细数据
            List<DemandForecastDetailProductDetailDTO> adjustProductCodes =
                    demandForecastDetailModifyVehicleDTO.getAdjustProductCodes();
            List<String> detailIds = new ArrayList<>();
            for (DemandForecastDetailProductDetailDTO detailDTO : adjustProductCodes) {
                if (CollectionUtils.isEmpty(detailDTO.getDetailIds())) {
                    continue;
                }
                detailIds.addAll(detailDTO.getDetailIds());
            }
            List<DemandForecastEstablishmentPO> establishmentPOS =
                    demandForecastEstablishmentDao.selectByPrimaryKeys(detailIds);
            for (DemandForecastEstablishmentPO establishmentPO : establishmentPOS) {
                // 获取业务预测值
                BigDecimal demandForecast = establishmentPO.getDemandForecast();
                // 业务预测值*综评系数，向上取整
                establishmentPO.setDemandForecast(demandForecast.multiply(comprehensiveCoefficient).setScale(0,
                        RoundingMode.UP));
            }
            // 明细数据
            updateList.addAll(DemandForecastEstablishmentConvertor.INSTANCE.po2Dtos(establishmentPOS));
            // 修改主机厂车型综评系数
            OemVehicleModelDTO oemVehicleModelDTO = new OemVehicleModelDTO();
            oemVehicleModelDTO.setOemCode(demandForecastDetailModifyVehicleDTO.getOemCode());
            oemVehicleModelDTO.setOemVehicleModelCode(demandForecastDetailModifyVehicleDTO.getVehicleModelCode());
            oemVehicleModelDTO.setComprehensiveCoefficient(demandForecastDetailModifyVehicleDTO.getComprehensiveCoefficient());
            oemVehicleModelService.doUpdateComprehensiveCoefficient(oemVehicleModelDTO);
        }
        // 修改明细数据
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.doUpdateBatch(updateList);
        }
        return BaseResponse.success("更新成功");
    }

    @Override
    public List<DemandForecastEstablishmentVO> selectDataPermissionByParams(Map<String, Object> params) {
        List<DemandForecastEstablishmentPO> list = demandForecastEstablishmentDao.selectDataPermissionByParams(params);
        return DemandForecastEstablishmentConvertor.INSTANCE.po2Vos(list);
    }

	@Override
	public void exportTemplate(HttpServletResponse response) throws Exception {
		response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("需求预测评审-导入模板", "UTF-8") + ".xlsx");
        List<String> headers = new ArrayList<>();
        headers.add("主机厂编码*");
        headers.add("产品编码*");
        Date now = new Date();
        for (int i = 0; i < 13; i++) {
			String yearMonth = DateUtils.dateToString(DateUtils.moveMonth(now, i), "yyyyMM");
			headers.add(yearMonth);
			
		}
        List<List<String>> headerList = getHeadList(headers);
        EasyExcel.write(response.getOutputStream())
        .registerWriteHandler(new CustomCellWriteHandler(null, null))
        .head(headerList)
        .sheet("需求预测评审")
        .doWrite(new ArrayList<>());
	}
	
	private List<List<String>> getHeadList(List<String> headers) {
        List<List<String>> headerList = new ArrayList<>();
        for (String headerStr : headers) {
            List<String> tmpList = new ArrayList<>();
            tmpList.add(headerStr);
            headerList.add(tmpList);
        }
        return headerList;
    }

	@Override
	public void importExcel(List<String> headers, List<List<String>> datas, String demandCategory) {
		if(CollectionUtils.isEmpty(datas)) {
			throw new BusinessException("请维护需要导入的数据!");
		}
		//获取表头的起始时间，结束时间
		String startTimeStr = headers.get(2);
		String endTimeStr = headers.get(headers.size() -1);
		Date forecastStartTime = DateUtils.getMonthFirstDay(DateUtils.stringToDate(startTimeStr, "yyyyMM"));
		Date forecastEndTime = DateUtils.getMonthLastDay(DateUtils.stringToDate(endTimeStr, "yyyyMM"));
		
		//获取最新的版本号id
		String forecastVersionId = demandForecastReviewService.selectMaxDemandForecastVersionId();
		//获取当前预测数据
		List<DemandForecastEstablishmentPO> currentEstablishmentList = demandForecastEstablishmentDao.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"forecastVersionId" , forecastVersionId,
    			"forecastStartTime" , forecastStartTime,
    			"forecastEndTime" , forecastEndTime,
    			"demandCategory" , ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode()));
//    			"demandCategory" , demandCategory));
		//按照主机厂编码，产品编码，预测时间分组
		Map<String, DemandForecastEstablishmentPO> currentEstablishmentMap = currentEstablishmentList
				.stream().collect(Collectors.toMap(e -> 
				String.join("&", e.getOemCode(), e.getProductCode(),DateUtils.dateToString(e.getForecastTime(), "yyyyMM"))
				, e-> e,(v1, v2) -> v1));
		
		List<String> unkeyList = currentEstablishmentList.stream()
			.map(e -> String.join("&", e.getOemCode(), e.getProductCode()))
			.distinct().collect(Collectors.toList());	
		//校验必填数据及本厂编码，产品编码是否存在
		List<String> combinationStrList = new ArrayList<>();
		List<DemandForecastEstablishmentPO> updateList = new ArrayList<>();
//		List<DemandForecastEstablishmentPO> addList = new ArrayList<>();
		for (int i = 0; i < datas.size(); i++) {
			List<String> demandForecastEstablishments = datas.get(i);
			String oemCode = demandForecastEstablishments.get(0);
			String productCode = demandForecastEstablishments.get(1);
			int rowNum = i + 2;
			if(StringUtils.isEmpty(oemCode)) {
				throw new BusinessException("行数：" + rowNum + ";[主机厂编码]必填");
			}
			if(StringUtils.isEmpty(productCode)) {
				throw new BusinessException("行数：" + rowNum + ";[产品编码]必填");
			}
			String combinationStr = oemCode + "&" + productCode; 
			if(combinationStrList.contains(combinationStr)) {
				throw new BusinessException("行数：" + rowNum + ";数据重复导入");
			}
			if(!unkeyList.contains(combinationStr)) {
				throw new BusinessException("行数：" + rowNum + ";主机厂&产品编码数据不存在");
			}
			combinationStrList.add(combinationStr);
			for (int z = 2; z < demandForecastEstablishments.size(); z++) {
				String unkey = combinationStr + "&" +  headers.get(z);
				String fieldContent = demandForecastEstablishments.get(z);
				if(StringUtils.isEmpty(fieldContent)) {
					continue;
				}
				if(!isValidNonNegativeInteger(fieldContent)) {
					throw new BusinessException("行数：" + rowNum + ";日期：" + headers.get(z) + ";需求预测值格式维护异常");
				}
				DemandForecastEstablishmentPO currentEstablishmentPO = currentEstablishmentMap.get(unkey);
				if(currentEstablishmentPO == null) {
					//新增
//					DemandForecastEstablishmentPO add = new DemandForecastEstablishmentPO();
//					add.setForecastVersionId(forecastVersionId);
//					add.setDemandCategory(demandCategory);
//					add.setOemCode(oemCode);
//					add.setVehicleModelCode(fieldContent);
//					add.setAccessPosition(fieldContent);
//					add.setProductCode(productCode);
//					add.setForecastTime(DateUtils.getMonthFirstDay(DateUtils.stringToDate(headers.get(z), "yyyyMM")));
//					add.setDemandForecast(new BigDecimal(fieldContent));
//					add.setAlgorithmForecast(BigDecimal.ZERO);
//					add.setCustomerForecast(BigDecimal.ZERO);
//					addList.add(add);
				}else {
					//修改
					//维护对应需要更新的数据
					DemandForecastEstablishmentPO update = new DemandForecastEstablishmentPO();
					update.setId(currentEstablishmentPO.getId());
					update.setVersionValue(currentEstablishmentPO.getVersionValue());
					update.setDemandForecast(new BigDecimal(fieldContent));
					updateList.add(update);
				}
			}
			
		}
//		if(CollectionUtils.isNotEmpty(addList)) {
//			//处理所有数据的车型，位置
//			List<String> productCodes = addList.stream().map(DemandForecastEstablishmentPO::getProductCode).distinct().collect(Collectors.toList());
//			List<NewProductStockPointVO> products = newMdsFeign.selectProductStockPointByProducts(productCodes);
//			Map<String,String> vehicleModelCodeMap = products.stream().filter(e -> StringUtils.isNotEmpty(e.getVehicleModelCode()))
//					.collect(Collectors.toMap(NewProductStockPointVO::getProductCode,NewProductStockPointVO::getVehicleModelCode,(v1, v2) -> v1));
//			Map<String,String> loadingPositionSubMap = products.stream().filter(e -> StringUtils.isNotEmpty(e.getLoadingPositionSub()))
//					.collect(Collectors.toMap(NewProductStockPointVO::getProductCode,NewProductStockPointVO::getLoadingPositionSub,(v1, v2) -> v1));
//			for (DemandForecastEstablishmentPO demandForecastEstablishmentPO : addList) {
//				String productCode = demandForecastEstablishmentPO.getProductCode();
//				String vehicleModelCode = vehicleModelCodeMap.get(productCode);
//				String loadingPositionSub = loadingPositionSubMap.get(productCode);
//				if(StringUtils.isEmpty(vehicleModelCode)) {
//					throw new BusinessException("产品编码 :" + productCode + "未维护车型");
//				}
//				if(StringUtils.isEmpty(loadingPositionSub)) {
//					throw new BusinessException("产品编码 :" + productCode + "未维护装车位置小类");
//				}
//				demandForecastEstablishmentPO.setVehicleModelCode(vehicleModelCode);
//				demandForecastEstablishmentPO.setAccessPosition(loadingPositionSub);
//			}
//			//数据插入
//			BasePOUtils.insertBatchFiller(addList);
//			demandForecastEstablishmentDao.insertBatch(addList);
//		}
		if(CollectionUtils.isNotEmpty(updateList)) {
			//数据更新
			BasePOUtils.updateBatchFiller(updateList);
			demandForecastEstablishmentDao.updateBatchSelective(updateList);
		}

	}
	
	public static boolean isValidNonNegativeInteger(String str) {
	        if (str == null || str.isEmpty()) {
	            return false;
	        }
	        try {
	            int number = Integer.parseInt(str);
	            return number >= 0;
	        } catch (NumberFormatException e) {
	            return false;
	        }
	    }

	@SneakyThrows
	@Override
	public List<DeliveryForecastCompareVO> selectComparePage(DeliveryForecastCompareDTO dto) {
		Integer pageSize = dto.getPageSize();
		dto.setPageSize(9999);
		List<DeliveryForecastCompareVO> pageList = demandForecastEstablishmentDao.selectComparePage(dto);
		if(CollectionUtils.isEmpty(pageList)) {
			return new ArrayList<>();
		}
		dto.setPageSize(pageSize);
		List<String> productCodes = pageList.stream().map(DeliveryForecastCompareVO::getProductCode)
				.distinct().collect(Collectors.toList());
		List<String> oemCodes = pageList.stream().map(DeliveryForecastCompareVO::getOemCode)
				.distinct().collect(Collectors.toList());
		String forecastVersionId = pageList.get(0).getForecastVersionId();
		//获取mds模块标识
        BaseResponse<String> scenarioByTenantCode = ipsNewFeign.getScenarioByTenantCode(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = scenarioByTenantCode.getData();
        ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolFactory.instance();
        
        Date startTime = getStartTime(dto);
		Date endTime = DateUtils.getMonthLastDay(DateUtils.moveMonth(startTime, 1));
        try {
        	//1.异步查询物料计划员
    		CompletableFuture<Map<String, String>> productFuture = CompletableFuture.supplyAsync(() -> {
    			List<NewProductStockPointVO> productInfoList = newMdsFeign.selectProductStockPointByParams(scenario, ImmutableMap.of(
    	    			"enabled", YesOrNoEnum.YES.getCode(), 
    	    			"productCodes" , productCodes));
    			Map<String, String> productMap = productInfoList.stream().filter( e -> StringUtils.isNotEmpty(e.getOrderPlanner()))
    					.collect(Collectors.toMap(NewProductStockPointVO::getProductCode,NewProductStockPointVO::getOrderPlanner,(v1, v2) -> v1));
        		return productMap;
    		}, threadPoolExecutor);
    		List<User> userList = ipsNewFeign.userList();
    		Map<String, String> userMap = userList.stream()
    				.collect(Collectors.toMap(User::getId,User::getCnName,(v1, v2) -> v1));
    		//2.异步查询当月预测数据
    		CompletableFuture<Map<String, BigDecimal>> forecastFuture = CompletableFuture.supplyAsync(() -> {
    			String planPeriod = DateUtils.dateToString(new Date(), "yyyyMM");
    			List<DemandForecastEstablishmentVO> demandForecastEstablishments = demandForecastEstablishmentDao.selectMonthDemandForecast(forecastVersionId, planPeriod);
    			Map<String, BigDecimal> forecastMap = demandForecastEstablishments.stream()
    					.collect(Collectors.toMap( e-> String.join("&", e.getDemandCategory(), e.getOemCode(), 
    							e.getVehicleModelCode(), e.getProductCode()),DemandForecastEstablishmentVO::getDemandForecast,(v1, v2) -> v1));
        		return forecastMap;
    		}, threadPoolExecutor);
    		//3.异步取上个月最新一版已发布的一致性需求预测数据中的业务预测值
            CompletableFuture<Map<String, BigDecimal>> consistenceForecastFuture = CompletableFuture.supplyAsync(() -> {
            	String planPeriod = DateUtils.dateToString(DateUtils.moveMonth(startTime, -1), "yyyyMM");
                String consistenceDemandForecastVersionId = consistenceDemandForecastVersionDao.selectLatestPublishedVersionId(planPeriod);
                List<ConsistenceDemandForecastDataVO> consistenceForecastList = consistenceDemandForecastDataService
                		.selectForecastQuantityByVersionId(consistenceDemandForecastVersionId, null);
                Map<String, BigDecimal> consistenceForecastMap = consistenceForecastList.stream()
        				.collect(Collectors.toMap( e-> String.join("&", e.getDemandCategory(), e.getOemCode(), 
        						e.getVehicleModelCode(), e.getProductCode(), e.getForecastTimeStr()), ConsistenceDemandForecastDataVO::getForecastQuantity,(v1, v2) -> v1));
        		return consistenceForecastMap;
    		}, threadPoolExecutor);
            //4.获取实际出货同需求预测评审中当月预测的逻辑，分为”已发“和”待发“
            SwitchRelationVO switchRelation =
                    dfpSwitchRelationBetweenProductService.getSwitchRelation(oemCodes, productCodes);
            //获取仓库发货记录
            Map<String, String> newOldMap = switchRelation.getNewOldMap();
            Map<String, String> oldNewMap = switchRelation.getOldNewMap();
            List<String> allProductCodes = switchRelation.getAllProductCodes();
            String beginDate = DateUtils.dateToString(DateUtils.getMonthFirstDay(startTime));
            String endDate = DateUtils.dateToString(DateUtils.getMonthLastDay(startTime),
                    DateUtils.COMMON_DATE_STR3);
            Map<String, Object> warehouseQueryMap = Maps.newHashMap();
            warehouseQueryMap.put("beginDate", beginDate);
            warehouseQueryMap.put("endDate", endDate);
    		warehouseQueryMap.put("productCodes", allProductCodes);
    		warehouseQueryMap.put("oemCodes", oemCodes);
    		//异步获取仓库发货记录
    		CompletableFuture<Map<String, BigDecimal>> warehouseReleaseRecordFuture = CompletableFuture.supplyAsync(() -> {
    			List<WarehouseReleaseRecordDayVO> warehouseReleaseRecordDayList = warehouseReleaseRecordDao
    	                .selectDayVOByParams(warehouseQueryMap);	
    	        Map<String, BigDecimal> warehouseReleaseRecordMap =
    	                getWarehouseReleaseRecordMap(warehouseReleaseRecordDayList, newOldMap, oldNewMap);
    			return warehouseReleaseRecordMap;
    		}, threadPoolExecutor);
            //异步获取中转库发货记录
            CompletableFuture<Map<String, BigDecimal>> warehouseReleaseToRecordFuture = CompletableFuture.supplyAsync(() -> {
            	List<WarehouseReleaseToWarehouseDayVO> warehouseReleaseToWarehouses = warehouseReleaseToWarehouseService
                		.selectDayVOByParams(warehouseQueryMap);
                Map<String, BigDecimal> warehouseReleaseToRecordMap =
                		getWarehouseReleaseToRecord(warehouseReleaseToWarehouses, newOldMap, oldNewMap);
    			return warehouseReleaseToRecordMap;
    		}, threadPoolExecutor);
            //异步获取当月待发货数据
            Date startDemandTime = DateUtils.getDayFirstTime(startTime);
            Date endDemandTime = DateUtils.getMonthLastDay(startDemandTime);
            CompletableFuture<Map<String, BigDecimal>> deliveryPlanPublishedCurrentMonthFuture = CompletableFuture.supplyAsync(() -> {
            	if(StringUtils.isNotEmpty(dto.getYearMonth()) 
        			&& !Objects.equals(DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH), dto.getYearMonth())) {
            		return new HashMap<String, BigDecimal>();
            	}
            	List<DeliveryPlanPublishedDayVO> deliveryPlanPublishedCurrentDayList = deliveryPlanPublishedDao
                        .selectDayVOByItemCodes(oemCodes, allProductCodes,
                                startDemandTime, endDemandTime);
                Map<String, BigDecimal> deliveryPlanPublishedCurrentMonthMap = getDeliveryPlanPublished(switchRelation, deliveryPlanPublishedCurrentDayList);
    			return deliveryPlanPublishedCurrentMonthMap;
    		}, threadPoolExecutor);
            //获取异步返回结果，获取产品信息
            Map<String, String> productMap = productFuture.get();
            //获取当月及下个月的预测数据
            Map<String, BigDecimal> forecastMap = forecastFuture.get();
            //取上个月最新一版已发布的一致性需求预测数据中的业务预测值
            Map<String, BigDecimal> consistenceForecastMap = consistenceForecastFuture.get();
            //获取仓库发货数据
            Map<String, BigDecimal> warehouseReleaseRecordMap = warehouseReleaseRecordFuture.get();
            //获取中转库发货数据
            Map<String, BigDecimal> warehouseReleaseToRecordMap = warehouseReleaseToRecordFuture.get();
            //获取当月待发货数据
            Map<String, BigDecimal> deliveryPlanPublishedCurrentMonthMap = deliveryPlanPublishedCurrentMonthFuture.get();
            //动态表头月份值
            int currMonth = DateUtils.getMonthByDate(startTime) + 1;
            int nextMonth = DateUtils.getMonthByDate(endTime) + 1;
            //获取当前月的每一天
            List<String> monthDays = getMonthDays();
    		for (DeliveryForecastCompareVO compareVO : pageList) {
    			//维护计划员
    			String demandCategory = compareVO.getDemandCategory();
    			String oemCode = compareVO.getOemCode();
    			String vehicleModelCode = compareVO.getVehicleModelCode();
    			String productCode = compareVO.getProductCode();
    			String orderPlannerId = productMap.get(productCode);
    			if(StringUtils.isNotEmpty(orderPlannerId)) {
    				compareVO.setPlanUserName(userMap.get(orderPlannerId));
    			}
    			String unkey = String.join("&", demandCategory, oemCode, vehicleModelCode, productCode);
    			List<DeliveryForecastCompareDetailVO> details = new ArrayList<>();
    			BigDecimal currMonthForecast = consistenceForecastMap.getOrDefault(String.join("&", unkey, 
    					DateUtils.dateToString(startTime, DateUtils.YEAR_MONTH)), BigDecimal.ZERO);
    			details.add(new DeliveryForecastCompareDetailVO(currMonth + "月评审预测", currMonthForecast.stripTrailingZeros().toPlainString()));
    			BigDecimal currMonthNewForecast = forecastMap.getOrDefault(unkey, BigDecimal.ZERO);
    			details.add(new DeliveryForecastCompareDetailVO(currMonth + "月最新预测", 
    					currMonthNewForecast.stripTrailingZeros().toPlainString()));
    			//实际发货，获取已发，待发货数量
    			DeliveryForecastCompareDetailVO deliveryForecast = new DeliveryForecastCompareDetailVO(currMonth + "月实际", null);
    			details.add(deliveryForecast);
    			//预测达成率 = 实际出货/最新预测
    			DeliveryForecastCompareDetailVO forecastRate = new DeliveryForecastCompareDetailVO(currMonth + "月预测达成率", null);
    			details.add(forecastRate);
    			
    			BigDecimal nextMonthForecast = consistenceForecastMap.getOrDefault(String.join("&", unkey, 
    					DateUtils.dateToString(endTime, DateUtils.YEAR_MONTH)), BigDecimal.ZERO);
    			details.add(new DeliveryForecastCompareDetailVO(nextMonth + "月最新预测", 
    					nextMonthForecast.stripTrailingZeros().toPlainString()));
    			//维护每天的发货数量
    			BigDecimal totalDeliverydQty = BigDecimal.ZERO;
    			BigDecimal totalNotDeliverydQty = BigDecimal.ZERO;
    			for (String dayStr : monthDays) {
    				//当天及以后的发货
    				Date deliveryTime = DateUtils.stringToDate(dayStr);
    				String oemProductDayKey = String.join("&", oemCode, productCode, dayStr);
    				if(deliveryTime.before(new Date())) {
    					//历史发货，仓库+中转库
    					BigDecimal hasDeliverydQty = warehouseReleaseRecordMap.getOrDefault(oemProductDayKey, BigDecimal.ZERO)
    							.add(warehouseReleaseToRecordMap.getOrDefault(oemProductDayKey, BigDecimal.ZERO));
    					details.add(new DeliveryForecastCompareDetailVO(dayStr, 
    							hasDeliverydQty.stripTrailingZeros().toPlainString()));
    					totalDeliverydQty = totalDeliverydQty.add(hasDeliverydQty);
    				}else {
    					//当天及以后的发货
    					BigDecimal notDeliverydQty = deliveryPlanPublishedCurrentMonthMap.getOrDefault(oemProductDayKey, BigDecimal.ZERO);
    					details.add(new DeliveryForecastCompareDetailVO(dayStr, 
    							notDeliverydQty.stripTrailingZeros().toPlainString()));
    					totalNotDeliverydQty = totalNotDeliverydQty.add(notDeliverydQty);
    				}
    			}
    			deliveryForecast.setHeaderValue("已发:" + totalDeliverydQty.stripTrailingZeros().toPlainString() + ";待发:"
    					+ totalNotDeliverydQty.stripTrailingZeros().toPlainString());
    			if(StringUtils.isNotEmpty(dto.getYearMonth()) 
            			&& !Objects.equals(DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH), dto.getYearMonth())) {
    				deliveryForecast.setHeaderValue(totalDeliverydQty.stripTrailingZeros().toPlainString());
            	}
    			deliveryForecast.setDeliveryQty(totalDeliverydQty);
    			deliveryForecast.setNotDeliveryQty(totalNotDeliverydQty);
    			if(currMonthNewForecast.compareTo(BigDecimal.ZERO) == 0) {
    				forecastRate.setHeaderValue("100%");
    			}else {
    				String forecastRateStr = totalDeliverydQty.add(totalNotDeliverydQty)
    						.multiply(BigDecimal.valueOf(100))
    						.divide(currMonthNewForecast, 0, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    				forecastRate.setHeaderValue(forecastRateStr + "%");
    			}
    			compareVO.setDetails(details);
    		}
		} catch (Exception e) {
			 log.error("查询发货预测对比数据失败：", e);
		}finally {
			CustomThreadPoolFactory.closeOrShutdown(threadPoolExecutor);
		}
		//需求类型转换
		pageList.forEach( e-> {
			e.setDemandCategory(EnumUtils.getDescByCode(ProductionDemandTypeEnum.class, e.getDemandCategory()));
		});
		if(StringUtils.isNotEmpty(dto.getPlanUserName())) {
			pageList = pageList.stream().filter(e-> Objects.equals(dto.getPlanUserName(), e.getPlanUserName()))
					.collect(Collectors.toList());
		}
		pageList.sort(Comparator.comparing(DeliveryForecastCompareVO::getDemandCategory).reversed()
				.thenComparing(DeliveryForecastCompareVO::getOemCode)
				.thenComparing(DeliveryForecastCompareVO::getVehicleModelCode)
				.thenComparing(DeliveryForecastCompareVO::getProductCode)
				);
		return pageList;
	}

	private Date getStartTime(DeliveryForecastCompareDTO dto) {
		Date startTime = DateUtils.getMonthFirstDay(new Date());
		if(StringUtils.isNotEmpty(dto.getYearMonth())) {
			startTime = DateUtils.getMonthFirstDay(DateUtils.stringToDate(dto.getYearMonth(), DateUtils.YEAR_MONTH));
		}
		return startTime;
	}

	/**
	 * 获取当前月的每一天
	 * @return
	 */
	private List<String> getMonthDays() {
		// 获取当前年月
        YearMonth currentYearMonth = YearMonth.now();
        // 获取该月的第一天和最后一天
        LocalDate firstDayOfMonth = currentYearMonth.atDay(1);
        LocalDate lastDayOfMonth = currentYearMonth.atEndOfMonth();
        // 创建一个列表来存储每一天的字符串格式
        List<String> daysOfMonthStr = new ArrayList<>();
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 从第一天到最后一天，逐天格式化并添加到列表中
        for (LocalDate date = firstDayOfMonth; !date.isAfter(lastDayOfMonth); date = date.plusDays(1)) {
            daysOfMonthStr.add(date.format(formatter));
        }
        // 打印每个月的每一天字符串格式
        List<String> monthDays = new ArrayList<>();
        for (String dateStr : daysOfMonthStr) {
            monthDays.add(dateStr);
        }
		return monthDays;
	}
	
	private Map<String, BigDecimal> getWarehouseReleaseRecordMap(List<WarehouseReleaseRecordDayVO> records,
            Map<String, String> newOldMap,
            Map<String, String> oldNewMap) {
		 List<WarehouseReleaseRecordDayVO> filterList = records.parallelStream()
	                .filter(x -> x != null && x.getMonthDay() != null
	                        && StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null).collect(Collectors.toList());
	        Map<String, BigDecimal> qtyMap = filterList.stream().collect(Collectors.groupingBy(item ->
	                item.getItemCode() + Constants.DELIMITER + item.getMonthDay(), Collectors
	                .reducing(BigDecimal.ZERO, WarehouseReleaseRecordDayVO::getSumQty, BigDecimal::add)));

	        for (WarehouseReleaseRecordDayVO item : filterList) {
	            BigDecimal sumQty = item.getSumQty();
	            String itemCode = item.getItemCode();
	            String yearMonth = item.getMonthDay();
	            if (newOldMap.containsKey(itemCode)) {
	                String xItemCode = newOldMap.get(itemCode);
	                String xKey = String.join(Constants.DELIMITER, xItemCode, yearMonth);
	                sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
	            }
	            if (oldNewMap.containsKey(itemCode)) {
	                String yItemCode = oldNewMap.get(itemCode);
	                String yKey = String.join(Constants.DELIMITER, yItemCode, yearMonth);
	                sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
	            }
	            item.setSumQty(sumQty);
	        }
        return filterList.stream().collect(
                Collectors.groupingBy(e -> e.getOemCode() + "&" + e.getItemCode() + "&" + e.getMonthDay(),
                        Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseRecordDayVO::getSumQty,
                                BigDecimal::add)));
	}
	
	private Map<String, BigDecimal> getWarehouseReleaseToRecord(List<WarehouseReleaseToWarehouseDayVO> records,
            Map<String, String> newOldMap,
            Map<String, String> oldNewMap) {
		List<WarehouseReleaseToWarehouseDayVO> filterList = records.parallelStream()
				.filter(x -> x != null && x.getMonthDay() != null
					&& StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null).collect(Collectors.toList());
		Map<String, BigDecimal> qtyMap = filterList.stream().collect(Collectors.groupingBy(item ->
				item.getItemCode() + Constants.DELIMITER + item.getMonthDay(), Collectors
				.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseDayVO::getSumQty, BigDecimal::add)));
		
		for (WarehouseReleaseToWarehouseDayVO item : filterList) {
			BigDecimal sumQty = item.getSumQty();
			String itemCode = item.getItemCode();
			String yearMonth = item.getMonthDay();
			if (newOldMap.containsKey(itemCode)) {
				String xItemCode = newOldMap.get(itemCode);
				String xKey = String.join(Constants.DELIMITER, xItemCode, yearMonth);
				sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
			}
			if (oldNewMap.containsKey(itemCode)) {
				String yItemCode = oldNewMap.get(itemCode);
				String yKey = String.join(Constants.DELIMITER, yItemCode, yearMonth);
				sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
			}
			item.setSumQty(sumQty);
		}
			// 获取每个月的发货量数据
		return filterList.stream().collect(
				Collectors.groupingBy(e -> e.getOemCode() + "&" + e.getItemCode() + "&" + e.getMonthDay(),
						Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseDayVO::getSumQty,
								BigDecimal::add)));
	}
	
	private Map<String, BigDecimal> getDeliveryPlanPublished(SwitchRelationVO switchRelation,
    		List<DeliveryPlanPublishedDayVO> deliveryPlanPublishedCurrentDayList) {
    	Map<String, String> newOldMap = switchRelation.getNewOldMap();
    	Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        Map<String, BigDecimal> qtyMap = deliveryPlanPublishedCurrentDayList.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedDayVO::getProductCode,
                        Collectors.reducing(BigDecimal.ZERO, DeliveryPlanPublishedDayVO::getSumDemandQuantity,
                                BigDecimal::add)));
        for (DeliveryPlanPublishedDayVO item : deliveryPlanPublishedCurrentDayList) {
            BigDecimal sumQty = item.getSumDemandQuantity();
            String itemCode = item.getProductCode();
            if (newOldMap.containsKey(itemCode)) {
                String xKey = newOldMap.get(itemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
            }
            if (oldNewMap.containsKey(itemCode)) {
                String yKey = oldNewMap.get(itemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
            }
            item.setSumDemandQuantity(sumQty);
        }
        return deliveryPlanPublishedCurrentDayList
                .stream().collect(Collectors.groupingBy(e -> e.getOemCode() + "&" + e.getProductCode() + "&" + e.getMonthDay(),
                        Collectors.reducing(BigDecimal.ZERO, DeliveryPlanPublishedDayVO::getSumDemandQuantity,
                                BigDecimal::add)));
    }

	@Override
	public List<DeliveryForecastCompareVO> vehicleModelComparePage(DeliveryForecastCompareDTO dto) {
		//可通过需求类型，车型过滤数据
		List<DeliveryForecastCompareVO> pageList = this.selectComparePage(dto);
		if(CollectionUtils.isEmpty(pageList)) {
			return new ArrayList<>();
		}
		List<String> oemCodes = pageList.stream().map(DeliveryForecastCompareVO::getOemCode)
				.distinct().collect(Collectors.toList());
		List<String> productCodes = pageList.stream().map(DeliveryForecastCompareVO::getProductCode)
				.distinct().collect(Collectors.toList());
		List<String> vehicleModelCodes = pageList.stream().map(DeliveryForecastCompareVO::getVehicleModelCode)
				.distinct().collect(Collectors.toList());
		//查询主机厂，用于维护客户
		Map<String, Object> oemQueryMap = Maps.newHashMap();
        oemQueryMap.put("oemCodeList", oemCodes);
        List<OemVO> oemVOS = oemService.selectByParams(oemQueryMap);
        Map<String, OemVO> oemMap = oemVOS.stream().collect(Collectors.toMap(OemVO::getOemCode,e->e,(v1, v2) -> v1));
        //查询物料信息，维护计划员及装车位置小类
  		List<NewProductStockPointVO> productInfoList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of(
      			"enabled", YesOrNoEnum.YES.getCode(), 
      			"productCodes" , productCodes));
  		Map<String, String> productMap = productInfoList.stream().filter( e -> StringUtils.isNotEmpty(e.getOrderPlanner()))
  				.collect(Collectors.toMap(NewProductStockPointVO::getProductCode,NewProductStockPointVO::getOrderPlanner,(v1, v2) -> v1));
  		List<User> userList = ipsNewFeign.userList();
  		Map<String, String> userMap = userList.stream()
  				.collect(Collectors.toMap(User::getId,User::getCnName,(v1, v2) -> v1));
  		Map<String, String> loadingPositionSubMap = productInfoList.stream().filter( e -> StringUtils.isNotEmpty(e.getLoadingPositionSub()))
  				.collect(Collectors.toMap(NewProductStockPointVO::getProductCode,NewProductStockPointVO::getLoadingPositionSub,(v1, v2) -> v1));
  		//查询主机厂车型信息,用户维护取数位置
        List<OemVehicleModelVO> vehicleModelVOList =
                oemVehicleModelService.selectByParams(ImmutableMap.of("oemCodeList", oemCodes,
                		"oemVehicleModelCodeList", vehicleModelCodes));
        Map<String, String> accessPositionMap = vehicleModelVOList.stream()
        		.filter(e-> StringUtils.isNotEmpty(e.getAccessPosition()))
        		.collect(Collectors.toMap(s -> String.join("&", s.getOemCode(),
                        s.getOemVehicleModelCode()), OemVehicleModelVO::getAccessPosition, (k1, k2) -> k2));
  		//维护客户信息,装车位置，取数位置，计划员
        for (DeliveryForecastCompareVO compareVO : pageList) {
        	String oemCode = compareVO.getOemCode();
        	String vehicleModelCode = compareVO.getVehicleModelCode();
        	String productCode = compareVO.getProductCode();
        	String loadingPositionSub = loadingPositionSubMap.getOrDefault(productCode, "");
        	String accessPosition = accessPositionMap.getOrDefault(String.join("&", oemCode, vehicleModelCode), "");
        	compareVO.setLoadingPositionSub(loadingPositionSub);
        	compareVO.setAccessPosition(accessPosition);
        	//维护客户信息
        	OemVO oemVO = oemMap.get(oemCode);
        	if(oemVO == null) {
        		throw new BusinessException(oemCode + "为维护主机厂信息");
        	}
        	compareVO.setCustomerCode(oemVO.getCustomerCode());
        	compareVO.setCustomerName(oemVO.getCustomerName());
        	//维护计划员
			String orderPlannerId = productMap.get(productCode);
			if(StringUtils.isNotEmpty(orderPlannerId)) {
				compareVO.setPlanUserName(userMap.get(orderPlannerId));
			}
        }
  		//按照需求类型，客户编码，车型，计划员进行分组维护报表数据信息
        Map<String, List<DeliveryForecastCompareVO>> compareMap = pageList.stream()
        		.collect(Collectors.groupingBy(e -> String.join("&", e.getDemandCategory(), e.getCustomerCode(),
        				e.getVehicleModelCode(), e.getPlanUserName(), e.getAccessPosition())));
        List<DeliveryForecastCompareVO> resultList = new ArrayList<>();
        for (List<DeliveryForecastCompareVO> compareList : compareMap.values()){
        	compareList = compareList.stream().filter(e-> Objects.equals(e.getAccessPosition(), e.getLoadingPositionSub()))
        			.collect(Collectors.toList());
        	if(CollectionUtils.isEmpty(compareList)) {
        		continue;
        	}
        	DeliveryForecastCompareVO sourceCompare= compareList.get(0);
        	List<DeliveryForecastCompareDetailVO> firstDetails = sourceCompare.getDetails();
        	DeliveryForecastCompareVO compareAdd = new DeliveryForecastCompareVO();
        	compareAdd.setDemandCategory(sourceCompare.getDemandCategory());
        	compareAdd.setCustomerCode(sourceCompare.getCustomerCode());
        	compareAdd.setCustomerName(sourceCompare.getCustomerName());
        	compareAdd.setVehicleModelCode(sourceCompare.getVehicleModelCode());
        	compareAdd.setPlanUserName(sourceCompare.getPlanUserName());
        	List<DeliveryForecastCompareDetailVO> details = new ArrayList<>();
        	BigDecimal newForecastHeaderValue = BigDecimal.ZERO;
        	BigDecimal deliveryHeaderValue = BigDecimal.ZERO;
        	for (int i = 0; i < firstDetails.size(); i++) {
        		DeliveryForecastCompareDetailVO detail = new DeliveryForecastCompareDetailVO(firstDetails.get(i).getHeaderName(), firstDetails.get(i).getHeaderValue());
        		BigDecimal totalHeaderValue = BigDecimal.ZERO;
        		BigDecimal totalDeliveryQtyValue = BigDecimal.ZERO;
        		BigDecimal totalNotDeliveryQtyValue = BigDecimal.ZERO;
        		for (DeliveryForecastCompareVO deliveryForecastCompareVO : compareList) {
					List<DeliveryForecastCompareDetailVO> currDetails = deliveryForecastCompareVO.getDetails();
					if(i == 2) {
						//实际发货
						totalDeliveryQtyValue = totalDeliveryQtyValue.add(currDetails.get(i).getDeliveryQty());
						totalNotDeliveryQtyValue = totalNotDeliveryQtyValue.add(currDetails.get(i).getNotDeliveryQty());
					}else if(i == 3) {
						//达成率统计
						continue;
					}else {
						totalHeaderValue = totalHeaderValue.add(new BigDecimal(currDetails.get(i).getHeaderValue()));
					}
				}
        		if(i == 1) {
					//最新预测
        			newForecastHeaderValue = totalHeaderValue;
        			detail.setHeaderValue(newForecastHeaderValue.stripTrailingZeros().toPlainString());
				}else if(i == 2) {
					//实际发货
					deliveryHeaderValue = totalDeliveryQtyValue.add(totalNotDeliveryQtyValue);
					detail.setHeaderValue("已发:" + totalDeliveryQtyValue.stripTrailingZeros().toPlainString() + ";待发:"
							+ totalNotDeliveryQtyValue.stripTrailingZeros().toPlainString());
				}else if(i == 3) {
					//达成率统计 预测达成率 = 实际出货/最新预测
					if(newForecastHeaderValue.compareTo(BigDecimal.ZERO) == 0) {
						detail.setHeaderValue("100%");
					}else {
						String forecastRateStr = deliveryHeaderValue.multiply(BigDecimal.valueOf(100))
								.divide(newForecastHeaderValue, 0, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
						detail.setHeaderValue(forecastRateStr + "%");
					}
				}else {
					detail.setHeaderValue(totalHeaderValue.stripTrailingZeros().toPlainString());
				}
				details.add(detail);
			}
        	compareAdd.setDetails(details);
        	resultList.add(compareAdd);
		}
        //数据过滤
        if(StringUtils.isNotEmpty(dto.getPlanUserName())) {
        	resultList = resultList.stream().filter(e-> Objects.equals(dto.getPlanUserName(), e.getPlanUserName()))
					.collect(Collectors.toList());
		}
        if(StringUtils.isNotEmpty(dto.getCustomerCode())) {
        	resultList = resultList.stream().filter(e-> Objects.equals(dto.getCustomerCode(), e.getCustomerCode()))
					.collect(Collectors.toList());
		}
        if(StringUtils.isNotEmpty(dto.getCustomerName())) {
        	resultList = resultList.stream().filter(e-> Objects.equals(dto.getCustomerName(), e.getCustomerName()))
					.collect(Collectors.toList());
		}
        resultList.sort(Comparator.comparing(DeliveryForecastCompareVO::getDemandCategory).reversed()
				.thenComparing(DeliveryForecastCompareVO::getCustomerCode)
				.thenComparing(DeliveryForecastCompareVO::getVehicleModelCode)
				);
		return resultList;
	}
	
	

}