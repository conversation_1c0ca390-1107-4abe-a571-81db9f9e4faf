package com.yhl.scp.dfp.delivery.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanPublishedCompareConvertor;
import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanPublishedCompareDO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanPublishedCompareDomainService;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedCompareDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedCompareDao;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedComparePO;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedPO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedCompareDayService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedCompareService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.rule.dto.AlgorithmConstraintRuleDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yhl.platform.common.utils.DateUtils.COMMON_DATE_STR1;
import static com.yhl.platform.common.utils.DateUtils.COMMON_DATE_STR3;

/**
 * <code>DeliveryPlanPublishedCompareServiceImpl</code>
 * <p>
 * 发货计划发布数量变化对比表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-25 10:53:27
 */
@Slf4j
@Service
public class DeliveryPlanPublishedCompareServiceImpl extends AbstractService implements DeliveryPlanPublishedCompareService {

    @Resource
    private DeliveryPlanPublishedCompareDao deliveryPlanPublishedCompareDao;

    @Resource
    private DeliveryPlanPublishedCompareDomainService deliveryPlanPublishedCompareDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private MdsFeign mdsFeign;
    @Resource
    private MpsFeign mpsFeign;
    @Resource
    private DeliveryPlanPublishedDao deliveryPlanPublishedDao;
    @Resource
    private DeliveryPlanPublishedCompareDayService deliveryPlanPublishedCompareDayService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(DeliveryPlanPublishedCompareDTO deliveryPlanPublishedCompareDTO) {
        // 0.数据转换
        DeliveryPlanPublishedCompareDO deliveryPlanPublishedCompareDO = DeliveryPlanPublishedCompareConvertor.INSTANCE.dto2Do(deliveryPlanPublishedCompareDTO);
        DeliveryPlanPublishedComparePO deliveryPlanPublishedComparePO = DeliveryPlanPublishedCompareConvertor.INSTANCE.dto2Po(deliveryPlanPublishedCompareDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanPublishedCompareDomainService.validation(deliveryPlanPublishedCompareDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryPlanPublishedComparePO);
        deliveryPlanPublishedCompareDao.insert(deliveryPlanPublishedComparePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(DeliveryPlanPublishedCompareDTO deliveryPlanPublishedCompareDTO) {
        // 0.数据转换
        DeliveryPlanPublishedCompareDO deliveryPlanPublishedCompareDO = DeliveryPlanPublishedCompareConvertor.INSTANCE.dto2Do(deliveryPlanPublishedCompareDTO);
        DeliveryPlanPublishedComparePO deliveryPlanPublishedComparePO = DeliveryPlanPublishedCompareConvertor.INSTANCE.dto2Po(deliveryPlanPublishedCompareDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanPublishedCompareDomainService.validation(deliveryPlanPublishedCompareDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryPlanPublishedComparePO);
        deliveryPlanPublishedCompareDao.update(deliveryPlanPublishedComparePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryPlanPublishedCompareDTO> list) {
        List<DeliveryPlanPublishedComparePO> newList = DeliveryPlanPublishedCompareConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryPlanPublishedCompareDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryPlanPublishedCompareDTO> list) {
        List<DeliveryPlanPublishedComparePO> newList = DeliveryPlanPublishedCompareConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryPlanPublishedCompareDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return deliveryPlanPublishedCompareDao.deleteBatch(idList);
        }
        return deliveryPlanPublishedCompareDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DeliveryPlanPublishedCompareVO selectByPrimaryKey(String id) {
        DeliveryPlanPublishedComparePO po = deliveryPlanPublishedCompareDao.selectByPrimaryKey(id);
        return DeliveryPlanPublishedCompareConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED_COMPARE")
    public List<DeliveryPlanPublishedCompareVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        queryCriteriaParam = deliveryPlanPublishedCompareDayService.assemblyPermission(queryCriteriaParam);
        if (StringUtils.isEmpty(sortParam)) {
            sortParam = "demand_time, vehicle_model_code, product_code";
        }
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }



    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED_COMPARE")
    public List<DeliveryPlanPublishedCompareVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryPlanPublishedCompareVO> dataList = deliveryPlanPublishedCompareDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryPlanPublishedCompareServiceImpl target = springBeanUtils.getBean(DeliveryPlanPublishedCompareServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryPlanPublishedCompareVO> selectByParams(Map<String, Object> params) {
        List<DeliveryPlanPublishedComparePO> list = deliveryPlanPublishedCompareDao.selectByParams(params);
        return DeliveryPlanPublishedCompareConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DeliveryPlanPublishedCompareVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DELIVERY_PLAN_PUBLISHED_COMPARE.getCode();
    }

    @Override
    public List<DeliveryPlanPublishedCompareVO> invocation(List<DeliveryPlanPublishedCompareVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doGenerateDeliveryPlanPublishedCompare(String scenario) {

        AlgorithmConstraintRuleDTO ruleDTO = mpsFeign.getAlgorithmConstraintRuleMap(scenario);
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        Date planStartTime = planningHorizon.getPlanStartTime();
        //默认设置为7天
        int offset = 7;
        if (ruleDTO != null && ruleDTO.getRuleValue() !=null){
            offset = Integer.parseInt(ruleDTO.getRuleValue());
        }
        Date endTime = DateUtils.moveDay(planStartTime, offset);
        Map<String, Object> params = new HashMap<>();
        params.put("startTimeStr", DateUtils.dateToString(planStartTime, COMMON_DATE_STR1));
        params.put("endTimeStr", DateUtils.dateToString(endTime, COMMON_DATE_STR1));
        params.put("publisher", SystemHolder.getUserId());
        List<DeliveryPlanPublishedPO> deliveryPlanPublishedPOList =
                deliveryPlanPublishedDao.selectByParams(params);
        if (CollectionUtils.isEmpty(deliveryPlanPublishedPOList)) {
            return;
        }

        // deliveryPlanPublishedPOList = deliveryPlanPublishedPOList.stream()
        //         .filter(t->t.getDemandTime().compareTo(endTime) <= 0 && t.getDemandTime().compareTo(planStartTime) >= 0 && SystemHolder.getUserId().equals(t.getPublisher())).collect(Collectors.toList());
        Map<String, Integer> newQtyMap = deliveryPlanPublishedPOList.stream()
                .collect(Collectors.groupingBy(t->t.getProductCode() + "&" + DateUtils.dateToString(t.getDemandTime(), COMMON_DATE_STR3),
                        Collectors.summingInt(DeliveryPlanPublishedPO::getDemandQuantity)));
        //查询旧版数据
        List<DeliveryPlanPublishedComparePO> deliveryPlanPublishedComparePOS = deliveryPlanPublishedCompareDao.selectByParams(new HashMap<>());
        List<DeliveryPlanPublishedComparePO> currUserNewData = deliveryPlanPublishedComparePOS.stream().filter(t -> SystemHolder.getUserId().equals(t.getPublisher())).collect(Collectors.toList());
        //删除时间范围外的数据
        List<String> deleteIds = deliveryPlanPublishedComparePOS.stream()
                .filter(t -> t.getDemandTime().compareTo(endTime) > 0 && t.getDemandTime().compareTo(planStartTime) < 0).map(BasePO::getId).collect(Collectors.toList());
        List<String> productList = deliveryPlanPublishedPOList.stream().map(DeliveryPlanPublishedPO::getProductCode).distinct().collect(Collectors.toList());
        productList.addAll(deliveryPlanPublishedComparePOS.stream().map(DeliveryPlanPublishedComparePO::getProductCode).distinct().collect(Collectors.toList()));
        productList = productList.stream().distinct().collect(Collectors.toList());

        Map<String, Integer> oldQtyMap = currUserNewData.stream().collect(Collectors.groupingBy(t->t.getProductCode() + "&" + DateUtils.dateToString(t.getDemandTime(), COMMON_DATE_STR3), Collectors.summingInt(DeliveryPlanPublishedComparePO::getDemandQuantity)));
        String mdsScenario = getMdsScenario();
        //查询本厂编码对应数据，获取车型编码
        params = new HashMap<>();
        params.put("productCodes", productList);
        List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductStockPointByParams(mdsScenario, params);
        Map<String, String> vehicleModelMap = newProductStockPointVOList.stream().filter(t-> StringUtils.isNotEmpty(t.getVehicleModelCode())).collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode, (k1, k2) -> k2));

        Map<String, DeliveryPlanPublishedPO> newPlanDataMap =
                deliveryPlanPublishedPOList.stream().collect(Collectors.toMap(t -> t.getProductCode() + "&" + DateUtils.dateToString(t.getDemandTime(), COMMON_DATE_STR3), Function.identity(),(t1,t2) ->t2));
        Map<String, DeliveryPlanPublishedComparePO> oldPlanDataMap = deliveryPlanPublishedComparePOS.stream().collect(Collectors.toMap(t -> t.getProductCode() + "&" + DateUtils.dateToString(t.getDemandTime(), COMMON_DATE_STR3), Function.identity()));

        List<DeliveryPlanPublishedComparePO> updateList = new ArrayList<>();
        Set<String> dataList = new HashSet<>(newPlanDataMap.keySet());
        // dataList.addAll(oldPlanDataMap.keySet());
        List<DeliveryPlanPublishedComparePO> insertList = new ArrayList<>();
        for (String key : dataList) {
            DeliveryPlanPublishedComparePO comparePO = new DeliveryPlanPublishedComparePO();
            Integer newQty = newQtyMap.get(key);
            if (newQty == null){
                log.info("key:{},newQty为null", key);
                newQty = 0;
            }
            if (oldPlanDataMap.containsKey(key)){
                DeliveryPlanPublishedComparePO publishedComparePO = oldPlanDataMap.get(key);
                BeanUtils.copyProperties(publishedComparePO, comparePO);
                Integer oldQty = oldQtyMap.get(key);
                if (oldQty == null){
                    log.info("key:{},oldQty为null", key);
                    oldQty = 0;
                }
                //变化量
                int i = newQty - oldQty;
                if (i != 0){
                    comparePO.setEnabled(YesOrNoEnum.YES.getCode());
                }else {
                    comparePO.setEnabled(YesOrNoEnum.NO.getCode());
                }
                comparePO.setDemandQuantity(newQty);
                comparePO.setVariableQuantity(i);
                updateList.add(comparePO);
            }else {
                DeliveryPlanPublishedPO po = newPlanDataMap.get(key);
                BeanUtils.copyProperties(po, comparePO);
                comparePO.setDemandQuantity(newQty);
                comparePO.setVariableQuantity(newQty);
                if (newQty != 0){
                    comparePO.setEnabled(YesOrNoEnum.YES.getCode());
                }else {
                    comparePO.setEnabled(YesOrNoEnum.NO.getCode());
                }
                comparePO.setId(UUIDUtil.getUUID());
                comparePO.setVehicleModelCode(vehicleModelMap.get(comparePO.getProductCode()));
                insertList.add(comparePO);
            }
        }
        if (CollectionUtils.isNotEmpty(deleteIds)){
            deliveryPlanPublishedCompareDao.deleteBatch(deleteIds);
        }
        if (CollectionUtils.isNotEmpty(insertList)){
            BasePOUtils.insertBatchFiller(insertList);
            deliveryPlanPublishedCompareDao.insertBatchWithPrimaryKey(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            BasePOUtils.updateBatchFiller(updateList);
            deliveryPlanPublishedCompareDao.updateBatch(updateList);
        }
    }

    private String getMdsScenario() {
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        return defaultScenario.getData();
    }

    /**
     * 变更同步关闭-批量失效
     * @param params
     * @return
     */
    @Deprecated
    public void doEnable(Map<String, Object> params) {
        //ids为空直接失效全部数据
        deliveryPlanPublishedCompareDao.updateEnabled(params);
    }
}
