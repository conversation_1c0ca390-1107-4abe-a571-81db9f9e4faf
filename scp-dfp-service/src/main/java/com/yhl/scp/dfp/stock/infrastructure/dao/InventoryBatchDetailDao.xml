<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.stock.infrastructure.dao.InventoryBatchDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.stock.infrastructure.po.InventoryBatchDetailPO">
        <!--@Table fdp_inventory_batch_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="original_product_code" jdbcType="VARCHAR" property="originalProductCode"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="subinventory" jdbcType="VARCHAR" property="subinventory"/>
        <result column="subinventory_description" jdbcType="VARCHAR" property="subinventoryDescription"/>
        <result column="freight_space" jdbcType="VARCHAR" property="freightSpace"/>
        <result column="freight_space_description" jdbcType="VARCHAR" property="freightSpaceDescription"/>
        <result column="batch" jdbcType="VARCHAR" property="batch"/>
        <result column="bar_code" jdbcType="VARCHAR" property="barCode"/>
        <result column="current_quantity" jdbcType="VARCHAR" property="currentQuantity"/>
        <result column="customer_num" jdbcType="VARCHAR" property="customerNum"/>
        <result column="part_num" jdbcType="VARCHAR" property="partNum"/>
        <result column="assigned_time" jdbcType="VARCHAR" property="assignedTime"/>
        <result column="last_update_date" jdbcType="VARCHAR" property="lastUpdateDate"/>
        <result column="stock_age" jdbcType="VARCHAR" property="stockAge"/>
        <result column="stock_age_day" jdbcType="VARCHAR" property="stockAgeDay"/>
        <result column="warranty_date" jdbcType="VARCHAR" property="warrantyDate"/>
        <result column="distance_enable_date" jdbcType="VARCHAR" property="distanceEnableDate"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="original_org_id" jdbcType="VARCHAR" property="originalOrgId"/>
        <result column="allocation_status" jdbcType="VARCHAR" property="allocationStatus"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO">
        <!-- TODO -->
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
<!--        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>-->
        <result column="measurement_unit" jdbcType="VARCHAR" property="measurementUnit"/>
        <result column="loading_Position" jdbcType="VARCHAR" property="loadingPosition"/>
    </resultMap>
    <resultMap id="VOResultMap02" extends="BaseResultMap" type="com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO">
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="product_classify" jdbcType="VARCHAR" property="productClassify"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="overdue_days" jdbcType="INTEGER" property="overdueDays"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,stock_point_code,product_code,original_product_code,operation_code,subinventory,subinventory_description,freight_space,freight_space_description,batch,bar_code,current_quantity
        ,customer_num,part_num,assigned_time,last_update_date,stock_age,stock_age_day,warranty_date,distance_enable_date,source_type
        ,original_org_id,allocation_status,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,stock_point_name,product_name,measurement_unit
    </sql>
    <sql id="VO_Column_List_02">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,stock_point_name,product_name,product_classify,product_id,stock_point_id,product_color,product_type,product_thickness
    </sql>
    <sql id="VO_Column_List_03">
        <include refid="Base_Column_List"/>,batch,bar_code,customer_num,part_num,assigned_time,last_update_date
        ,stock_age,stock_age_day,warranty_date,distance_enable_date,source_type,original_org_id,remark,enabled,creator
        ,modifier
    </sql>
    <sql id="VO_Column_List_04">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,overdue_days
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodeList != null and params.stockPointCodeList.size() > 0">
                and stock_point_code in
                <foreach collection="params.stockPointCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.originalProductCode != null and params.originalProductCode != ''">
                and original_product_code = #{params.originalProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.originalProductCodes != null and params.originalProductCodes.size() > 0">
                and original_product_code in
                <foreach collection="params.originalProductCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.originalProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.subinventory != null and params.subinventory != ''">
                and subinventory = #{params.subinventory,jdbcType=VARCHAR}
            </if>
            <if test="params.subinventoryDescription != null and params.subinventoryDescription != ''">
                and subinventory_description = #{params.subinventoryDescription,jdbcType=VARCHAR}
            </if>
            <if test="params.freightSpace != null and params.freightSpace != ''">
                and freight_space = #{params.freightSpace,jdbcType=VARCHAR}
            </if>
            <if test="params.freightSpaceDescription != null and params.freightSpaceDescription != ''">
                and freight_space_description = #{params.freightSpaceDescription,jdbcType=VARCHAR}
            </if>
            <if test="params.batch != null and params.batch != ''">
                and batch = #{params.batch,jdbcType=VARCHAR}
            </if>
            <if test="params.barCode != null and params.barCode != ''">
                and bar_code = #{params.barCode,jdbcType=VARCHAR}
            </if>
            <if test="params.currentQuantity != null and params.currentQuantity != ''">
                and current_quantity = #{params.currentQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.customerNum != null and params.customerNum != ''">
                and customer_num = #{params.customerNum,jdbcType=VARCHAR}
            </if>
            <if test="params.partNum != null and params.partNum != ''">
                and part_num = #{params.partNum,jdbcType=VARCHAR}
            </if>
            <if test="params.assignedTime != null and params.assignedTime != ''">
                and assigned_time = #{params.assignedTime,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null and params.lastUpdateDate != ''">
                and last_update_date = #{params.lastUpdateDate,jdbcType=VARCHAR}
            </if>
            <if test="params.stockAge != null and params.stockAge != ''">
                and stock_age = #{params.stockAge,jdbcType=VARCHAR}
            </if>
            <if test="params.stockAgeDay != null and params.stockAgeDay != ''">
                and stock_age_day = #{params.stockAgeDay,jdbcType=VARCHAR}
            </if>
            <if test="params.warrantyDate != null and params.warrantyDate != ''">
                and warranty_date = #{params.warrantyDate,jdbcType=VARCHAR}
            </if>
            <if test="params.distanceEnableDate != null and params.distanceEnableDate != ''">
                and distance_enable_date = #{params.distanceEnableDate,jdbcType=VARCHAR}
            </if>
            <if test="params.sourceType != null and params.sourceType != ''">
                and source_type = #{params.sourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.originalOrgId != null and params.originalOrgId != ''">
                and original_org_id = #{params.originalOrgId,jdbcType=VARCHAR}
            </if>
            <if test="params.productClassify != null and params.productClassify != ''">
                and product_classify = #{params.productClassify,jdbcType=VARCHAR}
            </if>
            <if test="params.allocationStatus != null and params.allocationStatus != ''">
                and allocation_status = #{params.allocationStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>

    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_inventory_batch_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_inventory_batch_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select stock_point_code,
        stock_point_name,
        batch,
        customer_num,
        subinventory,
        subinventory_description,
        product_code,
        operation_code,
        product_name,
        freight_space,
        freight_space_description,
        create_time,
        measurement_unit,
        version_value,
        modify_time,
        current_quantity
        from v_fdp_inventory_real_time_data
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>

    <select id="selectByConditionOurFactory" resultMap="VOResultMap02">
        <!-- TODO -->
        select
        <include refid="VO_Column_List_04"/>
        from v_glass_inventory
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                and ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>

    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="VO_Column_List_03"/>
        from fdp_inventory_batch_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectVOByParams" resultMap="VOResultMap02">
        select
        <include refid="VO_Column_List_02"/>
        from v_fdp_inventory_batch_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectRealTimeInventory" resultMap="VOResultMap">
        SELECT
            t.oem_code,
            t.product_code,
            SUM( t.current_quantity ) current_quantity
        FROM
            (
                SELECT
                    ospm.oem_code,
                    ng.raw_material product_code,
                    sum( ibd.current_quantity ) current_quantity
                FROM
                    mps_naked_glass ng
                        LEFT JOIN fdp_inventory_batch_detail ibd ON ng.semi_finished_product = ibd.product_code
                        LEFT JOIN mds_stock_point sp ON sp.stock_point_code = ibd.stock_point_code
                        LEFT JOIN mds_oem_stock_point_map ospm ON ospm.stock_point_code = ibd.stock_point_code
                WHERE
                    sp.stock_point_type IN ( "GN", "CK" )
                  AND ibd.current_quantity &gt; 0
                GROUP BY
                    ospm.oem_code,
                    ng.raw_material UNION ALL
                SELECT
                    ospm.oem_code,
                    ng.raw_material product_code,
                    sum( ibd.current_quantity ) current_quantity
                FROM
                    mps_naked_glass ng
                        LEFT JOIN fdp_inventory_batch_detail ibd ON ng.finished_product = ibd.product_code
                        LEFT JOIN mds_stock_point sp ON sp.stock_point_code = ibd.stock_point_code
                        LEFT JOIN mds_oem_stock_point_map ospm ON ospm.stock_point_code = ibd.stock_point_code
                WHERE
                    sp.stock_point_type IN ( "GN", "CK" )
                  AND ibd.current_quantity &gt; 0
                GROUP BY
                    ospm.oem_code,
                    ng.raw_material UNION ALL
                SELECT
                    t3.oem_code,
                    t1.product_code,
                    SUM( t1.current_quantity ) current_quantity
                FROM
                    fdp_inventory_batch_detail t1
                        LEFT JOIN mds_stock_point t2 ON t2.stock_point_code = t1.stock_point_code
                        LEFT JOIN mds_oem_stock_point_map t3 ON t3.stock_point_code = t1.stock_point_code
                WHERE
                    t2.stock_point_type IN ( "GN", "CK" )
                  AND t1.current_quantity &gt; 0
                GROUP BY
                    t3.oem_code,
                    t1.product_code
            ) t
        where
        t.product_code in
        <foreach collection="productCodes" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        GROUP BY
            t.oem_code,
            t.product_code
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryBatchDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid())
            from dual
        </selectKey>
        insert into fdp_inventory_batch_detail(id,
                                               stock_point_code,
                                               product_code,
                                               original_product_code,
                                               operation_code,
                                               subinventory,
                                               subinventory_description,
                                               freight_space,
                                               freight_space_description,
                                               batch,
                                               bar_code,
                                               current_quantity,
                                               customer_num,
                                               part_num,
                                               assigned_time,
                                               last_update_date,
                                               stock_age,
                                               stock_age_day,
                                               warranty_date,
                                               distance_enable_date,
                                               source_type,
                                               original_org_id,
                                               allocation_status,
                                               remark,
                                               enabled,
                                               creator,
                                               create_time,
                                               modifier,
                                               modify_time,
                                               version_value)
        values (#{id,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{originalProductCode,jdbcType=VARCHAR},
                #{operationCode,jdbcType=VARCHAR},
                #{subinventory,jdbcType=VARCHAR},
                #{subinventoryDescription,jdbcType=VARCHAR},
                #{freightSpace,jdbcType=VARCHAR},
                #{freightSpaceDescription,jdbcType=VARCHAR},
                #{batch,jdbcType=VARCHAR},
                #{barCode,jdbcType=VARCHAR},
                #{currentQuantity,jdbcType=VARCHAR},
                #{customerNum,jdbcType=VARCHAR},
                #{partNum,jdbcType=VARCHAR},
                #{assignedTime,jdbcType=VARCHAR},
                #{lastUpdateDate,jdbcType=VARCHAR},
                #{stockAge,jdbcType=VARCHAR},
                #{stockAgeDay,jdbcType=VARCHAR},
                #{warrantyDate,jdbcType=VARCHAR},
                #{distanceEnableDate,jdbcType=VARCHAR},
                #{sourceType,jdbcType=VARCHAR},
                #{originalOrgId,jdbcType=VARCHAR},
                #{allocationStatus,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryBatchDetailPO">
        insert into fdp_inventory_batch_detail(id,
                                               stock_point_code,
                                               product_code,
                                               original_product_code,
                                               operation_code,
                                               subinventory,
                                               subinventory_description,
                                               freight_space,
                                               freight_space_description,
                                               batch,
                                               bar_code,
                                               current_quantity,
                                               customer_num,
                                               part_num,
                                               assigned_time,
                                               last_update_date,
                                               stock_age,
                                               stock_age_day,
                                               warranty_date,
                                               distance_enable_date,
                                               source_type,
                                               original_org_id,
                                               allocation_status,
                                               remark,
                                               enabled,
                                               creator,
                                               create_time,
                                               modifier,
                                               modify_time,
                                               version_value)
        values (#{id,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{originalProductCode,jdbcType=VARCHAR},
                #{operationCode,jdbcType=VARCHAR},
                #{subinventory,jdbcType=VARCHAR},
                #{subinventoryDescription,jdbcType=VARCHAR},
                #{freightSpace,jdbcType=VARCHAR},
                #{freightSpaceDescription,jdbcType=VARCHAR},
                #{batch,jdbcType=VARCHAR},
                #{barCode,jdbcType=VARCHAR},
                #{currentQuantity,jdbcType=VARCHAR},
                #{customerNum,jdbcType=VARCHAR},
                #{partNum,jdbcType=VARCHAR},
                #{assignedTime,jdbcType=VARCHAR},
                #{lastUpdateDate,jdbcType=VARCHAR},
                #{stockAge,jdbcType=VARCHAR},
                #{stockAgeDay,jdbcType=VARCHAR},
                #{warrantyDate,jdbcType=VARCHAR},
                #{distanceEnableDate,jdbcType=VARCHAR},
                #{sourceType,jdbcType=VARCHAR},
                #{originalOrgId,jdbcType=VARCHAR},
                #{allocationStatus,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_inventory_batch_detail(id,
                                               stock_point_code,
                                               product_code,
                                               original_product_code,
                                               operation_code,
                                               subinventory,
                                               subinventory_description,
                                               freight_space,
                                               freight_space_description,
                                               batch,
                                               bar_code,
                                               current_quantity,
                                               customer_num,
                                               part_num,
                                               assigned_time,
                                               last_update_date,
                                               stock_age,
                                               stock_age_day,
                                               warranty_date,
                                               distance_enable_date,
                                               source_type,
                                               original_org_id,
                                               allocation_status,
                                               remark,
                                               enabled,
                                               creator,
                                               create_time,
                                               modifier,
                                               modify_time,
                                               version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
             #{entity.stockPointCode,jdbcType=VARCHAR},
             #{entity.productCode,jdbcType=VARCHAR},
             #{entity.originalProductCode,jdbcType=VARCHAR},
             #{entity.operationCode,jdbcType=VARCHAR},
             #{entity.subinventory,jdbcType=VARCHAR},
             #{entity.subinventoryDescription,jdbcType=VARCHAR},
             #{entity.freightSpace,jdbcType=VARCHAR},
             #{entity.freightSpaceDescription,jdbcType=VARCHAR},
             #{entity.batch,jdbcType=VARCHAR},
             #{entity.barCode,jdbcType=VARCHAR},
             #{entity.currentQuantity,jdbcType=VARCHAR},
             #{entity.customerNum,jdbcType=VARCHAR},
             #{entity.partNum,jdbcType=VARCHAR},
             #{entity.assignedTime,jdbcType=VARCHAR},
             #{entity.lastUpdateDate,jdbcType=VARCHAR},
             #{entity.stockAge,jdbcType=VARCHAR},
             #{entity.stockAgeDay,jdbcType=VARCHAR},
             #{entity.warrantyDate,jdbcType=VARCHAR},
             #{entity.distanceEnableDate,jdbcType=VARCHAR},
             #{entity.sourceType,jdbcType=VARCHAR},
             #{entity.originalOrgId,jdbcType=VARCHAR},
            #{entity.allocationStatus,jdbcType=VARCHAR},
             #{entity.remark,jdbcType=VARCHAR},
             #{entity.enabled,jdbcType=VARCHAR},
             #{entity.creator,jdbcType=VARCHAR},
             #{entity.createTime,jdbcType=TIMESTAMP},
             #{entity.modifier,jdbcType=VARCHAR},
             #{entity.modifyTime,jdbcType=TIMESTAMP},
             #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_inventory_batch_detail(id,
                                               stock_point_code,
                                               product_code,
                                               original_product_code,
                                               operation_code,
                                               subinventory,
                                               subinventory_description,
                                               freight_space,
                                               freight_space_description,
                                               batch,
                                               bar_code,
                                               current_quantity,
                                               customer_num,
                                               part_num,
                                               assigned_time,
                                               last_update_date,
                                               stock_age,
                                               stock_age_day,
                                               warranty_date,
                                               distance_enable_date,
                                               source_type,
                                               original_org_id,
                                               allocation_status,
                                               remark,
                                               enabled,
                                               creator,
                                               create_time,
                                               modifier,
                                               modify_time,
                                               version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
             #{entity.stockPointCode,jdbcType=VARCHAR},
             #{entity.productCode,jdbcType=VARCHAR},
             #{entity.originalProductCode,jdbcType=VARCHAR},
             #{entity.operationCode,jdbcType=VARCHAR},
             #{entity.subinventory,jdbcType=VARCHAR},
             #{entity.subinventoryDescription,jdbcType=VARCHAR},
             #{entity.freightSpace,jdbcType=VARCHAR},
             #{entity.freightSpaceDescription,jdbcType=VARCHAR},
             #{entity.batch,jdbcType=VARCHAR},
             #{entity.barCode,jdbcType=VARCHAR},
             #{entity.currentQuantity,jdbcType=VARCHAR},
             #{entity.customerNum,jdbcType=VARCHAR},
             #{entity.partNum,jdbcType=VARCHAR},
             #{entity.assignedTime,jdbcType=VARCHAR},
             #{entity.lastUpdateDate,jdbcType=VARCHAR},
             #{entity.stockAge,jdbcType=VARCHAR},
             #{entity.stockAgeDay,jdbcType=VARCHAR},
             #{entity.warrantyDate,jdbcType=VARCHAR},
             #{entity.distanceEnableDate,jdbcType=VARCHAR},
             #{entity.sourceType,jdbcType=VARCHAR},
             #{entity.originalOrgId,jdbcType=VARCHAR},
            #{entity.allocationStatus,jdbcType=VARCHAR},
             #{entity.remark,jdbcType=VARCHAR},
             #{entity.enabled,jdbcType=VARCHAR},
             #{entity.creator,jdbcType=VARCHAR},
             #{entity.createTime,jdbcType=TIMESTAMP},
             #{entity.modifier,jdbcType=VARCHAR},
             #{entity.modifyTime,jdbcType=TIMESTAMP},
             #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryBatchDetailPO">
        update fdp_inventory_batch_detail
        set stock_point_code          = #{stockPointCode,jdbcType=VARCHAR},
            product_code              = #{productCode,jdbcType=VARCHAR},
            original_product_code     = #{originalProductCode,jdbcType=VARCHAR},
            operation_code            = #{operationCode,jdbcType=VARCHAR},
            subinventory              = #{subinventory,jdbcType=VARCHAR},
            subinventory_description  = #{subinventoryDescription,jdbcType=VARCHAR},
            freight_space             = #{freightSpace,jdbcType=VARCHAR},
            freight_space_description = #{freightSpaceDescription,jdbcType=VARCHAR},
            batch                     = #{batch,jdbcType=VARCHAR},
            bar_code                  = #{barCode,jdbcType=VARCHAR},
            current_quantity          = #{currentQuantity,jdbcType=VARCHAR},
            customer_num              = #{customerNum,jdbcType=VARCHAR},
            part_num                  = #{partNum,jdbcType=VARCHAR},
            assigned_time             = #{assignedTime,jdbcType=VARCHAR},
            last_update_date          = #{lastUpdateDate,jdbcType=VARCHAR},
            stock_age                 = #{stockAge,jdbcType=VARCHAR},
            stock_age_day             = #{stockAgeDay,jdbcType=VARCHAR},
            warranty_date             = #{warrantyDate,jdbcType=VARCHAR},
            distance_enable_date      = #{distanceEnableDate,jdbcType=VARCHAR},
            source_type               = #{sourceType,jdbcType=VARCHAR},
            original_org_id           = #{originalOrgId,jdbcType=VARCHAR},
            allocation_status                    = #{allocationStatus,jdbcType=VARCHAR},
            remark                    = #{remark,jdbcType=VARCHAR},
            enabled                   = #{enabled,jdbcType=VARCHAR},
            modifier                  = #{modifier,jdbcType=VARCHAR},
            modify_time               = #{modifyTime,jdbcType=TIMESTAMP},
            version_value             = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.stock.infrastructure.po.InventoryBatchDetailPO">
        update fdp_inventory_batch_detail
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.originalProductCode != null and item.originalProductCode != ''">
                original_product_code = #{item.originalProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.subinventory != null and item.subinventory != ''">
                subinventory = #{item.subinventory,jdbcType=VARCHAR},
            </if>
            <if test="item.subinventoryDescription != null and item.subinventoryDescription != ''">
                subinventory_description = #{item.subinventoryDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpace != null and item.freightSpace != ''">
                freight_space = #{item.freightSpace,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpaceDescription != null and item.freightSpaceDescription != ''">
                freight_space_description = #{item.freightSpaceDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.batch != null and item.batch != ''">
                batch = #{item.batch,jdbcType=VARCHAR},
            </if>
            <if test="item.barCode != null and item.barCode != ''">
                bar_code = #{item.barCode,jdbcType=VARCHAR},
            </if>
            <if test="item.currentQuantity != null and item.currentQuantity != ''">
                current_quantity = #{item.currentQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.customerNum != null and item.customerNum != ''">
                customer_num = #{item.customerNum,jdbcType=VARCHAR},
            </if>
            <if test="item.partNum != null and item.partNum != ''">
                part_num = #{item.partNum,jdbcType=VARCHAR},
            </if>
            <if test="item.assignedTime != null and item.assignedTime != ''">
                assigned_time = #{item.assignedTime,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateDate != null and item.lastUpdateDate != ''">
                last_update_date = #{item.lastUpdateDate,jdbcType=VARCHAR},
            </if>
            <if test="item.stockAge != null and item.stockAge != ''">
                stock_age = #{item.stockAge,jdbcType=VARCHAR},
            </if>
            <if test="item.stockAgeDay != null and item.stockAgeDay != ''">
                stock_age_day = #{item.stockAgeDay,jdbcType=VARCHAR},
            </if>
            <if test="item.warrantyDate != null and item.warrantyDate != ''">
                warranty_date = #{item.warrantyDate,jdbcType=VARCHAR},
            </if>
            <if test="item.distanceEnableDate != null and item.distanceEnableDate != ''">
                distance_enable_date = #{item.distanceEnableDate,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceType != null and item.sourceType != ''">
                source_type = #{item.sourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.originalOrgId != null and item.originalOrgId != ''">
                original_org_id = #{item.originalOrgId,jdbcType=VARCHAR},
            </if>
            <if test="item.allocationStatus != null and item.allocationStatus != ''">
                allocation_status = #{item.allocationStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_inventory_batch_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="original_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="subinventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subinventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="subinventory_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subinventoryDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="freight_space = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.freightSpace,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="freight_space_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.freightSpaceDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="batch = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.batch,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bar_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.barCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="current_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.currentQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="customer_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.customerNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="assigned_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.assignedTime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_age = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockAge,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_age_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockAgeDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="warranty_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.warrantyDate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="distance_enable_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.distanceEnableDate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="original_org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalOrgId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="allocation_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.allocationStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_inventory_batch_detail
            <set>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.originalProductCode != null and item.originalProductCode != ''">
                    original_product_code = #{item.originalProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.operationCode != null and item.operationCode != ''">
                    operation_code = #{item.operationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.subinventory != null and item.subinventory != ''">
                    subinventory = #{item.subinventory,jdbcType=VARCHAR},
                </if>
                <if test="item.subinventoryDescription != null and item.subinventoryDescription != ''">
                    subinventory_description = #{item.subinventoryDescription,jdbcType=VARCHAR},
                </if>
                <if test="item.freightSpace != null and item.freightSpace != ''">
                    freight_space = #{item.freightSpace,jdbcType=VARCHAR},
                </if>
                <if test="item.freightSpaceDescription != null and item.freightSpaceDescription != ''">
                    freight_space_description = #{item.freightSpaceDescription,jdbcType=VARCHAR},
                </if>
                <if test="item.batch != null and item.batch != ''">
                    batch = #{item.batch,jdbcType=VARCHAR},
                </if>
                <if test="item.barCode != null and item.barCode != ''">
                    bar_code = #{item.barCode,jdbcType=VARCHAR},
                </if>
                <if test="item.currentQuantity != null and item.currentQuantity != ''">
                    current_quantity = #{item.currentQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.customerNum != null and item.customerNum != ''">
                    customer_num = #{item.customerNum,jdbcType=VARCHAR},
                </if>
                <if test="item.partNum != null and item.partNum != ''">
                    part_num = #{item.partNum,jdbcType=VARCHAR},
                </if>
                <if test="item.assignedTime != null and item.assignedTime != ''">
                    assigned_time = #{item.assignedTime,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateDate != null and item.lastUpdateDate != ''">
                    last_update_date = #{item.lastUpdateDate,jdbcType=VARCHAR},
                </if>
                <if test="item.stockAge != null and item.stockAge != ''">
                    stock_age = #{item.stockAge,jdbcType=VARCHAR},
                </if>
                <if test="item.stockAgeDay != null and item.stockAgeDay != ''">
                    stock_age_day = #{item.stockAgeDay,jdbcType=VARCHAR},
                </if>
                <if test="item.warrantyDate != null and item.warrantyDate != ''">
                    warranty_date = #{item.warrantyDate,jdbcType=VARCHAR},
                </if>
                <if test="item.distanceEnableDate != null and item.distanceEnableDate != ''">
                    distance_enable_date = #{item.distanceEnableDate,jdbcType=VARCHAR},
                </if>
                <if test="item.sourceType != null and item.sourceType != ''">
                    source_type = #{item.sourceType,jdbcType=VARCHAR},
                </if>
                <if test="item.originalOrgId != null and item.originalOrgId != ''">
                    original_org_id = #{item.originalOrgId,jdbcType=VARCHAR},
                </if>
                <if test="item.allocationStatus != null and item.remark != ''">
                    allocation_status = #{item.allocationStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_inventory_batch_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete
        from fdp_inventory_batch_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectByProductCodes" resultMap="VOResultMap">
        select
            t1.stock_point_code,
            t1.product_code,
            t1.subinventory,
            t1.subinventory_description,
            t1.freight_space,
            t1.freight_space_description,
            t1.current_quantity,
            t1.create_time,
            t1.stock_point_name,
            t1.product_name,
            t1.operation_code,
            t1.measurement_unit
        from v_fdp_inventory_real_time_data t1
        left join mds_stock_point t2 on t1.stock_point_code = t2.stock_point_code
        <where>
            <if test="stockPointType != null and stockPointType != ''">
                and t2.stock_point_type = #{stockPointType,jdbcType=VARCHAR}
            </if>
            <if test="productCodes != null and productCodes.size() > 0">
                and t1.product_code in
                <foreach item="item" index="index" collection="productCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectByParamMap" resultMap="VOResultMap">
        select stock_point_code
             , stock_point_name
             , subinventory
             , subinventory_description
             , product_code
             , product_name
             , freight_space
             , freight_space_description
             , create_time
             , measurement_unit
             , current_quantity
             , subinventory
             , subinventory_description
             , freight_space
             , freight_space_description
             , current_quantity
             , version_value
             , modify_time
             , operation_code
             , measurement_unit
        from v_fdp_inventory_real_time_data
        <where>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                AND product_code IN
                <foreach item="item" index="index" collection="params.productCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.stockPointCodes != null and params.stockPointCodes.size() > 0">
                AND stock_point_code IN
                <foreach item="item" index="index" collection="params.stockPointCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.freightSpaceList != null and params.freightSpaceList.size() > 0">
                AND freight_space IN
                <foreach item="item" index="index" collection="params.freightSpaceList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.subinventoryList != null and params.subinventoryList.size() > 0">
                AND subinventory IN
                <foreach item="item" index="index" collection="params.subinventoryList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.subinventory != null and params.subinventory != ''">
                AND subinventory = #{params.subinventory,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_inventory_batch_detail where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
    <delete id="deleteAll" parameterType="java.lang.String">
        delete from fdp_inventory_batch_detail where source_type = #{sourceType,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteAllByOrgId" parameterType="java.lang.String">
        delete from fdp_inventory_batch_detail where source_type = #{sourceType,jdbcType=VARCHAR}
                                                 and original_org_id = #{originalOrgId,jdbcType=VARCHAR}
    </delete>
    <delete id="doDeleteAllByOrgIds">
        delete from fdp_inventory_batch_detail where source_type = #{sourceType,jdbcType=VARCHAR}
        <if test="orgIds != null and orgIds.size() > 0">
            AND original_org_id IN
            <foreach item="item" index="index" collection="orgIds" open="(" separator="," close=")">
                 #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </delete>

    <select id="selectCollectGroupByLoadingPosit" resultMap="VOResultMap">
        SELECT
			a.stock_point_code,
			a.operation_code,
			b.loading_position,
			sum( a.current_quantity ) current_quantity
		FROM
			v_fdp_inventory_real_time_data a
			LEFT JOIN mds_product_stock_point b ON a.product_code = b.product_code
			AND a.stock_point_code = b.stock_point_code
		WHERE
			loading_position IS NOT NULL
		GROUP BY
			a.stock_point_code,
			a.operation_code,
			b.loading_position
    </select>

    <select id="selectCollectGroupByNoLoadingPosit" resultMap="VOResultMap">
        SELECT
			a.stock_point_code,
			a.operation_code,
			sum( a.current_quantity ) current_quantity
		FROM
			v_fdp_inventory_real_time_data a
			LEFT JOIN mds_product_stock_point b ON a.product_code = b.product_code
			AND a.stock_point_code = b.stock_point_code
		GROUP BY
			a.stock_point_code,
			a.operation_code
    </select>
    <select id="selectByVOParams" resultMap="VOResultMap">
        select
        fibd.id,
        fibd.stock_point_code,
        fibd.product_code,
        fibd.subinventory,
        fibd.subinventory_description,
        fibd.freight_space,
        fibd.freight_space_description,
        fibd.current_quantity,
        fibd.batch,
        fibd.customer_num,
        fibd.create_time,
        fibd.version_value,
        fibd.modify_time,
        fibd.batch,
        fibd.bar_code,
        fibd.customer_num,
        fibd.part_num,
        fibd.assigned_time,
        fibd.last_update_date,
        fibd.stock_age,
        fibd.stock_age_day,
        fibd.warranty_date,
        fibd.distance_enable_date,
        fibd.source_type,
        fibd.original_org_id,
        fibd.remark,
        fibd.enabled,
        fibd.creator,
        fibd.modifier
        from fdp_inventory_batch_detail  fibd
        left join mds_product_stock_point mpsp on fibd.product_code = mpsp.product_code and fibd.stock_point_code = mpsp.stock_point_code
        <where>
            <if test="params.productType != null and params.productType != ''">
               and mpsp.product_type = #{params.productType,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and mpsp.product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and mpsp.product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectAllGlassInventoryBatch" resultMap="VOResultMap">
        SELECT
            t1.id,
            t1.assigned_time,
            t1.product_code,
            t1.stock_point_code,
            t1.current_quantity
        FROM
            fdp_inventory_batch_detail t1
                LEFT JOIN mds_stock_point t2 ON t2.stock_point_code = t1.stock_point_code
                LEFT JOIN mds_product_stock_point t3 ON t3.product_code = t1.product_code
                AND t3.stock_point_code = t2.stock_point_code
        WHERE
            t2.stock_point_type = 'BC'
          AND t3.product_classify = 'RA.A'
    </select>

    <select id="selectByVOParams02" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List_02"/>
        from v_fdp_inventory_batch_detail
        <include refid="Base_Where_Condition"/>
    </select>
</mapper>
