package com.yhl.scp.dfp.material.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.material.dto.PartRiskLevelDTO;
import com.yhl.scp.dfp.material.service.PartRiskLevelService;
import com.yhl.scp.dfp.material.vo.PartRiskLevelDetailVO;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>PartRiskLevelController</code>
 * <p>
 * 零件风险等级控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:47:11
 */
@Slf4j
@Api(tags = "零件风险等级控制器")
@RestController
@RequestMapping("materialRiskLevel")
public class PartRiskLevelController extends BaseController {

    @Resource
    private PartRiskLevelService partRiskLevelService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @BusinessMonitorLog(businessCode = "零件风险等级确认", moduleCode = "DFP", businessFrequency = "MONTH")
    public BaseResponse<PageInfo<PartRiskLevelVO>> page() {
        List<PartRiskLevelVO> materialRiskLevelList = partRiskLevelService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<PartRiskLevelVO> pageInfo = new PageInfo<>(materialRiskLevelList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail")
    public BaseResponse<PageInfo<PartRiskLevelDetailVO>> detail(@RequestParam("materialRiskLevelId") String materialRiskLevelId) {
        List<PartRiskLevelDetailVO> listBaseResponse = partRiskLevelService.selectDetailByMaterialId(materialRiskLevelId);
        PageInfo<PartRiskLevelDetailVO> pageInfo = new PageInfo<>(listBaseResponse);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "更新零件风险等级")
    @PostMapping(value = "updateRiskLevel")
    public BaseResponse<Void> updateRiskLevel(@RequestParam("materialRiskLevelDetailId") String materialRiskLevelDetailId,
                                              @RequestParam("materialRiskLevel") String materialRiskLevel) {
        return partRiskLevelService.updateRiskLevelDetail(materialRiskLevelDetailId, materialRiskLevel);
    }

    @ApiOperation(value = "重新计算")
    @PostMapping(value = "reCalculate")
    public BaseResponse<Void> reCalculate(@RequestParam(name = "incrementCalculateFlag", required = false) String incrementCalculateFlag) {
        return partRiskLevelService.reCalculate(incrementCalculateFlag, null);
    }


    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        partRiskLevelService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "获取零件风险等级-根据主机厂编码和本厂编码")
    @GetMapping(value = "getPartRiskLevel")
    public List<LabelValue<String>> getPartRiskLevel(@RequestParam(value = "oemCode",required = false) String oemCode
            ,@RequestParam(value = "productCode",required = false) String productCode) {
        return partRiskLevelService.getPartRiskLevel(oemCode,productCode);
    }
    
    @ApiOperation(value = "更批量修改零件风险等级")
    @PostMapping(value = "batchUpdateLevel")
    public BaseResponse<Void> batchUpdateLevel(@RequestBody PartRiskLevelDTO dto ) {
        return partRiskLevelService.batchUpdateLevel(dto);
    }
}
