package com.yhl.scp.dfp.originDemand.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandInterfaceLogDTO;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandInterfaceLogService;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandInterfaceLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>FdpOriginDemandInterfaceLogController</code>
 * <p>
 * Edi装车需求接口同步记录表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 18:25:08
 */
@Slf4j
@Api(tags = "Edi装车需求接口同步记录表控制器")
@RestController
@RequestMapping("fdpOriginDemandInterfaceLog")
public class FdpOriginDemandInterfaceLogController extends BaseController {

    @Resource
    private FdpOriginDemandInterfaceLogService fdpOriginDemandInterfaceLogService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<FdpOriginDemandInterfaceLogVO>> page() {
        List<FdpOriginDemandInterfaceLogVO> fdpOriginDemandInterfaceLogList = fdpOriginDemandInterfaceLogService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<FdpOriginDemandInterfaceLogVO> pageInfo = new PageInfo<>(fdpOriginDemandInterfaceLogList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody FdpOriginDemandInterfaceLogDTO fdpOriginDemandInterfaceLogDTO) {
        return fdpOriginDemandInterfaceLogService.doCreate(fdpOriginDemandInterfaceLogDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody FdpOriginDemandInterfaceLogDTO fdpOriginDemandInterfaceLogDTO) {
        return fdpOriginDemandInterfaceLogService.doUpdate(fdpOriginDemandInterfaceLogDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        fdpOriginDemandInterfaceLogService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<FdpOriginDemandInterfaceLogVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, fdpOriginDemandInterfaceLogService.selectByPrimaryKey(id));
    }

}
