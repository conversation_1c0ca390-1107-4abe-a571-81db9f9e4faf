package com.yhl.scp.dfp.dispatch.service.impl;

import cn.hutool.core.date.StopWatch;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.das.core.InputBase;
import com.yhl.scp.dfp.dispatch.input.DfpAlgoInput;
import com.yhl.scp.dfp.dispatch.output.DfpAlgoOutput;
import com.yhl.scp.dfp.dispatch.service.DfpAlgoInputService;
import com.yhl.scp.dfp.dispatch.service.DfpAlgoOutputService;
import com.yhl.scp.dfp.dispatch.service.DispatchService;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class DispatchServiceImpl implements DispatchService {

    @Value("${schedule.url}")
    private String port;

    @Value("${schedule.create}")
    private String createUri;

    @Value("${schedule.check}")
    private String checkUri;

    @Value("${schedule.result}")
    private String resultUri;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private DfpAlgoInputService dfpAlgoInputService;

    @Resource
    private DfpAlgoOutputService dfpAlgoOutputService;

    @Override
    public void doSchedule(AlgorithmLog algorithmLog) {
        BaseResponse baseResponse;
        String status = AlgorithmLogStatusEnum.RUNNING.getCode();
        try {
            InputBase inputBase = new InputBase();
            inputBase.setAlgorithmLogId(algorithmLog.getId());
            inputBase.setExecutionNumber(algorithmLog.getExecutionNumber());
            inputBase.setModuleCode(SystemModuleEnum.DFP.getCode());
            inputBase.setScenario(algorithmLog.getScenario());
            // 组装输入数据
            StopWatch stopWatch = new StopWatch("获取算法输入数据");
            stopWatch.start("获取算法输入数据");
            DfpAlgoInput dfpAlgoInput = dfpAlgoInputService.getDfpAlgorithmData(algorithmLog.getScenario(), algorithmLog.getKpiData());
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.MULTIPART_FORM_DATA);
            // 调用远程算法服务执行算法
            String url = getBaseUrl(algorithmLog) + createUri;

            inputBase.setInput(dfpAlgoInput);
            log.info("url is " + url);
            log.info("inputBase is " + inputBase);
            baseResponse = restTemplate.postForObject(url, inputBase, BaseResponse.class);
            if (Boolean.FALSE.equals(baseResponse.getSuccess())) {
                status = AlgorithmLogStatusEnum.FAIL.getCode();
            }
        } catch (Exception e) {
            status = AlgorithmLogStatusEnum.FAIL.getCode();
            log.error("DispatchServiceImpl.doSchedule", e);
            throw new BusinessException("算法执行异常：" + e.getMessage());
        } finally {
            algorithmLog.setStatus(status);
            ipsFeign.updateAlgorithmLog(algorithmLog);
        }
    }

    @Override
    public BaseResponse doResultAnalysis(AlgorithmLog algorithmLog) throws UnsupportedEncodingException {
        log.info("调用算法服务获取算法结果，ScheduleServiceImpl.doResultAnalysis, executionNumber is " + algorithmLog.getExecutionNumber());
        String moduleCode = algorithmLog.getModuleCode();
        // 如果参数值中带&，需要转义处理
        BaseResponse baseResponse = restTemplate.postForObject(getBaseUrl(algorithmLog) + resultUri +
                "?executionNumber=" + algorithmLog.getExecutionNumber() +
                "&moduleCode=" + URLEncoder.encode(moduleCode, "UTF-8"), null, BaseResponse.class);
        assert baseResponse != null;
        if (Boolean.TRUE.equals(baseResponse.getSuccess())) {
            String data = JacksonUtils.toJson(baseResponse.getData());
            DfpAlgoOutput dfpAlgorithmOutput = JacksonUtils.toObj(data, new TypeReference<DfpAlgoOutput>() {
            });
            dfpAlgoOutputService.doAnalysisAlgorithmOutputData(dfpAlgorithmOutput, algorithmLog);
            log.info("算法结果解析成功， executionNumber is " + algorithmLog.getExecutionNumber());
            return BaseResponse.success("");
        }
        return BaseResponse.error("算法结果解析失败");
    }

    @Override
    public BaseResponse getAlgorithmStatus(AlgorithmLog algorithmLog) throws UnsupportedEncodingException {
        String moduleCode = algorithmLog.getModuleCode();
        // 如果参数值中带&，需要转义处理

        BaseResponse baseResponse = restTemplate.postForObject(getBaseUrl(algorithmLog) + checkUri +
                        "?executionNumber=" + algorithmLog.getExecutionNumber() + "&" + "moduleCode=" + URLEncoder.encode(moduleCode, "UTF-8"),
                null, BaseResponse.class);
        String executorStatus = String.valueOf(baseResponse.getData());
        if (AlgorithmLogStatusEnum.FAIL.getCode().equals(executorStatus)) {
            log.warn("algorithm run failed, executionNumber is " + algorithmLog.getExecutionNumber());
        }
        return baseResponse;
    }

    private String getBaseUrl(AlgorithmLog algorithmLog) {
        return "http://" + algorithmLog.getIpAddress() + ":" + port;
    }

}
