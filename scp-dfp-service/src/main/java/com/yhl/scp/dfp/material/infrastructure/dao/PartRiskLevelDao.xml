<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.material.infrastructure.dao.PartRiskLevelDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.material.infrastructure.po.PartRiskLevelPO">
        <!--@Table fdp_part_risk_level-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="total_score" jdbcType="VARCHAR" property="totalScore"/>
        <result column="material_risk_level" jdbcType="VARCHAR" property="materialRiskLevel"/>
        <result column="material_risk_level_calculate" jdbcType="VARCHAR" property="materialRiskLevelCalculate"/>
        <result column="material_risk_level_month" jdbcType="VARCHAR" property="materialRiskLevelMonth"/>
        <result column="update_remark" jdbcType="VARCHAR" property="updateRemark"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.material.vo.PartRiskLevelVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="risk_level" jdbcType="VARCHAR" property="oemRiskLevel"/>
        <result column="material_risk_level_detail_id" jdbcType="VARCHAR" property="materialRiskLevelDetailId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,oem_code,vehicle_model_code,product_code,remark,enabled,creator,create_time,modifier,modify_time,version_value,total_score,
        material_risk_level,
        material_risk_level_calculate,
		material_risk_level_month,
		update_remark
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,oem_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.totalScore != null and params.totalScore != ''">
                and total_score = #{params.totalScore,jdbcType=VARCHAR}
            </if>
            <if test="params.materialRiskLevel != null and params.materialRiskLevel != ''">
                and material_risk_level = #{params.materialRiskLevel,jdbcType=VARCHAR}
            </if>
            <if test="params.materialRiskLevelCalculate != null and params.materialRiskLevelCalculate != ''">
                and material_risk_level_calculate = #{params.materialRiskLevelCalculate,jdbcType=VARCHAR}
            </if>
            <if test="params.materialRiskLevelMonth != null and params.materialRiskLevelMonth != ''">
                and material_risk_level_month = #{params.materialRiskLevelMonth,jdbcType=VARCHAR}
            </if>
            <if test="params.updateRemark != null and params.updateRemark != ''">
                and update_remark = #{params.updateRemark,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_part_risk_level
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_part_risk_level
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_part_risk_level
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_part_risk_level
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_part_risk_level
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByProductCodeList" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_fdp_part_risk_level
        where 1=1
        <if test="productCodeList != null and productCodeList.size() > 0">
            and product_code in
            <foreach collection="productCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="oemCode != null and oemCode != ''">
            and oem_code = #{oemCode,jdbcType=VARCHAR}
        </if>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.material.infrastructure.po.PartRiskLevelPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_part_risk_level(
        id,
        oem_code,
        vehicle_model_code,
        product_code,
        total_score,
        material_risk_level,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        material_risk_level_calculate,
		material_risk_level_month,
		update_remark)
        values (
        #{id,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{totalScore,jdbcType=VARCHAR},
        #{materialRiskLevel,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{materialRiskLevelCalculate,jdbcType=VARCHAR},
        #{materialRiskLevelMonth,jdbcType=VARCHAR},
        #{updateRemark,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.material.infrastructure.po.PartRiskLevelPO">
        insert into fdp_part_risk_level(id,
                                            oem_code,
                                            vehicle_model_code,
                                            product_code,
                                            total_score,
                                            material_risk_level,
                                            remark,
                                            enabled,
                                            creator,
                                            create_time,
                                            modifier,
                                            modify_time,version_value,
                                            material_risk_level_calculate,
											material_risk_level_month,
											update_remark)
        values (#{id,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{totalScore,jdbcType=VARCHAR},
                #{materialRiskLevel,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
		        #{materialRiskLevelCalculate,jdbcType=VARCHAR},
		        #{materialRiskLevelMonth,jdbcType=VARCHAR},
		        #{updateRemark,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_part_risk_level(
        id,
        oem_code,
        vehicle_model_code,
        product_code,
        total_score,
        material_risk_level,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        material_risk_level_calculate,
		material_risk_level_month,
		update_remark)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.totalScore,jdbcType=VARCHAR},
            #{entity.materialRiskLevel,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
	        #{entity.materialRiskLevelCalculate,jdbcType=VARCHAR},
	        #{entity.materialRiskLevelMonth,jdbcType=VARCHAR},
	        #{entity.updateRemark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_part_risk_level(
        id,
        oem_code,
        vehicle_model_code,
        product_code,
        total_score,
        material_risk_level,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        material_risk_level_calculate,
		material_risk_level_month,
		update_remark)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.vehicleModelCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.totalScore,jdbcType=VARCHAR},
            #{entity.materialRiskLevel,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
	        #{entity.materialRiskLevelCalculate,jdbcType=VARCHAR},
	        #{entity.materialRiskLevelMonth,jdbcType=VARCHAR},
	        #{entity.updateRemark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.material.infrastructure.po.PartRiskLevelPO">
        update fdp_part_risk_level
        set oem_code           = #{oemCode,jdbcType=VARCHAR},
            vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
            product_code       = #{productCode,jdbcType=VARCHAR},
            total_score        = #{totalScore,jdbcType=VARCHAR},
            material_risk_level    = #{materialRiskLevel,jdbcType=VARCHAR},
            remark             = #{remark,jdbcType=VARCHAR},
            enabled            = #{enabled,jdbcType=VARCHAR},
            modifier           = #{modifier,jdbcType=VARCHAR},
            modify_time        = #{modifyTime,jdbcType=TIMESTAMP},
        	material_risk_level_calculate    	= #{materialRiskLevelCalculate,jdbcType=VARCHAR},
            material_risk_level_month    	= #{materialRiskLevelMonth,jdbcType=VARCHAR},
            update_remark    	= #{updateRemark,jdbcType=VARCHAR},
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_part_risk_level set
            version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.material.infrastructure.po.PartRiskLevelPO">
        update fdp_part_risk_level
        <set>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.totalScore != null and item.totalScore != ''">
                total_score = #{item.totalScore,jdbcType=VARCHAR},
            </if>
            <if test="item.materialRiskLevel != null and item.materialRiskLevel != ''">
                material_risk_level = #{item.materialRiskLevel,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.materialRiskLevelCalculate != null and item.materialRiskLevelCalculate != ''">
                material_risk_level_calculate = #{item.materialRiskLevelCalculate,jdbcType=VARCHAR},
            </if>
            <if test="item.materialRiskLevelMonth != null and item.materialRiskLevelMonth != ''">
                material_risk_level_month = #{item.materialRiskLevelMonth,jdbcType=VARCHAR},
            </if>
            <if test="item.updateRemark != null and item.updateRemark != ''">
                update_remark = #{item.updateRemark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_part_risk_level set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_part_risk_level
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="total_score = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.totalScore,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_risk_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialRiskLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="material_risk_level_calculate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialRiskLevelCalculate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_risk_level_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialRiskLevelMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.updateRemark,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_part_risk_level set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_part_risk_level
            <set>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.totalScore != null and item.totalScore != ''">
                    total_score = #{item.totalScore,jdbcType=VARCHAR},
                </if>
                <if test="item.materialRiskLevel != null and item.materialRiskLevel != ''">
                    material_risk_level = #{item.materialRiskLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.materialRiskLevelCalculate != null and item.materialRiskLevelCalculate != ''">
	                material_risk_level_calculate = #{item.materialRiskLevelCalculate,jdbcType=VARCHAR},
	            </if>
	            <if test="item.materialRiskLevelMonth != null and item.materialRiskLevelMonth != ''">
	                material_risk_level_month = #{item.materialRiskLevelMonth,jdbcType=VARCHAR},
	            </if>
	            <if test="item.updateRemark != null and item.updateRemark != ''">
	                update_remark = #{item.updateRemark,jdbcType=VARCHAR},
	            </if>
                
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER};
            update fdp_part_risk_level set
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_part_risk_level
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_part_risk_level where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
	
	<!-- 批量id+版本删除 -->
	<delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_part_risk_level where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>

    <delete id="deleteAll">
        delete from fdp_part_risk_level
        where 1=1
        <if test="notDeleteIds != null and notDeleteIds.size() > 0">
            and id not in
            <foreach collection="notDeleteIds" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </delete>
    
    <select id="selectLevelByOmeCodes" resultMap="VOResultMap">
        select
        	id,
        	oem_code,
        	vehicle_model_code,
        	product_code,
        	material_risk_level,
        	update_remark
        from fdp_part_risk_level
        where enabled = 'YES'
        and oem_code in 
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    
    <update id="batchUpdateLevel" parameterType="com.yhl.scp.dfp.material.dto.PartRiskLevelDTO">
        update fdp_part_risk_level
        set 
            material_risk_level    = #{materialRiskLevel,jdbcType=VARCHAR},
            update_remark    	= #{updateRemark,jdbcType=VARCHAR}
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>
