package com.yhl.scp.dfp.delivery.calc;

import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanDetailPO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanDetailVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanLockConfigVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanReplenishConfigVO;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.vo.OemInventorySubmissionVO;
import com.yhl.scp.dfp.oem.vo.OemTransportTimeVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.switchrelation.vo.DfpSwitchRelationBetweenProductVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.mds.box.vo.BoxInfoVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DeliveryPlanCalcSpace</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-20 11:35:20
 */
@Data
public class DeliveryPlanCalcSpace {
    private List<OemVO> oemVOList;
    private List<String> oemCodeList;
    // 主机厂数据转map，key为主机厂编码
    private Map<String, OemVO> oemVOMap;
    //物料主数据
    List<NewProductStockPointVO> newProductStockPointVOS;
    //物料主数据转map，key为产品编码
    Map<String, NewProductStockPointVO> productStockPointVOMap;
    //主机厂运输时间数据转map，key为主机厂编码
    Map<String, List<OemTransportTimeVO>> oemTransportTimeVOGroup;
    //车辆型号编码
    List<String> vehicleModelCodeList;
    // 发货计划锁定配置,key为主机厂编码
    Map<String, DeliveryPlanLockConfigVO> deliveryPlanLockConfigVOMap;
    // 发货计划补货策略配置,key为主机厂编码
    Map<String, DeliveryPlanReplenishConfigVO> deliveryPlanReplenishConfigVOMap;
    // 主机厂库存量,key为主机厂编码+产品编码
    Map<String, Integer> oemInventoryMap;
    // 中转库库存(期初库存的一部份),key为主机厂编码+产品编码
    Map<String, Integer> oemTransitInventoryMap;
    // 中转库在途数据,key为主机厂编码+产品编码
    Map<String,List<OemInventorySubmissionVO>> receivedOemInventorySubmissionMap;
    // 期初库存,key为主机厂编码+产品编码
    Map<String, Integer> productStockPointInventoryMap;
    // 安全库存,key为产品编码
    Map<String, SafetyStockLevelVO> safetyStockLevelVOMap;
    //在途库存,key为主机厂编码+产品编码
    Map<String, List<WarehouseReleaseToWarehouseVO>> productStockPointInRoadMap;
    // 零件风险等级数据和箱体信息
    // key为主机厂编码+产品编码
    Map<String, PartRiskLevelVO> materialRiskLevelVOMapOfJoinKey = new HashMap<>();
    // 产品箱体关系数据，key为产品编码
    Map<String, List<ProductBoxRelationVO>> productBoxRelationVOMapOfProductCode = new HashMap<>();
    // 箱体信息数据，key为箱体编码
    Map<String, BoxInfoVO> boxInfoVOMapOfId = new HashMap<>();
    //最新已发布数据，key为产品编码+主机厂编码
    Map<String, List<DeliveryPlanDetailVO>> deliveryPlanDetailByProductCodeAndOemCodeMap;
    List<String> oldCodeList;
    List<String> newCodeList;
    Map<String, DfpSwitchRelationBetweenProductVO> oldSwitchRelationMap;
    Map<String,DfpSwitchRelationBetweenProductVO> newSwitchRelationMap;
    Map<String,List<DeliveryPlanDetailPO>> savedDeliveryPlanDetailMap;
    //中转库与主机厂库存提报（发货清单号对应的中转库入库数量）
    Map<String, BigDecimal> shippingListNumberMap;
    
    //产品对应的是否整箱Map
    Map<String, String> productFullBoxMap;
    
    //主机厂放假时间Map
    Map<String, List<String>> oemHolidayMap;

    public void initializeCalcSpace() {

    }

    public void initOemVOList(List<OemVO> oemVOList) {
        if (oemVOList == null) {
            this.oemVOList = new ArrayList<>();
            this.oemCodeList = new ArrayList<>();
            this.oemVOMap = new HashMap<>();
            return;
        }
        this.oemVOList = oemVOList;
        this.oemCodeList = oemVOList.stream().map(OemVO::getOemCode).collect(Collectors.toList());
        this.oemVOMap = oemVOList.stream().collect(Collectors.toMap(OemVO::getOemCode,
                Function.identity(), (t1, t2) -> t2));
    }

    public void initOemTransportTimeVOGroup(List<OemTransportTimeVO> oemTransportTimeList) {
        if (CollectionUtils.isEmpty(oemTransportTimeList)){
            this.oemTransportTimeVOGroup = new HashMap<>();
            return;
        }
        this.oemTransportTimeVOGroup = oemTransportTimeList.stream().collect(Collectors.groupingBy(OemTransportTimeVO::getOemCode));
    }

    public void initNewProductStockPointVOList(List<NewProductStockPointVO> newProductStockPointVOS) {

        this.newProductStockPointVOS = newProductStockPointVOS;
        this.vehicleModelCodeList =
                newProductStockPointVOS.stream().map(NewProductStockPointVO::getVehicleModelCode).distinct().collect(Collectors.toList());
        this.productStockPointVOMap =
                newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode,
                Function.identity(), (oldValue, newValue) -> newValue));
    }

    public void initDeliveryPlanLockConfigVOMap(List<DeliveryPlanLockConfigVO> deliveryPlanLockConfigVO){
        if (CollectionUtils.isEmpty(deliveryPlanLockConfigVO)){
            this.deliveryPlanLockConfigVOMap = new HashMap<>();
            return;
        }
        this.deliveryPlanLockConfigVOMap = deliveryPlanLockConfigVO.stream().collect(Collectors.toMap(DeliveryPlanLockConfigVO::getOemCode,
                Function.identity(), (t1, t2) -> t2));
    }

    public void initDeliveryPlanReplenishConfigVOMap(List<DeliveryPlanReplenishConfigVO> deliveryPlanReplenishConfigVO){
        if (CollectionUtils.isEmpty(deliveryPlanReplenishConfigVO)){
            this.deliveryPlanReplenishConfigVOMap = new HashMap<>();
            return;
        }
        this.deliveryPlanReplenishConfigVOMap = deliveryPlanReplenishConfigVO.stream().collect(Collectors.toMap(DeliveryPlanReplenishConfigVO::getOemCode,
                Function.identity(), (t1, t2) -> t2));
    }

    public void initOemInventoryMap(List<OemInventorySubmissionVO> oemInventorySubmissionVOS,Date lastDay) {
        this.oemInventoryMap = new HashMap<>();
        this.oemTransitInventoryMap = new HashMap<>();
        if (CollectionUtils.isEmpty(oemInventorySubmissionVOS)){
            this.receivedOemInventorySubmissionMap = new HashMap<>();
            return;
        }
        oemInventorySubmissionVOS = oemInventorySubmissionVOS.stream()
                .filter(t -> null != t.getSubmissionDate()).collect(Collectors.toList());
        oemInventorySubmissionVOS.sort(Comparator.comparing(OemInventorySubmissionVO::getSubmissionDate));
        receivedOemInventorySubmissionMap =
                oemInventorySubmissionVOS.stream().collect(Collectors.groupingBy(t -> t.getOemCode() + "&&" + t.getProductCode()));
        oemInventorySubmissionVOS =
                oemInventorySubmissionVOS.stream().filter(t -> !lastDay.before(t.getSubmissionDate())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(oemInventorySubmissionVOS)) {
            return;
        }
        oemInventoryMap.putAll(oemInventorySubmissionVOS.stream().collect(Collectors
                .toMap(t -> t.getOemCode() + "&&" + t.getProductCode(),
                        t -> null == t.getOemInventoryQuantity() ? 0 : t.getOemInventoryQuantity().intValue(),
                        (t1, t2) -> t2)));
        oemTransitInventoryMap.putAll(oemInventorySubmissionVOS.stream().collect(Collectors
                .toMap(t -> t.getOemCode() + "&&" + t.getProductCode(),
                        t -> null == t.getStockInventoryQuantity() ? 0 : t.getStockInventoryQuantity().intValue(),
                        (t1, t2) -> t2)));
    }

    public void initPublishedDeliveryDetailMap(List<DeliveryPlanDetailVO> deliveryPlanDetailVOS){
        if (CollectionUtils.isEmpty(deliveryPlanDetailVOS)){
            this.deliveryPlanDetailByProductCodeAndOemCodeMap = new HashMap<>();
            return;
        }
        deliveryPlanDetailByProductCodeAndOemCodeMap = deliveryPlanDetailVOS.stream().collect(Collectors.groupingBy(t -> t.getProductCode() + "&&" + t.getOemCode()));
    }

    public void initSwitchRelationMap(List<DfpSwitchRelationBetweenProductVO> switchRelationBetweenProductVOS){
        if (CollectionUtils.isEmpty(switchRelationBetweenProductVOS)){
            this.oldCodeList = new ArrayList<>();
            this.newCodeList = new ArrayList<>();
            this.oldSwitchRelationMap = new HashMap<>();
            this.newSwitchRelationMap = new HashMap<>();
            return;
        }
        oldCodeList =
                switchRelationBetweenProductVOS.stream().map(item -> item.getOemCode() + "&&" +item.getOldProductCode()).collect(Collectors.toList());
        newCodeList =
                switchRelationBetweenProductVOS.stream().map(item -> item.getOemCode() + "&&" +item.getNewProductCode()).collect(Collectors.toList());
        oldSwitchRelationMap =
                switchRelationBetweenProductVOS.stream().collect(Collectors.toMap(item -> item.getOemCode() + "&&" +item.getOldProductCode(), Function.identity(),
                        (t1, t2) -> t1));
        newSwitchRelationMap =
                switchRelationBetweenProductVOS.stream().collect(Collectors.toMap(item -> item.getOemCode() + "&&" +item.getNewProductCode(), Function.identity(),
                        (t1, t2) -> t1));
    }

    public void initSavedDeliveryPlanDetailMap(List<DeliveryPlanDetailPO> deliveryPlanDetailPOS){
        if (CollectionUtils.isEmpty(deliveryPlanDetailPOS)){
            this.savedDeliveryPlanDetailMap = new HashMap<>();
            return;
        }
        savedDeliveryPlanDetailMap =
                deliveryPlanDetailPOS.stream().collect(Collectors.groupingBy(DeliveryPlanDetailPO::getDeliveryPlanDataId));
    }

    public List<OemTransportTimeVO> getOemTransportTimeVOList(String oemCode){
        return oemTransportTimeVOGroup.get(oemCode);
    }

    public OemVO getOemVO(String oemCode){
        return oemVOMap.get(oemCode);
    }

    public NewProductStockPointVO getNewProductStockPointVO(String productCode){
        return productStockPointVOMap.get(productCode);
    }

    public DfpSwitchRelationBetweenProductVO getOldSwitchRelationVO(String oldKey){
        return oldSwitchRelationMap.get(oldKey);
    }

    public DfpSwitchRelationBetweenProductVO getNewSwitchRelationVO(String newKey){
        return newSwitchRelationMap.get(newKey);
    }

    public DeliveryPlanLockConfigVO getDeliveryPlanLockConfigVO(String oemCode){
        return deliveryPlanLockConfigVOMap.get(oemCode);
    }

    public DeliveryPlanReplenishConfigVO getDeliveryPlanReplenishConfigVO(String oemCode){
        return deliveryPlanReplenishConfigVOMap.get(oemCode);
    }

    public SafetyStockLevelVO getSafetyStockLevelVO(String productCode){
        return safetyStockLevelVOMap.get(productCode);
    }

    public List<WarehouseReleaseToWarehouseVO> getProductStockPointInRoad(String key){
        return productStockPointInRoadMap.get(key);
    }

    public List<OemInventorySubmissionVO> getReceivedOemInventorySubmissionVO(String key){
        return receivedOemInventorySubmissionMap.get(key);
    }

    public Integer getProductStockPointInventory(String key){
        Integer inventory = productStockPointInventoryMap.get(key);
        if (null == inventory){
            return 0;
        }
        return inventory;
    }

    public Integer getOemInventory(String key){
        Integer inventory = oemInventoryMap.get(key);
        if (null == inventory){
            return 0;
        }
        return inventory;
    }

    public Integer getReceiveQuantity(Date date,
                                       Integer supplyDays,
                                       String key) {
        List<WarehouseReleaseToWarehouseVO> warehouseReleaseRecordList = getProductStockPointInRoad(key);
        List<OemInventorySubmissionVO> receiveOemInventorySubmissionVOList = getReceivedOemInventorySubmissionVO(key);
        int receiveQuantity = 0;
        if (CollectionUtils.isNotEmpty(warehouseReleaseRecordList)) {
            for (WarehouseReleaseToWarehouseVO warehouseReleaseRecordVO : warehouseReleaseRecordList) {
                if (warehouseReleaseRecordVO.getActualCompletionTime() != null) {
                    continue;
                }
                // 如果预计到达时间有值，实际到达时间没值
                if (warehouseReleaseRecordVO.getEstimatedCompletionTime() != null
                        && DateUtils.getDayFirstTime(warehouseReleaseRecordVO.getEstimatedCompletionTime()).compareTo(date) == 0) {
                    receiveQuantity += warehouseReleaseRecordVO.getSumQty().intValue();
                }

                // 预计到达时间没有值
                if (warehouseReleaseRecordVO.getEstimatedCompletionTime() == null
                        && DateUtils.moveDay(DateUtils.getDayFirstTime(warehouseReleaseRecordVO.getCreationDate()),
                        supplyDays).compareTo(date) == 0) {
                    receiveQuantity += warehouseReleaseRecordVO.getSumQty().intValue();
                }

            }
        }

        if (CollectionUtils.isEmpty(receiveOemInventorySubmissionVOList)) {
            return receiveQuantity;
        }
        for (OemInventorySubmissionVO oemInventorySubmissionVO : receiveOemInventorySubmissionVOList){
            if (oemInventorySubmissionVO.getSubmissionDate() != null && oemInventorySubmissionVO.getTransitWaitQuantity() != null && DateUtils.getDayFirstTime(oemInventorySubmissionVO.getSubmissionDate()).compareTo(date) == 0){
                receiveQuantity += oemInventorySubmissionVO.getTransitWaitQuantity().intValue();
            }
        }
        return receiveQuantity;
    }

}
