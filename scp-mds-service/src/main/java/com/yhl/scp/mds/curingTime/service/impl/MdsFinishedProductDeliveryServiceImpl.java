package com.yhl.scp.mds.curingTime.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesFinishedProductDelivery;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.curingTime.convertor.MdsFinishedProductDeliveryConvertor;
import com.yhl.scp.mds.curingTime.domain.entity.MdsFinishedProductDeliveryDO;
import com.yhl.scp.mds.curingTime.domain.service.MdsFinishedProductDeliveryDomainService;
import com.yhl.scp.mds.curingTime.dto.MdsFinishedProductDeliveryDTO;
import com.yhl.scp.mds.curingTime.infrastructure.dao.MdsFinishedProductDeliveryDao;
import com.yhl.scp.mds.curingTime.infrastructure.po.MdsFinishedProductDeliveryPO;
import com.yhl.scp.mds.curingTime.service.MdsFinishedProductDeliveryService;
import com.yhl.scp.mds.curingTime.vo.MdsFinishedProductDeliveryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MdsFinishedProductDeliveryServiceImpl</code>
 * <p>
 * mes系统成品发送属性接口同步中间表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-27 10:46:23
 */
@Slf4j
@Service
public class MdsFinishedProductDeliveryServiceImpl extends AbstractService implements MdsFinishedProductDeliveryService {

    @Resource
    private MdsFinishedProductDeliveryDao mdsFinishedProductDeliveryDao;

    @Resource
    private MdsFinishedProductDeliveryDomainService mdsFinishedProductDeliveryDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MdsFinishedProductDeliveryDTO mdsFinishedProductDeliveryDTO) {
        // 0.数据转换
        MdsFinishedProductDeliveryDO mdsFinishedProductDeliveryDO = MdsFinishedProductDeliveryConvertor.INSTANCE.dto2Do(mdsFinishedProductDeliveryDTO);
        MdsFinishedProductDeliveryPO mdsFinishedProductDeliveryPO = MdsFinishedProductDeliveryConvertor.INSTANCE.dto2Po(mdsFinishedProductDeliveryDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsFinishedProductDeliveryDomainService.validation(mdsFinishedProductDeliveryDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(mdsFinishedProductDeliveryPO);
        mdsFinishedProductDeliveryDao.insert(mdsFinishedProductDeliveryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MdsFinishedProductDeliveryDTO mdsFinishedProductDeliveryDTO) {
        // 0.数据转换
        MdsFinishedProductDeliveryDO mdsFinishedProductDeliveryDO = MdsFinishedProductDeliveryConvertor.INSTANCE.dto2Do(mdsFinishedProductDeliveryDTO);
        MdsFinishedProductDeliveryPO mdsFinishedProductDeliveryPO = MdsFinishedProductDeliveryConvertor.INSTANCE.dto2Po(mdsFinishedProductDeliveryDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsFinishedProductDeliveryDomainService.validation(mdsFinishedProductDeliveryDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(mdsFinishedProductDeliveryPO);
        mdsFinishedProductDeliveryDao.update(mdsFinishedProductDeliveryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MdsFinishedProductDeliveryDTO> list) {
        List<MdsFinishedProductDeliveryPO> newList = MdsFinishedProductDeliveryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        mdsFinishedProductDeliveryDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MdsFinishedProductDeliveryDTO> list) {
        List<MdsFinishedProductDeliveryPO> newList = MdsFinishedProductDeliveryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        mdsFinishedProductDeliveryDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return mdsFinishedProductDeliveryDao.deleteBatch(idList);
        }
        return mdsFinishedProductDeliveryDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MdsFinishedProductDeliveryVO selectByPrimaryKey(String id) {
        MdsFinishedProductDeliveryPO po = mdsFinishedProductDeliveryDao.selectByPrimaryKey(id);
        return MdsFinishedProductDeliveryConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MDS_FINISHED_PRODUCT_DELIVERY")
    public List<MdsFinishedProductDeliveryVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MDS_FINISHED_PRODUCT_DELIVERY")
    public List<MdsFinishedProductDeliveryVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MdsFinishedProductDeliveryVO> dataList = mdsFinishedProductDeliveryDao.selectByCondition(sortParam, queryCriteriaParam);
        MdsFinishedProductDeliveryServiceImpl target = SpringBeanUtils.getBean(MdsFinishedProductDeliveryServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MdsFinishedProductDeliveryVO> selectByParams(Map<String, Object> params) {
        List<MdsFinishedProductDeliveryPO> list = mdsFinishedProductDeliveryDao.selectByParams(params);
        return MdsFinishedProductDeliveryConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MdsFinishedProductDeliveryVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> sync(List<MesFinishedProductDelivery> o) {
        if (CollectionUtils.isEmpty(o)) {
            log.error("接口返回成品发送属性数据为空");
            return BaseResponse.error("接口返回成品发送属性数据为空");
        }
        List<String> kids = o.stream().map(MesFinishedProductDelivery::getKid).distinct().collect(Collectors.toList());
        List<MdsFinishedProductDeliveryPO> oldMdsFinishedProductDeliveryPOs = mdsFinishedProductDeliveryDao.selectByParams(ImmutableMap.of("kids", kids));
        List<MdsFinishedProductDeliveryDTO> insertMdsFinishedProductDeliveryDTOs = Lists.newArrayList();
        List<MdsFinishedProductDeliveryDTO> updateMdsFinishedProductDeliveryDTOs = Lists.newArrayList();
        Map<String, MdsFinishedProductDeliveryPO> oldFinishedProductDeliveryMap = oldMdsFinishedProductDeliveryPOs.stream().collect(Collectors.toMap(t ->
                t.getKid(), Function.identity(), (v1, v2) -> v1));
        for(MesFinishedProductDelivery mesFinishedProductDelivery : o){
            String kid = mesFinishedProductDelivery.getKid();
            MdsFinishedProductDeliveryDTO mdsFinishedProductDeliveryDTO = new MdsFinishedProductDeliveryDTO();
            if(oldFinishedProductDeliveryMap.containsKey(kid)) {
                log.info("已有数据，正在处理数据，kid:{}",kid);
                MdsFinishedProductDeliveryPO mdsFinishedProductDeliveryPO = oldFinishedProductDeliveryMap.get(kid);
                mdsFinishedProductDeliveryDTO=MdsFinishedProductDeliveryConvertor.INSTANCE.po2Dto(mdsFinishedProductDeliveryPO);
                mdsFinishedProductDeliveryDTO.setLastUpdateDate(mesFinishedProductDelivery.getLastUpdateDate());
                mdsFinishedProductDeliveryDTO.setLocatorCode(mesFinishedProductDelivery.getLocatorCode());
                mdsFinishedProductDeliveryDTO.setWarehouseCode(mesFinishedProductDelivery.getWarehosueCode());
                mdsFinishedProductDeliveryDTO.setCreateUser(mesFinishedProductDelivery.getUserName());
                mdsFinishedProductDeliveryDTO.setDeadlineFlag(mesFinishedProductDelivery.getAttribute3());
                mdsFinishedProductDeliveryDTO.setStorageTime(mesFinishedProductDelivery.getAttribute4());
                mdsFinishedProductDeliveryDTO.setOrderNo(mesFinishedProductDelivery.getAttribute9());
                mdsFinishedProductDeliveryDTO.setCompanyCode(mesFinishedProductDelivery.getBusinessUnitt());
                mdsFinishedProductDeliveryDTO.setStockPointCode(mesFinishedProductDelivery.getPlantCode());
                mdsFinishedProductDeliveryDTO.setProductCode(mesFinishedProductDelivery.getItemCode());
                mdsFinishedProductDeliveryDTO.setCreationDate(mesFinishedProductDelivery.getCreationDate());
                mdsFinishedProductDeliveryDTO.setEnabled(Objects.equals(mesFinishedProductDelivery.getEnableFlag(),"Y")?
                        YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                updateMdsFinishedProductDeliveryDTOs.add(mdsFinishedProductDeliveryDTO);
            }else{
                log.info("没有找到对应的数据，正在处理新增数据，kid:{}",kid);

                mdsFinishedProductDeliveryDTO.setKid(kid);
                mdsFinishedProductDeliveryDTO.setLastUpdateDate(mesFinishedProductDelivery.getLastUpdateDate());
                mdsFinishedProductDeliveryDTO.setLocatorCode(mesFinishedProductDelivery.getLocatorCode());
                mdsFinishedProductDeliveryDTO.setWarehouseCode(mesFinishedProductDelivery.getWarehosueCode());
                mdsFinishedProductDeliveryDTO.setCreateUser(mesFinishedProductDelivery.getUserName());
                mdsFinishedProductDeliveryDTO.setDeadlineFlag(mesFinishedProductDelivery.getAttribute3());
                mdsFinishedProductDeliveryDTO.setStorageTime(mesFinishedProductDelivery.getAttribute4());
                mdsFinishedProductDeliveryDTO.setOrderNo(mesFinishedProductDelivery.getAttribute9());
                mdsFinishedProductDeliveryDTO.setCompanyCode(mesFinishedProductDelivery.getBusinessUnitt());
                mdsFinishedProductDeliveryDTO.setStockPointCode(mesFinishedProductDelivery.getPlantCode());
                mdsFinishedProductDeliveryDTO.setProductCode(mesFinishedProductDelivery.getItemCode());
                mdsFinishedProductDeliveryDTO.setEnabled(Objects.equals(mesFinishedProductDelivery.getEnableFlag(),"Y")?
                        YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                mdsFinishedProductDeliveryDTO.setCreationDate(mesFinishedProductDelivery.getCreationDate());
                insertMdsFinishedProductDeliveryDTOs.add(mdsFinishedProductDeliveryDTO);
            }
        }
        if(CollectionUtils.isNotEmpty(insertMdsFinishedProductDeliveryDTOs)){
            List<List<MdsFinishedProductDeliveryDTO>> partition = com.google.common.collect.Lists.partition(insertMdsFinishedProductDeliveryDTOs, 2500);
            for (List<MdsFinishedProductDeliveryDTO> mdsFinishedProductDeliveryDTOS : partition) {
                this.doCreateBatch(mdsFinishedProductDeliveryDTOS);
            }
            log.info("MES成品发送属性新增数据条数：{}", insertMdsFinishedProductDeliveryDTOs.size());

        }
        if(CollectionUtils.isNotEmpty(updateMdsFinishedProductDeliveryDTOs)){
            List<List<MdsFinishedProductDeliveryDTO>> partition = com.google.common.collect.Lists.partition(updateMdsFinishedProductDeliveryDTOs, 2500);
            for (List<MdsFinishedProductDeliveryDTO> mdsFinishedProductDeliveryDTOS : partition) {
                this.doUpdateBatch(mdsFinishedProductDeliveryDTOS);
            }
            log.info("MES成品发送属性更新数据条数：{}", updateMdsFinishedProductDeliveryDTOs.size());

        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleSyncFinishedProductDelivery(String tenantId) {
        Map<String, Object> apiParamMap = new HashMap<>(2);
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.FINISHED_PRODUCT_DELIVERY.getCode(), apiParamMap);

        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MdsFinishedProductDeliveryVO> invocation(List<MdsFinishedProductDeliveryVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
