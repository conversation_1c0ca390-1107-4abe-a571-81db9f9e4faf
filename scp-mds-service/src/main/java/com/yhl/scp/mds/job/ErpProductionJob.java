package com.yhl.scp.mds.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>ErpProductionJob</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 11:59:37
 */
@Component
@Slf4j
public class ErpProductionJob {
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private NewProductStockPointService newProductStockPointService;

    @XxlJob("erpProductionJob")
    private ReturnT<String> erpProductionJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在DFP模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的同步物料信息job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());

            newProductStockPointService.syncProductStockPoints(scenario.getTenantId(),null);

            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步物料信息job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
