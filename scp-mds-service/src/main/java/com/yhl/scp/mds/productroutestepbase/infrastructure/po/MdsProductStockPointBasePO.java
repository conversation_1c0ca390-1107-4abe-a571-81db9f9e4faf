package com.yhl.scp.mds.productroutestepbase.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>MdsProductStockPointBasePO</code>
 * <p>
 * 产品工艺基础数据PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 11:29:01
 */
public class MdsProductStockPointBasePO extends BasePO implements Serializable {

    private static final long serialVersionUID = -69237707071120063L;

    /**
     * 库存点ID
     */
    private String stockPointId;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 库存点名称
     */
    private String stockPointName;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 编码名称
     */
    private String productName;
    /**
     * 长
     */
    private BigDecimal productLength;
    /**
     * 宽
     */
    private BigDecimal productWidth;
    /**
     * 厚
     */
    private BigDecimal productThickness;
    /**
     * 装车位置
     */
    private String loadPosition;
    /**
     * 颜色
     */
    private String productColor;
    /**
     * 玻璃颜色
     */
    private String glassColor;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 难度等级
     */
    private String difficultyLevel;
    /**
     * 风栅类型
     */
    private String gridType;
    /**
     * 生产模式
     */
    private String productionModel;
    /**
     * 钢化类型
     */
    private String tougheningType;
    /**
     * 膜系
     */
    private String membraneSystem;
    /**
     * HUD
     */
    private String hud;
    /**
     * 夹丝类型
     */
    private String clampType;
    /**
     * 印边
     */
    private String sealEdge;
    /**
     * 面积
     */
    private String productArea;
    /**
     * 曲率
     */
    private String curvature;
    /**
     * 编码目录号
     */
    private String dirNum;
    /**
     * 工艺类型
     */
    private String itemType;
    /**
     * 除膜工艺
     */
    private String attr1;
    /**
     * 物料标识
     */
    private String itemFlag;
    /**
     * 生产线组
     */
    private String lineGroup;
    /**
     * 工装大类
     */
    private String standardResourceId;
    /**
     * 零件号
     */
    private String partNum;

    public String getStockPointId() {
        return stockPointId;
    }

    public void setStockPointId(String stockPointId) {
        this.stockPointId = stockPointId;
    }

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getStockPointName() {
        return stockPointName;
    }

    public void setStockPointName(String stockPointName) {
        this.stockPointName = stockPointName;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public BigDecimal getProductLength() {
        return productLength;
    }

    public void setProductLength(BigDecimal productLength) {
        this.productLength = productLength;
    }

    public BigDecimal getProductWidth() {
        return productWidth;
    }

    public void setProductWidth(BigDecimal productWidth) {
        this.productWidth = productWidth;
    }

    public BigDecimal getProductThickness() {
        return productThickness;
    }

    public void setProductThickness(BigDecimal productThickness) {
        this.productThickness = productThickness;
    }

    public String getLoadPosition() {
        return loadPosition;
    }

    public void setLoadPosition(String loadPosition) {
        this.loadPosition = loadPosition;
    }

    public String getProductColor() {
        return productColor;
    }

    public void setProductColor(String productColor) {
        this.productColor = productColor;
    }

    public String getGlassColor() {
        return glassColor;
    }

    public void setGlassColor(String glassColor) {
        this.glassColor = glassColor;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getDifficultyLevel() {
        return difficultyLevel;
    }

    public void setDifficultyLevel(String difficultyLevel) {
        this.difficultyLevel = difficultyLevel;
    }

    public String getGridType() {
        return gridType;
    }

    public void setGridType(String gridType) {
        this.gridType = gridType;
    }

    public String getProductionModel() {
        return productionModel;
    }

    public void setProductionModel(String productionModel) {
        this.productionModel = productionModel;
    }

    public String getTougheningType() {
        return tougheningType;
    }

    public void setTougheningType(String tougheningType) {
        this.tougheningType = tougheningType;
    }

    public String getMembraneSystem() {
        return membraneSystem;
    }

    public void setMembraneSystem(String membraneSystem) {
        this.membraneSystem = membraneSystem;
    }

    public String getHud() {
        return hud;
    }

    public void setHud(String hud) {
        this.hud = hud;
    }

    public String getClampType() {
        return clampType;
    }

    public void setClampType(String clampType) {
        this.clampType = clampType;
    }

    public String getSealEdge() {
        return sealEdge;
    }

    public void setSealEdge(String sealEdge) {
        this.sealEdge = sealEdge;
    }

    public String getProductArea() {
        return productArea;
    }

    public void setProductArea(String productArea) {this.productArea = productArea; }

    public String getCurvature() {
        return curvature;
    }

    public void setCurvature(String curvature) {
        this.curvature = curvature;
    }

    public String getDirNum() {
        return dirNum;
    }

    public void setDirNum(String dirNum) {
        this.dirNum = dirNum;
    }

    public String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getAttr1() {
        return attr1;
    }

    public void setAttr1(String attr1) {
        this.attr1 = attr1;
    }

    public String getItemFlag() { return itemFlag; }

    public void setItemFlag(String itemFlag) { this.itemFlag = itemFlag; }

    public String getLineGroup() { return lineGroup; }

    public void setLineGroup(String lineGroup) { this.lineGroup = lineGroup; }

    public String getStandardResourceId() { return standardResourceId; }

    public void setStandardResourceId(String standardResourceId) {
        this.standardResourceId = standardResourceId;
    }

    public String getPartNum() { return partNum; }

    public void setPartNum(String partNum) { this.partNum = partNum; }
}
