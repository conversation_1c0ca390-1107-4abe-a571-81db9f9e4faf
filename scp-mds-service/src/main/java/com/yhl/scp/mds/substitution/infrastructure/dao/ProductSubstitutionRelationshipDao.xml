<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.substitution.infrastructure.dao.ProductSubstitutionRelationshipDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.substitution.infrastructure.po.ProductSubstitutionRelationshipPO">
        <!--@Table mps_product_substitution_relationship-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="operation_name" jdbcType="VARCHAR" property="operationName"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="raw_product_code" jdbcType="VARCHAR" property="rawProductCode"/>
        <result column="raw_product_name" jdbcType="VARCHAR" property="rawProductName"/>
        <result column="substitute_product_code" jdbcType="VARCHAR" property="substituteProductCode"/>
        <result column="substitute_product_name" jdbcType="VARCHAR" property="substituteProductName"/>
        <result column="unit_substitute" jdbcType="VARCHAR" property="unitSubstitute"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="failure_time" jdbcType="TIMESTAMP" property="failureTime"/>
        <result column="rule" jdbcType="VARCHAR" property="rule"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="bill_sequence_id" jdbcType="VARCHAR" property="billSequenceId"/>
        <result column="component_sequence_id" jdbcType="VARCHAR" property="componentSequenceId"/>
        <result column="data_source" jdbcType="VARCHAR" property="dataSource"/>
        <result column="film_coefficient" jdbcType="VARCHAR" property="filmCoefficient"/>
        <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted"/>
        <result column="substitution_type" jdbcType="VARCHAR" property="substitutionType"/>
        <result column="substitution_type_desc" jdbcType="VARCHAR" property="substitutionTypeDesc"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO">
        <result column="product_classify" jdbcType="VARCHAR" property="productClassify"/>
        <result column="raw_product_Id" jdbcType="VARCHAR" property="rawProductId"/>
        <result column="substitute_product_id" jdbcType="VARCHAR" property="substituteProductId"/>
        <result column="product_category" jdbcType="VARCHAR" property="productCategory"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,stock_point_code,stock_point_name,operation_code,operation_name,product_code,product_name,raw_product_code,
        raw_product_name,substitute_product_code,substitute_product_name,unit_substitute,priority,effective_time,
        failure_time,rule,remark,enabled,creator,create_time,modifier,modify_time,version_value,last_update_date,bill_sequence_id,
        component_sequence_id,data_source,film_coefficient,
        is_deleted,substitution_type,substitution_type_desc
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />,product_classify,raw_product_Id,substitute_product_id,product_category
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodeList != null and params.stockPointCodeList != ''">
                and stock_point_code in
                <foreach collection="params.stockPointCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.stockPointName != null and params.stockPointName != ''">
                and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.operationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.operationName != null and params.operationName != ''">
                and operation_name = #{params.operationName,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.rawProductCode != null and params.rawProductCode != ''">
                and raw_product_code = #{params.rawProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.rawProductName != null and params.rawProductName != ''">
                and raw_product_name = #{params.rawProductName,jdbcType=VARCHAR}
            </if>
            <if test="params.substituteProductCode != null and params.substituteProductCode != ''">
                and substitute_product_code = #{params.substituteProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.substituteProductName != null and params.substituteProductName != ''">
                and substitute_product_name = #{params.substituteProductName,jdbcType=VARCHAR}
            </if>
            <if test="params.unitSubstitute != null">
                and unit_substitute = #{params.unitSubstitute,jdbcType=VARCHAR}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.failureTime != null">
                and failure_time = #{params.failureTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.rule != null and params.rule != ''">
                and priority = #{params.rule,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.productClassify != null">
                and product_classify = #{params.productClassify,jdbcType=INTEGER}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.billSequenceId != null and params.billSequenceId != ''">
                and bill_sequence_id = #{params.billSequenceId,jdbcType=VARCHAR}
            </if>
            <if test="params.componentSequenceId != null and params.componentSequenceId != ''">
                and component_sequence_id = #{params.componentSequenceId,jdbcType=VARCHAR}
            </if>
            <if test="params.datSource != null and params.datSource != ''">
                and data_source = #{params.datSource,jdbcType=VARCHAR}
            </if>
            <if test="params.combineKeys != null and params.combineKeys.size() > 0">
                and CONCAT_WS('#',product_code, raw_product_code, substitute_product_code) in
                <foreach collection="params.combineKeys" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.filmCoefficient != null and params.filmCoefficient != ''">
                and film_coefficient = #{params.filmCoefficient,jdbcType=VARCHAR}
            </if>
            <if test="params.isDeleted != null and params.isDeleted != ''">
                and is_deleted = #{params.isDeleted,jdbcType=VARCHAR}
            </if>
            <if test="params.substitutionType != null and params.substitutionType != ''">
                and substitution_type = #{params.substitutionType,jdbcType=VARCHAR}
            </if>
            <if test="params.substitutionTypeDesc != null and params.substitutionTypeDesc != ''">
                and substitution_type_desc = #{params.substitutionTypeDesc,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_product_substitution_relationship
        where SYSDATE() &gt;= effective_time and SYSDATE() &lt;= failure_time
    </select>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_product_substitution_relationship
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_product_substitution_relationship
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mds_product_substitution_relationship
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_product_substitution_relationship
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_mds_product_substitution_relationship
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.substitution.infrastructure.po.ProductSubstitutionRelationshipPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_product_substitution_relationship(
        id,
        stock_point_code,
        stock_point_name,
        operation_code,
        operation_name,
        product_code,
        product_name,
        raw_product_code,
        raw_product_name,
        substitute_product_code,
        substitute_product_name,
        unit_substitute,
        priority,
        rule,
        effective_time,
        failure_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        last_update_date,
        bill_sequence_id,
        component_sequence_id,
        data_source,
        film_coefficient,
        is_deleted,
        substitution_type,
        substitution_type_desc)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{operationName,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{rawProductCode,jdbcType=VARCHAR},
        #{rawProductName,jdbcType=VARCHAR},
        #{substituteProductCode,jdbcType=VARCHAR},
        #{substituteProductName,jdbcType=VARCHAR},
        #{unitSubstitute,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{rule,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{failureTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{billSequenceId,jdbcType=VARCHAR},
        #{componentSequenceId,jdbcType=VARCHAR},
        #{dataSource,jdbcType=VARCHAR},
        #{filmCoefficient,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=VARCHAR},
        #{substitutionType,jdbcType=VARCHAR},
        #{substitutionTypeDesc,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.substitution.infrastructure.po.ProductSubstitutionRelationshipPO">
        insert into mds_product_substitution_relationship(
        id,
        stock_point_code,
        stock_point_name,
        operation_code,
        operation_name,
        product_code,
        product_name,
        raw_product_code,
        raw_product_name,
        substitute_product_code,
        substitute_product_name,
        unit_substitute,
        priority,
        rule,
        effective_time,
        failure_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        last_update_date,
        bill_sequence_id,
        component_sequence_id,
        data_source,
        film_coefficient,
        is_deleted,
        substitution_type,
        substitution_type_desc)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{operationName,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{rawProductCode,jdbcType=VARCHAR},
        #{rawProductName,jdbcType=VARCHAR},
        #{substituteProductCode,jdbcType=VARCHAR},
        #{substituteProductName,jdbcType=VARCHAR},
        #{unitSubstitute,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{rule,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{failureTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{billSequenceId,jdbcType=VARCHAR},
        #{componentSequenceId,jdbcType=VARCHAR},
        #{dataSource,jdbcType=VARCHAR},
        #{filmCoefficient,jdbcType=VARCHAR},
        #{isDeleted,jdbcType=VARCHAR},
        #{substitutionType,jdbcType=VARCHAR},
        #{substitutionTypeDesc,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_product_substitution_relationship(
        id,
        stock_point_code,
        stock_point_name,
        operation_code,
        operation_name,
        product_code,
        product_name,
        raw_product_code,
        raw_product_name,
        substitute_product_code,
        substitute_product_name,
        unit_substitute,
        priority,
        rule,
        effective_time,
        failure_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        last_update_date,
        bill_sequence_id,
        component_sequence_id,
        data_source,
        film_coefficient,
        is_deleted,
        substitution_type,
        substitution_type_desc)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.operationName,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.productName,jdbcType=VARCHAR},
        #{entity.rawProductCode,jdbcType=VARCHAR},
        #{entity.rawProductName,jdbcType=VARCHAR},
        #{entity.substituteProductCode,jdbcType=VARCHAR},
        #{entity.substituteProductName,jdbcType=VARCHAR},
        #{entity.unitSubstitute,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.rule,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.failureTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
        #{entity.billSequenceId,jdbcType=VARCHAR},
        #{entity.componentSequenceId,jdbcType=VARCHAR},
        #{entity.dataSource,jdbcType=VARCHAR},
        #{entity.filmCoefficient,jdbcType=VARCHAR},
        #{entity.isDeleted,jdbcType=VARCHAR},
        #{entity.substitutionType,jdbcType=VARCHAR},
        #{entity.substitutionTypeDesc,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_product_substitution_relationship(
        id,
        stock_point_code,
        stock_point_name,
        operation_code,
        operation_name,
        product_code,
        product_name,
        raw_product_code,
        raw_product_name,
        substitute_product_code,
        substitute_product_name,
        unit_substitute,
        priority,
        rule,
        effective_time,
        failure_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        last_update_date,
        bill_sequence_id,
        component_sequence_id,
        data_source,
        film_coefficient,
        is_deleted,
        substitution_type,
        substitution_type_desc)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.operationName,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.productName,jdbcType=VARCHAR},
        #{entity.rawProductCode,jdbcType=VARCHAR},
        #{entity.rawProductName,jdbcType=VARCHAR},
        #{entity.substituteProductCode,jdbcType=VARCHAR},
        #{entity.substituteProductName,jdbcType=VARCHAR},
        #{entity.unitSubstitute,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.rule,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.failureTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
        #{entity.billSequenceId,jdbcType=VARCHAR},
        #{entity.componentSequenceId,jdbcType=VARCHAR},
        #{entity.dataSource,jdbcType=VARCHAR},
        #{entity.filmCoefficient,jdbcType=VARCHAR},
        #{entity.isDeleted,jdbcType=VARCHAR},
        #{entity.substitutionType,jdbcType=VARCHAR},
        #{entity.substitutionTypeDesc,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.substitution.infrastructure.po.ProductSubstitutionRelationshipPO">
        update mds_product_substitution_relationship set
        stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
        stock_point_name = #{stockPointName,jdbcType=VARCHAR},
        operation_code = #{operationCode,jdbcType=VARCHAR},
        operation_name = #{operationName,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        product_name = #{productName,jdbcType=VARCHAR},
        raw_product_code = #{rawProductCode,jdbcType=VARCHAR},
        raw_product_name = #{rawProductName,jdbcType=VARCHAR},
        substitute_product_code = #{substituteProductCode,jdbcType=VARCHAR},
        substitute_product_name = #{substituteProductName,jdbcType=VARCHAR},
        unit_substitute = #{unitSubstitute,jdbcType=VARCHAR},
        priority = #{priority,jdbcType=INTEGER},
        rule = #{rule,jdbcType=VARCHAR},
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
        failure_time = #{failureTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
        bill_sequence_id = #{billSequenceId,jdbcType=VARCHAR},
        component_sequence_id = #{componentSequenceId,jdbcType=VARCHAR},
        data_source = #{dataSource,jdbcType=VARCHAR},
        film_coefficient = #{filmCoefficient,jdbcType=VARCHAR},
        is_deleted               = #{isDeleted,jdbcType=VARCHAR},
        substitution_type        = #{substitutionType,jdbcType=VARCHAR},
        substitution_type_desc        = #{substitutionTypeDesc,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.substitution.infrastructure.po.ProductSubstitutionRelationshipPO">
        update mds_product_substitution_relationship
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.operationName != null and item.operationName != ''">
                operation_name = #{item.operationName,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.rawProductCode != null and item.rawProductCode != ''">
                raw_product_code = #{item.rawProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.rawProductName != null and item.rawProductName != ''">
                raw_product_name = #{item.rawProductName,jdbcType=VARCHAR},
            </if>
            <if test="item.substituteProductCode != null and item.substituteProductCode != ''">
                substitute_product_code = #{item.substituteProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.substituteProductName != null and item.substituteProductName != ''">
                substitute_product_name = #{item.substituteProductName,jdbcType=VARCHAR},
            </if>
            <if test="item.unitSubstitute != null">
                unit_substitute = #{item.unitSubstitute,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.rule != null and item.rule != ''">
                rule = #{item.rule,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.failureTime != null">
                failure_time = #{item.failureTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.billSequenceId != null and item.billSequenceId != ''">
                bill_sequence_id = #{item.billSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.componentSequenceId != null and item.componentSequenceId != ''">
                component_sequence_id = #{item.componentSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSource != null and item.dataSource != ''">
                data_source = #{item.dataSource,jdbcType=VARCHAR},
            </if>
            <if test="item.filmCoefficient != null and item.filmCoefficient != ''">
                film_coefficient = #{item.filmCoefficient,jdbcType=VARCHAR},
            </if>
            <if test="item.isDeleted != null and item.isDeleted != ''">
                is_deleted = #{item.isDeleted,jdbcType=VARCHAR},
            </if>
            <if test="item.substitutionType != null and item.substitutionType != ''">
                substitution_type = #{item.substitutionType,jdbcType=VARCHAR},
            </if>
            <if test="item.substitutionTypeDesc != null and item.substitutionTypeDesc != ''">
                substitution_type_desc = #{item.substitutionTypeDesc,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_product_substitution_relationship
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="raw_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rawProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="raw_product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rawProductName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="substitute_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.substituteProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="substitute_product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.substituteProductName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit_substitute = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitSubstitute,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rule = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rule,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="failure_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.failureTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="bill_sequence_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.billSequenceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="component_sequence_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.componentSequenceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="data_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dataSource,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="film_coefficient = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.filmCoefficient,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.isDeleted,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="substitution_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.substitutionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="substitution_type_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.substitutionTypeDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_product_substitution_relationship
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.operationName != null and item.operationName != ''">
                operation_name = #{item.operationName,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.rawProductCode != null and item.rawProductCode != ''">
                raw_product_code = #{item.rawProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.rawProductName != null and item.rawProductName != ''">
                raw_product_name = #{item.rawProductName,jdbcType=VARCHAR},
            </if>
            <if test="item.substituteProductCode != null and item.substituteProductCode != ''">
                substitute_product_code = #{item.substituteProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.substituteProductName != null and item.substituteProductName != ''">
                substitute_product_name = #{item.substituteProductName,jdbcType=VARCHAR},
            </if>
            <if test="item.unitSubstitute != null">
                unit_substitute = #{item.unitSubstitute,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.rule != null and item.rule != ''">
                rule = #{item.rule,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.failureTime != null">
                failure_time = #{item.failureTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.billSequenceId != null and item.billSequenceId != ''">
                bill_sequence_id = #{item.billSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.componentSequenceId != null and item.componentSequenceId != ''">
                component_sequence_id = #{item.componentSequenceId,jdbcType=VARCHAR},
            </if>
            <if test="item.dataSource != null and item.dataSource != ''">
                data_source = #{item.dataSource,jdbcType=VARCHAR},
            </if>
            <if test="item.filmCoefficient != null and item.filmCoefficient != ''">
                film_coefficient = #{item.filmCoefficient,jdbcType=VARCHAR},
            </if>
            <if test="item.isDeleted != null and item.isDeleted != ''">
                is_deleted = #{item.isDeleted,jdbcType=VARCHAR},
            </if>
            <if test="item.substitutionType != null and item.substitutionType != ''">
                substitution_type = #{item.substitutionType,jdbcType=VARCHAR},
            </if>
            <if test="item.substitutionTypeDesc != null and item.substitutionTypeDesc != ''">
                substitution_type_desc = #{item.substitutionTypeDesc,jdbcType=VARCHAR},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_product_substitution_relationship where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_product_substitution_relationship where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
