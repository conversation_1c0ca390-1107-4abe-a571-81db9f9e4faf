package com.yhl.scp.mds.newproduct.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringConvertUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpProduct;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mdm.MdmProductionTime;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.newProduct.enums.ProductTallyOrderModeEnum;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.alt.infrastructure.dao.AlternativeMaterialBasicDao;
import com.yhl.scp.mds.baseResource.service.PhysicalResourceService;
import com.yhl.scp.mds.baseResource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.bom.service.ProductAboutBomService;
import com.yhl.scp.mds.box.service.BoxInfoService;
import com.yhl.scp.mds.box.vo.BoxInfoVO;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.newproduct.convertor.NewProductStockPointConvertor;
import com.yhl.scp.mds.newproduct.domain.entity.NewProductStockPointDO;
import com.yhl.scp.mds.newproduct.domain.service.NewProductStockPointDomainService;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointDTO;
import com.yhl.scp.mds.newproduct.enums.NewProductEnum;
import com.yhl.scp.mds.newproduct.enums.PlannerTypeEnum;
import com.yhl.scp.mds.newproduct.infrastructure.dao.NewProductStockPointDao;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointBaseVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.newproduct.vo.ProductMassProductionDetailVO;
import com.yhl.scp.mds.newproduct.vo.ProductMassProductionVO;
import com.yhl.scp.mds.productBox.service.ProductBoxRelationService;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import com.yhl.scp.mds.productroutestepbase.service.MdsProductStockPointBaseService;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.routing.service.NewRoutingService;
import com.yhl.scp.mds.routing.service.NewRoutingStepInputService;
import com.yhl.scp.mds.routing.service.NewRoutingStepResourceService;
import com.yhl.scp.mds.routing.service.NewRoutingStepService;
import com.yhl.scp.mds.routing.vo.NewRoutingStepInputVO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepResourceVO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepVO;
import com.yhl.scp.mds.routing.vo.NewRoutingVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ProductStockPointServiceImpl</code>
 * <p>
 * 物品应用实现
 * </p>
 *
 * @version 1.0
 * @since 2024-07-30 16:02:04
 */
@Slf4j
@Service
public class NewProductStockPointServiceImpl extends AbstractService implements NewProductStockPointService {

    @Resource
    private NewProductStockPointDao newProductStockPointDao;

    @Resource
    private NewProductStockPointDomainService newProductStockPointDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private ProductAboutBomService productAboutBomService;
    private AlternativeMaterialBasicDao alternativeMaterialBasicDao;

    @Resource
    private NewRoutingService newRoutingService;

    @Resource
    private NewRoutingStepService newRoutingStepService;

    @Resource
    private NewRoutingStepInputService newRoutingStepInputService;

    @Resource
    private NewRoutingStepResourceService newRoutingStepResourceService;
    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private MdsProductStockPointBaseService mdsProductStockPointBaseService;

    @Resource
    private ProductBoxRelationService productBoxRelationService;


    @Resource
    private PhysicalResourceService physicalResourceService;

    @Resource
    private BoxInfoService boxInfoService;

    @Override
    public BaseResponse<Void> doCreate(NewProductStockPointDTO newProductStockPointDTO) {
        // 0.数据转换
        NewProductStockPointDO newProductStockPointDO = NewProductStockPointConvertor.INSTANCE.dto2Do(newProductStockPointDTO);
        NewProductStockPointPO newProductStockPointPO = NewProductStockPointConvertor.INSTANCE.dto2Po(newProductStockPointDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        newProductStockPointDomainService.validation(newProductStockPointDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(newProductStockPointPO);
        newProductStockPointDao.insert(newProductStockPointPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(NewProductStockPointDTO newProductStockPointDTO) {
        // 0.数据转换
        NewProductStockPointDO newProductStockPointDO = NewProductStockPointConvertor.INSTANCE.dto2Do(newProductStockPointDTO);
        NewProductStockPointPO newProductStockPointPO = NewProductStockPointConvertor.INSTANCE.dto2Po(newProductStockPointDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        newProductStockPointDomainService.validation(newProductStockPointDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(newProductStockPointPO);
        newProductStockPointDao.update(newProductStockPointPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<NewProductStockPointDTO> list) {
        List<NewProductStockPointPO> newList = NewProductStockPointConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        newProductStockPointDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<NewProductStockPointDTO> list) {
        // 获取当前线程的堆栈跟踪
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        log.info("doUpdateBatch method called with {} records", list != null ? list.size() : 0);

        if (stackTrace.length > 2) {  // 索引0是getStackTrace，索引1是当前方法
            StackTraceElement caller = stackTrace[2];
            log.info("Direct caller: {}.{} ({}:{})",
                    caller.getClassName(),
                    caller.getMethodName(),
                    caller.getFileName(),
                    caller.getLineNumber());
        }

        // 打印完整调用链
        log.info("Complete call stack trace:");
        for (int i = 1; i < stackTrace.length; i++) {
            String indentation = StringUtils.repeat("  ", i - 1);

            log.info("{}at {}.{} ({}:{})",
                    indentation,
                    stackTrace[i].getClassName(),
                    stackTrace[i].getMethodName(),
                    stackTrace[i].getFileName(),
                    stackTrace[i].getLineNumber());
        }

        // 添加业务标识信息
        if (list != null && !list.isEmpty()) {
            NewProductStockPointDTO firstItem = list.get(0);
            log.info("Business context - First item in batch: ProductCode={}, StockPointCode={}",
                    firstItem.getProductCode(),
                    firstItem.getStockPointCode());
        }

        List<NewProductStockPointPO> newList = NewProductStockPointConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        log.info("Executing batch update in database...");
        newProductStockPointDao.updateBatch(newList);
        log.info("Batch update completed successfully");
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return newProductStockPointDao.deleteBatch(idList);
        }
        return newProductStockPointDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public NewProductStockPointVO selectByPrimaryKey(String id) {
        NewProductStockPointPO po = newProductStockPointDao.selectByPrimaryKey(id);
        return NewProductStockPointConvertor.INSTANCE.po2Vo(po);
    }

    public List<NewProductStockPointVO> selectByPrimaryKeys(List<String> ids) {
        List<NewProductStockPointPO> po = newProductStockPointDao.selectByPrimaryKeys(ids);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(po);
    }

    @Override
    @Expression(value = "PRODUCT_STOCK_POINT")
    public List<NewProductStockPointVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PRODUCT_STOCK_POINT")
    public List<NewProductStockPointVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<NewProductStockPointVO> dataList = newProductStockPointDao.selectByCondition(sortParam, queryCriteriaParam);
        NewProductStockPointServiceImpl target = springBeanUtils.getBean(NewProductStockPointServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<NewProductStockPointVO> selectByParams(Map<String, Object> params) {
        List<NewProductStockPointPO> list = newProductStockPointDao.selectByParams(params);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewProductStockPointVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PRODUCT_STOCK_POINT.getCode();
    }

    @Override
    public List<NewProductStockPointVO> invocation(List<NewProductStockPointVO> dataList, Map<String, Object> params, String invocation) {
        /*Map<String, String> userMap = ipsNewFeign.userList().stream().filter(item -> StringUtils.isNotEmpty(item.getId()) && StringUtils.isNotEmpty(item.getCnName()))
                .collect(Collectors.toMap(User::getId, User::getCnName, (t1, t2) -> t2));
        if (MapUtils.isEmpty(userMap)) {
            return dataList;
        }
        dataList.forEach(item -> {
            String orderPlanner = item.getOrderPlanner();
            if(StringUtils.isNotEmpty(orderPlanner)) {
                String[] split = orderPlanner.split(",");
                String orderPlannerName = Lists.newArrayList(split).stream().map(userMap::get).collect(Collectors.joining(","));
                item.setOrderPlannerName(orderPlannerName);
            }
            String productionPlanner = item.getProductionPlanner();
            if(StringUtils.isNotEmpty(productionPlanner)) {
                String[] split = productionPlanner.split(",");
                String productionPlannerName = Lists.newArrayList(split).stream().map(userMap::get).collect(Collectors.joining(","));
                item.setProductionPlannerName(productionPlannerName);
            }
            String materialPlanner = item.getMaterialPlanner();
            if(StringUtils.isNotEmpty(materialPlanner)) {
                String[] split = materialPlanner.split(",");
                String materialPlannerName = Lists.newArrayList(split).stream().map(userMap::get).collect(Collectors.joining(","));
                item.setMaterialPlannerName(materialPlannerName);
            }
        });*/
        return dataList;
    }

    @Override
    public List<NewProductStockPointVO> selectProductCode(List<String> vehicleModelCodeList, String productCode) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("vehicleCodeList", vehicleModelCodeList);
        if (StringUtils.isNotEmpty(productCode)) {
            queryMap.put("productCode", productCode);
        }
        return this.selectByParams(queryMap);
    }

    @Override
    public int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return 0;
        }
        newProductStockPointDomainService.checkDelete(removeVersionDTOS);
        return newProductStockPointDao.deleteBatchVersion(removeVersionDTOS);
    }

    @Override
    public BaseResponse<List<LabelValue<String>>> dropDownAll() {
        List<LabelValue<String>> result = Lists.newArrayList();
        List<NewProductStockPointVO> newProductStockPointVOS = this.selectAll();
        if (CollectionUtils.isNotEmpty(newProductStockPointVOS)) {
            newProductStockPointVOS.forEach(x -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(x.getProductCode());
                labelValue.setValue(x.getId());
                result.add(labelValue);
            });
        }
        return BaseResponse.success(result);
    }

    @Override
    public List<NewProductStockPointVO> selectByProductCode(List<String> codeList) {
        if (CollectionUtils.isNotEmpty(codeList)) {
            List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByProductCode(codeList);
            return NewProductStockPointConvertor.INSTANCE.po2Vos(newProductStockPointPOS);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<NewProductStockPointVO> selectByVehicleModelCode(List<String> vehicleModelCodeList) {
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByVehicleModelCode(vehicleModelCodeList);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(newProductStockPointPOS);
    }

    @Override
    public List<NewProductStockPointVO> selectByStockPointCodes(List<String> stockPointCodes) {
        return NewProductStockPointConvertor.INSTANCE.po2Vos(newProductStockPointDao.selectByStockPointCodes(stockPointCodes));
    }

    @Override
    public BaseResponse<Void> syncProductStockPoints(String tenantId, String stockPoint) {
        Map params = MapUtil.newHashMap();
        List<NewStockPointVO> newStockPointVOS;
        if (StringUtils.isNotEmpty(stockPoint)) {
            params.put("tenantId", tenantId);
            params.put("enabled", YesOrNoEnum.YES.getCode());
            params.put("stockPointCode", stockPoint);
            newStockPointVOS = newStockPointService.selectByParams(params);
        } else {
            params.put("tenantId", tenantId);
            params.put("enabled", YesOrNoEnum.YES.getCode());
            newStockPointVOS = newStockPointService.selectByParams(params);
        }
        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return BaseResponse.error("库存点信息为空");
        }
        for (NewStockPointVO vo : newStockPointVOS) {
            String interfaceFlag = vo.getInterfaceFlag();
            if (StringUtils.isNotEmpty(interfaceFlag)) {
                String[] split = interfaceFlag.split(",");
                boolean flag = Arrays.stream(split).anyMatch(x -> ApiCategoryEnum.PRODUCT.getCode().equals(x));
                if (flag) {
                    Map<String, Object> newStockPoingMap = new HashMap<>(2);
                    newStockPoingMap.put("stockPointCode", vo.getStockPointCode());
                    log.info("库存点：{}，触发物料接口", vo.getStockPointCode());
                    newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                            ApiCategoryEnum.PRODUCT.getCode(), newStockPoingMap);
                }
            }
        }

        return BaseResponse.success("同步成功");
    }

    @Override
    public List<String> selectFields(String field) {
        return newProductStockPointDao.selectFieldsByField(field);
    }

    @Override
    public List<NewProductStockPointVO> selectProductCodeLike(String stockPointCode, String productCode,
                                                              List<String> productTypes) {
        List<NewProductStockPointPO> list =
                newProductStockPointDao.selectProductCodeLike(StringUtils.isNotBlank(productCode)
                        ? StringConvertUtils.convertToLike(productCode) : null, stockPointCode, productTypes);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewProductStockPointVO> selectProductCodeLikeByVehicleByStock(String productCode,
                                                                              List<String> vehicleModelCodeList,
                                                                              String stockPointCode) {
        List<NewProductStockPointPO> list =
                newProductStockPointDao.selectProductCodeLikeByVehicleByStock(org.apache.commons.lang3.StringUtils.isNotBlank(productCode) ?
                        StringConvertUtils.convertToLike(productCode) : null, vehicleModelCodeList, stockPointCode);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewProductStockPointVO> selectByUserId(String userId, List<String> stockPointCodeList) {
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByUserId(userId, stockPointCodeList);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(newProductStockPointPOS);
    }

    @Override
    public List<NewProductStockPointVO> selectTwoData() {
        return newProductStockPointDao.selectTwoData();
    }

    @Override
    public List<NewProductStockPointVO> selectVOByParams(Map<String, Object> params) {
        return newProductStockPointDao.selectVOByParams(params);
    }

    @Override
    public List<LabelValue<String>> getVehicleModelCode(String stockPointCode, String productCode) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("stockPointCode", stockPointCode);
        params.put("productCode", productCode);
        List<NewProductStockPointVO> newProductStockPointVOS = selectByParams(params);
        if (CollectionUtils.isNotEmpty(newProductStockPointVOS)) {
            return newProductStockPointVOS.stream()
                    .map(x -> new LabelValue<>(x.getVehicleModelCode(), x.getVehicleModelCode()))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<String> getLoadingPositionSub() {
        return newProductStockPointDao.selectLoadingPositionSub();
    }

    @Override
    public BaseResponse<Void> syncProductionTime(String tenantId) {
        Map params = MapUtil.builder().put("tenantId", tenantId).put("enabled", YesOrNoEnum.YES.getCode()).build();
        List<NewStockPointVO> newStockPointVOS = newStockPointService.selectByParams(params);
        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return BaseResponse.error("库存点信息为空");
        }
        for (NewStockPointVO vo : newStockPointVOS) {
            String interfaceFlag = vo.getInterfaceFlag();
            if (StringUtils.isNotEmpty(interfaceFlag)) {
                String[] split = interfaceFlag.split(",");
                boolean flag = Arrays.stream(split).anyMatch(x -> ApiCategoryEnum.PRODUCT_TIME.getCode().equals(x));
                if (flag) {
                    Map<String, Object> newStockPoingMap = new HashMap<>(2);
                    newStockPoingMap.put("stockPointCode", vo.getStockPointCode());
                    newStockPoingMap.put("triggerType", DcpConstants.TASKS_MANUAL_TRIGGER);
                    newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MDM.getCode(),
                            ApiCategoryEnum.PRODUCT_TIME.getCode(), newStockPoingMap);
                }
            }
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public List<NewProductStockPointVO> selectProductListByParamOnDynamicColumns(List<String> dynamicColumns, Map<String, Object> params) {
        String dynamicColumn = getDynamicColumn(dynamicColumns);
        log.info("物料主数据查询列：{}", dynamicColumn);
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectProductListByParamOnDynamicColumns(dynamicColumn, params);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(newProductStockPointPOS);
    }

    @Override
    public List<NewProductStockPointVO> selectProductTypeLike(String productType) {
        List<NewProductStockPointPO> list =
                newProductStockPointDao.selectProductTypeLike(StringUtils.isNotEmpty(productType) ?
                        StringConvertUtils.convertToLike(productType) : null);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewProductStockPointVO> selectSupplyTypeLike(String supplyType) {
        List<NewProductStockPointPO> list =
                newProductStockPointDao.selectSupplyTypeLike(StringUtils.isNotEmpty(supplyType) ?
                        StringConvertUtils.convertToLike(supplyType) : null);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewProductStockPointVO> selectProductColorLike(String productColor) {
        List<NewProductStockPointPO> list =
                newProductStockPointDao.selectProductColorLike(StringUtils.isNotEmpty(productColor) ?
                        StringConvertUtils.convertToLike(productColor) : null);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(list);
    }

    public List<NewProductStockPointVO> selectByLoadPosition(List<String> accessPositionList) {
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByLoadPosition(accessPositionList);
        return NewProductStockPointConvertor.INSTANCE.po2Vos(newProductStockPointPOS);
    }

    private String getDynamicColumn(List<String> columns) {
        // 获取
        List<String> allowedColumns = newProductStockPointDao.getAllColumns();
        if (columns == null || columns.isEmpty()) {
            return "*";
        }
        StringBuilder safeColumns = new StringBuilder();
        for (String col : columns) {
            if (col == null || col.trim().isEmpty()) continue;
            for (String allowedCol : allowedColumns) {
                if (allowedCol.equalsIgnoreCase(col.trim())) {
                    if (safeColumns.length() > 0) {
                        safeColumns.append(", ");
                    }
                    safeColumns.append(allowedCol);
                    break;
                }
            }
        }
        return safeColumns.toString();
    }


    @Override
    public List<LabelValue<String>> getOemByProductCode(String productCode) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("productCode", productCode);
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByParams(params);
        if (CollectionUtils.isNotEmpty(newProductStockPointPOS)) {
            List<String> stockPointByStockList = newProductStockPointPOS.stream()
                    .map(NewProductStockPointPO::getStockPointCode)
                    .collect(Collectors.toList());
            BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
            List<OemStockPointMapVO> oemStockPointMapVOS = dfpFeign.selectOemStockPointByStockPointCode(scenario.getData(), stockPointByStockList);
            if (CollectionUtils.isNotEmpty(oemStockPointMapVOS)) {
                List<LabelValue<String>> list = oemStockPointMapVOS.stream()
                        .map(x -> new LabelValue<>(x.getOemName(), x.getOemCode()))
                        .collect(Collectors.toList());
                return list.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(
                                        lv -> Arrays.asList(lv.getLabel(), lv.getValue()),
                                        lv -> lv,
                                        (existing, replacement) -> existing
                                ),
                                map -> new ArrayList<>(map.values())
                        ));
            }
            return Collections.emptyList();
        }
        return Collections.emptyList();
    }

    @Override
    public List<LabelValue<String>> getOemByProductCodeAndVehicle(String productCode) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("productCode", productCode);
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByParams(params);
        if (CollectionUtils.isNotEmpty(newProductStockPointPOS)) {
            List<String> vehicleModelCodeList = newProductStockPointPOS.stream()
                    .map(NewProductStockPointPO::getVehicleModelCode)
                    .collect(Collectors.toList());
            BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
            List<OemVehicleModelVO> oemVehicleModelVOS = dfpFeign.selectOemVehicleModelByVehicleModelCode(scenario.getData(), vehicleModelCodeList);
            if (CollectionUtils.isNotEmpty(oemVehicleModelVOS)) {
                List<LabelValue<String>> list = oemVehicleModelVOS.stream()
                        .map(x -> new LabelValue<>(x.getOemName(), x.getOemCode()))
                        .collect(Collectors.toList());
                return list.stream()
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(
                                        lv -> Arrays.asList(lv.getLabel(), lv.getValue()),
                                        lv -> lv,
                                        (existing, replacement) -> existing
                                ),
                                map -> new ArrayList<>(map.values())
                        ));
            }
            return Collections.emptyList();
        }
        return Collections.emptyList();
    }

    @Override
    public void doHandover(String plannerType, String originalPlanner, String currentPlanner, List<String> productIds) {
        if (!EnumUtils.getCodeList(PlannerTypeEnum.class).contains(plannerType)) {
            throw new BusinessException("不可识别的计划员类型");
        }
        if (StringUtils.equals(originalPlanner, currentPlanner)) {
            throw new BusinessException("原人员现人员相同不能交接");
        }
        if (CollectionUtils.isEmpty(productIds)) {
            throw new BusinessException("未勾选需要交接物料数据");
        }
        List<NewProductStockPointPO> productList;
        NewProductStockPointConvertor instance = NewProductStockPointConvertor.INSTANCE;
        if (PlannerTypeEnum.ORDER_PLANNER.getCode().equals(plannerType)) {
            Map<String, Object> params = ImmutableMap.of("orderPlanner", originalPlanner, "ids", productIds);
            productList = newProductStockPointDao.selectByParams(params);
            if (CollectionUtils.isEmpty(productList)) {
                throw new BusinessException("未找到该订单计划员数据");
            }
            productList.forEach(item -> {
                String orderPlanner = Arrays.stream(item.getOrderPlanner().split(","))
                        .map(x -> x.equals(originalPlanner) ? currentPlanner : x).distinct()
                        .collect(Collectors.joining(","));
                item.setOrderPlanner(orderPlanner);
            });
        } else if (PlannerTypeEnum.PRODUCTION_PLANNER.getCode().equals(plannerType)) {
            Map<String, Object> params = ImmutableMap.of("productionPlanner", originalPlanner, "ids", productIds);
            productList = newProductStockPointDao.selectByParams(params);
            if (CollectionUtils.isEmpty(productList)) {
                throw new BusinessException("未找到该生产计划员数据");
            }
            productList.forEach(item -> {
                String productionPlanner = Arrays.stream(item.getProductionPlanner().split(","))
                        .map(x -> x.equals(originalPlanner) ? currentPlanner : x).distinct()
                        .collect(Collectors.joining(","));
                item.setProductionPlanner(productionPlanner);
            });
        } else if (PlannerTypeEnum.MATERIAL_PLANNER.getCode().equals(plannerType)) {
            Map<String, Object> params = ImmutableMap.of("materialPlanner", originalPlanner, "ids", productIds);
            productList = newProductStockPointDao.selectByParams(params);
            if (CollectionUtils.isEmpty(productList)) {
                throw new BusinessException("未找到该材料计划员数据");
            }
            productList.forEach(item -> {
                String materialPlanner = Arrays.stream(item.getMaterialPlanner().split(","))
                        .map(x -> x.equals(originalPlanner) ? currentPlanner : x).distinct()
                        .collect(Collectors.joining(","));
                item.setMaterialPlanner(materialPlanner);
            });
        } else {
            Map<String, Object> params = ImmutableMap.of("purchasePlanner", originalPlanner, "ids", productIds);
            productList = newProductStockPointDao.selectByParams(params);
            if (CollectionUtils.isEmpty(productList)) {
                throw new BusinessException("未找到该材料采购员数据");
            }
            productList.forEach(item -> {
                String purchasePlanner = Arrays.stream(item.getPurchasePlanner().split(","))
                        .map(x -> x.equals(originalPlanner) ? currentPlanner : x).distinct()
                        .collect(Collectors.joining(","));
                item.setPurchasePlanner(purchasePlanner);
            });
        }
        List<NewProductStockPointDTO> dtoList = instance.po2Dtos(productList);
        Lists.partition(dtoList, 500).forEach(this::doUpdateBatch);
    }

    @Override
    public List<NewProductStockPointVO> selectProduct4LoadingDemandSubmission() {
        return newProductStockPointDao.selectProduct4LoadingDemandSubmission();
    }

    @Override
    public List<String> selectVehiclesByProductCode(List<String> codeList) {
        return newProductStockPointDao.selectVehiclesByProductCode(codeList);
    }

    @Override
    public BaseResponse<Void> sync(List<ErpProduct> o) {
        if (CollectionUtils.isEmpty(o)) {
            return BaseResponse.success();
        }

        String materialBuyerIds = getMaterialBuyerIdsSafely();

        List<String> productCodes = o.stream().map(ErpProduct::getItemCode).distinct().collect(Collectors.toList());
        // 处理库存点物品
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByProductCode(productCodes);

//        List<NewProductStockPointVO> newProductStockPointVOS = this.selectByProductCode(productCodes);
        Map<String, NewProductStockPointPO> oldProductStockPointPOMap =
                CollectionUtils.isEmpty(newProductStockPointPOS) ?
                        MapUtil.newHashMap() :
                        newProductStockPointPOS.stream().collect(
                                Collectors.toMap(t -> t.getStockPointCode() + "|" + t.getProductCode(),
                                        Function.identity(), (v1, v2) -> v1));
        List<NewProductStockPointDTO> insertNewProductStockPointDTOs = new ArrayList<>();
        List<NewProductStockPointDTO> updateNewProductStockPointDTOs = new ArrayList<>();
        for (ErpProduct erpProduct : o) {
            String itemCode = erpProduct.getItemCode();
            String stockPointCode = erpProduct.getOrganizationCode();
            String dataKey = stockPointCode + "|" + itemCode;
            if (org.apache.commons.lang3.StringUtils.isBlank(itemCode)) {
                continue;
            }
            NewProductStockPointDTO newProductStockPointDTO = new NewProductStockPointDTO();
            if (oldProductStockPointPOMap.containsKey(dataKey)) {
                // 更新库存点
                NewProductStockPointPO newProductStockPointPO = oldProductStockPointPOMap.get(dataKey);
                newProductStockPointDTO = NewProductStockPointConvertor.INSTANCE.po2Dto(newProductStockPointPO);
                newProductStockPointDTO.setStockPointCode(erpProduct.getOrganizationCode());
                newProductStockPointDTO.setProductCode(erpProduct.getItemCode());
                newProductStockPointDTO.setProductName(erpProduct.getItemDescription());
                newProductStockPointDTO.setProductType(erpProduct.getItemType());
                newProductStockPointDTO.setProductClassify(erpProduct.getItemCategory());
                newProductStockPointDTO.setClassifyDesc(erpProduct.getCategoryDescription());
                newProductStockPointDTO.setVehicleModelCode(erpProduct.getModelCode());
                newProductStockPointDTO.setSupplyType(erpProduct.getBuildInWipFlag());
                newProductStockPointDTO.setMeasurementUnit(erpProduct.getPrimaryUnitOfMeasure());
                newProductStockPointDTO.setLoadingPosition(erpProduct.getPositionA());
                newProductStockPointDTO.setLoadingPositionSub(erpProduct.getPositionC());
                newProductStockPointDTO.setVehicleModelType(erpProduct.getCarCategory());
                newProductStockPointDTO.setPlannerCode(erpProduct.getPlannerCode());
                newProductStockPointDTO.setPoCategory(erpProduct.getPoCategory());
                newProductStockPointDTO.setOrganizationId(erpProduct.getOrganizationId());
                newProductStockPointDTO.setInventoryItemId(erpProduct.getInventoryItemId());
                //除InActive为N外，其他都为YES
                newProductStockPointDTO.setEnabled("Active".equals(erpProduct.getInventoryItemStatusCode()) ?
                        YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                newProductStockPointDTO.setInventoryItemStatusCode(erpProduct.getInventoryItemStatusCode());
                if (Objects.nonNull(erpProduct.getArea())) {
                    newProductStockPointDTO.setProductArea(BigDecimal.valueOf(erpProduct.getArea()));
                }
                if (Objects.nonNull(erpProduct.getUnitWeight())) {
                    newProductStockPointDTO.setProductWeight(BigDecimal.valueOf(erpProduct.getUnitWeight()));
                }
                newProductStockPointDTO.setWeightUnit(erpProduct.getWeightUomCode());
                newProductStockPointDTO.setExpireDate(erpProduct.getShelfLifeDays());
                newProductStockPointDTO.setSpecialDesc(erpProduct.getCharacterSpecification());
                newProductStockPointDTO.setLastUpdateDate(erpProduct.getLastUpdateDate());
                newProductStockPointDTO.setItemFlag(erpProduct.getItemFlag());
                newProductStockPointDTO.setIsbj(erpProduct.getISBJ());
                newProductStockPointDTO.setItemCost(erpProduct.getItemCost());
                parseProductCode(newProductStockPointDTO);
                if (StringUtils.isNotBlank(materialBuyerIds) && newProductStockPointDTO.getProductClassify().contains("RA.A")) {
                    newProductStockPointDTO.setPurchasePlanner(materialBuyerIds);
                }
                updateNewProductStockPointDTOs.add(newProductStockPointDTO);
            } else {
                // 新增销售组织
                newProductStockPointDTO.setStockPointCode(erpProduct.getOrganizationCode());
                newProductStockPointDTO.setProductCode(erpProduct.getItemCode());
                newProductStockPointDTO.setProductName(erpProduct.getItemDescription());
                newProductStockPointDTO.setProductType(erpProduct.getItemType());
                newProductStockPointDTO.setProductClassify(erpProduct.getItemCategory());
                newProductStockPointDTO.setClassifyDesc(erpProduct.getCategoryDescription());
                newProductStockPointDTO.setVehicleModelCode(erpProduct.getModelCode());
                newProductStockPointDTO.setSupplyType(erpProduct.getBuildInWipFlag());
                newProductStockPointDTO.setMeasurementUnit(erpProduct.getPrimaryUnitOfMeasure());
                newProductStockPointDTO.setLoadingPosition(erpProduct.getPositionA());
                newProductStockPointDTO.setLoadingPositionSub(erpProduct.getPositionC());
                newProductStockPointDTO.setVehicleModelType(erpProduct.getCarCategory());
                newProductStockPointDTO.setPlannerCode(erpProduct.getPlannerCode());
                newProductStockPointDTO.setPoCategory(erpProduct.getPoCategory());
                newProductStockPointDTO.setOrganizationId(erpProduct.getOrganizationId());
                newProductStockPointDTO.setInventoryItemId(erpProduct.getInventoryItemId());
                newProductStockPointDTO.setEnabled("Active".equals(erpProduct.getInventoryItemStatusCode()) ?
                        YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                newProductStockPointDTO.setInventoryItemStatusCode(erpProduct.getInventoryItemStatusCode());
                if (Objects.nonNull(erpProduct.getArea())) {
                    newProductStockPointDTO.setProductArea(BigDecimal.valueOf(erpProduct.getArea()));
                }
                if (Objects.nonNull(erpProduct.getUnitWeight())) {
                    newProductStockPointDTO.setProductWeight(BigDecimal.valueOf(erpProduct.getUnitWeight()));
                }
                newProductStockPointDTO.setWeightUnit(erpProduct.getWeightUomCode());
                newProductStockPointDTO.setExpireDate(erpProduct.getShelfLifeDays());
                newProductStockPointDTO.setSpecialDesc(erpProduct.getCharacterSpecification());
                newProductStockPointDTO.setLastUpdateDate(erpProduct.getLastUpdateDate());
                newProductStockPointDTO.setItemFlag(erpProduct.getItemFlag());
                newProductStockPointDTO.setIsbj(erpProduct.getISBJ());
                newProductStockPointDTO.setItemCost(erpProduct.getItemCost());
                //解析productCode
                parseProductCode(newProductStockPointDTO);
                if (StringUtils.isNotBlank(materialBuyerIds) && newProductStockPointDTO.getProductClassify().contains("RA.A")) {
                    newProductStockPointDTO.setPurchasePlanner(materialBuyerIds);
                }
                insertNewProductStockPointDTOs.add(newProductStockPointDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(insertNewProductStockPointDTOs)) {
            List<List<NewProductStockPointDTO>> partitions = Lists.partition(insertNewProductStockPointDTOs, 1000);
            for (List<NewProductStockPointDTO> part : partitions) {
                this.doCreateBatch(part);
            }
            log.info("新增物料成功，共{}条", insertNewProductStockPointDTOs.size());
        }
        if (CollectionUtils.isNotEmpty(updateNewProductStockPointDTOs)) {
            List<List<NewProductStockPointDTO>> partitions = Lists.partition(updateNewProductStockPointDTOs, 1000);
            for (List<NewProductStockPointDTO> part : partitions) {
                this.doUpdateBatch(part);
            }
            log.info("更新物料成功，共{}条", updateNewProductStockPointDTOs.size());
            //同步bom状态
            productAboutBomService.syncBomStatus(updateNewProductStockPointDTOs);
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public int doLogicDeleteBatch(List<RemoveVersionDTO> deleteProductList) {

        return newProductStockPointDao.doLogicDeleteBatch(deleteProductList);

    }

    @Override
    public List<String> selectVehicleModelCode() {
        Map<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<NewProductStockPointVO> newProductStockPointVOS = this.selectByParams(params);
        if (CollectionUtils.isNotEmpty(newProductStockPointVOS)) {
            return newProductStockPointVOS.stream()
                    .map(NewProductStockPointVO::getVehicleModelCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<NewProductStockPointVO> selectByStockCodeAndProductCode(Collection<ProductSubstitutionRelationshipVO> values) {
        List<NewProductStockPointPO> productStockPointPOS = newProductStockPointDao.selectByStockCodeAndProductCode(values);
        List<NewProductStockPointVO> newProductStockPointVOS = NewProductStockPointConvertor.INSTANCE.po2Vos(productStockPointPOS);
        return newProductStockPointVOS;
    }

    @Override
    public List<NewProductStockPointVO> selectSrmByStockCodeAndProductCode(Collection<SrmSupplierPurchase> values) {
        List<NewProductStockPointPO> productStockPointPOS = newProductStockPointDao.selectSrmByStockCodeAndProductCode(values);
        List<NewProductStockPointVO> newProductStockPointVOS = NewProductStockPointConvertor.INSTANCE.po2Vos(productStockPointPOS);
        return newProductStockPointVOS;
    }

    /**
     * 物料信息-同步过来需要做数据处理
     *
     * @param newProductStockPointDTO
     */
    private void parseProductCode(NewProductStockPointDTO newProductStockPointDTO) {
        String productClassify = newProductStockPointDTO.getProductClassify();
        String productCode = newProductStockPointDTO.getProductCode();
        String productName = newProductStockPointDTO.getProductName();

        try {
            if (!productCode.endsWith("-DEL")) {

                if (productClassify.contains("RA.A")) {

                    //原片
                    newProductStockPointDTO.setProductCategory(NewProductEnum.ORIGINAL_FILM.getCode());
                    //productCode从第4位开始截取四位
                    if (productCode.length() >= 7) {

                        String productLength = productCode.substring(3, 7);
                        newProductStockPointDTO.setProductLength(BigDecimal.valueOf(Integer.parseInt(productLength)));

                    }
                    //productCode从第8位开始截取四位(宽度)
                    if (productCode.length() >= 11) {

                        String productWidth = productCode.substring(7, 11);
                        newProductStockPointDTO.setProductWidth(BigDecimal.valueOf(Integer.parseInt(productWidth)));
                    }
                    //productCode从第12位开始截取4位(厚度)
                    if (productCode.length() >= 15) {

                        String productThickness = productCode.substring(11, 15);
                        newProductStockPointDTO.setProductThickness(BigDecimal.valueOf(Integer.parseInt(productThickness)).divide(BigDecimal.valueOf(100)));
                    }
                    //productCode若只有16位取最后1位为颜色，编码超过16位的都从16位往后截取，都是颜色
                    if (productCode.length() >= 16) {

                        String productColor = productCode.substring(15);
                        newProductStockPointDTO.setProductColor(productColor);
                    }
                }
                //毛坯
                if (productClassify.contains("C0.0")) {
                    if (!productName.contains("切割半成品")) {
                        //productCode前4位表示长度
                        if (productCode.length() >= 4) {

                            String productLength = productCode.substring(0, 4);
                            newProductStockPointDTO.setProductLength(BigDecimal.valueOf(Integer.parseInt(productLength)));
                        }
                        //productCode第五位开始四位表示宽
                        if (productCode.length() >= 8) {

                            String productWidth = productCode.substring(4, 8);
                            newProductStockPointDTO.setProductWidth(BigDecimal.valueOf(Integer.parseInt(productWidth)));
                        }
                        //productCode第9位开始四位表示厚度
                        if (productCode.length() >= 12) {

                            String productThickness = productCode.substring(8, 12);
                            newProductStockPointDTO.setProductThickness(BigDecimal.valueOf(Integer.parseInt(productThickness)).divide(BigDecimal.valueOf(100)));
                        }
                        //productCode最后一位往前取，遇到数字停止截取，截取出来的为颜色
                        String productColor = extractColor(productCode);
                        newProductStockPointDTO.setProductColor(productColor);
                    }
                }
                if (productClassify.contains("RA.V")) {
                    newProductStockPointDTO.setProductCategory(NewProductEnum.PVB.getCode());
                }
                if (productClassify.contains("BB") || productClassify.contains("BG") || productClassify.contains("BJ")) {
                    newProductStockPointDTO.setProductCategory(NewProductEnum.B_TYPE.getCode());
                }
            }
        } catch (Exception e) {
            log.error("解析productCode失败,物料分类为:{},物料编码为:{}", productClassify, productCode);
            log.error("解析失败原因;{}", e.getMessage());
        }
    }

    private String extractColor(String productCode) {
        if (productCode == null || productCode.isEmpty()) {
            return "";
        }

        StringBuilder color = new StringBuilder();
        boolean foundDigit = false;

        for (int i = productCode.length() - 1; i >= 0; i--) {
            char c = productCode.charAt(i);

            if (Character.isDigit(c)) {
                if (!foundDigit) {
                    foundDigit = true;
                } else {
                    break;
                }
            } else {
                color.insert(0, c);
            }
        }
        return color.toString();
    }

    @Override
    public List<NewProductStockPointBaseVO> selectBaseInfoByStockPointCodes(List<String> stockPointCodes) {
        return newProductStockPointDao.selectBaseInfoByStockPointCodes(stockPointCodes);
    }

    @Override
    public List<String> selectProductCodeByParams(Map<String, Object> params) {
        return newProductStockPointDao.selectProductCodeByParams(params);
    }

    @Override
    public BaseResponse<Void> syncProductTime(List<MdmProductionTime> mdmProductionTimes) {
        try {
            if (CollectionUtils.isEmpty(mdmProductionTimes)) {
                return BaseResponse.success();
            }

            // 提取产品编码列表
            List<String> productCodes = mdmProductionTimes.stream()
                    .map(MdmProductionTime::getItemNumber)
                    .distinct()
                    .collect(Collectors.toList());

            // 根据产品编码查询现有的库存点物品
            List<NewProductStockPointVO> newProductStockPointVOS = this.selectByProductCode(productCodes);

            // 将库存点物品按productCode分组
            Map<String, List<NewProductStockPointVO>> productToStockPointsMap =
                    newProductStockPointVOS.stream()
                            .collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode));

            // 准备需要更新的数据集合
            List<NewProductStockPointDTO> updateNewProductStockPointDTOs = Lists.newArrayList();

            for (MdmProductionTime mdmProductionTime : mdmProductionTimes) {
                String itemNumber = mdmProductionTime.getItemNumber();
                if (StringUtils.isBlank(itemNumber)) {
                    continue;
                }

                // 获取与当前itemNumber相关的所有库存点
                List<NewProductStockPointVO> stockPointsForProduct = productToStockPointsMap.get(itemNumber);
                if (stockPointsForProduct == null || stockPointsForProduct.isEmpty()) {
                    continue;
                }

                // 查找匹配的库存点（根据organizationCode）
                Optional<NewProductStockPointVO> matchingStockPoint = stockPointsForProduct.stream()
                        .filter(stockPoint -> stockPoint.getStockPointCode().equals(mdmProductionTime.getOrganizationCode()))
                        .findFirst();

                if (matchingStockPoint.isPresent()) {
                    NewProductStockPointVO oldData = matchingStockPoint.get();
                    NewProductStockPointDTO updateDTO = new NewProductStockPointDTO();

                    // 复制旧对象属性到新DTO中，保留原有的itemNumber和organizationCode
                    BeanUtils.copyProperties(oldData, updateDTO);
                    updateDTO.setProductSop(mdmProductionTime.getSopTime());
                    updateDTO.setProductEop(mdmProductionTime.getEopTime());
                    parseProductCode(updateDTO);

                    updateNewProductStockPointDTOs.add(updateDTO);
                }
            }

            // 执行批量更新操作
            if (CollectionUtils.isNotEmpty(updateNewProductStockPointDTOs)) {
                this.doUpdateBatch(updateNewProductStockPointDTOs);
            }

            return BaseResponse.success("同步成功");
        } catch (Exception e) {
            log.error("同步产品时间报错,{}", e.getMessage());
            if (Objects.nonNull(e.getCause())) {
                log.error("同步产品时间报错原因,{}", e.getCause().toString());
            }
            throw new BusinessException("同步产品时间报错" + e.getMessage());
        }
    }

    @Override
    public String checkTallyOrderMode(String productCode) {
        List<String> productList = Arrays.asList(productCode.split(","));
        if (productList.size() == 1) {
            return null;
        }
        List<NewProductStockPointPO> productInfoList = newProductStockPointDao.selectByProductCode(productList);
        //理货单模式种类
        List<String> tallyOrderModeList = productInfoList.stream()
                .map(e -> StringUtils.isEmpty(e.getTallyOrderMode()) ? ProductTallyOrderModeEnum.MES.getCode() : e.getTallyOrderMode())
                .distinct().collect(Collectors.toList());
        if ((tallyOrderModeList.size() > 1)) {
            throw new BusinessException("产品编码理货单模式不一致");
        }
        return tallyOrderModeList.get(0);
    }

    @Override
    public List<String> selectYpProductCodes() {
        return newProductStockPointDao.selectYpProductCodes();
    }

    @Override
    public List<NewProductStockPointVO> getYpFactoryCode() {
        return newProductStockPointDao.selectYpFactoryCode();
    }

    @Override
    public Map<String, String> checkPorductRouting(List<String> productIds) {
        Map<String, String> returnMap = new HashMap<>();
        //查询产品工艺路径候选资源配置信息（物理资源）
        List<PhysicalResourceVO> physicalResourceVO = physicalResourceService.selectAllResourceSequence();
        //库存点，工序代码，资源id
        List<String> physicalResourceKeys = physicalResourceVO.stream().map(e -> String.join("&", e.getStockPointCode(), e.getSequenceCode(), e.getId()))
                .collect(Collectors.toList());
        //校验产品工艺路径,首次校验
        List<String> needEachProductIds = getCheckproductRoutingMap(productIds, returnMap, physicalResourceKeys);
        //递归校验(递归校验次数)
        int eachCount = 0;
        while (CollUtil.isNotEmpty(needEachProductIds) && eachCount < 10) {
            List<String> eachProductIds = getCheckproductRoutingMap(needEachProductIds, returnMap, physicalResourceKeys);
            needEachProductIds = eachProductIds;
            eachCount++;
        }
        return returnMap;
    }

    /**
     * 校验产品工艺路径
     *
     * @param productIds
     * @param returnMap
     * @param physicalResourceKeys
     * @return
     */
    private List<String> getCheckproductRoutingMap(List<String> productIds, Map<String, String> returnMap, List<String> physicalResourceKeys) {
        //处理需要递归查询的数据
        List<String> needEachProductIds = new ArrayList<>();
        List<NewProductStockPointPO> productList = newProductStockPointDao.selectByPrimaryKeys(productIds);
        Map<String, String> productMap = productList.stream()
                .collect(Collectors.toMap(NewProductStockPointPO::getId, NewProductStockPointPO::getProductCode, (v1, v2) -> v1));
        //1.校验工艺路径是否有效
        List<NewRoutingVO> routingList = newRoutingService.selectByParams(ImmutableMap.of(
                "productIds", productIds));
        Map<String, NewRoutingVO> routingMap = routingList.stream()
                .collect(Collectors.toMap(NewRoutingVO::getProductId, Function.identity(), (v1, v2) -> v1));
        Map<String, String> routingIdMap = new HashMap<>();
        for (String productId : productIds) {
            NewRoutingVO routingVO = routingMap.get(productId);
            if (routingVO == null) {
                returnMap.put(productMap.get(productId), "产品编码不存在工艺路径，请联系工艺维护");
                continue;
            } else if (YesOrNoEnum.NO.getCode().equals(routingVO.getEnabled())) {
                returnMap.put(routingVO.getProductCode(), "产品编码对应的工艺路径已失效，请联系工艺维护");
                continue;
            }
            routingIdMap.put(routingVO.getId(), routingVO.getProductCode());
        }
        if (routingIdMap.isEmpty()) {
            return needEachProductIds;
        }
        //2.校验工艺路径步骤
        List<NewRoutingStepVO> routingStepList = newRoutingStepService.selectByParams(ImmutableMap.of(
                "routingIds", new ArrayList<>(routingIdMap.keySet())));
        Map<String, List<NewRoutingStepVO>> routingStepMap = routingStepList.stream()
                .collect(Collectors.groupingBy(NewRoutingStepVO::getRoutingId));
        //key工艺路径步骤id, value：工序编码（顺序号）
        Map<String, Integer> sequenceNoMap = routingStepList.stream()
                .collect(Collectors.toMap(NewRoutingStepVO::getId, NewRoutingStepVO::getSequenceNo, (v1, v2) -> v1));
        Map<String, List<String>> routingStepIdMap = new HashMap<>();
        List<String> routingStepIds = new ArrayList<>();
        Map<String, String> routingIdTwoMap = new HashMap<>();
        for (Entry<String, String> routingIdEntry : routingIdMap.entrySet()) {
            String routingId = routingIdEntry.getKey();
            String productCode = routingIdEntry.getValue();
            List<NewRoutingStepVO> currRoutingStepList = routingStepMap.get(routingId);
            if (CollectionUtils.isEmpty(currRoutingStepList)) {
                returnMap.put(productCode, "产品编码的工艺路径不存在工艺路径步骤，请联系工艺维护");
                continue;
            }
            currRoutingStepList = currRoutingStepList.stream()
                    .filter(e -> YesOrNoEnum.YES.getCode().equals(e.getEnabled())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currRoutingStepList)) {
                returnMap.put(productCode, "产品编码的工艺路径不存在有效的工艺路径步骤，请联系工艺维护");
                continue;
            }
            routingIdTwoMap.put(routingId, productCode);
            List<String> crrentRoutingStepIds = currRoutingStepList.stream().map(NewRoutingStepVO::getId).collect(Collectors.toList());
            routingStepIds.addAll(crrentRoutingStepIds);
            routingStepIdMap.put(routingId, crrentRoutingStepIds);
        }

        //3.校验工艺路径步骤输入物品
        List<NewRoutingStepInputVO> routingStepInputList = newRoutingStepInputService.selectByParams(ImmutableMap.of(
                "routingStepIds", routingStepIds));
        //只查无效的工艺路径输入物品
        List<String> queryInputProductIds = routingStepInputList.stream()
                .filter(e -> YesOrNoEnum.NO.getCode().equals(e.getEnabled()))
                .map(NewRoutingStepInputVO::getInputProductId).distinct().collect(Collectors.toList());
        List<NewProductStockPointPO> inputProductList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryInputProductIds)) {
            inputProductList = newProductStockPointDao.selectByPrimaryKeys(queryInputProductIds);
        }
        //无效BOM的物料信息
        Map<String, String> inputProductMap = inputProductList.stream()
                .collect(Collectors.toMap(NewProductStockPointPO::getId, NewProductStockPointPO::getProductCode, (v1, v2) -> v1));
        Map<String, List<NewRoutingStepInputVO>> routingStepInputMap = routingStepInputList.stream()
                .collect(Collectors.groupingBy(NewRoutingStepInputVO::getRoutingId));
        //处理需要递归查询的数据
        List<String> removeKeys = new ArrayList<>();
        for (Entry<String, String> routingIdEntry : routingIdTwoMap.entrySet()) {
            String routingId = routingIdEntry.getKey();
            String productCode = routingIdEntry.getValue();
            //既有有效的BOM，也有无效的BOM
            List<NewRoutingStepInputVO> currRoutingStepInputList = routingStepInputMap.get(routingId);
            if (CollectionUtils.isEmpty(currRoutingStepInputList)) {
                returnMap.put(productCode, "产品编码不存在产品BOM，请联系工艺维护");
                removeKeys.add(routingId);
                continue;
            }
            List<String> inputPorductCodes = new ArrayList<>();
            for (NewRoutingStepInputVO inputVO : currRoutingStepInputList) {
                String inputPorductCode = inputProductMap.get(inputVO.getInputProductId());
                if (StringUtils.isNotEmpty(inputPorductCode) && !inputPorductCodes.contains(inputPorductCode)) {
                    inputPorductCodes.add(inputPorductCode);
                }
            }
            //过滤出有效的BOM
            currRoutingStepInputList = currRoutingStepInputList.stream()
                    .filter(e -> YesOrNoEnum.YES.getCode().equals(e.getEnabled())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(currRoutingStepInputList)) {
                returnMap.put(productCode, "产品编码的产品BOM《BOM的物料编码:"
                        + String.join(",", inputPorductCodes) + "》已失效，请联系工艺维护");
                removeKeys.add(routingId);
                continue;
            }
            //维护有效，需要递归查询的物料
            List<String> inputProductIds = currRoutingStepInputList.stream()
                    .filter(e -> StringUtils.isNotEmpty(e.getInputProductId()))
                    .map(NewRoutingStepInputVO::getInputProductId).collect(Collectors.toList());
            needEachProductIds.addAll(inputProductIds);
        }
        //如果这个工艺路径在输入物品已经校验失败了，候选资源就不校验该工艺路径
        removeKeys.forEach(e -> {
            routingIdTwoMap.remove(e);
        });

        //4.校验工艺路径步骤候选资源
        List<NewRoutingStepResourceVO> routingStepResourceList = newRoutingStepResourceService.selectByParams(ImmutableMap.of(
                "routingStepIds", routingStepIds));
        Map<String, List<NewRoutingStepResourceVO>> routingStepResourceMap = routingStepResourceList.stream()
                .collect(Collectors.groupingBy(NewRoutingStepResourceVO::getRoutingStepId));
        List<String> twoRemoveKeys = new ArrayList<>();
        for (Entry<String, String> routingIdEntry : routingIdTwoMap.entrySet()) {
            String routingId = routingIdEntry.getKey();
            String productCode = routingIdEntry.getValue();
            List<String> currentStepIds = routingStepIdMap.get(routingId);
            for (String stepId : currentStepIds) {
                List<NewRoutingStepResourceVO> currRoutingStepResourceList = routingStepResourceMap.get(stepId);
                Integer sequenceNo = sequenceNoMap.get(stepId);
                String sequenceNoStr = sequenceNo == null ? "" : sequenceNo.toString();
                if (CollectionUtils.isEmpty(currRoutingStepResourceList)) {
                    returnMap.put(productCode, "产品编码的《工序 " + sequenceNoStr + "》产线节拍缺失，请联系工艺维护");
                    twoRemoveKeys.add(routingId);
                    break;
                }
                currRoutingStepResourceList = currRoutingStepResourceList.stream()
                        .filter(e -> YesOrNoEnum.YES.getCode().equals(e.getEnabled())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(currRoutingStepResourceList)) {
                    returnMap.put(productCode, "产品编码的《工序 " + sequenceNoStr + "》产线节拍无效，请联系工艺维护");
                    twoRemoveKeys.add(routingId);
                    break;
                }
            }
        }
        //
        twoRemoveKeys.forEach(e -> {
            routingIdTwoMap.remove(e);
        });

        //如果这个工艺路径校验没有问题，那么最后再校验：其工序代码xx和候选资源不一致
        List<String> checkResourceRoutingIds = new ArrayList<>();
        for (Entry<String, String> routingIdEntry : routingIdTwoMap.entrySet()) {
            String routingId = routingIdEntry.getKey();
            String productCode = routingIdEntry.getValue();
            if (!returnMap.containsKey(productCode)) {
                checkResourceRoutingIds.add(routingId);
            }
        }
        if (CollectionUtils.isNotEmpty(checkResourceRoutingIds)) {
            //通过工艺路径ID,查询对应的候选资源数据信息
            List<NewRoutingStepResourceVO> routingStepResources = newRoutingStepResourceService.selectRoutingStepResoueceBase(checkResourceRoutingIds);
            Map<String, List<NewRoutingStepResourceVO>> stepResourceMap = routingStepResources.stream()
                    .collect(Collectors.groupingBy(NewRoutingStepResourceVO::getRoutingId));
            for (String routingId : checkResourceRoutingIds) {
                String productCode = routingIdMap.get(routingId);
                List<NewRoutingStepResourceVO> routingResourceList = stepResourceMap.get(routingId);
                if (CollectionUtils.isEmpty(routingResourceList)) {
                    returnMap.put(productCode, "产品编码的工序无法匹配到产线,请联系工艺维护");
                    continue;
                }
                for (NewRoutingStepResourceVO resource : routingResourceList) {
                    String resourceUnkey = String.join("&", resource.getStockPointCode(), resource.getSequenceNo().toString(), resource.getPhysicalResourceId());
                    if (!physicalResourceKeys.contains(resourceUnkey)) {
                        returnMap.put(productCode, "工序代码《工序 " + resource.getSequenceNo() + "》和候选资源不一致,请联系工艺维护");
                        break;
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(needEachProductIds)) {
            List<NewProductStockPointPO> needEachProductList = newProductStockPointDao.selectByPrimaryKeys(needEachProductIds);
            needEachProductIds = needEachProductList.stream()
                    .filter(e -> "制造".equals(e.getSupplyType()) && !"C0.0".equals(e.getProductClassify()))
                    .map(NewProductStockPointPO::getId).collect(Collectors.toList());
        }
        return needEachProductIds;
    }

    private String getMaterialBuyerIdsSafely() {
        try {
            List<CollectionValueVO> materialBuyers = ipsFeign.getByCollectionCode("MATERIAL_BUYER");
            if (CollectionUtils.isEmpty(materialBuyers)) {
                log.warn("MATERIAL_BUYER 字典信息为空");
                return "";
            }

            List<String> userNames = materialBuyers.stream()
                    .map(CollectionValueVO::getCollectionValue)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(userNames)) {
                log.warn("MATERIAL_BUYER 字典值为空");
                return "";
            }

            String scenario = ipsNewFeign.getDefaultScenario(
                    RzzSystemModuleEnum.IPS.getCode(),
                    TenantCodeEnum.FYQB.getCode()
            ).getData();

            List<User> users = ipsNewFeign.selectUserByParams(scenario, ImmutableMap.of("userNames", userNames));
            if (CollectionUtils.isEmpty(users)) {
                log.warn("找不到材料采购员");
                return "";
            }

            return users.stream().map(User::getId).collect(Collectors.joining(","));
        } catch (Exception e) {
            log.error("获取材料采购员信息失败", e);
            return "";
        }
    }

    @Override
    public Map<String, NewProductStockPointVO> selectMaxEopMinSop(Map<String, Object> params) {
        return newProductStockPointDao.selectMaxEopMinSop(params)
                .stream().collect(Collectors.toMap(NewProductStockPointVO::getVehicleModelCode, e -> e, (v1, v2) -> v1));
    }

    @Override
    public List<LabelValue<String>> selectDropDownByVehicleModelCode(String vehicleModelCode) {
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointPO> productList = newProductStockPointDao.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(),
                "vehicleModelCode", vehicleModelCode,
                "stockPointCode", rangeData));
        if (CollectionUtils.isNotEmpty(productList)) {
            return productList.stream()
                    .map(x -> new LabelValue<>(x.getProductCode() + "(" + x.getProductName() + ")", x.getProductCode()))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<ProductMassProductionVO> selectProductMassProduction(List<String> productCodes) {
        //1.获取物料属性信息，默认获取销售组织的
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointPO> productInfoList = newProductStockPointDao.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(), "stockPointCode", rangeData, "productCodes", productCodes));
        Map<String, NewProductStockPointPO> productInfoMap = productInfoList.stream()
                .collect(Collectors.toMap(NewProductStockPointPO::getProductCode, e -> e, (v1, v2) -> v1));
        //2.获取包装方式，包装规格
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        List<ProductBoxRelationVO> productBoxRelationVOS = productBoxRelationService.selectVOByProductCodeList(productCodes);
        productBoxRelationVOS = productBoxRelationVOS.stream()
                .filter(e -> YesOrNoEnum.YES.getCode().equals(e.getEnabled())).collect(Collectors.toList());
        Map<String, List<ProductBoxRelationVO>> productBoxRelationMap = productBoxRelationVOS.stream()
                .collect(Collectors.groupingBy(ProductBoxRelationVO::getProductCode));
        //3.获取箱体信息的长款高
        List<String> boxCodes = productBoxRelationVOS.stream().filter(e -> StringUtils.isNotEmpty(e.getBoxCode()))
                .distinct().map(ProductBoxRelationVO::getBoxCode).collect(Collectors.toList());
        Map<String, BoxInfoVO> boxInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(boxCodes)) {
            List<BoxInfoVO> boxInfoList = boxInfoService.selectByParams(ImmutableMap.of(
                    "enabled", YesOrNoEnum.YES.getCode(), "boxCodes", boxCodes));
            boxInfoMap = boxInfoList.stream()
                    .collect(Collectors.toMap(BoxInfoVO::getBoxCode, e -> e, (v1, v2) -> v1));
        }
        for (ProductBoxRelationVO productBoxRelationVO : productBoxRelationVOS) {
            String boxCode = productBoxRelationVO.getBoxCode();
            if (StringUtils.isNotEmpty(boxCode) && boxInfoMap.containsKey(boxCode)) {
                BoxInfoVO boxInfoVO = boxInfoMap.get(boxCode);
                productBoxRelationVO.setBoxHeight(boxInfoVO.getBoxHeight());
                productBoxRelationVO.setBoxWidth(boxInfoVO.getBoxWidth());
                productBoxRelationVO.setBoxLength(boxInfoVO.getBoxLength());
            }
        }

        //4.获取零件号
        List<PartRelationMapVO> partRelationMapList = dfpFeign.selectPartRelationMapByParams(scenario.getData(), ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(), "productCodeList", productCodes));
        Map<String, List<PartRelationMapVO>> partRelationMaps = partRelationMapList.stream().collect(Collectors.groupingBy(PartRelationMapVO::getProductCode));

        List<ProductMassProductionVO> resultList = new ArrayList<>();
        for (String productCode : productCodes) {
            ProductMassProductionVO pmp = new ProductMassProductionVO();
            pmp.setProductCode(productCode);
            ProductMassProductionDetailVO detailInfo = new ProductMassProductionDetailVO();
            NewProductStockPointPO productInfo = productInfoMap.get(productCode);
            if (productInfo == null) {
                throw new BusinessException("未获取到物料【 " + productCode + "】数据信息");
            }
            detailInfo.setProductName(productInfo.getProductName());
            detailInfo.setLoadingPositionSub(productInfo.getLoadingPositionSub());
            detailInfo.setProductSop(productInfo.getProductSop());
            detailInfo.setProductEop(productInfo.getProductEop());
            //维护包装方式，包装箱规格
            List<ProductBoxRelationVO> pbrList = productBoxRelationMap.get(productCode);
            if (CollectionUtils.isEmpty(pbrList)) {
                throw new BusinessException("产品 " + productCode + " 未维护产品与成品箱关系");
            }
            pbrList = pbrList.stream().filter(e -> e.getPriority() != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pbrList)) {
                throw new BusinessException("产品 " + productCode + " 未维护产品与成品箱关系优先级");
            }
            pbrList = pbrList.stream().filter(e -> e.getStandardLoad() != null).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pbrList)) {
                throw new BusinessException("产品 " + productCode + " 未维护产品与成品箱关系标准装载量");
            }
            pbrList.sort(Comparator.comparing(ProductBoxRelationVO::getPriority));
            detailInfo.setBoxTypes(pbrList);
            //维护零件号
            List<PartRelationMapVO> partRelationList = partRelationMaps.get(productCode);
            if (CollectionUtils.isNotEmpty(partRelationList)) {
                partRelationList.sort(Comparator.comparing(PartRelationMapVO::getModifyTime).reversed());
                List<LabelValue<String>> partNumbers = Lists.newArrayList();
                partRelationList.forEach(x -> {
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setLabel(x.getPartNumber());
                    labelValue.setValue(x.getPartNumber());
                    partNumbers.add(labelValue);
                });
                detailInfo.setPartNumbers(partNumbers);
            }
            pmp.setDetailInfo(detailInfo);
            resultList.add(pmp);
        }
        return resultList;
    }

    @Override
    public List<String> selectDistinctProductCodesByParams(Map<String, Object> params) {
        return newProductStockPointDao.selectDistinctProductCodesByParams(params);
    }

    @Override
    public void updateOrderPlanner(List<String> productCodes, String orderPlanner) {
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointPO> productInfoList = newProductStockPointDao.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(), "stockPointCode", rangeData, "productCodes", productCodes));
        if (CollectionUtils.isNotEmpty(productInfoList)) {
            productInfoList.forEach(e -> {
                e.setOrderPlanner(orderPlanner);
                e.setInventoryItemStatusCode("Inactive");
            });
            newProductStockPointDao.updateBatch(productInfoList);
        }
    }

}
