package com.yhl.scp.mds.newproduct.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldChangeTime;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointBaseVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <code>ProductStockPointDao</code>
 * <p>
 * 物品DAO
 * </p>
 *
 * @version 1.0
 * @since 2024-07-30 16:02:03
 */
public interface NewProductStockPointDao extends BaseDao<NewProductStockPointPO, NewProductStockPointVO> {

    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> removeVersionDTOS);

    /**
     * 根据物品编码批量查询
     *
     * @param codeList
     * @return
     */
    List<NewProductStockPointPO> selectByProductCode(@Param("codeList") List<String> codeList);

    /**
     * 根据车型编码获取物品库存点
     *
     * @param vehicleModelCodeList
     * @return
     */
    List<NewProductStockPointPO> selectByVehicleModelCode(@Param("vehicleModelCodeList") List<String> vehicleModelCodeList);

    List<NewProductStockPointPO> selectByStockPointCodes(@Param("stockPointCodes") List<String> stockPointCodeList);

    List<String> selectFieldsByField(@Param("field") String field);

    List<NewProductStockPointPO> selectProductCodeLike(@Param("productCode") String productCode,
                                                       @Param("stockPointCode") String stockPointCode, @Param("productTypes") List<String> productTypes);

    List<NewProductStockPointPO> selectProductCodeLikeByVehicleByStock(@Param("productCode") String productCode,
                                                                       @Param("vehicleModelCodeList") List<String> vehicleModelCodeList,
                                                                       @Param("stockPointCode") String stockPointCode);

    List<NewProductStockPointPO> selectByUserId(@Param("userId") String userId, @Param("list") List<String> list);

    List<NewProductStockPointVO> selectTwoData();

    List<String> selectLoadingPositionSub();

    /**
     * 获取库存点物品所有列
     *
     * @return column
     */
    @Select("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'mds_product_stock_point'")
    List<String> getAllColumns();


    List<NewProductStockPointPO> selectProductListByParamOnDynamicColumns(@Param("dynamicColumn") String dynamicColumn, @Param("params") Map<String, Object> params);

    List<NewProductStockPointPO> selectProductTypeLike(String productType);

    List<NewProductStockPointPO> selectSupplyTypeLike(String supplyType);


    List<NewProductStockPointPO> selectProductColorLike(String productColor);


    List<NewProductStockPointPO> selectByLoadPosition(@Param("accessPositionList") List<String> accessPositionList);

    List<NewProductStockPointVO> selectProduct4LoadingDemandSubmission();

    List<String> selectVehiclesByProductCode(@Param("codeList") List<String> codeList);

    int doLogicDeleteBatch(@Param("deleteProductList") List<RemoveVersionDTO> deleteProductList);

    List<NewProductStockPointPO> selectByProductCodeStockPointCode(@Param("list") List<MesMoldChangeTime> mesMoldChangeTimeList);

    List<NewProductStockPointPO> selectByStockCodeAndProductCode(@Param("list") Collection<ProductSubstitutionRelationshipVO> list);

    List<NewProductStockPointPO> selectSrmByStockCodeAndProductCode(@Param("list") Collection<SrmSupplierPurchase> list);

    List<NewProductStockPointBaseVO> selectBaseInfoByStockPointCodes(@Param("stockPointCodes") List<String> stockPointCodes);

    List<String> selectProductCodeByParams(@Param("params") Map<String, Object> params);

    List<String> selectYpProductCodes();

    List<NewProductStockPointVO> selectYpFactoryCode();

    List<NewProductStockPointVO> selectMaxEopMinSop(@Param("params") Map<String, Object> params);

    List<String> selectDistinctProductCodesByParams(@Param("params") Map<String, Object> params);
}
