package com.yhl.scp.mds.routing.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mds.routing.dto.NewRoutingDTO;
import com.yhl.scp.mds.routing.service.NewRoutingService;
import com.yhl.scp.mds.routing.vo.NewRoutingVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>NewRoutingController</code>
 * <p>
 * 新-生产路径控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 09:26:43
 */
@Slf4j
@Api(tags = "新-生产路径控制器")
@RestController
@RequestMapping("newRouting")
public class NewRoutingController extends BaseController {

    @Resource
    private NewRoutingService newRoutingService;


    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<NewRoutingVO>> page() {
        List<NewRoutingVO> routingList = newRoutingService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<NewRoutingVO> pageInfo = new PageInfo<>(routingList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody NewRoutingDTO routingDTO) {
        return newRoutingService.doCreate(routingDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody NewRoutingDTO routingDTO) {
        return newRoutingService.doUpdate(routingDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
    	newRoutingService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<NewRoutingVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, newRoutingService.selectByPrimaryKey(id));
    }

    @ApiOperation("下载错误数据")
    @GetMapping(value = "downloadErrorData")
    public void downloadErrorData(HttpServletResponse response) {
        newRoutingService.downloadErrorData(response);
    }
}
