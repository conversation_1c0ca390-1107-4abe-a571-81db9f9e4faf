package com.yhl.scp.mds.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.curingTime.service.MdsCuringTimeService;
import com.yhl.scp.mds.routing.service.MdsOpYieldService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>CuringTimeJob</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-24 15:57:42
 */
@Component
@Slf4j
public class CuringTimeJob {
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private MdsCuringTimeService mdsCuringTimeService;
    @XxlJob("curingTimeJob")
    private ReturnT<String> curingTimeJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MDS模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的固化时间job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            mdsCuringTimeService.handleSyncCuringTime(scenario.getTenantId());
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的固化时间job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
