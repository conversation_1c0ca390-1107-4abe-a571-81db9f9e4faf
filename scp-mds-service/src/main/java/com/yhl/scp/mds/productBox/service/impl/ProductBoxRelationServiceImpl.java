package com.yhl.scp.mds.productBox.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesProductBoxRelation;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.box.infrastructure.dao.BoxInfoDao;
import com.yhl.scp.mds.box.infrastructure.po.BoxInfoPO;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.productBox.convertor.ProductBoxRelationConvertor;
import com.yhl.scp.mds.productBox.domain.entity.ProductBoxRelationDO;
import com.yhl.scp.mds.productBox.domain.service.ProductBoxRelationDomainService;
import com.yhl.scp.mds.productBox.dto.ProductBoxRelationDTO;
import com.yhl.scp.mds.productBox.infrastructure.dao.ProductBoxRelationDao;
import com.yhl.scp.mds.productBox.infrastructure.po.ProductBoxRelationPO;
import com.yhl.scp.mds.productBox.service.ProductBoxRelationService;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ProductBoxRelationServiceImpl</code>
 * <p>
 * 产品与成品箱关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 21:03:48
 */
@Slf4j
@Service
public class ProductBoxRelationServiceImpl extends AbstractService implements ProductBoxRelationService {

    @Resource
    private ProductBoxRelationDao productBoxRelationDao;

    @Resource
    private ProductBoxRelationDomainService productBoxRelationDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private BoxInfoDao boxInfoDao;


    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(ProductBoxRelationDTO productBoxRelationDTO) {
        // 0.数据转换
        ProductBoxRelationDO productBoxRelationDO = ProductBoxRelationConvertor.INSTANCE.dto2Do(productBoxRelationDTO);
        ProductBoxRelationPO productBoxRelationPO = ProductBoxRelationConvertor.INSTANCE.dto2Po(productBoxRelationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        productBoxRelationDomainService.validation(productBoxRelationDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(productBoxRelationPO);
        productBoxRelationDao.insert(productBoxRelationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(ProductBoxRelationDTO productBoxRelationDTO) {
        // 0.数据转换
        ProductBoxRelationDO productBoxRelationDO = ProductBoxRelationConvertor.INSTANCE.dto2Do(productBoxRelationDTO);
        ProductBoxRelationPO productBoxRelationPO = ProductBoxRelationConvertor.INSTANCE.dto2Po(productBoxRelationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        productBoxRelationDomainService.validation(productBoxRelationDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(productBoxRelationPO);
        productBoxRelationDao.update(productBoxRelationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProductBoxRelationDTO> list) {
        List<ProductBoxRelationPO> newList = ProductBoxRelationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        productBoxRelationDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ProductBoxRelationDTO> list) {
        List<ProductBoxRelationPO> newList = ProductBoxRelationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        productBoxRelationDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return productBoxRelationDao.deleteBatch(idList);
        }
        return productBoxRelationDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProductBoxRelationVO selectByPrimaryKey(String id) {
        return productBoxRelationDao.selectVODetailById(id);
    }

    @Override
    @Expression(value = "PRODUCT_BOX_RELATION")
    public List<ProductBoxRelationVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PRODUCT_BOX_RELATION")
    public List<ProductBoxRelationVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProductBoxRelationVO> dataList = productBoxRelationDao.selectByCondition(sortParam, queryCriteriaParam);
        ProductBoxRelationServiceImpl target = springBeanUtils.getBean(ProductBoxRelationServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProductBoxRelationVO> selectByParams(Map<String, Object> params) {
        List<ProductBoxRelationPO> list = productBoxRelationDao.selectByParams(params);
        return ProductBoxRelationConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ProductBoxRelationVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS) {
        if (CollectionUtils.isEmpty(removeVersionDTOS)) {
            return 0;
        }
        productBoxRelationDomainService.checkDelete(removeVersionDTOS);
        return productBoxRelationDao.deleteBatchVersion(removeVersionDTOS);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PRODUCT_BOX_RELATION.getCode();
    }

    @Override
    public List<ProductBoxRelationVO> invocation(List<ProductBoxRelationVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<ProductBoxRelationVO> selectVOByProductCodeList(List<String> productCodeList) {
        return productBoxRelationDao.selectVOByProductCodeList(productCodeList);
    }

    /**
     * 查询箱体类型
     * @return
     */
    @Override
    public List<LabelValue<String>> queryBoxType() {
        List<ProductBoxRelationVO> productBoxRelationVOS = this.selectAll();
        return productBoxRelationVOS.stream()
                                    .map(x -> new LabelValue<>(x.getBoxType(), x.getBoxType()))
                                    .collect(Collectors.toList());

    }

    @Override
    public BaseResponse<Void> syncProductBoxRelation(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("reqCode", "FY_PKN_CTN_TYPE_REL_FOR_BPIM");
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.PRODUCT_BOX_RELATION.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleProductBoxRelation(List<MesProductBoxRelation> mesProductBoxRelationList) {
        if (CollectionUtils.isEmpty(mesProductBoxRelationList)) {
            return BaseResponse.success();
        }
        List<ProductBoxRelationDTO> insertDtoS = new ArrayList<>();
        List<ProductBoxRelationDTO> updateDtoS = new ArrayList<>();
        Set<String> boxCodes =
                mesProductBoxRelationList.stream().map(MesProductBoxRelation::getTypeCode).collect(Collectors.toSet());
        HashMap<String, Object> boxInfoMap = MapUtil.newHashMap(3);
        boxInfoMap.put("boxCodes", boxCodes);
        List<BoxInfoPO> oldBoxInfoPos = boxInfoDao.selectByParams(boxInfoMap);
        Map<String, BoxInfoPO> oldBoxInfoPosMap = oldBoxInfoPos.stream().collect(
                Collectors.toMap(BoxInfoPO::getBoxCode, Function.identity(), (v1, v2) -> v1));
        Set<String> kids =
                mesProductBoxRelationList.stream().map(MesProductBoxRelation::getKid).collect(Collectors.toSet());

        HashMap<String, Object> map = MapUtil.newHashMap(3);
        map.put("kids", kids);
        List<ProductBoxRelationPO> oldPos = productBoxRelationDao.selectByParams(map);
        Map<String, ProductBoxRelationPO> oldPosMap = oldPos.stream().collect(
                Collectors.toMap(ProductBoxRelationPO::getKid, Function.identity(), (v1, v2) -> v1));
        List<String> productStockPointId =
                mesProductBoxRelationList.stream().map(MesProductBoxRelation::getItemId).collect(Collectors.toList());
        HashMap<String, Object> mapProductStockPointId = MapUtil.newHashMap(3);
        mapProductStockPointId.put("productStockPointIdList", productStockPointId);
        List<ProductBoxRelationPO> oldProductStockPointIdPos = productBoxRelationDao.selectByParams(mapProductStockPointId);
        Map<String, ProductBoxRelationPO> boxIdMap = oldProductStockPointIdPos.stream()
                .collect(Collectors.toMap(
                        ProductBoxRelationPO::getBoxId,
                        Function.identity(),
                        BinaryOperator.maxBy(
                                Comparator.comparing(
                                        ProductBoxRelationPO::getPriority,
                                        Comparator.nullsLast(Comparator.<Integer>naturalOrder())
                                )
                        )
                ));

        for (MesProductBoxRelation mesProductBoxRelation : mesProductBoxRelationList) {

            if (!oldBoxInfoPosMap.containsKey(mesProductBoxRelation.getTypeCode())) {
                continue;
            }
            ProductBoxRelationDTO dto = new ProductBoxRelationDTO();
            String  boxId = oldBoxInfoPosMap.get(mesProductBoxRelation.getTypeCode()).getId();
            if (oldPosMap.containsKey(mesProductBoxRelation.getKid())) {
                ProductBoxRelationPO oldPo = oldPosMap.get(mesProductBoxRelation.getKid());
                BeanUtils.copyProperties(oldPo, dto);
                generateDto(mesProductBoxRelation,  dto,boxId);
                updateDtoS.add(dto);
            } else {
                generateDto(mesProductBoxRelation,  dto,boxId);
                // 获取当前最大 priority（可能来自数据库或之前插入的数据）
                Integer maxPriority = boxIdMap.containsKey(dto.getBoxId())
                        ? boxIdMap.get(dto.getBoxId()).getPriority()
                        : null;
                // 设置新 priority：null -> 1，否则 +1
                Integer newPriority = maxPriority == null ? 1 : maxPriority + 1;
                dto.setPriority(newPriority);
                // 更新 boxIdMap 缓存，确保后续相同 boxId 的数据能基于最新值递增
                ProductBoxRelationPO tempPo = new ProductBoxRelationPO();
                tempPo.setBoxId(dto.getBoxId());
                tempPo.setPriority(newPriority);
                boxIdMap.put(dto.getBoxId(), tempPo);

                insertDtoS.add(dto);
            }

        }
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            doCreateBatch(insertDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateDtoS)) {
            doUpdateBatch(updateDtoS);
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public List<ProductBoxRelationVO> selectProductBoxRelationByProductStockPointId(List<String> productStockPointIdList) {
        return productBoxRelationDao.selectProBoxRelationByProStockPointId(productStockPointIdList);
    }
    private void generateDto(MesProductBoxRelation mesProductBoxRelation,
                             ProductBoxRelationDTO dto, String boxId) {
        String enabled = "Y".equals(mesProductBoxRelation.getEnableFlag()) ? YesOrNoEnum.YES.getCode() :
                YesOrNoEnum.NO.getCode();
        dto.setBoxId(boxId);
        dto.setKid(mesProductBoxRelation.getKid());
        dto.setLastUpdateTime(mesProductBoxRelation.getLastUpdateDate());
        dto.setProductStockPointId(mesProductBoxRelation.getItemId());
        dto.setBoxType(mesProductBoxRelation.getRemark());
        dto.setEnabled(enabled);
        dto.setPlanArea(mesProductBoxRelation.getScheduleRegionCode());
       if(StringUtils.isNotEmpty(mesProductBoxRelation.getStandardLoadQty())){
           dto.setStandardLoad(Integer.valueOf(mesProductBoxRelation.getStandardLoadQty()));
       }
    }
}
