package com.yhl.scp.mds.stock.controller;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.stock.dto.NewStockPointDTO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Slf4j
@Api(tags = "新-库存点控制器")
@RestController
@RequestMapping("newStockPoint")
public class NewStockPointController extends BaseController {

    @Resource
    private NewStockPointService newStockPointService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<NewStockPointVO>> page() {
        List<NewStockPointVO> stockPointList = newStockPointService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<NewStockPointVO> pageInfo = new PageInfo<>(stockPointList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "销售组织展示")
    @GetMapping(value = "selectSaleOrganizeByOrganizeType")
    public BaseResponse<List<NewStockPointVO>> selectSaleOrgaByOrganizeType() {
        List<NewStockPointVO> StockPointVOSByOrganizeType = newStockPointService.selectSaleOrgaByOrganizeType();
        return BaseResponse.success(StockPointVOSByOrganizeType);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody NewStockPointDTO newStockPointDTO) {
        if ("BPIM".equals(newStockPointDTO.getInterfaceSource())) {
            int randomNumber = 10000 + (int) (Math.random() * 90000);
            newStockPointDTO.setOrganizeId("9" + randomNumber);
        }
        return newStockPointService.doCreate(newStockPointDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody NewStockPointDTO newStockPointDTO) {
        return newStockPointService.doUpdate(newStockPointDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<RemoveVersionDTO> removeVersionDTOS) {
        newStockPointService.deleteBatchVersion(removeVersionDTOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<NewStockPointVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, newStockPointService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "下拉查询（全部库存点）")
    @GetMapping(value = "dropDownAll")
    public BaseResponse<List<NewStockPointVO>> dropDownAll() {
        List<NewStockPointVO> newStockPointVOS = newStockPointService.selectAll();
        return BaseResponse.success(newStockPointVOS);
    }

    @ApiOperation(value = "下拉查询（有效库存点）")
    @GetMapping(value = "dropDownEnable")
    public BaseResponse<List<NewStockPointVO>> dropDownEnable() {
        Map<String, Object> enableMap = MapUtil.newHashMap();
        enableMap.put("enabled", YesOrNoEnum.YES.getCode());
        List<NewStockPointVO> newStockPointVOS = newStockPointService.selectByParams(enableMap);
        return BaseResponse.success(newStockPointVOS);
    }

    @ApiOperation(value = "同步库存点")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncStockPoints() {
        return newStockPointService.syncStockPoints(SystemHolder.getTenantCode());
    }

    @ApiOperation(value = "通过销售组织查询库存点信息")
    @GetMapping(value = "selectByOrganizeTypes")
    public BaseResponse<List<NewStockPointVO>> selectByOrganizeTypes(
            @RequestParam(value = "organizeTypes", required = false) List<String> organizeTypes) {
        List<NewStockPointVO> StockPointVOSByOrganizeType = newStockPointService.selectByOrganizeTypes(organizeTypes);
        return BaseResponse.success(StockPointVOSByOrganizeType);
    }

    @ApiOperation(value = "免堆期新增修改下拉查询")
    @GetMapping(value = "dropDownForProFreeStorage")
    public BaseResponse<List<NewStockPointVO>> dropDownForProFreeStorage() {
        Map<String, Object> enableMap = MapUtil.newHashMap();
        enableMap.put("enabled", YesOrNoEnum.YES.getCode());
        enableMap.put("stockPointType", StockPointTypeEnum.MT.getCode());
        List<NewStockPointVO> newStockPointVOS = newStockPointService.selectByParams(enableMap);
        return BaseResponse.success(newStockPointVOS);
    }

    @ApiOperation(value = "库存点下拉")
    @GetMapping(value = "pointDown")
    public BaseResponse<List<LabelValue<String>>> pointDown(@RequestParam(value = "stockPointCode", required = false) String stockPointCode) {
        return BaseResponse.success(newStockPointService.pointDown(stockPointCode));
    }

    @ApiOperation(value = "库存点下拉")
    @GetMapping(value = "getInterfaceFlag")
    public BaseResponse<List<LabelValue<String>>> getInterfaceFlag() {
        return BaseResponse.success(newStockPointService.getInterfaceFlag());
    }

    @ApiOperation(value = "库存点名称下拉")
    @GetMapping(value = "stockPointNameDropDown")
    public BaseResponse<List<LabelValue<String>>> stockPointNameDropDown(@RequestParam(value = "stockPointName", required = false) String stockPointName) {
        return BaseResponse.success(newStockPointService.stockPointNameDropDown(stockPointName));
    }

    @ApiOperation(value = "模板导出")
    @GetMapping(value = "exportTemplate")
    public void exportTemplate(HttpServletResponse response) {
        newStockPointService.exportTemplate(response);
    }

    @ApiOperation(value = "组织库存点下拉-仅国内中转库")
    @GetMapping(value = "stockPointDownOnly")
    public BaseResponse<List<LabelValue<String>>> stockPointDownOnly() {
        return BaseResponse.success(newStockPointService.stockPointDownOnly());
    }
}
