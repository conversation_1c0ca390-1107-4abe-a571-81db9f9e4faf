package com.yhl.scp.mds.productroutestepbase.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MdsProductStockPointBaseDO</code>
 * <p>
 * 产品工艺基础数据DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 11:29:01
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MdsProductStockPointBaseDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -36886976035564120L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 库存点ID
     */
    private String stockPointId;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 库存点名称
     */
    private String stockPointName;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 编码名称
     */
    private String productName;
    /**
     * 长
     */
    private BigDecimal productLength;
    /**
     * 宽
     */
    private BigDecimal productWidth;
    /**
     * 厚
     */
    private BigDecimal productThickness;
    /**
     * 装车位置
     */
    private String loadPosition;
    /**
     * 颜色
     */
    private String productColor;
    /**
     * 玻璃颜色
     */
    private String glassColor;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 难度等级
     */
    private String difficultyLevel;
    /**
     * 风栅类型
     */
    private String gridType;
    /**
     * 生产模式
     */
    private String productionModel;
    /**
     * 钢化类型
     */
    private String tougheningType;
    /**
     * 膜系
     */
    private String membraneSystem;
    /**
     * HUD
     */
    private String hud;
    /**
     * 夹丝类型
     */
    private String clampType;
    /**
     * 印边
     */
    private String sealEdge;
    /**
     * 面积
     */
    private String productArea;
    /**
     * 曲率
     */
    private String curvature;
    /**
     * 编码目录号
     */
    private String dirNum;
    /**
     * 工艺类型
     */
    private String itemType;
    /**
     * 除膜工艺
     */
    private String attr1;
    /**
     * 物料标识
     */
    private String itemFlag;
    /**
     * 生产线组
     */
    private String lineGroup;
    /**
     * 工装大类
     */
    private String standardResourceId;
    /**
     * 零件号
     */
    private String partNum;
}
