<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.curingTime.infrastructure.dao.MdsFinishedProductDeliveryDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.curingTime.infrastructure.po.MdsFinishedProductDeliveryPO">
        <!--@Table mds_finished_product_delivery-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="company_code" jdbcType="VARCHAR" property="companyCode"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="warehouse_code" jdbcType="VARCHAR" property="warehouseCode"/>
        <result column="locator_code" jdbcType="VARCHAR" property="locatorCode"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="deadline_flag" jdbcType="VARCHAR" property="deadlineFlag"/>
        <result column="storage_time" jdbcType="VARCHAR" property="storageTime"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mds.curingTime.vo.MdsFinishedProductDeliveryVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,product_code,company_code,stock_point_code,last_update_date,warehouse_code,locator_code,order_no,deadline_flag,storage_time,create_user,creation_date,kid,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.companyCode != null and params.companyCode != ''">
                and company_code = #{params.companyCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.warehouseCode != null and params.warehouseCode != ''">
                and warehouse_code = #{params.warehouseCode,jdbcType=VARCHAR}
            </if>
            <if test="params.locatorCode != null and params.locatorCode != ''">
                and locator_code = #{params.locatorCode,jdbcType=VARCHAR}
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                and order_no = #{params.orderNo,jdbcType=VARCHAR}
            </if>
            <if test="params.deadlineFlag != null and params.deadlineFlag != ''">
                and deadline_flag = #{params.deadlineFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.storageTime != null and params.storageTime != ''">
                and storage_time = #{params.storageTime,jdbcType=VARCHAR}
            </if>
            <if test="params.createUser != null and params.createUser != ''">
                and create_user = #{params.createUser,jdbcType=VARCHAR}
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.creation_date != null">
                and creation_date = #{params.creationDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_finished_product_delivery
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_finished_product_delivery
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mds_finished_product_delivery
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_finished_product_delivery
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.curingTime.infrastructure.po.MdsFinishedProductDeliveryPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_finished_product_delivery(
        id,
        product_code,
        company_code,
        stock_point_code,
        last_update_date,
        warehouse_code,
        locator_code,
        order_no,
        deadline_flag,
        storage_time,
        create_user,
        creation_date,
        kid,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{companyCode,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{warehouseCode,jdbcType=VARCHAR},
        #{locatorCode,jdbcType=VARCHAR},
        #{orderNo,jdbcType=VARCHAR},
        #{deadlineFlag,jdbcType=VARCHAR},
        #{storageTime,jdbcType=VARCHAR},
        #{createUser,jdbcType=VARCHAR},
        #{creationDate,jdbcType=TIMESTAMP},
        #{kid,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mds.curingTime.infrastructure.po.MdsFinishedProductDeliveryPO">
        insert into mds_finished_product_delivery(id,
                                                  product_code,
                                                  company_code,
                                                  stock_point_code,
                                                  last_update_date,
                                                  warehouse_code,
                                                  locator_code,
                                                  order_no,
                                                  deadline_flag,
                                                  storage_time,
                                                  create_user,
                                                  creation_date,
                                                  kid,
                                                  remark,
                                                  enabled,
                                                  creator,
                                                  create_time,
                                                  modifier,
                                                  modify_time,
                                                  version_value)
        values (#{id,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{companyCode,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{lastUpdateDate,jdbcType=TIMESTAMP},
                #{warehouseCode,jdbcType=VARCHAR},
                #{locatorCode,jdbcType=VARCHAR},
                #{orderNo,jdbcType=VARCHAR},
                #{deadlineFlag,jdbcType=VARCHAR},
                #{storageTime,jdbcType=VARCHAR},
                #{createUser,jdbcType=VARCHAR},
                #{creationDate,jdbcType=TIMESTAMP},
                #{kid,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_finished_product_delivery(
        id,
        product_code,
        company_code,
        stock_point_code,
        last_update_date,
        warehouse_code,
        locator_code,
        order_no,
        deadline_flag,
        storage_time,
        create_user,
        creation_date,
        kid,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.companyCode,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.warehouseCode,jdbcType=VARCHAR},
            #{entity.locatorCode,jdbcType=VARCHAR},
            #{entity.orderNo,jdbcType=VARCHAR},
            #{entity.deadlineFlag,jdbcType=VARCHAR},
            #{entity.storageTime,jdbcType=VARCHAR},
            #{entity.createUser,jdbcType=VARCHAR},
            #{entity.creationDate,jdbcType=TIMESTAMP},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_finished_product_delivery(
        id,
        product_code,
        company_code,
        stock_point_code,
        last_update_date,
        warehouse_code,
        locator_code,
        order_no,
        deadline_flag,
        storage_time,
        create_user,
        creation_date,
        kid,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.companyCode,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.warehouseCode,jdbcType=VARCHAR},
            #{entity.locatorCode,jdbcType=VARCHAR},
            #{entity.orderNo,jdbcType=VARCHAR},
            #{entity.deadlineFlag,jdbcType=VARCHAR},
            #{entity.storageTime,jdbcType=VARCHAR},
            #{entity.createUser,jdbcType=VARCHAR},
            #{entity.creationDate,jdbcType=TIMESTAMP},
            #{entity.kid,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.curingTime.infrastructure.po.MdsFinishedProductDeliveryPO">
        update mds_finished_product_delivery
        set product_code     = #{productCode,jdbcType=VARCHAR},
            company_code     = #{companyCode,jdbcType=VARCHAR},
            stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
            last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
            warehouse_code   = #{warehouseCode,jdbcType=VARCHAR},
            locator_code     = #{locatorCode,jdbcType=VARCHAR},
            order_no         = #{orderNo,jdbcType=VARCHAR},
            deadline_flag    = #{deadlineFlag,jdbcType=VARCHAR},
            storage_time     = #{storageTime,jdbcType=VARCHAR},
            create_user      = #{createUser,jdbcType=VARCHAR},
            creation_date    = #{creationDate,jdbcType=TIMESTAMP},
            kid              = #{kid,jdbcType=VARCHAR},
            remark           = #{remark,jdbcType=VARCHAR},
            enabled          = #{enabled,jdbcType=VARCHAR},
            modifier         = #{modifier,jdbcType=VARCHAR},
            modify_time      = #{modifyTime,jdbcType=TIMESTAMP},
            version_value    = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mds.curingTime.infrastructure.po.MdsFinishedProductDeliveryPO">
        update mds_finished_product_delivery
        <set>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.companyCode != null and item.companyCode != ''">
                company_code = #{item.companyCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.warehouseCode != null and item.warehouseCode != ''">
                warehouse_code = #{item.warehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="item.locatorCode != null and item.locatorCode != ''">
                locator_code = #{item.locatorCode,jdbcType=VARCHAR},
            </if>
            <if test="item.orderNo != null and item.orderNo != ''">
                order_no = #{item.orderNo,jdbcType=VARCHAR},
            </if>
            <if test="item.deadlineFlag != null and item.deadlineFlag != ''">
                deadline_flag = #{item.deadlineFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.storageTime != null and item.storageTime != ''">
                storage_time = #{item.storageTime,jdbcType=VARCHAR},
            </if>
            <if test="item.createUser != null and item.createUser != ''">
                create_user = #{item.createUser,jdbcType=VARCHAR},
            </if>
            <if test="item.creationDate != null">
                creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_finished_product_delivery
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="company_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.companyCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="warehouse_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.warehouseCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="locator_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.locatorCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="deadline_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deadlineFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="storage_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.storageTime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_user = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createUser,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creation_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creationDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_finished_product_delivery
            <set>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.companyCode != null and item.companyCode != ''">
                    company_code = #{item.companyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.warehouseCode != null and item.warehouseCode != ''">
                    warehouse_code = #{item.warehouseCode,jdbcType=VARCHAR},
                </if>
                <if test="item.locatorCode != null and item.locatorCode != ''">
                    locator_code = #{item.locatorCode,jdbcType=VARCHAR},
                </if>
                <if test="item.orderNo != null and item.orderNo != ''">
                    order_no = #{item.orderNo,jdbcType=VARCHAR},
                </if>
                <if test="item.deadlineFlag != null and item.deadlineFlag != ''">
                    deadline_flag = #{item.deadlineFlag,jdbcType=VARCHAR},
                </if>
                <if test="item.storageTime != null and item.storageTime != ''">
                    storage_time = #{item.storageTime,jdbcType=VARCHAR},
                </if>
                <if test="item.createUser != null and item.createUser != ''">
                    create_user = #{item.createUser,jdbcType=VARCHAR},
                </if>
                <if test="item.creationDate != null">
                    creation_date = #{item.creationDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.kid != null and item.kid != ''">
                    kid = #{item.kid,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_finished_product_delivery
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_finished_product_delivery where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
