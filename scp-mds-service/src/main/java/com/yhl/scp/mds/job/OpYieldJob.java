package com.yhl.scp.mds.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.routing.service.MdsOpYieldService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OpYieldJob</code>
 * <p>
 * MES工序成品率接口定时任务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-13 16:57:07
 */
@Component
@Slf4j
public class OpYieldJob {

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private MdsOpYieldService mdsOpYieldService;

    @XxlJob("opYieldJob")
    private ReturnT<String> opYieldJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在MDS模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理scenario：{}下的工序成品率job", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            mdsOpYieldService.handleSyncOpYield(scenario);
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步工序成品率job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }

}
