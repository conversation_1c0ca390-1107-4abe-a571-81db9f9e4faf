package com.yhl.scp.mds.productroutestepbase.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpProductStockPointBase;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.service.MdsProductBomVersionService;
import com.yhl.scp.mds.candidateResource.service.ProductionCandidateResourceService;
import com.yhl.scp.mds.candidateResource.vo.ProductionCandidateResourceVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.mold.service.MoldToolingService;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.service.ProductCandidateResourceTimeService;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.productroutestepbase.convertor.MdsProductStockPointBaseConvertor;
import com.yhl.scp.mds.productroutestepbase.domain.entity.MdsProductStockPointBaseDO;
import com.yhl.scp.mds.productroutestepbase.domain.service.MdsProductStockPointBaseDomainService;
import com.yhl.scp.mds.productroutestepbase.dto.MdsProductStockPointBaseDTO;
import com.yhl.scp.mds.productroutestepbase.dto.MdsProductStockPointBaseExportDTO;
import com.yhl.scp.mds.productroutestepbase.infrastructure.dao.MdsProductStockPointBaseDao;
import com.yhl.scp.mds.productroutestepbase.infrastructure.po.MdsProductStockPointBasePO;
import com.yhl.scp.mds.productroutestepbase.service.MdsProductStockPointBaseService;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.routing.service.NewRoutingService;
import com.yhl.scp.mds.routing.service.NewRoutingStepInputService;
import com.yhl.scp.mds.routing.service.StandardStepService;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mps.feign.MpsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <code>MdsProductStockPointBaseServiceImpl</code>
 * <p>
 * 产品工艺基础数据应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 11:29:01
 */
@Slf4j
@Service
public class MdsProductStockPointBaseServiceImpl extends AbstractService implements MdsProductStockPointBaseService {

    @Resource
    private MdsProductStockPointBaseDao mdsProductStockPointBaseDao;

    @Resource
    private MdsProductStockPointBaseDomainService mdsProductStockPointBaseDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private MdsProductStockPointBaseService mdsProductStockPointBaseService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewProductStockPointService newProductStockPointService;

    @Resource
    private MdsProductBomVersionService mdsProductBomVersionService;

    @Resource
    private MdsProductBomService mdsProductBomService;

    @Resource
    private ProductCandidateResourceTimeService productCandidateResourceTimeService;

    @Resource
    private ProductionCandidateResourceService productCandidateResourceService;

    @Resource
    private MoldToolingService moldToolingService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewRoutingService newRoutingService;

    @Resource
    private NewRoutingStepInputService newRoutingStepInputService;

    @Resource
    private StandardStepService standardStepService;

    @Resource
    private DfpFeign dfpFeign;
    @Autowired
    private MpsFeign mpsFeign;

    // 接口取过来的数据为空或者为“/”，数据库统一显示为“/”
    private static String normalizeValue(Object value) {
        return Objects.isNull(value) || "/".equals(String.valueOf(value).trim()) ? "/" : String.valueOf(value);
    }

    @Override
    public BaseResponse<Void> doCreate(MdsProductStockPointBaseDTO mdsProductStockPointBaseDTO) {
        // 0.数据转换
        MdsProductStockPointBaseDO mdsProductStockPointBaseDO = MdsProductStockPointBaseConvertor.INSTANCE.dto2Do(mdsProductStockPointBaseDTO);
        MdsProductStockPointBasePO mdsProductStockPointBasePO = MdsProductStockPointBaseConvertor.INSTANCE.dto2Po(mdsProductStockPointBaseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsProductStockPointBaseDomainService.validation(mdsProductStockPointBaseDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(mdsProductStockPointBasePO);
        mdsProductStockPointBaseDao.insert(mdsProductStockPointBasePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MdsProductStockPointBaseDTO mdsProductStockPointBaseDTO) {
        // 0.数据转换
        MdsProductStockPointBaseDO mdsProductStockPointBaseDO = MdsProductStockPointBaseConvertor.INSTANCE.dto2Do(mdsProductStockPointBaseDTO);
        MdsProductStockPointBasePO mdsProductStockPointBasePO = MdsProductStockPointBaseConvertor.INSTANCE.dto2Po(mdsProductStockPointBaseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsProductStockPointBaseDomainService.validation(mdsProductStockPointBaseDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(mdsProductStockPointBasePO);
        mdsProductStockPointBaseDao.update(mdsProductStockPointBasePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MdsProductStockPointBaseDTO> list) {
        List<MdsProductStockPointBasePO> newList = MdsProductStockPointBaseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        mdsProductStockPointBaseDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MdsProductStockPointBaseDTO> list) {
        List<MdsProductStockPointBasePO> newList = MdsProductStockPointBaseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        mdsProductStockPointBaseDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return mdsProductStockPointBaseDao.deleteBatch(idList);
        }
        return mdsProductStockPointBaseDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MdsProductStockPointBaseVO selectByPrimaryKey(String id) {
        MdsProductStockPointBasePO po = mdsProductStockPointBaseDao.selectByPrimaryKey(id);
        return MdsProductStockPointBaseConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "PRODUCT_STOCK_POINT_BASE")
    public List<MdsProductStockPointBaseVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PRODUCT_STOCK_POINT_BASE")
    public List<MdsProductStockPointBaseVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MdsProductStockPointBaseVO> dataList = mdsProductStockPointBaseDao.selectByCondition(sortParam, queryCriteriaParam);
        MdsProductStockPointBaseServiceImpl target = springBeanUtils.getBean(MdsProductStockPointBaseServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MdsProductStockPointBaseVO> selectByParams(Map<String, Object> params) {
        List<MdsProductStockPointBasePO> list = mdsProductStockPointBaseDao.selectByParams(params);
        return MdsProductStockPointBaseConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MdsProductStockPointBaseVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void export(HttpServletResponse response) {
        List<MdsProductStockPointBasePO> productStockPointBasePOS = mdsProductStockPointBaseDao.selectByParams(new HashMap<>(2));
        List<MdsProductStockPointBaseExportDTO> productStockPointBaseExportDTOS = MdsProductStockPointBaseConvertor.INSTANCE.po2ExportDtos(productStockPointBasePOS);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("产品工艺基础数据.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream(), MdsProductStockPointBaseExportDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("产品工艺基础数据")
                    .doWrite(productStockPointBaseExportDTOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PRODUCT_STOCK_POINT_BASE.getCode();
    }

    @Override
    public List<MdsProductStockPointBaseVO> invocation(List<MdsProductStockPointBaseVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> syncProductStockPointBase(String scenario,
                                                        List<ErpProductStockPointBase> productStockPointBases) {
        if (CollectionUtils.isEmpty(productStockPointBases)) {
            return BaseResponse.success();
        }
        DynamicDataSourceContextHolder.setDataSource(scenario);

        List<String> productCodes = productStockPointBases.stream().map(ErpProductStockPointBase::getItemNumber)
                .distinct().collect(Collectors.toList());
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("productCodes", productCodes);
        Map<String, MdsProductStockPointBaseVO> oldPOMap = mdsProductStockPointBaseService.selectByProduct(params)
                .stream().collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode,
                        Function.identity(), (v1, v2) -> v1));

        List<MdsProductStockPointBaseDTO> insertMdsProductStockPointBaseDTOs = Lists.newArrayList();
        List<MdsProductStockPointBaseDTO> updateMdsProductStockPointBaseDTOs = Lists.newArrayList();
        for (ErpProductStockPointBase erpProductStockPointBase : productStockPointBases) {
            MdsProductStockPointBaseDTO mdsProductStockPointBaseDTO = new MdsProductStockPointBaseDTO();
            if (oldPOMap.containsKey(erpProductStockPointBase.getItemNumber())) {
                MdsProductStockPointBaseVO oldPO = oldPOMap.get(erpProductStockPointBase.getItemNumber());
                BeanUtils.copyProperties(oldPO, mdsProductStockPointBaseDTO);
                mdsProductStockPointBaseDTO.setProductCode(erpProductStockPointBase.getItemNumber());
                mdsProductStockPointBaseDTO.setProductName(erpProductStockPointBase.getItemDesc());
                String lengthStr = erpProductStockPointBase.getLength();
                mdsProductStockPointBaseDTO.setProductLength((lengthStr != null && !lengthStr.trim().isEmpty())
                        ? BigDecimal.valueOf(Double.parseDouble(lengthStr)) : null);
                String widthStr = erpProductStockPointBase.getWidth();
                mdsProductStockPointBaseDTO.setProductWidth((widthStr != null && !widthStr.trim().isEmpty())
                        ? BigDecimal.valueOf(Double.parseDouble(widthStr)) : null);
                String plyStr = erpProductStockPointBase.getPly();
                mdsProductStockPointBaseDTO.setProductThickness((plyStr != null && !plyStr.trim().isEmpty())
                        ? BigDecimal.valueOf(Double.parseDouble(plyStr)) : null);
                mdsProductStockPointBaseDTO.setLoadPosition(erpProductStockPointBase.getPositionA());
                mdsProductStockPointBaseDTO.setDirNum(erpProductStockPointBase.getDirNum());
                mdsProductStockPointBaseDTO.setItemType(erpProductStockPointBase.getItemType());
                //需进行处理的数据
                mdsProductStockPointBaseDTO.setProductColor(normalizeValue(erpProductStockPointBase.getColorCode()));
                mdsProductStockPointBaseDTO.setGlassColor(normalizeValue(erpProductStockPointBase.getGlassColor()));
                mdsProductStockPointBaseDTO.setProductType(normalizeValue(erpProductStockPointBase.getModelCode()));
                mdsProductStockPointBaseDTO.setDifficultyLevel(normalizeValue(erpProductStockPointBase.getDifficulty()));
                mdsProductStockPointBaseDTO.setGridType(normalizeValue(erpProductStockPointBase.getBarType()));
                mdsProductStockPointBaseDTO.setProductionModel(normalizeValue(erpProductStockPointBase.getProductionMode()));
                mdsProductStockPointBaseDTO.setTougheningType(normalizeValue(erpProductStockPointBase.getGanghuaType()));
                mdsProductStockPointBaseDTO.setMembraneSystem(normalizeValue(erpProductStockPointBase.getItemDmmx()));
                mdsProductStockPointBaseDTO.setHud(normalizeValue(erpProductStockPointBase.getItemPvblx()));
                mdsProductStockPointBaseDTO.setClampType(normalizeValue(erpProductStockPointBase.getWhetherWire()));
                mdsProductStockPointBaseDTO.setSealEdge(normalizeValue(erpProductStockPointBase.getPressBargin()));
                mdsProductStockPointBaseDTO.setProductArea(normalizeValue(erpProductStockPointBase.getAcreage()));
                mdsProductStockPointBaseDTO.setCurvature(normalizeValue(erpProductStockPointBase.getCurvature()));
                mdsProductStockPointBaseDTO.setAttr1(normalizeValue(erpProductStockPointBase.getAttr1()));
                mdsProductStockPointBaseDTO.setPartNum(normalizeValue(erpProductStockPointBase.getPartNum()));
                updateMdsProductStockPointBaseDTOs.add(mdsProductStockPointBaseDTO);
            } else {
                String id = UUIDUtil.getUUID();
                mdsProductStockPointBaseDTO.setId(id);
                mdsProductStockPointBaseDTO.setProductCode(erpProductStockPointBase.getItemNumber());
                mdsProductStockPointBaseDTO.setProductName(erpProductStockPointBase.getItemDesc());
                String lengthStr = erpProductStockPointBase.getLength();
                mdsProductStockPointBaseDTO.setProductLength((lengthStr != null && !lengthStr.trim().isEmpty())
                        ? BigDecimal.valueOf(Double.parseDouble(lengthStr)) : null);
                String widthStr = erpProductStockPointBase.getWidth();
                mdsProductStockPointBaseDTO.setProductWidth((widthStr != null && !widthStr.trim().isEmpty())
                        ? BigDecimal.valueOf(Double.parseDouble(widthStr)) : null);
                String plyStr = erpProductStockPointBase.getPly();
                mdsProductStockPointBaseDTO.setProductThickness((plyStr != null && !plyStr.trim().isEmpty())
                        ? BigDecimal.valueOf(Double.parseDouble(plyStr)) : null);
                mdsProductStockPointBaseDTO.setLoadPosition(erpProductStockPointBase.getPositionA());
                mdsProductStockPointBaseDTO.setDirNum(erpProductStockPointBase.getDirNum());
                mdsProductStockPointBaseDTO.setItemType(erpProductStockPointBase.getItemType());
                //需进行处理的数据
                mdsProductStockPointBaseDTO.setProductColor(normalizeValue(erpProductStockPointBase.getColorCode()));
                mdsProductStockPointBaseDTO.setGlassColor(normalizeValue(erpProductStockPointBase.getGlassColor()));
                mdsProductStockPointBaseDTO.setProductType(normalizeValue(erpProductStockPointBase.getModelCode()));
                mdsProductStockPointBaseDTO.setDifficultyLevel(normalizeValue(erpProductStockPointBase.getDifficulty()));
                mdsProductStockPointBaseDTO.setGridType(normalizeValue(erpProductStockPointBase.getBarType()));
                mdsProductStockPointBaseDTO.setProductionModel(normalizeValue(erpProductStockPointBase.getProductionMode()));
                mdsProductStockPointBaseDTO.setTougheningType(normalizeValue(erpProductStockPointBase.getGanghuaType()));
                mdsProductStockPointBaseDTO.setMembraneSystem(normalizeValue(erpProductStockPointBase.getItemDmmx()));
                mdsProductStockPointBaseDTO.setHud(normalizeValue(erpProductStockPointBase.getItemPvblx()));
                mdsProductStockPointBaseDTO.setClampType(normalizeValue(erpProductStockPointBase.getWhetherWire()));
                mdsProductStockPointBaseDTO.setSealEdge(normalizeValue(erpProductStockPointBase.getPressBargin()));
                mdsProductStockPointBaseDTO.setProductArea(normalizeValue(erpProductStockPointBase.getAcreage()));
                mdsProductStockPointBaseDTO.setCurvature(normalizeValue(erpProductStockPointBase.getCurvature()));
                mdsProductStockPointBaseDTO.setAttr1(normalizeValue(erpProductStockPointBase.getAttr1()));
                mdsProductStockPointBaseDTO.setPartNum(normalizeValue(erpProductStockPointBase.getPartNum()));
                insertMdsProductStockPointBaseDTOs.add(mdsProductStockPointBaseDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(insertMdsProductStockPointBaseDTOs)) {
            // 新增数据持久化
            BulkOperationUtils.bulkUpdateOrCreate(insertMdsProductStockPointBaseDTOs,
                    dtoList -> this.doCreateBatch(dtoList), 20);
        }

        if (CollectionUtils.isNotEmpty(updateMdsProductStockPointBaseDTOs)) {
            // 修改数据持久化
            log.info("待更新数据数量（去重前）：{} 条", updateMdsProductStockPointBaseDTOs.size());
            List<String> distinctList = updateMdsProductStockPointBaseDTOs.stream()
                    .map(MdsProductStockPointBaseDTO::getId).distinct().collect(Collectors.toList());
            // 打印去重后数量
            log.info("待更新数据数量（去重后）：{} 条", distinctList.size());
            BulkOperationUtils.bulkUpdateOrCreate(updateMdsProductStockPointBaseDTOs,
                    dtoList -> this.doUpdateBatch(dtoList), 20);
        }

        DynamicDataSourceContextHolder.clearDataSource();
        return BaseResponse.success("同步成功");
    }

    @Override
    public List<MdsProductStockPointBaseVO> selectByProduct(Map<String, Object> params) {
        return MdsProductStockPointBaseConvertor.INSTANCE.po2Vos(mdsProductStockPointBaseDao.selectByProduct(params));
    }

    @Override
    public BaseResponse<Void> syncData(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.PRODUCT_STOCK_POINT_BASE.getCode(), params);
        return BaseResponse.success("同步操作完成");
    }

    public BaseResponse<MdsProductStockPointBaseVO> processItemFlags(List<String> productCodes) {
        for (String productCode : productCodes) {
            // 根据product_code在v_mds_rou_product_bom_version中查找对应的id
            List<String> bomVersionIdList = mdsProductBomVersionService.selectIdByProductCode(productCode);

            if (!bomVersionIdList.isEmpty()) {
                // 使用bomVersionId在v_mds_rou_product_bom中查找匹配的io_product_code
                String bomVersionId = bomVersionIdList.get(0);
                List<String> ioProductCodes = mdsProductBomService.selectIoProductIdsByBomVersionId(bomVersionId);

                if (CollectionUtils.isNotEmpty(ioProductCodes)) {
                    // 通过物料编码集合去物料主数据查询
                    Map<String, Object> params = new HashMap<>();
                    params.put("productCodes", ioProductCodes);
                    List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectVOByParams(params);

                    // 只保留productType为P的数据
                    newProductStockPointVOS = newProductStockPointVOS.stream()
                            .filter(x -> "P".equals(x.getProductType()))
                            .collect(Collectors.toList());

                    Set<String> itemFlagSet = newProductStockPointVOS.stream()
                            .map(NewProductStockPointVO::getItemFlag)
                            .collect(Collectors.toSet());

                    String newItemFlag = null;

                    if (itemFlagSet.contains("调光")) {
                        newItemFlag = "调光";
                    } else if (itemFlagSet.contains("PET膜") && itemFlagSet.contains("夹丝")) {
                        newItemFlag = "PET夹丝";
                    } else if (itemFlagSet.contains("PET膜")) {
                        newItemFlag = "PET";
                    } else if (itemFlagSet.contains("夹丝")) { // 新增：只包含夹丝的情况
                        newItemFlag = "夹丝";
                    }

                    // 如果没有匹配任何条件，默认值为空字符串（""）
                    if (newItemFlag == null) {
                        newItemFlag = "";
                    }

                    // 更新mds_product_stock_point_base表中的item_flag字段
                    mdsProductStockPointBaseService.updateItemFlagByProductCode(productCode, newItemFlag);
                }
            }
        }
        return BaseResponse.success("处理完成");
    }

    @Override
    public List<String> selectAllProductCodes() {
        return mdsProductStockPointBaseDao.selectAllProduct();
    }

    @Override
    public void updateItemFlagByProductCode(String productCode, String newItemFlag) {
        mdsProductStockPointBaseDao.updateItemFlagByProductCode(productCode, newItemFlag);
    }

    public BaseResponse<MdsProductStockPointBaseVO> updateProductLineGroup(List<String> productCodes) {
        // 获取A中数据
        List<ProductCandidateResourceTimeVO> AList = productCandidateResourceTimeService.selectByProductCode(productCodes);
        // 获取A集合的物料编码集合
        Set<String> ACodeSet = AList.stream().map(ProductCandidateResourceTimeVO::getProductCode).collect(Collectors.toSet());

        // 获取关键工序组合列表："S1-20", "S2-40"
        Set<String> keyProcesses = getKeyProcesses();

        // 对于AList中的数据进行处理
        Map<String, String> AMap = AList.stream()
                .filter(v -> {
                    String key = v.getStockPointCode() + "-" + v.getOperationCode();
                    return keyProcesses.contains(key);
                })
                .collect(Collectors.groupingBy(
                        ProductCandidateResourceTimeVO::getProductCode,
                        Collectors.collectingAndThen(
                                Collectors.minBy(Comparator.comparingInt(v ->
                                        v.getPriority() != null && YesOrNoEnum.YES.getCode().equals(v.getEnabled())
                                                ? v.getPriority()
                                                : Integer.MAX_VALUE)),
                                optional -> optional.map(ProductCandidateResourceTimeVO::getLineGroup).orElse(null)
                        )
                ));

        Map<String, String> lineGroupUpdateMap = new HashMap<>(AMap);

        // 过滤掉已经在AList中存在的productCode
        List<String> BProductCodes = productCodes.stream()
                .filter(code -> !ACodeSet.contains(code))
                .collect(Collectors.toList());

        // 如果有剩余未处理的productCode，则查询B表
        if (!BProductCodes.isEmpty()) {
            List<ProductionCandidateResourceVO> BList = productCandidateResourceService.selectByProductCode(BProductCodes);

            // 对于BList中的数据进行处理
            Map<String, String> BMap = BList.stream()
                    .filter(Objects::nonNull)
                    .filter(v -> {
                        String key = v.getStockPointCode() + "-" + v.getStandardStepCode();
                        return keyProcesses.contains(key);
                    })
                    .filter(v -> v.getProductCode() != null && v.getStandardResourceCode() != null)
                    .collect(Collectors.groupingBy(
                            ProductionCandidateResourceVO::getProductCode,
                            Collectors.collectingAndThen(
                                    Collectors.minBy(Comparator.comparingInt(v -> v.getPriority() != null ? v.getPriority() : Integer.MAX_VALUE)),
                                    optional -> optional.map(ProductionCandidateResourceVO::getStandardResourceCode).orElse(null)
                            )
                    ));

            lineGroupUpdateMap.putAll(BMap);

        }

        // 过滤出未匹配的产品编码
        Set<String> matchedProductCodes = new HashSet<>(lineGroupUpdateMap.keySet());
        List<String> unmatchedProductCodes = productCodes.stream()
                .filter(code -> !matchedProductCodes.contains(code))
                .collect(Collectors.toList());

        // 处理未匹配的产品编码的新逻辑
        for (String productCode : unmatchedProductCodes) {
            // 查询v_mds_rou_routing视图获取id列的值
            List<String> routingIds = newRoutingService.selectRoutingIdsByProductCode(productCode);
            if (routingIds != null && !routingIds.isEmpty()) {
                for (String routingId : routingIds) {
                    // 根据routing_id从v_mds_rou_routing_step_input获取input_product_id
                    List<String> inputProductIds = newRoutingStepInputService.selectInputProductIdsByRoutingId(routingId);
                    if (inputProductIds != null && !inputProductIds.isEmpty()) {
                        for (String inputProductId : inputProductIds) {
                            // 从v_mds_rou_product_candidate_resource获取所需信息
                            List<ProductionCandidateResourceVO> candidateResources = productCandidateResourceService.selectCandidateResourceByProductId(Collections.singletonList(inputProductId));

                            if (candidateResources != null && !candidateResources.isEmpty()) {
                                // 筛选出符合条件的数据并按优先级排序
                                Optional<ProductionCandidateResourceVO> firstMatchedCandidateResource =
                                        candidateResources.stream()
                                                .filter(v -> {
                                                    String key = v.getStockPointCode() + "-" + v.getStandardStepCode();
                                                    return keyProcesses.contains(key);
                                                })
                                                .min(Comparator.comparingInt(v ->
                                                        v.getPriority() != null ? v.getPriority() : Integer.MAX_VALUE));

                                if (firstMatchedCandidateResource.isPresent()) {
                                    ProductionCandidateResourceVO candidateResource = firstMatchedCandidateResource.get();

                                    // 查询物料主数据表newProductStockPoint获取ProductType
                                    Map<String, Object> params = new HashMap<>();
                                    params.put("productCodes", Collections.singletonList(candidateResource.getProductCode()));
                                    List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectVOByParams(params);

                                    // 只保留ProductType为SA的数据
                                    newProductStockPointVOS = newProductStockPointVOS.stream()
                                            .filter(x -> "SA".equals(x.getProductType()))
                                            .collect(Collectors.toList());

                                    if (!newProductStockPointVOS.isEmpty()) {
                                        lineGroupUpdateMap.putIfAbsent(productCode, candidateResource.getStandardResourceCode()); // 存在多条满足的数据时取第一条
                                        break;
                                    }
                                }
                            }
                        }
                        if (lineGroupUpdateMap.containsKey(productCode)) {
                            break;
                        }
                    }
                }
            }

            // 确保所有productCodes都有对应的lineGroup值，如果没有匹配的数据则设置为空字符串""
            if (!lineGroupUpdateMap.containsKey(productCode)) {
                lineGroupUpdateMap.put(productCode, "");
            }
        }

        // 更新mds_product_stock_point_base表中的line_group字段
        for (Map.Entry<String, String> entry : lineGroupUpdateMap.entrySet()) {
            mdsProductStockPointBaseService.updateLineGroupByProductCode(entry.getKey(), entry.getValue());
        }

        return BaseResponse.success("处理完成");
    }

    @Override
    public void updateLineGroupByProductCode(String key, String value) {
        mdsProductStockPointBaseDao.updateLineGroupByProductCode(key, value);
    }

    @Override
    public BaseResponse<MdsProductStockPointBaseVO> updateStandardResourceForToolCategory(String scenario, List<String> productCodes) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return BaseResponse.success("没有需要处理的数据");
        }
        Map<String, String> productStandardResourceMap = mpsFeign.selectMoldToolingRelationsByProductCodes(scenario, productCodes);
        if (MapUtil.isEmpty(productStandardResourceMap)) {
            log.info("MPS查询工装关系与目录号关系无数据，没有需要处理数据");
            return BaseResponse.success("没有需要处理的数据");
        }
        // 更新mds_product_stock_point_base表中的standard_resource_id字段
        for (Map.Entry<String, String> entry : productStandardResourceMap.entrySet()) {
            mdsProductStockPointBaseService.updateStandardResourceIdByProductCode(entry.getKey(), entry.getValue());
        }
        return BaseResponse.success("处理完成");
    }

    @Override
    public void updateStandardResourceIdByProductCode(String key, String value) {
        mdsProductStockPointBaseDao.updateStandardResourceIdByProductCode(key, value);
    }

    @Override
    public Map<String, String> selectByLineGroup(List<String> lineGroupList) {
        //查询生产组织库存点数据
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode(),
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<String> stockPointCodes = Arrays.asList(rangeData.split(","));
        List<MdsProductStockPointBaseVO> baseVOs = mdsProductStockPointBaseDao.selectByLineGroup(lineGroupList, stockPointCodes);
        return baseVOs.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getLineGroup()) && !"/".equals(e.getLineGroup()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductId,
                        MdsProductStockPointBaseVO::getLineGroup, (v1, v2) -> v1));
    }

    /**
     * 后处理逻辑：处理同步完成的数据
     */
    @Transactional
    public void processSyncedData(String scenario) {
        List<String> syncedProductCodes = mdsProductStockPointBaseService.getRecentlyModifiedProductCodes();

        if (CollectionUtils.isEmpty(syncedProductCodes)) {
            log.info("没有需要处理的新同步数据");
            return;
        }
        combinedProcess(scenario,syncedProductCodes);
        log.info("后处理逻辑完成，共处理 {} 条数据", syncedProductCodes.size());
    }

    @Override
    public void combinedProcess(String scenario, List<String> syncedProductCodes) {
        processItemFlags(syncedProductCodes);
        updateProductLineGroup(syncedProductCodes);
        updateStandardResourceForToolCategory(scenario, syncedProductCodes);
    }

    @Override
    public List<String> getRecentlyModifiedProductCodes() {
        // 查询 mds_product_stock_point_base 表中所有 line_group 为 NULL 或者为空的数据
        List<String> productCodesWithEmptyLineGroup = mdsProductStockPointBaseService.selectProductCodesWithEmptyLineGroup();

        if (CollectionUtils.isEmpty(productCodesWithEmptyLineGroup)) {
            log.info("没有找到 line_group 为 NULL 或空的产品数据");
            return Collections.emptyList();
        }

        // 使用上一步得到的 product_codes 去 mds_rou_routing 表中查找 enabled 为 YES 的记录
        List<String> filteredProductCodes = newRoutingService.selectEnabledProductCodes(productCodesWithEmptyLineGroup);

        if (CollectionUtils.isEmpty(filteredProductCodes)) {
            log.info("没有找到 enabled 为 YES 的产品数据");
            return Collections.emptyList();
        }

        return filteredProductCodes;
    }

    @Transactional
    public BaseResponse<Void> processSelectedProducts(List<String> selectedProductCodes) {
        if (CollectionUtils.isEmpty(selectedProductCodes)) {
            log.info("没有选择的数据进行同步");
            return BaseResponse.success("未选择任何数据进行同步");
        }

        try {
            processItemFlags(selectedProductCodes);
            updateProductLineGroup(selectedProductCodes);
            updateStandardResourceForToolCategory(SystemHolder.getScenario(), selectedProductCodes);

            log.info("选中数据同步完成，共处理 {} 条数据", selectedProductCodes.size());
            return BaseResponse.success("数据更新成功");
        } catch (Exception e) {
            log.error("处理选中的产品编码时发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("数据更新过程中出现错误", e);
        }
    }

    public List<String> selectProductCodesWithEmptyLineGroup() {
        return mdsProductStockPointBaseDao.selectProductCodesWithEmptyLineGroup();
    }

    @Override
    public Map<String, List<String>> selectFg2SaList(List<String> fgCodes) {
        Map<String, List<String>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(fgCodes)) {
            return result;
        }
        List<Map<String, String>> maps = mdsProductStockPointBaseDao.selectFg2SaList(fgCodes);
        if (CollectionUtils.isEmpty(maps)) {
            return result;
        }
        for (Map<String, String> map : maps) {
            String fgCode = map.get("fgCode");
            String saCode = map.get("saCode");
            if (StringUtils.isNotBlank(fgCode) && StringUtils.isNotBlank(saCode)) {
                if (result.containsKey(fgCode)) {
                    List<String> saCodes = result.get(fgCode);
                    if (!saCodes.contains(saCode)) {
                        saCodes.add(saCode);
                        result.put(fgCode, saCodes);
                    }
                } else {
                    result.put(fgCode, new ArrayList<>(Collections.singleton(saCode)));
                }
            }
        }
        return result;
    }

    //获取关键工序
    private Set<String> getKeyProcesses() {
        return standardStepService.selectAll().stream()
                .filter(item -> "FORMING_PROCESS".equals(item.getStandardStepType()))
                .map(item -> item.getStockPointCode() + "-" + item.getStandardStepCode())
                .collect(Collectors.toSet());
    }

    @Override
    public List<MdsProductStockPointBaseVO> selectByProductCodes(List<String> productCodes) {
        return mdsProductStockPointBaseDao.selectByProductCodes(productCodes);
    }

    @Override
    public void syncPartNum(List<String> productCodes) {

        if (CollectionUtils.isEmpty(productCodes)) {
            log.info("没有 productCodes，跳过 part_num 同步");
            return;
        }

        List<Map<String, Object>> list = mdsProductStockPointBaseDao.selectPartNumByProductCodes(productCodes);

        for (Map<String, Object> row : list) {
            String productCode = (String) row.get("product_code");
            String partNum = (String) row.get("part_num");
            String productName = (String) row.get("product_name");

            if (StringUtils.isBlank(productCode) || StringUtils.isBlank(partNum)) {
                continue;
            }
            Map<String, Object> params = new HashMap<>();
            params.put("productCode", productCode);
            params.put("partNumber", partNum);
            params.put("partName", productName);
            params.put("sourceType", "ERP");

            try {
                dfpFeign.syncPartNumFromErp(params);
            } catch (Exception e) {
                log.error("同步零件号失败: {}", params, e);
            }
        }
    }

    @Override
    public List<String> getModifiedProductCodes() {
        // 计算 2 小时前的时间
        Timestamp twoHoursAgo = new Timestamp(System.currentTimeMillis() - TimeUnit.HOURS.toMillis(2));
        return mdsProductStockPointBaseDao.selectProductCodesByModifyTime(twoHoursAgo);
    }

    public void syncPartNumber() {
        try {
            log.info("开始执行每2小时part_num 增量同步任务");
            List<String> productCodes = getModifiedProductCodes();
            if (productCodes == null || productCodes.isEmpty()) {
                log.info("没有需要同步的 product_code");
                return;
            }
            log.info("准备同步 {} 个 product_code", productCodes.size());
            syncPartNum(productCodes);
            log.info("本次 part_num 同步完成，共处理 {} 个产品", productCodes.size());
        } catch (Exception e) {
            log.error("执行定时同步 part_num 出错", e);
        }
    }
}