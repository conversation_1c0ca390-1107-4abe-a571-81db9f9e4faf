package com.yhl.scp.mds.baseResource.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PhysicalResourceDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 112767721857680780L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 标准资源ID
     */
    private String standardResourceId;
    /**
     * 物理资源代码
     */
    private String physicalResourceCode;
    /**
     * 物理资源名称
     */
    private String physicalResourceName;
    /**
     * 资源类别
     */
    private String resourceCategory;
    /**
     * 资源类型
     */
    private String resourceType;
    /**
     * 资源门类
     */
    private String resourceClassification;
    /**
     * 子任务类型
     */
    private String subtaskType;
    /**
     * 堆叠限制类型
     */
    private String assignQuantityType;
    /**
     * 显示顺序
     */
    private Integer displayIndex;
    /**
     * 是否瓶颈资源
     */
    private String bottleneck;
    /**
     * 是否无限能力
     */
    private String infiniteCapacity;
    /**
     * 工序代码
     */
    private String sequenceCode;
    /**
     * 是否可变工时
     */
    private String variableWorkHours;
    /**
     * 资源量系数
     */
    private String resourceQuantityCoefficient;
    /**
     * 计量单位ID
     */
    private String countingUnitId;
    /**
     * 制造效率
     */
    private BigDecimal productionEfficiency;
    /**
     * 设置效率
     */
    private BigDecimal setupEfficiency;
    /**
     * 清洗效率
     */
    private BigDecimal cleanupEfficiency;
    /**
     * 设置时间
     */
    private Integer setupDuration;
    /**
     * 清洗时间
     */
    private Integer cleanupDuration;
    /**
     * 前缓冲时间
     */
    private Integer bufferTimeBefore;
    /**
     * 后缓冲时间
     */
    private Integer bufferTimeAfter;
    /**
     * 制造最大中断时间
     */
    private Integer maxProductionSuspendDuration;
    /**
     * 设置最大中断时间
     */
    private Integer maxSetupSuspendDuration;
    /**
     * 清理最大中断时间
     */
    private Integer maxCleanupSuspendDuration;
    /**
     * 生产线
     */
    private String productionLine;
    /**
     * 是否严格遵守生产线约束
     */
    private String strictProductionLineConstraints;
    private String noBufferActionType;
    /**
     * 资源锁定时间
     */
    private Integer noBufferActionDuration;
    /**
     * 单位制造批量
     */
    private Integer lotSize;
    /**
     * 最大制造批量
     */
    private Integer maxLotSize;
    /**
     * 制造时刻尾数调整单位
     */
    private String productionDateLastNumChangeUnit;
    /**
     * 制造时间尾数调整单位
     */
    private String productionTimeLastNumChangeUnit;
    /**
     * 制造时间取值方式
     */
    private String productionDurationLogic;
    /**
     * 设置和清洗时间取值方式
     */
    private String setupAndCleanupDurationLogic;
    /**
     * 动态设置和清洗时间取值方式
     */
    private String dynamicSetupAndCleanupDurationLogic;
    /**
     * 切换间取值切换
     */
    private String changeoverDurationLogic;
    /**
     * 动态切换时间取值方式
     */
    private String dynamicChangeoverDurationLogic;
    /**
     * 生效时间
     */
    private Date effectiveTime;
    /**
     * 失效时间
     */
    private Date expiryTime;

}
