package com.yhl.scp.mds.product.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.job.ProcessSyncedDataJob;
import com.yhl.scp.mds.product.dto.ProductCandidateResourceTimeDTO;
import com.yhl.scp.mds.product.service.ProductCandidateResourceTimeService;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>ProductCandidateResourceTimeController</code>
 * <p>
 * 产品资源生产关系表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 11:00:15
 */
@Slf4j
@Api(tags = "产品资源生产关系表控制器")
@RestController
@RequestMapping("productCandidateResourceTime")
public class ProductCandidateResourceTimeController extends BaseController {

    @Resource
    private ProductCandidateResourceTimeService productCandidateResourceTimeService;

    @ApiOperation(value = "test")
    @GetMapping(value = "test")
    public BaseResponse<String> test() {
        productCandidateResourceTimeService.afterCandidateResourceOnProduct();
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<ProductCandidateResourceTimeVO>> page() {
        List<ProductCandidateResourceTimeVO> productCandidateResourceTimeList = productCandidateResourceTimeService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ProductCandidateResourceTimeVO> pageInfo = new PageInfo<>(productCandidateResourceTimeList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ProductCandidateResourceTimeDTO productCandidateResourceTimeDTO) {
        return productCandidateResourceTimeService.doCreate(productCandidateResourceTimeDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ProductCandidateResourceTimeVO productCandidateResourceTimeVO) {
        productCandidateResourceTimeService.updateData(productCandidateResourceTimeVO);
        return BaseResponse.success();
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        productCandidateResourceTimeService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<ProductCandidateResourceTimeVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, productCandidateResourceTimeService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "export")
    public void export(HttpServletResponse response) {
        productCandidateResourceTimeService.export(response);
    }


    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncMoldChangeTime() {
        return productCandidateResourceTimeService.syncMoldChangeTime(SystemHolder.getTenantCode());
    }
}
