package com.yhl.scp.mds.supplier.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesProductQualifiedSupplier;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.supplier.convertor.SupplierOwnerInfoConvertor;
import com.yhl.scp.mds.supplier.domain.entity.SupplierOwnerInfoDO;
import com.yhl.scp.mds.supplier.domain.service.SupplierOwnerInfoDomainService;
import com.yhl.scp.mds.supplier.dto.SupplierOwnerInfoDTO;
import com.yhl.scp.mds.supplier.infrastructure.dao.SupplierOwnerInfoDao;
import com.yhl.scp.mds.supplier.infrastructure.po.SupplierOwnerInfoPO;
import com.yhl.scp.mds.supplier.service.SupplierOwnerInfoService;
import com.yhl.scp.mds.supplier.vo.SupplierOwnerInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>SupplierOwnerInfoServiceImpl</code>
 * <p>
 * 应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-02 16:20:47
 */
@Slf4j
@Service
public class SupplierOwnerInfoServiceImpl extends AbstractService implements SupplierOwnerInfoService {

    @Resource
    private SupplierOwnerInfoDao supplierOwnerInfoDao;

    @Resource
    private SupplierOwnerInfoDomainService supplierOwnerInfoDomainService;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    public BaseResponse<Void> doCreate(SupplierOwnerInfoDTO supplierOwnerInfoDTO) {
        // 0.数据转换
        SupplierOwnerInfoDO supplierOwnerInfoDO = SupplierOwnerInfoConvertor.INSTANCE.dto2Do(supplierOwnerInfoDTO);
        SupplierOwnerInfoPO supplierOwnerInfoPO = SupplierOwnerInfoConvertor.INSTANCE.dto2Po(supplierOwnerInfoDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        supplierOwnerInfoDomainService.validation(supplierOwnerInfoDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(supplierOwnerInfoPO);
        supplierOwnerInfoDao.insert(supplierOwnerInfoPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(SupplierOwnerInfoDTO supplierOwnerInfoDTO) {
        // 0.数据转换
        SupplierOwnerInfoDO supplierOwnerInfoDO = SupplierOwnerInfoConvertor.INSTANCE.dto2Do(supplierOwnerInfoDTO);
        SupplierOwnerInfoPO supplierOwnerInfoPO = SupplierOwnerInfoConvertor.INSTANCE.dto2Po(supplierOwnerInfoDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        supplierOwnerInfoDomainService.validation(supplierOwnerInfoDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(supplierOwnerInfoPO);
        supplierOwnerInfoDao.update(supplierOwnerInfoPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<SupplierOwnerInfoDTO> list) {
        List<SupplierOwnerInfoPO> newList = SupplierOwnerInfoConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        supplierOwnerInfoDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<SupplierOwnerInfoDTO> list) {
        List<SupplierOwnerInfoPO> newList = SupplierOwnerInfoConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        supplierOwnerInfoDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return supplierOwnerInfoDao.deleteBatch(idList);
        }
        return supplierOwnerInfoDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public SupplierOwnerInfoVO selectByPrimaryKey(String id) {
        SupplierOwnerInfoPO po = supplierOwnerInfoDao.selectByPrimaryKey(id);
        return SupplierOwnerInfoConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "SUPPLIER_OWNER_INFO")
    public List<SupplierOwnerInfoVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "SUPPLIER_OWNER_INFO")
    public List<SupplierOwnerInfoVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SupplierOwnerInfoVO> dataList = supplierOwnerInfoDao.selectByCondition(sortParam, queryCriteriaParam);
        SupplierOwnerInfoServiceImpl target = SpringBeanUtils.getBean(SupplierOwnerInfoServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SupplierOwnerInfoVO> selectByParams(Map<String, Object> params) {
        List<SupplierOwnerInfoPO> list = supplierOwnerInfoDao.selectByParams(params);
        return SupplierOwnerInfoConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<SupplierOwnerInfoVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<SupplierOwnerInfoVO> selectBySupplierIds(List<String> supplierIds) {
        return supplierOwnerInfoDao.selectBySupplierIds(supplierIds);
    }

    @Override
    public BaseResponse<Void> syncProductQualifiedSupplier(String tenantId) {

        // 调用远程的mes工序成品率接口信息
        Map<String, Object> newStockPoingMap = new HashMap<>(2);
        newStockPoingMap.put("triggerType", DcpConstants.TASKS_MANUAL_TRIGGER);
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.PRODUCT_QUALIFIED_SUPPLIER.getCode(), newStockPoingMap);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> sync(List<MesProductQualifiedSupplier> o) {
        if (CollectionUtils.isEmpty(o)) {
            return BaseResponse.success();
        }

        List<String> supplierIds = o.stream().map(MesProductQualifiedSupplier::getAslId).distinct().
                collect(Collectors.toList());
        Map<String, Object> newStockPoingMap = new HashMap<>(2);
        newStockPoingMap.put("enable", YesOrNoEnum.YES.getCode());
        List<NewStockPointVO> stockPoints = newStockPointService.selectByParams(newStockPoingMap);
        Map<String, NewStockPointVO> stockMap = stockPoints.stream().collect(Collectors.
                toMap(NewStockPointVO::getOrganizeId, Function.identity(), (v1, v2) -> v1));

        List<SupplierOwnerInfoDTO> insertSupplierOwnerInfoDTOs = Lists.newArrayList();
        List<SupplierOwnerInfoDTO> updateSupplierOwnerInfoDTOs = Lists.newArrayList();
        List<SupplierOwnerInfoVO> supplierOwnerInfoVOS = this.selectBySupplierIds(supplierIds);
        Map<String, SupplierOwnerInfoVO> oldSupplierMap = supplierOwnerInfoVOS.stream().collect(Collectors.toMap(t ->
                t.getSupplierId() + "|" + t.getProductId() + "|" + t.getPlantId(), Function.identity(), (v1, v2) -> v1));
        for(MesProductQualifiedSupplier supplier : o){
            String supplierId = supplier.getAslId();
            String productId = supplier.getItemId();
            String plantId = supplier.getPlantId();
            String dataKey = supplierId + "|" + productId + "|" + plantId;
            String stockPointCode = null;
            if (stockMap.containsKey(plantId)) {
                stockPointCode = stockMap.get(plantId).getStockPointCode();
            }
            SupplierOwnerInfoDTO supplierOwnerInfoDTO = new SupplierOwnerInfoDTO();
            if(oldSupplierMap.containsKey(dataKey)) {

                SupplierOwnerInfoVO supplierOwnerInfoVO = oldSupplierMap.get(dataKey);
                supplierOwnerInfoDTO=SupplierOwnerInfoConvertor.INSTANCE.vo2Dto(supplierOwnerInfoVO);
                supplierOwnerInfoDTO.setSupplierId(supplierId);
                supplierOwnerInfoDTO.setProductId(productId);
                supplierOwnerInfoDTO.setPlantId(plantId);
                supplierOwnerInfoDTO.setOwnerType(Objects.equals(supplier.getOwnerType(),"Y")?
                        YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode());
                supplierOwnerInfoDTO.setSupplierCode(supplier.getSupplierCode());
                supplierOwnerInfoDTO.setProductCode(supplier.getItemCode());
                supplierOwnerInfoDTO.setLocatorDesc(supplier.getBpim());
                supplierOwnerInfoDTO.setLastUpdateDate(supplier.getLastUpdateDate());
                supplierOwnerInfoDTO.setStockPointCode(stockPointCode);
                updateSupplierOwnerInfoDTOs.add(supplierOwnerInfoDTO);
            }else{
                supplierOwnerInfoDTO.setSupplierId(supplierId);
                supplierOwnerInfoDTO.setProductId(productId);
                supplierOwnerInfoDTO.setPlantId(plantId);
                supplierOwnerInfoDTO.setOwnerType(Objects.equals(supplier.getOwnerType(),"Y")?
                        YesOrNoEnum.YES.getCode():YesOrNoEnum.NO.getCode());
                supplierOwnerInfoDTO.setSupplierCode(supplier.getSupplierCode());
                supplierOwnerInfoDTO.setProductCode(supplier.getItemCode());
                supplierOwnerInfoDTO.setLocatorDesc(supplier.getBpim());
                supplierOwnerInfoDTO.setLastUpdateDate(supplier.getLastUpdateDate());
                supplierOwnerInfoDTO.setStockPointCode(stockPointCode);
                supplierOwnerInfoDTO.setEnabled(YesOrNoEnum.YES.getCode());
                insertSupplierOwnerInfoDTOs.add(supplierOwnerInfoDTO);
            }
        }
        if(CollectionUtils.isNotEmpty(insertSupplierOwnerInfoDTOs)){
            List<List<SupplierOwnerInfoDTO>> partition = com.google.common.collect.Lists.partition(insertSupplierOwnerInfoDTOs, 2500);
            for (List<SupplierOwnerInfoDTO> supplierOwnerInfoDTOs : partition) {
                this.doCreateBatch(supplierOwnerInfoDTOs);
            }
        }
        if(CollectionUtils.isNotEmpty(updateSupplierOwnerInfoDTOs)){
            List<List<SupplierOwnerInfoDTO>> partition = com.google.common.collect.Lists.partition(updateSupplierOwnerInfoDTOs, 2500);
            for (List<SupplierOwnerInfoDTO> supplierOwnerInfoDTOs : partition) {
                this.doUpdateBatch(supplierOwnerInfoDTOs);
            }
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
//        return ObjectTypeEnum.SUPPLIER_OWNER_INFO.getCode();
        return null;
    }

    @Override
    public List<SupplierOwnerInfoVO> invocation(List<SupplierOwnerInfoVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
