package com.yhl.scp.mds.excel.serviceImpl;

import cn.hutool.core.collection.ListUtil;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.newproduct.convertor.NewProductStockPointConvertor;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointDTO;
import com.yhl.scp.mds.newproduct.infrastructure.dao.NewProductStockPointDao;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ProductStockPointExcelService</code>
 * <p>
 * 库存点导入导出
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-05 11:06:49
 */
@Service
public class NewProductStockPointExcelService extends AbstractExcelService<NewProductStockPointDTO, NewProductStockPointPO, NewProductStockPointVO> {

    @Resource
    private NewProductStockPointService newProductStockPointService;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private NewProductStockPointDao newProductStockPointDao;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    protected void fillIdForUpdateData(List<NewProductStockPointDTO> updateList, Map<String, NewProductStockPointPO> existingDataMap) {
        for (NewProductStockPointDTO productStockPointDTO : updateList) {
            NewProductStockPointPO newProductStockPointPO = existingDataMap.get(productStockPointDTO.getStockPointCode() + "&" + productStockPointDTO.getProductCode());
            if (Objects.nonNull(newProductStockPointPO)) {
                productStockPointDTO.setId(newProductStockPointPO.getId());
            }
        }
    }

    @Override
    protected ImportRelatedDataHolder<NewProductStockPointPO> prepareData(List<NewProductStockPointDTO> productStockPointDTOS) {
        // 1.配送提前期、制造变动提前期、制造固定提前期、采购变动提前期、采购固定提前期、库存有效期、订货间隔期
        // 找到数据库现在所有的数据
        List<NewProductStockPointPO> alreadyExitData = newProductStockPointDao.selectByParams(new HashMap<>(2));
        Map<String, NewProductStockPointPO> existingDataMap = alreadyExitData.stream().collect(Collectors.toMap(p -> p.getStockPointCode() + "&" + p.getProductCode(), Function.identity(), (v1, v2) -> v1));
        // 组成唯一键的字段
        List<String> uniqueKeys = ListUtil.of("stockPointCode", "productCode");
        // 外键字段
        List<String> foreignKeys = ListUtil.of("stockPointCode");
        List<NewStockPointVO> stockPointVOS = newStockPointService.selectByParams(new HashMap<>(2));
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();
        List<SimpleVO> stockVOS = stockPointVOS.stream().map(NewStockPointVO::toSimpleVO).collect(Collectors.toList());
        foreignDataMap.put("stockPointCode", stockVOS);
        return ImportRelatedDataHolder.<NewProductStockPointPO>builder()
                .existingData(alreadyExitData)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(existingDataMap)
                .build();

    }

    @Override
    public void exportTemplate(HttpServletResponse response, String excelStrategy) {
        super.exportTemplate(response, excelStrategy);
    }

    @Override
    protected void doInsert(ImportAnalysisResultHolder<NewProductStockPointDTO, NewProductStockPointPO> resultHolder, ImportContext importContext) {
//        List<NewProductStockPointDTO> insertList = resultHolder.getInsertList();
        List<NewProductStockPointDTO> updateList = resultHolder.getUpdateList();
//        List<NewProductStockPointPO> deteleList = resultHolder.getDeleteList();
//        if (CollectionUtils.isNotEmpty(deteleList)) {
//            List<String> ids = deteleList.stream().map(NewProductStockPointPO::getId).collect(Collectors.toList());
//            getBaseService().doDelete(ids);
//        }
//        if (CollectionUtils.isNotEmpty(insertList)) {
//            for (NewProductStockPointDTO productStockPointDTO : insertList) {
//                productStockPointDTO.setId(UUIDUtil.getUUID());
//            }
//            newProductStockPointService.doCreateBatch(insertList);
//        }
//        updateList.forEach(t->{
//            t.setBusinessSpecial(null);
//            t.setClassifyDesc(null);
//            t.setCoreProcess(null);
//        });
//        List<NewProductStockPointPO> newProductStockPointPOS = NewProductStockPointConvertor.INSTANCE.dto2Pos(updateList);

        List<NewProductStockPointPO> list= new ArrayList<>();
        List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectByPrimaryKeys(updateList.stream().map(NewProductStockPointDTO::getId).collect(Collectors.toList()));
        newProductStockPointVOS.forEach(t->{
            NewProductStockPointPO newProductStockPointPO = new NewProductStockPointPO();
            newProductStockPointPO.setId(t.getId());
            updateList.stream().filter(y->y.getId().equals(t.getId())).findFirst().ifPresent(y->{
                newProductStockPointPO.setSaleType(y.getSaleType());
                newProductStockPointPO.setBusinessSpecial(y.getBusinessSpecial());
                newProductStockPointPO.setCoreProcess(y.getCoreProcess());
                newProductStockPointPO.setProductSpecial(y.getProductSpecial());
                newProductStockPointPO.setProductUser(y.getProductUser());
            });
//            newProductStockPointPO.setSaleType(t.getSaleType());
//            newProductStockPointPO.setBusinessSpecial(t.getBusinessSpecial());
//            newProductStockPointPO.setCoreProcess(t.getCoreProcess());
//            newProductStockPointPO.setProductSpecial(t.getProductSpecial());
            newProductStockPointPO.setVersionValue(t.getVersionValue());
            list.add(newProductStockPointPO);
        });
//        newProductStockPointPOS.forEach(t->{
//            NewProductStockPointPO newProductStockPointPO = new NewProductStockPointPO();
//            newProductStockPointPO.setId(t.getId());
//            newProductStockPointPO.setSaleType(t.getSaleType());
//            newProductStockPointPO.setBusinessSpecial(t.getBusinessSpecial());
//            newProductStockPointPO.setCoreProcess(t.getCoreProcess());
//            newProductStockPointPO.setProductSpecial(t.getProductSpecial());
//            newProductStockPointPO.setProductUser(t.getProductUser());
//            list.add(newProductStockPointPO);
//        });
        if (CollectionUtils.isNotEmpty(list)) {

            BasePOUtils.updateBatchFiller(list);
            newProductStockPointDao.updateBatchSelective(list);
        }
    }

    @Override
    public BaseDao<NewProductStockPointPO, NewProductStockPointVO> getBaseDao() {
        return newProductStockPointDao;
    }

    @Override
    public Function<NewProductStockPointDTO, NewProductStockPointPO> getDTO2POConvertor() {
        return NewProductStockPointConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<NewProductStockPointDTO> getDTOClass() {
        return NewProductStockPointDTO.class;
    }

    @Override
    public BaseService<NewProductStockPointDTO, NewProductStockPointVO> getBaseService() {
        return newProductStockPointService;
    }


    @Override
    protected List<NewProductStockPointDTO> getCustomizedExampleData() {
        NewProductStockPointDTO build = NewProductStockPointDTO.builder()
                // .stockPointId("stock").productId("product").productType("成品")
                // .daysRetrieved("是")
                // .quantityCalculated("是")
                // .participationCalculated("是")
                // .salesOrderShortage("是")
                // .demandForecastShortage("是")
                // .consumptionShortage("是")
                // .outboundShortage("是")
                // .lowerThanMin("是")
                // .lowerThanTarget("是")
                // .lowerThanMax("是")
                // .keyMaterial("是")
                // .longPeriodMaterial("是")
                // .autoFixPegging("是")
                // .acrossStockPointAllowed("是")
                // .computationClass("是")
                .build();
        return Collections.singletonList(build);
    }

    @Override
    protected void specialVerification(ImportAnalysisResultHolder<NewProductStockPointDTO, NewProductStockPointPO> resultHolder, ImportContext importContext) {
        if(CollectionUtils.isNotEmpty(resultHolder.getInsertList())){
            throw new BusinessException("导入失败，有数据不在现有表");
        }
        List<User> users = ipsNewFeign.userList();
        resultHolder.getUpdateList().forEach(x->{
            User user = users.stream().filter(y->y.getUserName().equals(x.getProductUser())).findFirst().orElse(null);
            if(Objects.isNull(user)){
                throw new BusinessException("导入失败，有人员字段无法匹配");
            }
            x.setProductUser(user.getId());
        });
    }
}
