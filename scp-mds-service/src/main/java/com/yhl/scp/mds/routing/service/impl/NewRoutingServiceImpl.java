package com.yhl.scp.mds.routing.service.impl;

import cn.hutool.core.date.StopWatch;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.routing.enums.ConnectionTypeEnum;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.service.MdsProductBomVersionService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.bom.vo.ProductRiskLevelVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.routing.convertor.NewRoutingConvertor;
import com.yhl.scp.mds.routing.convertor.NewRoutingStepConvertor;
import com.yhl.scp.mds.routing.domain.entity.NewRoutingDO;
import com.yhl.scp.mds.routing.domain.service.NewRoutingDomainService;
import com.yhl.scp.mds.routing.dto.NewRoutingDTO;
import com.yhl.scp.mds.routing.dto.NewRoutingStepDTO;
import com.yhl.scp.mds.routing.dto.NewRoutingValidateExcelDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingDao;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingPO;
import com.yhl.scp.mds.routing.service.*;
import com.yhl.scp.mds.routing.vo.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>RoutingServiceImpl</code>
 * <p>
 * 生产路径应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 09:26:43
 */
@Slf4j
@Service
public class NewRoutingServiceImpl extends AbstractService implements NewRoutingService {

    @Resource
    private NewRoutingDao newRoutingDao;

    @Resource
    private NewRoutingDomainService newRoutingDomainService;

    @Resource
    private ProductRoutingService productRoutingService;

    @Resource
    private ProductRoutingStepService productRoutingStepService;

    @Resource
    private NewRoutingStepService newRoutingStepService;

    @Resource
    private NewRoutingStepInputService newRoutingStepInputService;

    @Resource
    private NewRoutingStepOutputService newRoutingStepOutputService;

    @Resource
    private NewRoutingStepResourceService newRoutingStepResourceService;

    @Resource
    private MdsProductBomService mdsProductBomService;

    @Resource
    private MdsProductBomVersionService mdsProductBomVersionService;

    @Resource
    private NewProductStockPointService newProductStockPointService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private  IpsFeign ipsFeign;

    protected static final Map<String, List<Integer>> PARALLEL_OPERATION_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        PARALLEL_OPERATION_MAP.put(getCollectionCode(this.ipsFeign,"SPECIAL_STOCK_POINT"), Lists.newArrayList(15, 20,
                30));
        PARALLEL_OPERATION_MAP.put(getCollectionCode(this.ipsFeign,"SPECIAL_STOCK_POINT_S2"), Lists.newArrayList(40,
                45));
    }
    private  String getCollectionCode(IpsFeign ipsFeign,String collectionCode) {
        String stockPoint = ipsFeign.getByCollectionCode(collectionCode).stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置"+collectionCode));
        return stockPoint;
    }
    public BaseResponse<Void> doCreate(NewRoutingDTO routingDTO) {
        // 0.数据转换
        NewRoutingDO routingDO = NewRoutingConvertor.INSTANCE.dto2Do(routingDTO);
        NewRoutingPO routingPO = NewRoutingConvertor.INSTANCE.dto2Po(routingDTO);
        // 1.数据校验
        newRoutingDomainService.validation(routingDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(routingPO);
        newRoutingDao.insertWithPrimaryKey(routingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(NewRoutingDTO routingDTO) {
        // 0.数据转换
        NewRoutingDO routingDO = NewRoutingConvertor.INSTANCE.dto2Do(routingDTO);
        NewRoutingPO routingPO = NewRoutingConvertor.INSTANCE.dto2Po(routingDTO);
        // 1.数据校验
        newRoutingDomainService.validation(routingDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(routingPO);
        newRoutingDao.update(routingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<NewRoutingDTO> list) {
        List<NewRoutingPO> newList = NewRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                newRoutingDao.insertBatchWithPrimaryKey(poList), 2500);
    }

    @Override
    public void doUpdateBatch(List<NewRoutingDTO> list) {
        List<NewRoutingPO> newList = NewRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                newRoutingDao.updateBatch(poList), 2500);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return newRoutingDao.deleteBatch(idList);
        }
        return newRoutingDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public NewRoutingVO selectByPrimaryKey(String id) {
        NewRoutingPO po = newRoutingDao.selectByPrimaryKey(id);
        return NewRoutingConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_rou_routing")
    public List<NewRoutingVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_rou_routing")
    public List<NewRoutingVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<NewRoutingVO> dataList = newRoutingDao.selectByCondition(sortParam, queryCriteriaParam);
        NewRoutingServiceImpl target = SpringBeanUtils.getBean(NewRoutingServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<NewRoutingVO> selectByParams(Map<String, Object> params) {
        List<NewRoutingPO> list = newRoutingDao.selectByParams(params);
        return NewRoutingConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NewRoutingVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.NEW_ROUTING.getCode();
    }

    @Override
    public List<NewRoutingVO> invocation(List<NewRoutingVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void doTransitionRouting(List<String> routingSequenceIds, String scenario) {
        ExecutorService executor = Executors.newFixedThreadPool(6);
        try {
            StopWatch stopWatch = new StopWatch("路径、路径步骤转化处理");
            // 查询增量更新的物品工艺路径及物品工艺路径步骤数据
            stopWatch.start("查询产品路径列表");
            List<ProductRoutingVO> productRoutingList;
            if (CollectionUtils.isNotEmpty(routingSequenceIds)) {
                productRoutingList = productRoutingService.selectVOByParams(ImmutableMap.of("routingSequenceIds", routingSequenceIds)).stream().filter(x -> StringUtils.isNotBlank(x.getProductCode())).collect(Collectors.toList());
            } else {
                productRoutingList = productRoutingService.selectByRoutingSequenceIdNotNull().stream().filter(x -> StringUtils.isNotBlank(x.getProductCode())).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(productRoutingList)) {
                log.error("未获取到需要转换的物品工艺路径数据!");
                return;
            }
            stopWatch.stop();

            stopWatch.start("查询路径、路径步骤数据");
            List<String> productRoutingIds = productRoutingList.stream().map(ProductRoutingVO::getId).collect(Collectors.toList());
            // 查询工艺路径数据
            CompletableFuture<List<NewRoutingPO>> routingFuture = CompletableFuture.supplyAsync(() -> {
                DynamicDataSourceContextHolder.setDataSource(scenario);
                List<NewRoutingPO> routingList = newRoutingDao.selectByPrimaryKeys(productRoutingIds);
                DynamicDataSourceContextHolder.clearDataSource();
                return routingList;
            }, executor);
            // 查询工艺路径步骤数据
            CompletableFuture<List<ProductRoutingStepVO>> productRoutingStepFuture = CompletableFuture.supplyAsync(() -> {
                DynamicDataSourceContextHolder.setDataSource(scenario);
                List<ProductRoutingStepVO> productRoutingSteps = productRoutingStepService.selectByRoutingIds(productRoutingIds);
                DynamicDataSourceContextHolder.clearDataSource();
                return productRoutingSteps;
            }, executor);
            CompletableFuture<List<NewRoutingStepVO>> routingStepFuture = productRoutingStepFuture.thenApplyAsync((productRoutingSteps) -> {
                DynamicDataSourceContextHolder.setDataSource(scenario);
                List<String> routingStepIds = productRoutingSteps.stream().map(ProductRoutingStepVO::getId).collect(Collectors.toList());
                List<NewRoutingStepVO> routingSteps = CollectionUtils.isEmpty(routingStepIds) ? Lists.newArrayList() : newRoutingStepService.selectVOByPrimaryKeys(routingStepIds);
                DynamicDataSourceContextHolder.clearDataSource();
                return routingSteps;
            }, executor);
            CompletableFuture.allOf(routingFuture, productRoutingStepFuture, routingStepFuture);
            // 取数并作数据转换
            List<NewRoutingPO> routingList = routingFuture.join();
            List<ProductRoutingStepVO> productRoutingSteps = productRoutingStepFuture.join();
            List<NewRoutingStepVO> routingSteps = routingStepFuture.join();

            Map<String, NewRoutingPO> routingMap = routingList.stream().collect(Collectors.toMap(NewRoutingPO::getId, Function.identity(), (v1, v2) -> v1));
            Map<String, NewRoutingStepVO> routingStepMap = routingSteps.stream().collect(Collectors.toMap(NewRoutingStepVO::getId, Function.identity(), (v1, v2) -> v1));
            // 产品工艺路径步骤先过滤无效数据，然后用routingId去重，存在的为有效数据，不存在则为无效数据
            List<String> effectiveRoutingIds = productRoutingSteps.stream().filter(e -> YesOrNoEnum.YES.getCode().equals(e.getEnabled())).map(ProductRoutingStepVO::getRoutingId).distinct().collect(Collectors.toList());
            stopWatch.stop();

            stopWatch.start("核心处理逻辑");
            NewRoutingConvertor instance = NewRoutingConvertor.INSTANCE;
            List<NewRoutingDTO> insertBatchRoutingList = new ArrayList<>();
            List<NewRoutingDTO> updateBatchRoutingList = new ArrayList<>();
            CompletableFuture<Void> routingAssembleFuture = CompletableFuture.runAsync(() ->
                    productRoutingList.forEach(productRoutingVO -> {
                NewRoutingDTO newRoutingDTO = new NewRoutingDTO();
                if (Objects.nonNull(routingMap.get(productRoutingVO.getId()))) {
                    // 修改
                    NewRoutingPO odlRoutingPO = routingMap.get(productRoutingVO.getId());
                    newRoutingDTO = instance.po2Dto(odlRoutingPO);
                    transitionRouting(newRoutingDTO, productRoutingVO);
                    updateBatchRoutingList.add(newRoutingDTO);
                } else {
                    // 新增
                    newRoutingDTO.setId(productRoutingVO.getId());
                    transitionRouting(newRoutingDTO, productRoutingVO);
                    insertBatchRoutingList.add(newRoutingDTO);
                }
                if (!effectiveRoutingIds.contains(newRoutingDTO.getId())) {
                    newRoutingDTO.setEnabled(YesOrNoEnum.NO.getCode());
                    newRoutingDTO.setEffective(YesOrNoEnum.NO.getCode());
                }
            }), executor);

            // 物品工艺路径步骤转换产品工艺路径步骤
            NewRoutingStepConvertor stepInstance = NewRoutingStepConvertor.INSTANCE;
            List<NewRoutingStepDTO> insertBatchRoutingStepList = new ArrayList<>();
            List<NewRoutingStepDTO> updateBatchRoutingStepList = new ArrayList<>();
            CompletableFuture<Void> routingStepAssembleFuture = CompletableFuture.runAsync(() ->
                    productRoutingSteps.forEach(productRoutingStepVO -> {
                NewRoutingStepDTO newRoutingStepDTO = new NewRoutingStepDTO();
                if (Objects.nonNull(routingStepMap.get(productRoutingStepVO.getId()))) {
                    // 修改
                    NewRoutingStepVO oldRoutingStepVO = routingStepMap.get(productRoutingStepVO.getId());
                    newRoutingStepDTO = stepInstance.vo2Dto(oldRoutingStepVO);
                    transitionRoutingStep(newRoutingStepDTO, productRoutingStepVO);
                    updateBatchRoutingStepList.add(newRoutingStepDTO);
                } else {
                    // 新增
                    newRoutingStepDTO.setId(productRoutingStepVO.getId());
                    transitionRoutingStep(newRoutingStepDTO, productRoutingStepVO);
                    insertBatchRoutingStepList.add(newRoutingStepDTO);
                }
            }), executor);
            CompletableFuture.allOf(routingAssembleFuture, routingStepAssembleFuture).join();
            stopWatch.stop();
            // 数据入库
            stopWatch.start("数据入库");
            if (CollectionUtils.isNotEmpty(insertBatchRoutingList)) {
                this.doCreateBatchWithPrimaryKey(insertBatchRoutingList);
            }
            if (CollectionUtils.isNotEmpty(updateBatchRoutingList)) {
                this.doUpdateBatchSelective(updateBatchRoutingList);
            }
            if (CollectionUtils.isNotEmpty(insertBatchRoutingStepList)) {
                newRoutingStepService.doCreateBatchWithPrimaryKey(insertBatchRoutingStepList);
            }
            if (CollectionUtils.isNotEmpty(updateBatchRoutingStepList)) {
                newRoutingStepService.doUpdateBatchSelective(updateBatchRoutingStepList);
            }
            stopWatch.stop();

            // 将时间时间小于等于当前时间的设置为无效
            stopWatch.start("将时间时间小于等于当前时间的设置为无效");
            newRoutingDao.updateEnableForExpiryTime();
            newRoutingStepService.updateEnableForExpiryTime();
            stopWatch.stop();

            // 根据采购类别判断工艺路径是否有效
            stopWatch.start("根据采购类别判断工艺路径是否有效");
            checkEnableFlagByPoCategory(scenario);
            stopWatch.stop();
            log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
        } finally {
            if (!executor.isShutdown()) {
                try {
                    log.info("Shutting down IOExecutor...");
                    executor.shutdown();
                    if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                        log.warn("IOExecutor did not terminate in 10 seconds, forcing shutdown.");
                        executor.shutdownNow();
                        if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                            log.error("IOExecutor did not terminate even after shutdownNow.");
                        }
                    }
                    log.info("IOExecutor shut down successfully.");
                } catch (InterruptedException ie) {
                    log.error("Shutdown of IOExecutor interrupted.", ie);
                    executor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * 物品工艺路径步骤转产品工艺路径步骤
     * @param newRoutingStepDTO     路径步骤
     * @param productRoutingStepVO  产品路径步骤
     */
    private void transitionRoutingStep(NewRoutingStepDTO newRoutingStepDTO, ProductRoutingStepVO productRoutingStepVO) {
        newRoutingStepDTO.setId(productRoutingStepVO.getId());
        newRoutingStepDTO.setRoutingId(productRoutingStepVO.getRoutingId());
        Integer sequenceNo = productRoutingStepVO.getSequenceNo();
        newRoutingStepDTO.setSequenceNo(sequenceNo);
        newRoutingStepDTO.setPreRoutingStepSequenceNo(productRoutingStepVO.getPreRoutingStepSequenceNo());
        newRoutingStepDTO.setNextRoutingStepSequenceNo(productRoutingStepVO.getNextRoutingStepSequenceNo());
        newRoutingStepDTO.setYield(productRoutingStepVO.getYield());
        newRoutingStepDTO.setScrapStrategy(productRoutingStepVO.getScrapStrategy());
        newRoutingStepDTO.setPercentageScrapRate(productRoutingStepVO.getPercentageScrapRate());
        newRoutingStepDTO.setScrap(productRoutingStepVO.getScrap());
        newRoutingStepDTO.setPreProcessRatio(productRoutingStepVO.getPreProcessRatio());
        newRoutingStepDTO.setProcessingType(productRoutingStepVO.getProcessingType());
        newRoutingStepDTO.setConnectionTask(productRoutingStepVO.getConnectionTask());

        newRoutingStepDTO.setConnectionType(ConnectionTypeEnum.ES.getCode());
        String connectionType = productRoutingStepVO.getConnectionType();
        if (StringUtils.isNotBlank(connectionType)
                && EnumUtils.getCodeList(ConnectionTypeEnum.class).contains(connectionType)) {
            newRoutingStepDTO.setConnectionType(connectionType);
        }
        String stockPointCode = productRoutingStepVO.getStockPointCode();
        if (PARALLEL_OPERATION_MAP.containsKey(stockPointCode)
                && PARALLEL_OPERATION_MAP.get(stockPointCode).contains(sequenceNo)) {
            newRoutingStepDTO.setConnectionType(ConnectionTypeEnum.SSEE.getCode());
        }
        newRoutingStepDTO.setMaxConnectionDuration(productRoutingStepVO.getMaxConnectionDuration());
        newRoutingStepDTO.setMinConnectionDuration(productRoutingStepVO.getMinConnectionDuration());
        newRoutingStepDTO.setStandardStepId(productRoutingStepVO.getStandardStepId());
        newRoutingStepDTO.setCountingUnitId(productRoutingStepVO.getCountingUnitId());
        newRoutingStepDTO.setExpireReason(productRoutingStepVO.getExpireReason());
        newRoutingStepDTO.setRemark(productRoutingStepVO.getRemark());
        newRoutingStepDTO.setEnabled(productRoutingStepVO.getEnabled());
        newRoutingStepDTO.setRoutingRowId(productRoutingStepVO.getOperationSequenceId());
        newRoutingStepDTO.setEffective(productRoutingStepVO.getEffective());
        newRoutingStepDTO.setEffectiveBeginTime(productRoutingStepVO.getStartDate());
        newRoutingStepDTO.setEffectiveEndTime(productRoutingStepVO.getDisableDate());
        if (newRoutingStepDTO.getEffectiveEndTime() != null
                && newRoutingStepDTO.getEffectiveEndTime().before(new Date())) {
            newRoutingStepDTO.setEnabled(YesOrNoEnum.NO.getCode());
            newRoutingStepDTO.setEffective(YesOrNoEnum.NO.getCode());
        }
    }

    /**
     * 物品工艺路径转产品工艺路径
     * @param newRoutingDTO     路径
     * @param productRoutingVO  产品路径
     */
    private void transitionRouting(NewRoutingDTO newRoutingDTO, ProductRoutingVO productRoutingVO) {
        newRoutingDTO.setProductId(productRoutingVO.getProductId());
        newRoutingDTO.setStockPointId(productRoutingVO.getStockPointCode());
        newRoutingDTO.setRoutingCode(productRoutingVO.getRoutingCode());
        newRoutingDTO.setRoutingName(productRoutingVO.getRoutingName());
        newRoutingDTO.setPriority(productRoutingVO.getPriority());
        newRoutingDTO.setLeadTime(productRoutingVO.getLeadTime());
        newRoutingDTO.setProductionVersion(productRoutingVO.getProductionVersion());
        newRoutingDTO.setProductionCost(productRoutingVO.getProductionCost());
        newRoutingDTO.setMaxQuantity(productRoutingVO.getMaxQuantity());
        newRoutingDTO.setMinQuantity(productRoutingVO.getMinQuantity());
        newRoutingDTO.setLotSize(productRoutingVO.getLotSize());
        newRoutingDTO.setOutsourcingWorkHours(productRoutingVO.getOutsourcingWorkHours());
        newRoutingDTO.setEffectiveTime(productRoutingVO.getEffectiveTime());
        newRoutingDTO.setExpiryTime(productRoutingVO.getExpiryTime());
        newRoutingDTO.setCurrencyUnitId(productRoutingVO.getCurrencyUnitId());
        newRoutingDTO.setCountingUnitId(productRoutingVO.getCountingUnitId());
        newRoutingDTO.setExpireReason(productRoutingVO.getExpireReason());
        newRoutingDTO.setRoutingHeadId(productRoutingVO.getRoutingSequenceId());
        newRoutingDTO.setProductCode(productRoutingVO.getProductCode());
        newRoutingDTO.setRemark(productRoutingVO.getRemark());
        newRoutingDTO.setEnabled(productRoutingVO.getEnabled());
        newRoutingDTO.setEffective(productRoutingVO.getEffective());
        newRoutingDTO.setProductCode(productRoutingVO.getProductCode());
        if (newRoutingDTO.getExpiryTime() != null && newRoutingDTO.getExpiryTime().before(new Date())) {
        	newRoutingDTO.setEnabled(YesOrNoEnum.NO.getCode());
        	newRoutingDTO.setEffective(YesOrNoEnum.NO.getCode());
		}
    }

    @Override
    public void doCreateBatchWithPrimaryKey(List<NewRoutingDTO> list) {
        List<NewRoutingPO> newList = NewRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList->
                        newRoutingDao.insertBatchWithPrimaryKey(poList), 2500);
    }

    @Override
    public void doUpdateBatchSelective(List<NewRoutingDTO> list) {
        List<NewRoutingPO> newList = NewRoutingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, poList ->
                newRoutingDao.updateBatchSelective(poList), 2500);
    }

    @Override
    public void doDeleteRoutingDate(List<String> deleteProductRoutingList, List<String> deleteBomVersionList,
                                    List<String> deleteBomLineList, List<String> deleteProductRoutingStepList) {
        if (CollectionUtils.isNotEmpty(deleteProductRoutingList)) {
            // 删除对应的工艺路径，工艺路径步骤，输入物品，输出物品，候选资源
            List<ProductRoutingVO> productRoutingVOS =
                    productRoutingService.selectByRoutingSequenceIds(deleteProductRoutingList);
            List<String> routingIds = productRoutingVOS.stream().map(ProductRoutingVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(routingIds)) {
                newRoutingDao.doLogicDeleteBatchByIds(routingIds);
                newRoutingStepService.doLogicDeleteBatchByRoutingIds(routingIds);
                newRoutingStepInputService.doLogicDeleteBatchByRoutingIds(routingIds);
                newRoutingStepOutputService.doLogicDeleteBatchByRoutingIds(routingIds);
                newRoutingStepResourceService.doLogicDeleteBatchByRoutingIds(routingIds);
            }
        }
        if (CollectionUtils.isNotEmpty(deleteProductRoutingStepList)) {
            // 删除对应的工艺路径步骤，输入物品，输出物品，候选资源
            List<ProductRoutingStepVO> productRoutingStepVOS =
                    productRoutingStepService.selectByOperationSequenceIds(deleteProductRoutingStepList);
            List<String> routingStepIds = productRoutingStepVOS.stream().map(ProductRoutingStepVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(routingStepIds)) {
                newRoutingStepService.doLogicDeleteBatchByIds(routingStepIds);
                newRoutingStepInputService.doLogicDeleteBatchByRoutingStepIds(routingStepIds);
                newRoutingStepOutputService.doLogicDeleteBatchByRoutingStepIds(routingStepIds);
                newRoutingStepResourceService.doLogicDeleteBatchByRoutingStepIds(routingStepIds);
            }
        }
        if (CollectionUtils.isNotEmpty(deleteBomVersionList)) {
            // 删除多条输入物品
            List<ProductBomVersionVO> productBomVersionVOS =
                    mdsProductBomVersionService.selectByBillSequenceIds(deleteBomVersionList);
            List<String> productBomIds = productBomVersionVOS.stream().map(ProductBomVersionVO::getId)
                    .collect(Collectors.toList());
            List<ProductBomVO> productBomVOS = mdsProductBomService.selectByBillBomVersionIds(productBomIds);
            List<String> routingStepInputIds = productBomVOS.stream().map(ProductBomVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(routingStepInputIds)) {
                newRoutingStepInputService.doLogicDeleteBatchByIds(routingStepInputIds);
            }
        }
        if (CollectionUtils.isNotEmpty(deleteBomLineList)) {
            // 删除具体的输入物品
            List<ProductBomVO> productBomVOS = mdsProductBomService.selectByComponentSequenceIds(deleteBomLineList);
            List<String> routingStepInputIds = productBomVOS.stream().map(ProductBomVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(routingStepInputIds)) {
                newRoutingStepInputService.doLogicDeleteBatchByIds(routingStepInputIds);
            }
        }
    }

    @Override
    public Map<String, List<ProductRiskLevelVO>> selectProductRiskLevelMap(List<String> productCodeList) {
        Map<String, List<ProductRiskLevelVO>> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(productCodeList)) {
            return resultMap;
        }
        String scenario = SystemHolder.getScenario();
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange =
                ipsNewFeign.getScenarioBusinessRange(scenario, "PRODUCT_ORGANIZATION", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        if (StringUtils.isNotBlank(rangeData)) {
            List<String> rangeList = Arrays.asList(rangeData.split(","));
            return newRoutingDao.selectProductRiskLevel(rangeList, productCodeList).stream().collect(Collectors
                    .groupingBy(ProductRiskLevelVO::getProductCode));
        }
        return resultMap;
    }

    @Override
    public Map<String, List<NewRoutingValidateVO>> validateRoutingList() {
        CompletableFuture<List<NewRoutingValidateVO>> future1 = CompletableFuture.supplyAsync(() ->
                newRoutingDao.selectStepSequenceInputNotExist());
        CompletableFuture<List<NewRoutingValidateVO>> future2 = CompletableFuture.supplyAsync(() ->
                newRoutingDao.selectStepSequenceResourceNotExist());
        CompletableFuture<List<NewRoutingValidateVO>> future3 = CompletableFuture.supplyAsync(() ->
                newRoutingDao.selectStepSequenceResourceWithoutBeat());
        CompletableFuture<List<NewRoutingValidateVO>> future4 = CompletableFuture.supplyAsync(() ->
                newRoutingDao.selectStepSequenceWithoutYield());
        CompletableFuture<List<NewRoutingValidateVO>> future5 = CompletableFuture.supplyAsync(() ->
                newRoutingDao.selectProductionLineResourceNotMatch());
        Map<String, List<NewRoutingValidateVO>> resultMap = new HashMap<>();
        try {
            resultMap.put("1", getFutureResult(future1));
            resultMap.put("2", getFutureResult(future2));
            resultMap.put("3", getFutureResult(future3));
            resultMap.put("4", getFutureResult(future4));
            resultMap.put("5", getFutureResult(future5));
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("校验工艺路径失败", e);
            throw new BusinessException("校验工艺路径失败", e.getMessage());
        }
        return resultMap;
    }

    /**
     * 封装获取 Future 结果的逻辑
     */
    private <T> T getFutureResult(CompletableFuture<T> future) {
        try {
            return future.get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断状态
            log.error("异步任务被中断", e);
            throw new BusinessException("异步任务被中断");
        } catch (ExecutionException e) {
            log.error("异步任务执行异常", e.getCause());
            throw new BusinessException("异步任务执行异常", e.getCause());
        }
    }

    @SneakyThrows
    @Override
    public void downloadErrorData(HttpServletResponse response) {
        List<NewRoutingValidateExcelDTO> newRoutingValidateExcelDTOS = new ArrayList<>();
        Map<String, List<NewRoutingValidateVO>> stringListMap = validateRoutingList();
        if (stringListMap.containsKey("1")) {
            stringListMap.get("1").forEach(t -> {
                NewRoutingValidateExcelDTO newRoutingValidateExcelDTO = new NewRoutingValidateExcelDTO();
                newRoutingValidateExcelDTO.setRoutingCode(t.getRoutingCode());
                newRoutingValidateExcelDTO.setRoutingName(t.getRoutingName());
                newRoutingValidateExcelDTO.setProductCode(t.getProductCode());
                newRoutingValidateExcelDTO.setErrorType("第一道工序无输入");
                newRoutingValidateExcelDTOS.add(newRoutingValidateExcelDTO);
            });
        }
        if (stringListMap.containsKey("2")) {
            stringListMap.get("2").forEach(t -> {
                NewRoutingValidateExcelDTO newRoutingValidateExcelDTO = new NewRoutingValidateExcelDTO();
                newRoutingValidateExcelDTO.setRoutingCode(t.getRoutingCode());
                newRoutingValidateExcelDTO.setRoutingName(t.getRoutingName());
                newRoutingValidateExcelDTO.setProductCode(t.getProductCode());
                newRoutingValidateExcelDTO.setErrorType("每个工序必须要有候选资源");
                newRoutingValidateExcelDTOS.add(newRoutingValidateExcelDTO);
            });
        }
        if (stringListMap.containsKey("3")) {
            stringListMap.get("3").forEach(t -> {
                NewRoutingValidateExcelDTO newRoutingValidateExcelDTO = new NewRoutingValidateExcelDTO();
                newRoutingValidateExcelDTO.setRoutingCode(t.getRoutingCode());
                newRoutingValidateExcelDTO.setRoutingName(t.getRoutingName());
                newRoutingValidateExcelDTO.setProductCode(t.getProductCode());
                newRoutingValidateExcelDTO.setErrorType("每个候选资源必须存在节拍");
                newRoutingValidateExcelDTOS.add(newRoutingValidateExcelDTO);
            });
        }
        if (stringListMap.containsKey("4")) {
            stringListMap.get("4").forEach(t -> {
                NewRoutingValidateExcelDTO newRoutingValidateExcelDTO = new NewRoutingValidateExcelDTO();
                newRoutingValidateExcelDTO.setRoutingCode(t.getRoutingCode());
                newRoutingValidateExcelDTO.setRoutingName(t.getRoutingName());
                newRoutingValidateExcelDTO.setProductCode(t.getProductCode());
                newRoutingValidateExcelDTO.setErrorType("每个工序步骤必须存在成品率且不为0");
                newRoutingValidateExcelDTOS.add(newRoutingValidateExcelDTO);
            });
        }
        if (stringListMap.containsKey("5")) {
            stringListMap.get("5").forEach(t -> {
                NewRoutingValidateExcelDTO newRoutingValidateExcelDTO = new NewRoutingValidateExcelDTO();
                newRoutingValidateExcelDTO.setRoutingCode(t.getRoutingCode());
                newRoutingValidateExcelDTO.setRoutingName(t.getRoutingName());
                newRoutingValidateExcelDTO.setProductCode(t.getProductCode());
                newRoutingValidateExcelDTO.setErrorType("产线与工序代码:" + t.getSequenceNo() + "不匹配");
                newRoutingValidateExcelDTOS.add(newRoutingValidateExcelDTO);
            });
        }
        String fileName = URLEncoder.encode(ObjectTypeEnum.NEW_ROUTING.getDesc() + "校验错误数据", "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), NewRoutingValidateExcelDTO.class)
                .sheet(ObjectTypeEnum.NEW_ROUTING.getDesc()).doWrite(newRoutingValidateExcelDTOS);
    }

	@Override
	public void checkEnableFlagByPoCategory(String scenario) {
		// 1.查询所有的工艺路径数据信息
        Map<String, List<NewRoutingVO>> routingVOMap = newRoutingDao.selectForCheckEnableFlag()
                .stream().collect(Collectors.groupingBy(NewRoutingVO::getProductCode));
		// 2.按照产品编码分组，将 size>1 产品编码提取出来
		List<String> productCodes = new ArrayList<>();
		for (Entry<String, List<NewRoutingVO>> routingEntry : routingVOMap.entrySet()) {
			String productCode = routingEntry.getKey();
			if(routingEntry.getValue().size() > 1) {
				productCodes.add(productCode);
			}
		}
		// 3.获取这些产品编码
        if (CollectionUtils.isEmpty(productCodes)) {
            return;
        }
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(
                scenario, "SALE_ORGANIZATION", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
		// 4.查询产品数据信息
		Map<String, Object> productParams = new HashMap<>();
		productParams.put("productCodes", productCodes);
		productParams.put("stockPointCode", rangeData);
		List<NewProductStockPointVO> productInfoList = newProductStockPointService.selectByParams(productParams);
        Map<String, String> productMap = productInfoList.stream().collect(Collectors
                .toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getPoCategory, (v1, v2) -> v1));
		// 5.处理有效，无效的工艺路径数据
		List<NewRoutingPO> updateRoutingList = new ArrayList<>();
		for (Entry<String, List<NewRoutingVO>> routingEntry : routingVOMap.entrySet()) {
			String productCode = routingEntry.getKey();
			String poCategory = productMap.get(productCode);
			List<NewRoutingVO> routingList = routingEntry.getValue();
            if (routingList.size() > 1 && StringUtils.isNotEmpty(poCategory)) {
                String[] poCategories = poCategory.split("\\.");
                String stockPointCode = "";
                if (poCategories.length > 1) {
                    stockPointCode = poCategories[1];
                }
                List<String> stockPointCodes = routingList.stream().map(NewRoutingVO::getStockPointId)
                        .distinct().collect(Collectors.toList());
                if (StringUtils.isEmpty(stockPointCode) || !stockPointCodes.contains(stockPointCode)) {
                    continue;
                }
                for (NewRoutingVO routing : routingList) {
                    NewRoutingPO update = new NewRoutingPO();
                    update.setId(routing.getId());
                    update.setModifyTime(new Date());
                    if (stockPointCode.equals(routing.getStockPointId())
                            && YesOrNoEnum.NO.getCode().equals(routing.getEnabled())) {
                        // 有效（无效改有效）
                        update.setEnabled(YesOrNoEnum.YES.getCode());
                        updateRoutingList.add(update);
                    } else if (!stockPointCode.equals(routing.getStockPointId())
                            && YesOrNoEnum.YES.getCode().equals(routing.getEnabled())) {
                        // 无效（有效改无效）
                        update.setEnabled(YesOrNoEnum.NO.getCode());
                        updateRoutingList.add(update);
                    }
                }
            }
        }
        // 批量处理有效无效数据
        if (CollectionUtils.isNotEmpty(updateRoutingList)) {
            BulkOperationUtils.bulkUpdateOrCreate(updateRoutingList, newRoutingDao::updateBatchSelective, 2500);
        }
    }

	@Override
	public void updateEnableForExpiryTime() {
		newRoutingDao.updateEnableForExpiryTime();
	}

    @Override
    public List<String> selectRoutingIdsByProductCode(String productCodeList) {
        return newRoutingDao.selectRoutingIdsByProductCode(productCodeList);
    }

    public List<String> selectEnabledProductCodes(List<String> productCodes) {
        return newRoutingDao.selectEnabledProductCodes(productCodes);
    }

}