package com.yhl.scp.mrp.supplier.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.enums.YesOrNoEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>MaterialSupplierPurchaseExcelDTO</code>
 * <p>
 * 材料与供应商关系DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-27 14:56:04
 */
@ApiModel(value = "材料与供应商关系DTO")
@Data
public class MaterialSupplierPurchaseExcelDTO implements Serializable {

    private static final long serialVersionUID = -3466626557482067740L;

    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @ExcelProperty(value = "组织")
    private String stockPointCode;

    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @ExcelProperty(value = "库存点说明")
    private String stockPointName;

    /**
     * 物料
     */
    @ApiModelProperty(value = "物料")
    @ExcelProperty(value = "物料")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @ExcelProperty(value = "物料名称")
    private String materialName;

    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    @ExcelProperty(value = "物料类型")
    private String materialType;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @ExcelProperty(value = "物料属性")
    private String materialProperty;

    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    @ExcelProperty(value = "计划员")
    private String planner;

    /**
     * 最小起订量
     */
    @ApiModelProperty(value = "最小起订量")
    @ExcelProperty(value = "最小起订量")
    private BigDecimal minOrderQty;

    /**
     * 包装批量
     */
    @ApiModelProperty(value = "包装批量")
    @ExcelProperty(value = "包装批量")
    private BigDecimal packageLot;

    /**
     * 是否专用
     */
    @ApiModelProperty(value = "是否专用")
    @ExcelProperty(value = "是否专用")
    private String specific;

    /**
     * 要货计划锁定期(天)
     */
    @ApiModelProperty(value = "要货计划锁定期(天)")
    @ExcelProperty(value = "要货计划锁定期(天)")
    private BigDecimal requestCargoPlanLockDay;

    /**
     * 订单下达提前期(天)
     */
    @ApiModelProperty(value = "订单下达提前期(天)")
    @ExcelProperty(value = "订单下达提前期(天)")
    private BigDecimal orderPlacementLeadTimeDay;

    /**
     * 要货模式
     */
    @ApiModelProperty(value = "要货模式")
    @ExcelProperty(value = "要货模式")
    private String demandPattern;

    /**
     * 采购比例
     */
    @ApiModelProperty(value = "采购比例")
    @ExcelProperty(value = "采购比例")
    private String purchaseRatio;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    /**
     * 最小安全库存天数
     */
    @ApiModelProperty(value = "最小安全库存天数")
    @ExcelProperty(value = "最小安全库存天数")
    private BigDecimal minInventoryDays;

    /**
     * 目标安全库存天数
     */
    @ApiModelProperty(value = "目标安全库存天数")
    @ExcelProperty(value = "标准安全库存天数")
    private BigDecimal goalSafetyInventoryDays;

    /**
     * 最大安全库存天数
     */
    @ApiModelProperty(value = "最大安全库存天数")
    @ExcelProperty(value = "最大安全库存天数")
    private BigDecimal maxInventoryDays;

    /**
     * 是否寄售
     */
    @ApiModelProperty(value = "是否寄售")
    @ExcelProperty(value = "是否寄售")
    private String consignment;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    @ExcelProperty(value = "是否启用")
    private String enabled;
}
