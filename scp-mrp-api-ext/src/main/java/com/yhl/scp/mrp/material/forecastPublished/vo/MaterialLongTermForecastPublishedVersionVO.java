package com.yhl.scp.mrp.material.forecastPublished.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialLongTermForecastPublishedVersionVO</code>
 * <p>
 * 材料长期预测版本表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-07 14:53:48
 */
@ApiModel(value = "材料长期预测版本表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialLongTermForecastPublishedVersionVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 230351513588868059L;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private String versionCode;

    @Override
    public void clean() {

    }

}
