package com.yhl.scp.mrp.originalFilm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName OriginalFilmDemandConsultParams
 * @Description TODO
 * @Date 2025-03-24 14:35:54
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Data
public class OriginalFilmDemandConsultParam {

    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    private String productThickness;

    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String productColor;

    /**
     * 原片征询版本ID
     */
    @ApiModelProperty(value = "原片征询版本ID")
    private String originalFilmDemandConsultVersionId;

    /**
     * 需求月份
     */
    @ApiModelProperty(value = "需求月份")
    private String demandedMonth;

    @ApiModelProperty(value = "每页大小")
    private int pageSize = 10;

    @ApiModelProperty(value = "第几页")
    private int pageNum = 1;

    @ApiModelProperty(value = "排序字段")
    private String orderBy;
}
