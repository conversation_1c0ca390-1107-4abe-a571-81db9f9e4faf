package com.yhl.scp.mrp.material.plan.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mrp.inventory.dto.MaterialPlanInventoryShiftParamDTO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedDTO;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDTO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftPageVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftVO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequirementDetailDTO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>NoGlassInventoryShiftService</code>
 * <p>
 * 非原片库存推移表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10 15:14:26
 */
public interface NoGlassInventoryShiftService extends BaseService<NoGlassInventoryShiftDTO, NoGlassInventoryShiftVO> {

    /**
     * 查询所有
     *
     * @return list {@link NoGlassInventoryShiftVO}
     */
    List<NoGlassInventoryShiftVO> selectAll();

    void doDeleteByMaterialPlanVersionId(String materialPlanVersionId);

    PageInfo<MaterialPlanInventoryShiftPageVO> selectPage2(MaterialPlanInventoryShiftParamDTO materialPlanInventoryShiftParamDTO);

    BaseResponse<String> checkAdjust(NoGlassInventoryShiftDTO noGlassInventoryShiftDTO);

    BaseResponse<String> doAdjustData(NoGlassInventoryShiftDTO noGlassInventoryShiftDTO);

    void doPublish();

    /**
     *  计算推移调整量
     * @param dateIndex 日期索引
     * @param lockPeriod 锁定期
     * @param endingInventory 期末库存
     * @param openingInventory 期初库存
     * @param demandQuantity 毛需求
     * @param satisfyStandardDemand 毛需求已满足量
     * @param usedAsReplaceQuantity 替代其他需求
     * @param supplyQuantitySum 供应量(计划采购供应、在途供应)
     * @param minStockQuantity 最小安全库存数量
     * @param standardStockQuantity 标准安全库存数量
     * @return 推移调整量
     */
    BigDecimal calculateDisplacementQuantity(int dateIndex, int lockPeriod, BigDecimal endingInventory, BigDecimal openingInventory, BigDecimal demandQuantity,
                                             BigDecimal satisfyStandardDemand, BigDecimal usedAsReplaceQuantity,
                                             BigDecimal supplyQuantitySum, BigDecimal minStockQuantity, BigDecimal standardStockQuantity, BigDecimal maxStockQuantity,
                                             BigDecimal beforeDaySupplyQuantitySum);

    /**
     *  计算预警
     * @param endingInventory 期末库存
     * @param minStockQuantity 最小安全安全数量
     * @param maxStockQuantity 最高安全库存数量
     * @return 预警信息
     */
    String verificationWarning(BigDecimal endingInventory, BigDecimal minStockQuantity, BigDecimal maxStockQuantity);

    /**
     * 计算调减量调整
     * @param planAdjustQuantity 调整量
     * @param planPurchaseSupplyQuantity 计划采购供应
     * @return 调减量
     */
    BigDecimal reductionPlanAdjustQuantity(BigDecimal planAdjustQuantity, BigDecimal planPurchaseSupplyQuantity);

    List<String> getMaterialCodeByFgProductIdList(List<String> fgProductIdList);

    List<NoGlassInventoryShiftVO> selectNoGlassMaterialShit(String latestVersionId, List<String> productCodeList, String startTime, String endTime);

    List<NoGlassInventoryShiftVO> selectVOByParams(Map<String, Object> detailParams);

    Date getLockPeriodStartTime(String materialPlanVersionId);

    void deDeleteByProductCodeList(List<String> planUserProductCodeList);

    Date getLastCreateTime(String userId);

    Map<String, String> getVehicleModelCode(List<String> productCodeList);
}
