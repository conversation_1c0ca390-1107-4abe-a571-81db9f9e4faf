package com.yhl.scp.mrp.material.plan.dto;

import java.util.Date;
import java.util.List;

/**
 * @ClassName MrpCalcParams
 * @Description TODO
 * @Date 2025-07-29 11:10:10
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
public class MrpCalcParamDTO {

    private String calcType;

    private String mrpCalcDate;

    private String mpsDemandRule;

    private String mrpCalcProductCode;

    public String getCalcType() {
        return calcType;
    }

    public void setCalcType(String calcType) {
        this.calcType = calcType;
    }

    public String getMrpCalcDate() {
        return mrpCalcDate;
    }

    public void setMrpCalcDate(String mrpCalcDate) {
        this.mrpCalcDate = mrpCalcDate;
    }

    public String getMpsDemandRule() {
        return mpsDemandRule;
    }

    public void setMpsDemandRule(String mpsDemandRule) {
        this.mpsDemandRule = mpsDemandRule;
    }

    public String getMrpCalcProductCode() {
        return mrpCalcProductCode;
    }

    public void setMrpCalcProductCode(String mrpCalcProductCode) {
        this.mrpCalcProductCode = mrpCalcProductCode;
    }
}
