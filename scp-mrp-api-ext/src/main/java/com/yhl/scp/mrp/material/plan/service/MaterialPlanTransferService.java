package com.yhl.scp.mrp.material.plan.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanTransferDTO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanTransferVO;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPlanTransferService</code>
 * <p>
 * 调拨计划应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:13:56
 */
public interface MaterialPlanTransferService extends BaseService<MaterialPlanTransferDTO, MaterialPlanTransferVO> {

    /**
     * 查询所有
     *
     * @return list {@link MaterialPlanTransferVO}
     */
    List<MaterialPlanTransferVO> selectAll();

    List<MaterialPlanTransferVO> getLatestPublisheData();

    /**
     * 柜号下拉
     *
     * @param id 主键id
     * @return 柜号
     */
    List<LabelValue<String>> containerNumberDown(String id);

    /**
     * 发布
     *
     * @param ids id集合
     */
    BaseResponse<Void> publish(List<String> ids);

    void doCreateBatch02(List<MaterialPlanTransferDTO> list);

    void doUpdateBatchSelective(List<MaterialPlanTransferDTO> list);

    List<MaterialPlanTransferVO> selectVOByParams(Map<String, Object> params);

    /**
     * 发货地下拉
     *
     * @param stockPointNameFrom
     * @return
     */
    List<LabelValue<String>> stockPointNameFromDropDown(String stockPointNameFrom);

    List<MaterialPlanTransferVO> pageMt(Pagination pagination, String sortParam, String queryCriteriaParam);
    BaseResponse<Void> doCancelled(List<String> ids);

}
