package com.yhl.scp.mrp.material.plan.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanVersionDTO;
import com.yhl.scp.mrp.material.plan.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanVersionVO;

import java.util.List;

/**
 * <code>MaterialPlanVersionService</code>
 * <p>
 * 物料计划版本应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:07:27
 */
public interface MaterialPlanVersionService extends BaseService<MaterialPlanVersionDTO, MaterialPlanVersionVO> {

    /**
     * 查询所有
     *
     * @return list {@link MaterialPlanVersionVO}
     */
    List<MaterialPlanVersionVO> selectAll();

    MaterialPlanVersionVO selectLatestVersion(String materialType, String userId);

    String selectLatestVersionId();

}
