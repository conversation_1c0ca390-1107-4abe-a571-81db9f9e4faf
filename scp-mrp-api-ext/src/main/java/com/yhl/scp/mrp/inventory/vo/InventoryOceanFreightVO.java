package com.yhl.scp.mrp.inventory.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>InventoryOceanFreightVO</code>
 * <p>
 * 浮法海运_提单号VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04 10:53:04
 */
@ApiModel(value = "浮法海运_提单号VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryOceanFreightVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 738473642091395996L;

        /**
     * 提单号
     */
        @ApiModelProperty(value = "提单号")
    @FieldInterpretation(value = "提单号")
    private String blNum;
        /**
     * 箱号
     */
        @ApiModelProperty(value = "箱号")
    @FieldInterpretation(value = "箱号")
    private String boxNum;
    /**
     * 船公司
     */
    @ApiModelProperty(value = "船公司")
    @FieldInterpretation(value = "船公司")
    private String carrierCode;
        /**
     * 版本号
     */
        @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private Integer versionValue;
        /**
     * 结束时间
     */
        @ApiModelProperty(value = "结束时间")
    @FieldInterpretation(value = "结束时间")
    private Date endTime;

    @Override
    public void clean() {

    }

}
