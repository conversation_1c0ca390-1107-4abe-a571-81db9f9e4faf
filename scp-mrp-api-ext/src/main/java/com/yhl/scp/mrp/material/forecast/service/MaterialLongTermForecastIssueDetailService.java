package com.yhl.scp.mrp.material.forecast.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.material.forecast.dto.MaterialLongTermForecastIssueDetailDTO;
import com.yhl.scp.mrp.material.forecast.vo.MaterialLongTermForecastIssueDetailVO;

import java.util.List;

/**
 * <code>MaterialLongTermForecastIssueDetailService</code>
 * <p>
 * 材料长期预测下发明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-23 15:46:19
 */
public interface MaterialLongTermForecastIssueDetailService extends BaseService<MaterialLongTermForecastIssueDetailDTO, MaterialLongTermForecastIssueDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link MaterialLongTermForecastIssueDetailVO}
     */
    List<MaterialLongTermForecastIssueDetailVO> selectAll();

}
