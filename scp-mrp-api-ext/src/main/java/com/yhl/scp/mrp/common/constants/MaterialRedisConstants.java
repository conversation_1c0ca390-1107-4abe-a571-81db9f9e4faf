package com.yhl.scp.mrp.common.constants;

/**
 * @ClassName MaterialRedisConstants
 * @Description TODO
 * @Date 2025-01-08 15:32:46
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
public class MaterialRedisConstants {

    /**
     * 要货计划号序列Key, 例：供应商代码#PLAN_NEED
     */
    public static final String PLAN_NEED_SERIAL_NUMBER_KEY = "PLAN_NEED";

    /**
     * MRP运行Key, 例：MRP_COMPUTE#noGlass
     */
    public static final String MRP_COMPUTE_KEY = "MRP_COMPUTE";

    /**
     * 非原片MRP结果发布key
     */
    public static final String NO_GLASS_MRP_PUBLISHED = "NO_GLASS_MRP_PUBLISHED";


    public static final String GLASS_MRP_PUBLISHED = "GLASS_MRP_PUBLISHED";

    /**
     * 材料风险等级MRP运行Key, 例：userId#MRP_RISK
     */
    public static final String MATERIAL_RISK_LEVEL_RISK_KEY = "MATERIAL_RISK";

    /**
     * 要货计划下发Key, 例：userId#PLAN_NEED_ISSUE
     */
    public static final String MATERIAL_PLAN_NEED_ISSUE = "PLAN_NEED_ISSUE";

    /**
     * 长期预测发布Key, 例：userId#LONG_TERM_FORECAST_PUBLISH
     */
    public static final String LONG_TERM_FORECAST_PUBLISH = "LONG_TERM_FORECAST_PUBLISH";

    /**
     *  超期呆滞库存发布Key, 例：userId#INVENTORY_OVERDUE_STAGNANT_PUBLISH
     */
    public static final String INVENTORY_OVERDUE_STAGNANT_PUBLISH = "INVENTORY_OVERDUE_STAGNANT_PUBLISH";

    /**
     * MRP计算使用毛需求版本key, 例：userId#MRP_COMPUTE_GROSS_DEMAND_VERSION_CODE
     */
    public static final String MRP_COMPUTE_GROSS_DEMAND_VERSION_CODE = "MRP_COMPUTE_GROSS_DEMAND_VERSION_CODE";
}
