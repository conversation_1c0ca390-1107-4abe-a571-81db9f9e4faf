package com.yhl.scp.mrp.material.plan.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialPlanVersionDTO</code>
 * <p>
 * 物料计划版本DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:07:25
 */
@ApiModel(value = "物料计划版本DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPlanVersionDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -69232424753616148L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private String versionCode;
    /**
     * 主生产计划版本号
     */
    @ApiModelProperty(value = "主生产计划版本号")
    private String masterPlanVersionCode;
    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
    private String publishStatus;
    /**
     * 版本类型
     */
    @ApiModelProperty(value = "版本类型")
    private String versionType;
    /**
     * 材料类型
     */
    @ApiModelProperty(value = "材料类型")
    private String materialType;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 发布人
     */
    @ApiModelProperty(value = "发布人")
    private String publisher;
    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    private Date publishTime;

}
