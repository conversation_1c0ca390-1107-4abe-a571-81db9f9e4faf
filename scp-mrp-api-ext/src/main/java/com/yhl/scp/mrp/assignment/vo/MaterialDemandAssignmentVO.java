package com.yhl.scp.mrp.assignment.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialDemandAssignmentVO</code>
 * <p>
 * 材料需求分配表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-27 15:56:22
 */
@ApiModel(value = "材料需求分配表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialDemandAssignmentVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 737499464358270021L;

    /**
     * 材料需求ID
     */
    @ApiModelProperty(value = "材料需求ID")
    @FieldInterpretation(value = "材料需求ID")
    private String materialDemandId;
    /**
     * 采购订单ID
     */
    @ApiModelProperty(value = "采购订单ID")
    @FieldInterpretation(value = "采购订单ID")
    private String purchaseOrderInfoId;
    /**
     * 分配数量
     */
    @ApiModelProperty(value = "分配数量")
    @FieldInterpretation(value = "分配数量")
    private BigDecimal assignmentQuantity;
    /**
     * 要货模式
     */
    @ApiModelProperty(value = "要货模式")
    @FieldInterpretation(value = "要货模式")
    private String demandPattern;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    @Override
    public void clean() {

    }

}
