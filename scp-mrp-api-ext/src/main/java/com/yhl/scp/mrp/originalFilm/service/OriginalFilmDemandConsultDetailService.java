package com.yhl.scp.mrp.originalFilm.service;

import java.util.List;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultDetailDTO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultDetailVO;

/**
 * <code>OriginalFilmDemandConsultDetailService</code>
 * <p>
 * 原片需求征询明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 10:05:11
 */
public interface OriginalFilmDemandConsultDetailService extends BaseService<OriginalFilmDemandConsultDetailDTO, OriginalFilmDemandConsultDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link OriginalFilmDemandConsultDetailVO}
     */
    List<OriginalFilmDemandConsultDetailVO> selectAll();

    /**
     * 通过原片需求汇总ids删除原片需求汇总明细数据
     * @param idList
     */
	void deleteByConsultSummaryIds(List<String> idList);

	/**
	 * 获取车型枚举
	 * @return
	 */
	List<LabelValue<String>> selectVehicleModels();

	/**
	 * 通过车型获取对应的成品物料信息枚举
	 * @param vehicleModelCode
	 * @return
	 */
	List<LabelValue<String>> selectFinishProducts(String vehicleModelCode);

	/**
	 * 通过成品物料编码获取对应的原料
	 * @param productId
	 * @return
	 */
	List<LabelValue<String>> selectOrginalFilmProducts(String productId);

	List<OriginalFilmDemandConsultDetailVO> selectByDemandSourceIds(List<String> consultSummaryIds);

}
