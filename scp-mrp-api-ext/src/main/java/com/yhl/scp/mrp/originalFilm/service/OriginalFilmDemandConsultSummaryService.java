package com.yhl.scp.mrp.originalFilm.service;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultParam;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultSummaryDTO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultSummaryVO;

/**
 * <code>OriginalFilmDemandConsultSummaryService</code>
 * <p>
 * 原片需求征询汇总应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 09:26:46
 */
public interface OriginalFilmDemandConsultSummaryService extends BaseService<OriginalFilmDemandConsultSummaryDTO, OriginalFilmDemandConsultSummaryVO> {

    /**
     * 查询所有
     *
     * @return list {@link OriginalFilmDemandConsultSummaryVO}
     */
    List<OriginalFilmDemandConsultSummaryVO> selectAll();

	void insertWithPrimaryKey(OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO);

	BaseResponse<Void> doDemandCalculation(OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO);

	void export(HttpServletResponse response, Pagination pagination, String sortParam, String queryCriteriaParam,
			String exportType);

	List<LabelValue<String>> selectAllColor();

	List<OriginalFilmDemandConsultSummaryVO> selectByPage2(OriginalFilmDemandConsultParam originalFilmDemandConsultParam);

}
