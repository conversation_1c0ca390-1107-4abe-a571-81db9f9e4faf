package com.yhl.scp.mrp.inventory.service;

import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpOriginalFilmFFInventory;
import com.yhl.scp.mrp.inventory.dto.InventoryAlternativeRelationshipDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryFloatGlassDetailDTO;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassDetailVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>InventoryFloatGlassDetailService</code>
 * <p>
 * 原片浮法库存批次明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:31:57
 */
public interface InventoryFloatGlassDetailService extends BaseService<InventoryFloatGlassDetailDTO, InventoryFloatGlassDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link InventoryFloatGlassDetailVO}
     */
    List<InventoryFloatGlassDetailVO> selectAll();

    List<InventoryFloatGlassDetailVO> selectByPage(Pagination pagination, String sortParam,
                                                   String queryCriteriaParam, String overdueSort);

    /**
     * 浮法库存可用查找
     *
     * @param productCode           物料编码
     * @param alternativeType       替代类型
     * @param cuttingRatePercentage 切裁率百分比
     * @return 可作为替代原片
     */
    List<InventoryAlternativeRelationshipVO> availableSearch(String productCode, String alternativeType, String cuttingRatePercentage);

    /**
     * 原片替代（浮法）
     *
     * @param list 替代关系
     * @return BaseResponse
     */
    BaseResponse<Void> alternative(List<InventoryAlternativeRelationshipDTO> list);
    void doCreateBatchWithPartition(List<InventoryFloatGlassDetailDTO> list,Integer pageSize);
    void doUpdateBatchWithPartition(List<InventoryFloatGlassDetailDTO> list,Integer pageSize);

    BaseResponse<Void>  syncOriginalFilmFFInventory(String tenantId);

    BaseResponse<Void> sync(String scenario,List<ErpOriginalFilmFFInventory> o);

    List<InventoryFloatGlassDetailVO> selectProductInventory();
    int doDeleteByStockPointCode(String stockPointCode);

    void exportTemplateOutsourcing(HttpServletResponse response);

    void uploadOutsourcing(MultipartFile file);

}
