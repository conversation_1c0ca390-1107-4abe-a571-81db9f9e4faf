package com.yhl.scp.biz.common.aop;

import cn.hutool.json.JSONUtil;
import com.yhl.scp.biz.common.util.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 请求日志切面
 * </p>
 *
 * <AUTHOR>
 */
@Aspect
@Slf4j
@Component
public class RequestLogAspect {

    @Pointcut("within(@org.springframework.cloud.openfeign.FeignClient *)")
    public void feignClientPointCut() {
    }

    @Pointcut("execution(* com.yhl..controller..*(..))" +
            " && !execution(* com.yhl..*FeignController.*(..))" +
            " && !execution(* com.yhl..controller..setReqAndRes(..))" +
            " && !execution(* com.yhl..controller..exceptionHandler(..))" +
            " && !execution(* com.yhl..controller..initBinder(..))")
    public void pointCut() {
    }

    @Before("pointCut() && !feignClientPointCut()")
    public void doBefore(JoinPoint joinPoint) {
        ServletRequestAttributes attributes = (ServletRequestAttributes)
                RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();
        try {
            if (!HttpUtils.isMonitorRequest(request.getRequestURI()) && log.isDebugEnabled()) {
                String methodName = joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName();
                log.debug("==> 接收到请求,请求URL:{},请求方法:{},处理方法:{}", request.getRequestURL().toString(),
                        request.getMethod(),
                        methodName);
                log.debug("==> 请求头:{}", JSONUtil.toJsonStr(this.getHeader(request)));
                log.debug("==> 请求参数:{}", JSONUtil.toJsonStr(this.getRequestParams(joinPoint)));
            }
        } catch (Exception e) {
        }
    }


//    @Around("pointCut()")
//    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//        Object result = proceedingJoinPoint.proceed();
//        stopWatch.stop();
//        try {
//            if (!HttpUtil.isMonitorRequest() && log.isDebugEnabled()) {
////                log.debug("<== 请求处理完成!返回数据:{},处理时长:{}ms!", JSONUtil.toJsonStr(result), stopWatch.getTotalTimeMillis());
//            }
//        } catch (Exception e) {
//        }
//        return result;
//    }
//
//    @After("pointCut()")
//    public void doAfter(JoinPoint joinPoint) {
//    }

    /**
     * 获取入参
     *
     * @param joinPoint
     * @return
     */
    private Map<String, Object> getRequestParams(JoinPoint joinPoint) {
        Map<String, Object> requestParams = new HashMap<>();
        //参数名
        String[] paramNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        //参数值
        Object[] paramValues = joinPoint.getArgs();
        for (int i = 0; i < paramNames.length; i++) {
            Object value = paramValues[i];
            //如果是文件对象
            if (value instanceof MultipartFile) {
                MultipartFile file = (MultipartFile) value;
                value = file.getOriginalFilename();
            }
            if (value instanceof HttpServletRequest || value instanceof HttpServletResponse
                    || value instanceof MultipartFile[]) {
                value = "[二进制]";
            }
            requestParams.put(paramNames[i], value);
        }
        return requestParams;
    }

    /**
     * 获取报文体
     *
     * @param request
     * @return
     */
    private Map<String, Object> getHeader(HttpServletRequest request) {
        Map<String, Object> headerParams = new HashMap<>(1);
        // 获取所有请求头名称并遍历
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headerParams.put(headerName, headerValue);
        }
        return headerParams;
    }
}
