package com.yhl.scp.biz.common.aop;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.yhl.platform.common.ComponentSystemHolder;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.biz.common.mqProducer.RedisMessageProducer;
import com.yhl.scp.biz.common.params.BusinessMonitorLogParam;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <code>BusinessMonitorLogAspect</code>
 * <p>
 * BusinessMonitorLog
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24 11:11:55
 */
@Aspect
@Component
@Slf4j
public class BusinessMonitorLogAspect {

    @Resource
    private RedisMessageProducer redisMessageProducer;

    private static final String QUEUE_NAME = "businessMonitorLogQueue";

    private static final ThreadLocal<Date> stopWatchThreadLocal = new ThreadLocal<>();

    @Pointcut("@annotation(com.yhl.scp.biz.common.annotation.BusinessMonitorLog)")
    public void pointCut() {
    }

    @Before("pointCut()")
    public void doBefore(@SuppressWarnings("unused") JoinPoint joinPoint) {
        stopWatchThreadLocal.set(new Date());
    }

    @AfterReturning(value = "pointCut()", returning = "result")
    public void doAfter(JoinPoint joinPoint, @SuppressWarnings("unused") Object result) {
        Date startDate = stopWatchThreadLocal.get();
        stopWatchThreadLocal.remove();

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        BusinessMonitorLog annotation = signature.getMethod().getAnnotation(BusinessMonitorLog.class);
        String userid = getUserId(joinPoint, signature);
        Map<String, Object> header = getHeader();
        BusinessMonitorLogParam businessMonitorLogParam = assembleBusinessMonitorLogParam(header,
                annotation.businessCode(), annotation.businessFrequency(), annotation.operationMethod(),
                startDate, "SUCCESS", null, userid, annotation.moduleCode());
        redisMessageProducer.sendMessage(QUEUE_NAME, JSONUtil.toJsonStr(businessMonitorLogParam));
    }

    @AfterThrowing(pointcut = "pointCut()", throwing = "ex")
    public void doAfterThrowing(JoinPoint joinPoint, @SuppressWarnings("unused") Throwable ex) {
        Date startDate = stopWatchThreadLocal.get();
        // 清除ThreadLocal变量以避免内存泄漏
        stopWatchThreadLocal.remove();
        // 获取注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        BusinessMonitorLog annotation = signature.getMethod().getAnnotation(BusinessMonitorLog.class);
        String userid = getUserId(joinPoint, signature);
        Map<String, Object> header = getHeader();
        BusinessMonitorLogParam businessMonitorLogParam = assembleBusinessMonitorLogParam(header,
                annotation.businessCode(), annotation.businessFrequency(), annotation.operationMethod(),
                startDate, "FAIL", ex.toString(), userid, annotation.moduleCode());
        redisMessageProducer.sendMessage(QUEUE_NAME, JSONUtil.toJsonStr(businessMonitorLogParam));
    }

    /**
     * 获取报文体
     *
     * @return java.util.Map
     */
    private Map<String, Object> getHeader() {
        Map<String, Object> headerParams = new HashMap<>(16);
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Objects.isNull(attributes)) {
            log.warn("The current request is not a session request, and the request headers cannot be obtained.");
            return headerParams;
        }
        HttpServletRequest request = attributes.getRequest();
        // 获取所有请求头名称并遍历
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            headerParams.put(headerName, headerValue);
        }
        return headerParams;
    }

    /**
     * 组装业务监控日志
     *
     * @param headerMap         请求头Map
     * @param businessCode      业务代码
     * @param businessFrequency 业务频次
     * @param operationMethod   操作方式
     * @param startDate         开始时间
     * @param status            状态
     * @param remark            备注
     * @param moduleCode        模块代码
     * @return com.yhl.scp.biz.common.params.BusinessMonitorLogParam
     */
    private BusinessMonitorLogParam assembleBusinessMonitorLogParam(Map<String, Object> headerMap, String businessCode,
                                                                    String businessFrequency, String operationMethod,
                                                                    Date startDate, String status, String remark, String userid,
                                                                    String moduleCode) {

        BusinessMonitorLogParam businessMonitorLogParam = new BusinessMonitorLogParam();
        String scenario = (String) headerMap.get("scenario");
        if (StringUtils.isBlank(scenario)) {
            scenario = DynamicDataSourceContextHolder.getDataSource();
        }
        if (StringUtils.isBlank(userid)) {
            userid = (String) headerMap.get("userid");
        }
        if (StringUtils.isBlank(userid)) {
            userid = ComponentSystemHolder.getUserId();
        }
        businessMonitorLogParam.setScenario(scenario);
        businessMonitorLogParam.setBusinessCode(businessCode);
        businessMonitorLogParam.setOperationMethod(operationMethod);
        businessMonitorLogParam.setOperationStatus(status);
        businessMonitorLogParam.setOperator(userid);
        businessMonitorLogParam.setBusinessFrequency(businessFrequency);
        businessMonitorLogParam.setStartTime(startDate);
        businessMonitorLogParam.setEndTime(new Date());

        businessMonitorLogParam.setId(UUIDUtil.getUUID());
        businessMonitorLogParam.setCreator(userid);
        Date date = new Date();
        businessMonitorLogParam.setCreateTime(date);
        businessMonitorLogParam.setModifier(userid);
        businessMonitorLogParam.setModifyTime(date);
        businessMonitorLogParam.setRemark(remark);
        businessMonitorLogParam.setModuleCode(moduleCode);
        return businessMonitorLogParam;
    }


    /**
     * mps自动排产获取用户id使用
     *
     * @param joinPoint
     * @param signature
     * @return
     */
    private static String getUserId(JoinPoint joinPoint, MethodSignature signature) {
        String userid = null;
        String methodName = signature.getDeclaringTypeName();
        if ("com.yhl.scp.mps.domain.dispatch.process.doAnalysisAlgorithmOutputData".equals(methodName)) {
            Object[] args = joinPoint.getArgs();
            JSONObject jsonObject = JSONUtil.parseObj(args[1]);
            userid = jsonObject.getStr("creator");
        }
        return userid;
    }

}