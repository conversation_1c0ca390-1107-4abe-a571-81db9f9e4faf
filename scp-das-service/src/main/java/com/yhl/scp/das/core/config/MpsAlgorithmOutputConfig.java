package com.yhl.scp.das.core.config;

import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * <code>AlgorithmOutputConfig</code>
 * <p>
 * 读取yml配置文件，算法输出csv配置
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021-11-22 13:47:23
 */
@Component
//@ConfigurationProperties(prefix = "mps.param")
public class MpsAlgorithmOutputConfig implements Serializable {

    private static final long serialVersionUID = -7067501506797252558L;

    private List<OutParam> output;

    public static class OutParam implements Serializable {

        private static final long serialVersionUID = -7703747866161722972L;

        /**
         * csv文件名称
         */
        private String path;

        /**
         * csv对应的实体类名
         */
        private String name;

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public List<OutParam> getOutput() {
        return output;
    }

    public void setOutput(List<OutParam> output) {
        this.output = output;
    }
}
