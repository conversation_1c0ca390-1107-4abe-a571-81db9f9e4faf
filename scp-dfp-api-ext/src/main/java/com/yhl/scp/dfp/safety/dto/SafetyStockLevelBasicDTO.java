package com.yhl.scp.dfp.safety.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.common.excel.ExcelPropertyCheck;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>SafetyStockLevelDTO</code>
 * <p>
 * 安全库存水位DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-19 17:32:29
 */
@ApiModel(value = "安全库存水位DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SafetyStockLevelBasicDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 768102353196847124L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @ExcelProperty(value = "库存点编码*")
    @ExcelPropertyCheck(required = true)
    private String stockCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @ExcelProperty(value = "主机厂编码*")
    @ExcelPropertyCheck(required = true)
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    private String oemName;
    /**
     * 供应类型
     */
    @ApiModelProperty(value = "供应类型")
    private String supplyType;
    /**
     * 主机厂风险等级
     */
    @ApiModelProperty(value = "主机厂风险等级")
    private String oemRiskLevel;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 零件风险等级
     */
    @ApiModelProperty(value = "零件风险等级")
    private String materialRiskLevel;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @ExcelProperty(value = "产品编码*")
    @ExcelPropertyCheck(required = true)
    private String productCode;
    /**
     * 零件名称
     */
    @ApiModelProperty(value = "零件名称")
    private String materialName;
    /**
     * 最小库存天数
     */
    @ApiModelProperty(value = "最小库存天数")
    @ExcelProperty(value = "最小库存天数*")
    @ExcelPropertyCheck(required = true)
    private BigDecimal minStockDay;
    /**
     * 标准库存天数
     */
    @ApiModelProperty(value = "标准库存天数")
    @ExcelProperty(value = "标准安全库存天数*")
    @ExcelPropertyCheck(required = true)
    private BigDecimal standardStockDay;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    
    private String creator;
    
    private Date createTime;

}
