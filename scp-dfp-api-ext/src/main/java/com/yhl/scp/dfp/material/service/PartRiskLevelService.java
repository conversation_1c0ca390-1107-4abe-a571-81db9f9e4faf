package com.yhl.scp.dfp.material.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.material.dto.PartRiskLevelDTO;
import com.yhl.scp.dfp.material.vo.PartRiskLevelDetailVO;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;

import java.util.List;

/**
 * <code>PartRiskLevelService</code>
 * <p>
 * 零件风险等级应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:03:33
 */
public interface PartRiskLevelService extends BaseService<PartRiskLevelDTO, PartRiskLevelVO> {

    /**
     * 查询所有
     *
     * @return list {@link PartRiskLevelVO}
     */
    List<PartRiskLevelVO> selectAll();

    List<PartRiskLevelDetailVO> selectDetailByMaterialId(String materialRiskLevelId);

    BaseResponse<Void> reCalculate(String incrementCalculateFlag, String dataBaseName);

    BaseResponse<Void> updateRiskLevelDetail(String materialRiskLevelDetailId, String materialRiskLevel);

    /**
     * 批量删除
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    List<PartRiskLevelVO> selectByProductCodeList(List<String> productCodeList);

    void doDeleteAll(List<String> notDeleteIds);

    List<LabelValue<String>> getPartRiskLevel(String oemCode,String productCode);

	BaseResponse<Void> batchUpdateLevel(PartRiskLevelDTO dto);
}
