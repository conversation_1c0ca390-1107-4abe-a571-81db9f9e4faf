package com.yhl.scp.dfp.material.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseDTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <code>PartRiskLevelDTO</code>
 * <p>
 * 零件风险等级DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:03:33
 */
@ApiModel(value = "零件风险等级DTO")
@Data
public class PartRiskLevelDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 670018649318969028L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 总评分
     */
    @ApiModelProperty(value = "总评分")
    @FieldInterpretation(value = "总评分")
    private BigDecimal totalScore;

    /**
     * 零件风险等级
     */
    @ApiModelProperty(value = "零件风险等级")
    @FieldInterpretation(value = "零件风险等级")
    private String materialRiskLevel;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    
    /**
     * 零件风险等级（计算后）
     */
    @ApiModelProperty(value = "零件风险等级（计算后）")
    @FieldInterpretation(value = "零件风险等级（计算后）")
    private String materialRiskLevelCalculate;
    
    /**
     * 零件风险等级（上月后）
     */
    @ApiModelProperty(value = "零件风险等级（上月后）")
    @FieldInterpretation(value = "零件风险等级（上月后）")
    private String materialRiskLevelMonth;
    
    /**
     * 修改原因
     */
    @ApiModelProperty(value = "修改原因")
    @FieldInterpretation(value = "修改原因")
    private String updateRemark;
    
    private List<String> ids;

}
