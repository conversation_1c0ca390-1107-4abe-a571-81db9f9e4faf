package com.yhl.scp.dfp.material.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <code>PartRiskLevelVO</code>
 * <p>
 * 零件风险等级VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:03:33
 */
@ApiModel(value = "零件风险等级VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PartRiskLevelVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -24125344307108040L;

    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 主机厂风险等级
     */
    @ApiModelProperty(value = "主机厂风险等级")
    @FieldInterpretation(value = "主机厂风险等级")
    private String oemRiskLevel;

    /**
     * 零件风险等级编辑ID
     */
    @ApiModelProperty(value = "零件风险等级编辑ID")
    @FieldInterpretation(value = "零件风险等级ID")
    private String materialRiskLevelDetailId;

    /**
     * 总评分
     */
    @ApiModelProperty(value = "总评分")
    @FieldInterpretation(value = "总评分")
    private BigDecimal totalScore;

    /**
     * 零件风险等级
     */
    @ApiModelProperty(value = "零件风险等级")
    @FieldInterpretation(value = "零件风险等级")
    private String materialRiskLevel;
    
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    @FieldInterpretation(value = "产品名称")
    private String productName;
    
    /**
     * 零件生命周期表现评分
     */
    @ApiModelProperty(value = "零件生命周期表现评分")
    @FieldInterpretation(value = "零件生命周期表现评分")
    private String partPerformanceScore;
    
    /**
     * 零件生命周期表现情况
     */
    @ApiModelProperty(value = "零件生命周期表现情况")
    @FieldInterpretation(value = "零件生命周期表现情况")
    private String partPerformanceSituation;
    
    /**
     * 半年内月均消耗表现评分
     */
    @ApiModelProperty(value = "半年内月均消耗表现评分")
    @FieldInterpretation(value = "半年内月均消耗表现评分")
    private String yearPerformanceScore;
    
    /**
     * 半年内月均消耗表现情况
     */
    @ApiModelProperty(value = "半年内月均消耗表现情况")
    @FieldInterpretation(value = "半年内月均消耗表现情况")
    private String yearPerformanceSituation;
    
    /**
     * 主机厂风险等级表现评分
     */
    @ApiModelProperty(value = "主机厂风险等级表现评分")
    @FieldInterpretation(value = "主机厂风险等级表现评分")
    private String oemPerformanceScore;
    
    /**
     * 主机厂风险等级表现情况
     */
    @ApiModelProperty(value = "主机厂风险等级表现情况")
    @FieldInterpretation(value = "主机厂风险等级表现情况")
    private String oemPerformanceSituation;
    
    /**
     * 连续3个月预测准确率表现评分
     */
    @ApiModelProperty(value = "连续3个月预测准确率表现评分")
    @FieldInterpretation(value = "连续3个月预测准确率表现评分")
    private String monthPerformanceScore;
    
    /**
     * 连续3个月预测准确率表现情况
     */
    @ApiModelProperty(value = "连续3个月预测准确率表现情况")
    @FieldInterpretation(value = "连续3个月预测准确率表现情况")
    private String monthPerformanceSituation;
    
    /**
     * 动态表头
     */
    @ApiModelProperty(value = "动态表头")
    @FieldInterpretation(value = "动态表头")
    private List<String> headerList;
    
    /**
     * 动态表头详情
     */
    @ApiModelProperty(value = "动态表头详情")
    @FieldInterpretation(value = "动态表头详情")
    List<HeaderPartRiskLevelVO> headerDetailList;
    
    /**
     * 零件风险等级（计算后）
     */
    @ApiModelProperty(value = "零件风险等级（计算后）")
    @FieldInterpretation(value = "零件风险等级（计算后）")
    private String materialRiskLevelCalculate;
    
    /**
     * 零件风险等级（上月后）
     */
    @ApiModelProperty(value = "零件风险等级（上月后）")
    @FieldInterpretation(value = "零件风险等级（上月后）")
    private String materialRiskLevelMonth;
    
    /**
     * 修改原因
     */
    @ApiModelProperty(value = "修改原因")
    @FieldInterpretation(value = "修改原因")
    private String updateRemark;

    @Override
    public void clean() {

    }
}
