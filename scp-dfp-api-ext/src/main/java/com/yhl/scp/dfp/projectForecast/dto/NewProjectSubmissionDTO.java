package com.yhl.scp.dfp.projectForecast.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <code>NewProjectSubmissionDTO</code>
 * <p>
 * 新项目提报DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-30 16:59:57
 */
@ApiModel(value = "新项目提报DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NewProjectSubmissionDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 576643079189883280L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    private String oemCode;
    /**
     * 销售类型
     */
    @ApiModelProperty(value = "销售类型")
    private String saleType;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 主机厂零件号
     */
    @ApiModelProperty(value = "主机厂零件号")
    private String partNumber;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 是否镀膜
     */
    @ApiModelProperty(value = "是否镀膜")
    private String coatingFlag;
    /**
     * 是否夹丝
     */
    @ApiModelProperty(value = "是否夹丝")
    private String crimpFlag;
    /**
     * 是否调光
     */
    @ApiModelProperty(value = "是否调光")
    private String dimmingFlag;
    /**
     * 新产品分类
     */
    @ApiModelProperty(value = "新产品分类")
    private String newProductType;
    /**
     * 材料采购系数
     */
    @ApiModelProperty(value = "材料采购系数")
    private String materialProcurementCoefficient;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    private String organization;
    /**
     * 零件名称
     */
    @ApiModelProperty(value = "零件名称")
    private String partName;
    /**
     * SOP
     */
    @ApiModelProperty(value = "SOP")
    private String sop;
    /**
     * 设备大类
     */
    @ApiModelProperty(value = "设备大类")
    private String equipmentCategory;
    /**
     * 业务员
     */
    @ApiModelProperty(value = "业务员")
    private String salesMan;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String demandType;

    /**
     * 动态字段表头及值
     */
    private Map<String, BigDecimal> dateNumMap;

}
