package com.yhl.scp.dfp.feign;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.FeignConfig;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.common.constants.ServletContextConstants;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpCustomer;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpSaleOrganize;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.grp.GrpEdiDeliveryDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesTransportRouting;
import com.yhl.scp.dfp.calendar.dto.ResourceCalendarRangeDTO;
import com.yhl.scp.dfp.calendar.vo.WorkHourStatisticsVO;
import com.yhl.scp.dfp.carrier.dto.CarrierDataDTO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataVO;
import com.yhl.scp.dfp.consignmentProduct.dto.ConsignmentProductDTO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.delivery.vo.*;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderApiDTO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.massProduction.dto.MassProductionHandoverDTO;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.newProduct.dto.NewProductTrialSubmissionDTO;
import com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionDetailVO;
import com.yhl.scp.dfp.oem.dto.OemAddressInventoryLogDTO;
import com.yhl.scp.dfp.oem.vo.OemRiskLevelVO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandForecastInterfaceLogDTO;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandInterfaceLogDTO;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO;
import com.yhl.scp.dfp.report.vo.HighRiskProductOrderVO;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.dto.InventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.dto.InventoryDataDTO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.dfp.stock.vo.OriginalFilmInTransitVO;
import com.yhl.scp.dfp.warehouse.dto.EnRouteDTO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordDTO;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseRecordLogDTO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <code>DfpFeign</code>
 * <p>
 * DfpFeign
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-22 14:33:35
 */
@FeignClient(value = ServletContextConstants.DFP, path = "/", configuration = FeignConfig.class, url = "${dfp.feign.url:}")
public interface DfpFeign {

    /**
     * 根据计划周期查询发货计划
     *
     * @param scenario   场景
     * @param planPeriod 计划周期
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return java.util.List<com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2>
     */
    @GetMapping(value = "deliveryPlan/selectVO2ByPlanPeriod")
    List<DeliveryPlanVO2> selectVO2ByPlanPeriod(@RequestHeader("scenario") String scenario,
                                                @RequestParam(value = "planPeriod", required = false) String planPeriod,
                                                @RequestParam(value = "startTime", required = false) String startTime,
                                                @RequestParam(value = "endTime", required = false) String endTime);

    /**
     * 根据发货版本ID查询数据
     *
     * @param scenario          场景
     * @param deliveryVersionId 发货版本ID
     * @return java.util.List<com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2>
     */
    @GetMapping(value = "deliveryPlan/selectVO2ByDeliveryVersionId")
    List<DeliveryPlanVO2> selectVO2ByDeliveryVersionId(@RequestHeader("scenario") String scenario,
                                                       @RequestParam(value = "deliveryVersionId", required = false) String deliveryVersionId);

    /**
     * 查询半品与成品库存
     *
     * @param scenario     场景
     * @param productCodes 产品代码列表
     * @return java.util.List<com.yhl.scp.dfp.stock.dto.InventoryDataDTO>
     */
    @PostMapping(value = "inventoryRealTimeData/selectInventoryByProductCodes")
    List<InventoryDataDTO> selectInventoryByProductCodes(@RequestHeader("scenario") String scenario,
                                                         @RequestBody List<String> productCodes);


    /**
     * 根据计划周期查询最新一致性业务预测数据
     *
     * @param scenario   场景
     * @param planPeriod 计划周期
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return java.util.List<com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2>
     */
    @GetMapping(value = "ConsistenceDemand/selectVO2ByPlanPeriod")
    List<DeliveryPlanVO2> selectConsistenceDemandForecastDataByPlanPeriod(@RequestHeader("scenario") String scenario,
                                                                          @RequestParam(value = "planPeriod",
                                                                                  required = false) String planPeriod,
                                                                          @RequestParam(value = "startTime",
                                                                                  required = false) String startTime,
                                                                          @RequestParam(value = "endTime", required =
                                                                                  false) String endTime);

    /**
     * 获取一致性需求预测版本的所有版本
     *
     * @param scenario 场景
     * @return java.util.List<com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO>
     */
    @GetMapping(value = "consistenceDemandForecastVersion/selectConsistenceDemandForecastVersion")
    List<ConsistenceDemandForecastVersionVO> selectConsistenceDemandForecastVersion(@RequestHeader("scenario") String scenario);


    /**
     * 根据本厂编码查询风险等级
     *
     * @param scenario        场景
     * @param productCodeList 产品代码列表
     * @return java.util.List<com.yhl.scp.dfp.material.vo.PartRiskLevelVO>
     */
    @PostMapping(value = "materialRiskLeve/selectByProductCodeList")
    List<PartRiskLevelVO> selectMaterialRiskLeveByProductCodeList(@RequestHeader("scenario") String scenario,
                                                                  @RequestBody List<String> productCodeList);

    /**
     * 根据本厂编码查询安全库存水位
     *
     * @param scenario        场景
     * @param productCodeList 产品代码列表
     * @return java.util.List<com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO>
     */
    @PostMapping(value = "safetyStockLevel/selectByProductCodeList")
    List<SafetyStockLevelVO> selectSafetyStockLevelByProductCodeList(@RequestHeader("scenario") String scenario,
                                                                     @RequestBody List<String> productCodeList);

    /**
     * 根据本厂编码查询装车需求
     *
     * @param scenario        场景
     * @param productCodeList 产品代码列表
     * @return java.util.List<com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO>
     */
    @PostMapping(value = "loadingDemandSubmission/selectByProductCodeList")
    List<LoadingDemandSubmissionVO> selectLoadingDemandSubmissionByProductCode(@RequestHeader("scenario") String scenario,
                                                                               @RequestBody List<String> productCodeList);

    @PostMapping(value = "deliveryPlan/selectByParams")
    List<DeliveryPlanVO> queryAll(@RequestHeader("scenario") String scenario);


    @PostMapping(value = "deliveryPlan/queryCopy")
    List<DeliveryPlanVO> queryCopy(@RequestHeader("scenario") String scenario);

    @PostMapping(value = "saleOrganize/handleData")
    BaseResponse<Void> handleSaleOrganizes(@RequestHeader("scenario") String scenario,
                                           @RequestBody List<ErpSaleOrganize> o);

    @PostMapping(value = "oem/handleData")
    BaseResponse<Void> handleCustomers(@RequestHeader("scenario") String scenario, @RequestBody List<ErpCustomer> o);

    /**
     * 根据版本参数获取一致性需求预测数据
     *
     * @param scenario 场景
     * @param params   查询参数
     * @return java.util.List<com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO>
     */
    @PostMapping(value = "consistenceDemandForecastData/selectDemandForecastDataByParams")
    List<ConsistenceDemandForecastDataVO> selectDemandForecastDataByParams(@RequestHeader("scenario") String scenario,
                                                                           @RequestBody Map<String, Object> params);

    @PostMapping(value = "consistenceDemandForecastDataDetail/selectDemandForecastByStockPointId")
    List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastByStockPointId(@RequestHeader("scenario") String scenario,
                                                                                   String id);

    /**
     * 根据一致性预测需求数据参数获取相应明细数据
     *
     * @param scenario 场景
     * @param params   查询参数
     * @return java.util.List<com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO>
     */
    @PostMapping(value = "consistenceDemandForecastDataDetail/selectDemandForecastByDataDetailByParams")
    List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastByDataDetailByParams(@RequestHeader("scenario") String scenario,
                                                                                         @RequestBody Map<String,
                                                                                                 Object> params);

    /**
     * 根据一致性预测需求数据参数获取相应明细数据
     *
     * @param scenario 场景
     * @param params   查询参数
     * @return java.util.List<com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO>
     */
    @PostMapping(value = "consistenceDemandForecastDataDetail/selectDemandForecastByDataDetailVOByParams")
    List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastByDataDetailVOByParams(
            @RequestHeader("scenario") String scenario,
            @RequestBody Map<String, Object> params);

    @PostMapping(value = "consistenceDemandForecastDataDetail/selectDemandForecastByDataDetailForecastQuantitySumByOemCodesAndMonths")
    List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastByDataDetailForecastQuantitySumByOemCodesAndMonths(
            @RequestHeader("scenario") String scenario,
            @RequestBody Map<String, Object> params);

    @PostMapping(value = "inventoryRealTimeData/selectByParams2")
    List<InventoryRealTimeDataVO> selectInventoryRealTimeDataByParams(@RequestHeader("scenario") String scenario,
                                                                      @RequestBody Map<String, Object> params);

    /**
     * 根据版本ID查询一致性业务预测数据
     *
     * @param scenario   场景
     * @param versionIds 版本ID列表
     * @return java.util.List<com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2>
     */
    @PostMapping(value = "consistenceDemand/selectConsistenceDemandForecastDataByVersionId")
    List<DeliveryPlanVO2> selectConsistenceDemandForecastDataByVersionId(@RequestHeader("scenario") String scenario,
                                                                         @RequestBody List<String> versionIds);


    /**
     * 查询装车日历-计算工时
     *
     * @param scenario 场景
     * @param dto      资源日历范围请求对象
     * @return java.util.List<com.yhl.scp.dfp.calendar.vo.WorkHourStatisticsVO>
     */
    @PostMapping(value = "dfpResourceCalendar/dfpResourceCalendarCalWorkHour")
    List<WorkHourStatisticsVO> dfpResourceCalendarCalWorkHour(@RequestHeader("scenario") String scenario,
                                                              @RequestBody ResourceCalendarRangeDTO dto);

    /**
     * 根据参数查询主机厂信息
     *
     * @param scenario 场景
     * @param params   查询参数
     * @return java.util.List<com.yhl.scp.dfp.oem.vo.OemVO>
     */
    @PostMapping(value = "oem/selectOemByParams")
    List<OemVO> selectOemByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    /**
     * 手动同步库存批次明细数据
     *
     * @param scenario                 场景
     * @param mesInventoryBatchDetails 批次明细数据
     * @param orgIds                   组织ID列表
     * @return com.yhl.platform.common.entity.BaseResponse<java.lang.Void>
     */
    @ApiOperation(value = "同步库存批次明细数据")
    @PostMapping("/inventoryBatchDetail/handleData")
    BaseResponse<Void> handleInventoryBatchDetail(@RequestHeader("scenario") String scenario,
                                                  @RequestBody List<MesInventoryBatchDetail> mesInventoryBatchDetails,@RequestParam("orgIds") List<String> orgIds);

    @ApiOperation(value = "需求映射插入数据")
    @PostMapping(value = "partRelationMap/updatePartMappingData")
    BaseResponse<Void> updatePartMappingData(@RequestHeader("scenario") String scenario,
                                             @RequestBody List<PartRelationMapVO> list);

    @ApiOperation(value = "需求映射插入MDM数据")
    @PostMapping(value = "partRelationMap/updateMDMPartMappingData")
    BaseResponse<Void> updateMDMPartMappingData(@RequestHeader("scenario") String scenario,
                                                @RequestBody List<PartRelationMapVO> list);

    @ApiOperation(value = "根据物品代码查询在制库存结果")
    @PostMapping("inventory/selectInventoryDataByProductCodes")
    List<InventoryBatchDetailVO> selectInventoryDataByProductCodes(@RequestHeader("scenario") String dfpScenario,
                                                                   @RequestBody List<String> productCodes,
                                                                   @RequestParam("stockPointType") String stockPointType);

    /**
     * 同步OA新品试制卡
     *
     * @param scenario 场景
     * @param list     新品试制数据列表
     * @return com.yhl.platform.common.entity.BaseResponse<java.lang.Void>
     */
    @ApiOperation(value = "新品试制卡插入数据")
    @PostMapping(value = "newProductTrailSubmission/updateSyncNewProductTrailSubmissionData")
    BaseResponse<Void> updateSyncNewProductTrialSubmissionData(@RequestHeader("scenario") String scenario,
                                                               @RequestBody List<NewProductTrialSubmissionDTO> list);

    @ApiOperation(value = "根据物品代码查询在制库存结果")
    @PostMapping("inventory/selectInventoryDataByParams")
    List<InventoryBatchDetailVO> selectInventoryDataByParams(@RequestHeader("scenario") String dfpScenario,
                                                             @RequestBody Map<String, Object> params);

    @ApiOperation(value = "获取实时库存数据")
    @PostMapping("inventory/selectInventoryBatchDetailByParams")
    List<InventoryBatchDetailVO> selectInventoryBatchDetailByParams(@RequestHeader("scenario") String dfpScenario,
                                                             @RequestBody Map<String, Object> params);

    @ApiOperation(value = "根据主机厂查询映射关系")
    @PostMapping("stockPointMap/selectOemStockPointByOemCodes")
    List<OemStockPointMapVO> selectOemStockPointByOemCodes(@RequestBody List<String> oemCodes);

    @ApiOperation(value = "获取仓库收发货记录数据")
    @PostMapping("warehouseReleaseRecord/selectWarehouseReleaseRecordByParams")
    List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordByParams(@RequestHeader("scenario") String scenario,
                                                                        @RequestBody Map<String, Object> params);

    @GetMapping(value = "deliveryPlanVersion/selectNewestDeliveryPlanVersion")
    DeliveryPlanVersionVO selectNewestDeliveryPlanVersion(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "根据日需求版本查询")
    @PostMapping("demandVersion/selectWarehouseReleaseRecordByParams")
    List<CleanDemandDataDetailVO> selectCleanDemandDataDetailVOByVersionId(@RequestBody List<String> versionIds);

    @ApiOperation(value = "收发货记录数据同步")
    @PostMapping("warehouseReleaseRecord/updateWareHouseReleaseData")
    BaseResponse<Void> syncWareHouseReleaseData(@RequestHeader("scenario") String scenario,
                                                  @RequestBody List<WarehouseReleaseRecordDTO> list);

    @PostMapping(value = "transportRouting/handleData")
    BaseResponse<Void> handleTransportRouting(@RequestHeader("scenario") String scenario, @RequestBody List<MesTransportRouting> o);

    @ApiOperation(value = "收发货记录日志同步")
    @PostMapping("dfpWarehouseReleaseRecordLog/syncWarehouseLog")
    BaseResponse<Void> syncWarehouseLog(@RequestHeader("scenario") String scenario, @RequestBody List<WarehouseReleaseRecordLogDTO> list);

    @ApiOperation(value = "获取主机厂车型下拉框")
	@GetMapping(value = "oemVehicleModel/selectAllVehicleModelCodes")
	List<LabelValue<String>> selectAllVehicleModelCodes(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "同步GRP/MES装车需求数据")
    @PostMapping("fdpOriginDemandInterfaceLog/syncOriginDemandLog")
    BaseResponse<Void> syncOriginDemandLog(@RequestHeader("scenario") String scenario, @RequestBody List<FdpOriginDemandInterfaceLogDTO> list, @RequestParam("importType") String importType);

    @ApiOperation(value = "同步GRP/MES装车预测数据")
    @PostMapping("fdpOriginDemandMesInterfaceLog/syncOriginDemandForecastLog")
    BaseResponse<Void> syncOriginDemandForecastLog(@RequestHeader("scenario") String scenario, @RequestBody List<FdpOriginDemandForecastInterfaceLogDTO> list, @RequestParam("importType") String importType);


    @ApiOperation(value = "同步主机厂地址")
    @PostMapping(value = "oemAddress/handleData")
    BaseResponse<Void> handleOemAddress(@RequestHeader("scenario") String scenario,
                                        @RequestBody List<OemAddressInventoryLogDTO> o);
    @ApiOperation(value = "定时任务同步运输路径")
    @PostMapping(value = "transportRouting/synTransportRouting")
    BaseResponse<Void> synTransportRouting(@RequestHeader("scenario") String scenario, @RequestParam("tenantId") String tenantId);

    @ApiOperation(value = "定时任务同步主机厂")
    @PostMapping(value = "customer/synCustomer")
    BaseResponse<Void> synCustomer(@RequestHeader("scenario") String scenario, @RequestParam("organizeId") String organizeId,
                                   @RequestParam("tenantId") String tenantId);

    @ApiOperation(value = "根据库存点编码查询映射关系")
    @PostMapping("stockPointMap/selectOemStockPointByStockPointCode")
    List<OemStockPointMapVO> selectOemStockPointByStockPointCode(@RequestHeader("scenario") String scenario,
                                                                 @RequestBody List<String> stockPointByStockList);


    @ApiOperation(value = "获取最新版发货计划")
    @PostMapping("deliveryPlanPublished/selectDeliveryPlanPublishedByParams")
    List<DeliveryPlanVO2> selectDeliveryPlanPublishedByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation("查询库存批次明细VO")
    @PostMapping("inventoryBatchDetail/selectInventoryBatchDetailVOByParams")
    List<InventoryBatchDetailVO> selectInventoryBatchDetailVOByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation("查询所有原片库存批次")
    @PostMapping("inventoryBatchDetail/selectAllGlassInventoryBatch")
    List<InventoryBatchDetailVO> selectAllGlassInventoryBatch(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "根据车型编码查询主机厂信息")
    @PostMapping("oemVehicleModel/selectOemByOemVehicleCode")
    List<OemVehicleModelVO> selectOemVehicleModelByVehicleModelCode(@RequestHeader("scenario") String scenario,
                                                                    @RequestBody List<String> vehicleModelCodeList);

    @ApiOperation(value = "查询日需求数据")
    @PostMapping("cleanDemandData/selectCleanDemandDataByVersionId")
    List<CleanDemandDataVO> selectCleanDemandDataByVersionId(@RequestBody List<String> versionIdList);


    @ApiOperation(value = "查询日需求明细数据")
    @PostMapping("cleanDemandData/cleanDemandDataDetail")
    List<CleanDemandDataDetailVO> selectCleanDemandDataDetailVOByDataIds(@RequestBody List<String> cleanDemandDataId);

    @ApiOperation(value = "FYSL发货记录数据同步")
    @PostMapping("warehouseReleaseRecord/syncWareHouseReleaseFYSLData")
    BaseResponse<Void> syncWareHouseReleaseFYSLData(@RequestHeader("scenario") String scenario,
                                                    @RequestBody List<WarehouseReleaseRecordDTO> arrayList);
    @ApiOperation(value = "查询乘用车市场信息")
    @PostMapping(value = "passengerCarSale/selectPassengerCarSaleByParams")
    List<PassengerCarSaleVO> selectPassengerCarSaleByParams(@RequestHeader("scenario") String scenario,
                                                                           @RequestBody Map<String, Object> params);
    @ApiOperation(value = "查询原片在途数据")
    @PostMapping(value = "originalFilmInTransit/selectOriginalFilmInTransitByParams")
    List<OriginalFilmInTransitVO> selectOriginalFilmInTransitByParams(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "获取全量发布的发货计划")
    @PostMapping("deliveryPublish/selectAllPublishDeliveryPlan")
    List<DeliveryPlanPublishedVO> selectAllPublishDeliveryPlan();

    @ApiOperation(value = "根据参数获取全量发布的发货计划")
    @PostMapping("deliveryPublish/selectPublishDeliveryPlanByParams")
    List<DeliveryPlanPublishedVO> selectPublishDeliveryPlanByParams(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "FYDS在途数据同步")
    @PostMapping("warehouseReleaseRecord/syncFYDSData")
    BaseResponse<Void> syncFYDSData(@RequestHeader("scenario") String scenario,
                                    @RequestBody List<EnRouteDTO> list);
    /**
     * 手动同步库存批次明细数据
     *
     * @param scenario                 场景
     * @param erpInventoryBatchDetails 批次明细数据
     * @return com.yhl.platform.common.entity.BaseResponse<java.lang.Void>
     */
    @ApiOperation(value = "同步库存批次明细数据")
    @PostMapping("/erpInventoryBatchDetail/handleData")
    BaseResponse<Void> handleErpInventoryBatchDetail(@RequestHeader("scenario") String scenario,@RequestBody List<ErpInventoryBatchDetail> erpInventoryBatchDetails,@RequestParam("orgId") String orgId);

    @ApiOperation(value = "ERP委托产品关系")
    @PostMapping("consignmentProduct/syncConsignmentProductData")
    BaseResponse<Void> syncConsignmentProductData(@RequestHeader("scenario") String scenario,
                                                  @RequestBody List<ConsignmentProductDTO> list);

    @ApiOperation(value = "EDI理货单数据回传-GRP")
    @PostMapping("deliveryDockingOrder/updateDeliveryDockingStatus")
    BaseResponse<Void> updateDeliveryDockingStatus(@RequestHeader("scenario") String scenario,
                                                   @RequestBody List<GrpEdiDeliveryDetail> list);
    @ApiOperation(value = "发货对接单状态回传")
    @PostMapping("deliveryDockingOrder/dockingOrderFeedback")
    BaseResponse<Void> dockingOrderFeedback(@RequestHeader("scenario") String scenario, @RequestBody DeliveryDockingOrderApiDTO deliveryDockingOrderApiDTO);

    @ApiOperation(value = "未来30天发货计划")
    @PostMapping("/deliveryPlan/getFutrue30Days")
    List<DeliveryPlanPublishedVO> getDeliveryPlanFuture30Days(@RequestHeader("scenario") String scenario,
                                                              @RequestBody List<String> planUserFactoryCodeList);

    @ApiOperation(value = "发货理货单数据回传-MES")
    @PostMapping("deliveryDockingOrder/updateMesDeliveryDockingStatus")
    BaseResponse<Void> updateMesDeliveryDockingStatus(@RequestHeader("scenario") String data,
                                                      @RequestBody Map<String, Object> map);

    @ApiOperation(value = "获取实时库存汇总数据(按照库存点，装车位置，工序编码分组汇总)")
    @PostMapping("inventory/selectCollectByGroupType")
    List<InventoryBatchDetailVO> selectCollectByGroupType(@RequestHeader("scenario") String dfpScenario,
    		@RequestParam("groupTyep") String groupTyep);

    @ApiOperation(value = "查询最新需求版本")
    @PostMapping("demandVersion/selectNewdemandVersion")
    String selectNewdemandVersion(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "查询时间范围内日需求")
    @PostMapping("cleanDemand/selectCleanDemandDetail")
    List<CleanDemandDataDetailVO> selectCleanDemandDetail(@RequestHeader("scenario") String scenario, @RequestBody List<String> cleanDemandIdList,
                                                          @RequestParam(value = "scopeStart") String scopeStart, @RequestParam(value = "scopeEnd") String scopeEnd);
    @ApiOperation(value = "获取实时库存数据（自定义）")
    @PostMapping("inventoryDetail/selectInventoryBatchDetailPageCustomize")
    PageInfo<InventoryBatchDetailVO> selectInventoryBatchDetailPageCustomize(@RequestBody Pagination pagination,
                                                                         @RequestParam(value = "sortParam",required = false) String sortParam,
                                                                         @RequestParam(value = "queryCriteriaParam",required = false) String queryCriteriaParam,
                                                                         @RequestParam(value = "overdueSort",required = false) String overdueSort);
    @ApiOperation(value = "添加实时库存数据")
    @PostMapping("inventoryDetail/addInventoryBatchDetail")
    void addInventoryBatchDetail(@RequestBody List<InventoryBatchDetailDTO> inventoryBatchDetailDTOList);

    @ApiOperation(value = "修改实时库存数据")
    @PostMapping("inventoryDetail/updateInventoryBatchDetail")
    void updateInventoryBatchDetail(@RequestBody List<InventoryBatchDetailDTO> inventoryBatchDetailDTOList);

    @ApiOperation(value = "删除实时库存数据")
    @PostMapping("inventoryDetail/deleteInventoryBatchDetail")
    void deleteInventoryBatchDetail(@RequestBody List<String> ids);

    @ApiOperation(value = "查询实时库存详情数据")
    @PostMapping("inventoryDetail/selectInventoryBatchDetailVOByParams02")
    List<InventoryBatchDetailVO> selectInventoryBatchDetailVOByParams02(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询实时库存详情数据")
    @PostMapping("inventoryDetail/selectInventoryBatchDetailVOByParams03")
    List<InventoryBatchDetailVO> selectInventoryBatchDetailVOByParams03(@RequestHeader("scenario") String scenario,@RequestBody Map<String, Object> params);

    @PostMapping("inventoryDetail/selectInventoryBatchDetailPage")
    PageInfo<InventoryBatchDetailVO> selectInventoryBatchDetailPage( @RequestBody Map<String, Object> params);

    @ApiOperation(value = "GRP承运商")
    @PostMapping("carrierData/syncCarrierData")
    BaseResponse<Void> syncCarrierData(@RequestHeader("scenario") String scenario,
                                       @RequestBody List<CarrierDataDTO> list);

    @ApiOperation(value = "生产计划变更通知表-变更关闭")
    @PostMapping("deliveryPlanPublishedCompare/deliveryPlanPublishedCompareDoEnable")
    BaseResponse<Void> deliveryPlanPublishedCompareDoEnable(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);



    @ApiOperation(value = "获取最新版发货计划发货数量统计数据")
    @PostMapping("deliveryPlanPublished/selectSumDeliveryPlanPublished")
	List<DeliveryPlanPublishedVO> selectSumDeliveryPlanPublished(@RequestBody Map<String, Object> map);

    @ApiOperation(value = "查询库存推移数据")
    @PostMapping("inventory/selectInventoryShiftList")
    List<InventoryShiftVO> selectInventoryShiftList(@RequestBody Map<String, Object> map);

    @ApiOperation(value = "查询日需求数据")
    @PostMapping("cleanDemandData/selectCleanDemandDataByParams")
    List<CleanDemandDataVO> selectCleanDemandDataByParams(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询最新版本")
    @GetMapping("consistenceDemandForecastVersion/selectConsistenceDemandLatestVersionId")
    String selectConsistenceDemandLatestVersionId(@RequestHeader("scenario") String scenario);

    @ApiOperation("根据时间查询历史收发货数据")
    @PostMapping("warehouseReleaseRecord/selectWarehouseReleaseRecordByDate")
    List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordByDate(@RequestParam(value = "startDate",required = false) Date startDate,
                                                                      @RequestParam(value = "endDate",required = false) Date endDate);

    @ApiOperation("根据时间查询历史收发货物料对应的发货数量（已总和）")
    @PostMapping("warehouseReleaseRecord/selectWarehouseReleaseRecordSumQtyByDate")
    List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordSumQtyByDate(@RequestHeader("scenario") String scenario,
                                                                            @RequestBody List<String> productCodes,
                                                                            @RequestParam(value = "startDate", required = false) String startDate,
                                                                            @RequestParam(value = "endDate", required = false) String endDate);
    @PostMapping(value = "oem/selectOemVOByParams")
    List<OemVO> selectOemVOByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    /**
     * 查询发货计划，dynamicColumns返回字段
     *
     * @param feignDynamicParam 动态字段 + 参数
     * @return DeliveryPlanPublishedVO {@link DeliveryPlanPublishedVO}
     */
    @ApiOperation(value = "已发布发货计划查询-返回指定列")
    @PostMapping(value = "deliveryPlan/selectDeliveryPlanPublishedByParamOnDynamicColumns")
    List<DeliveryPlanPublishedVO> selectDeliveryPlanPublishedByParamOnDynamicColumns(
            @RequestHeader("scenario") String scenario,
            @RequestBody FeignDynamicParam feignDynamicParam);

    @ApiOperation(value = "根据参数查询新品试制数据")
    @PostMapping("newProductTrailSubmission/selectNewProductTrialSubmissionDetailByParams")
    List<NewProductTrialSubmissionDetailVO> selectNewProductTrialSubmissionDetailByParams(@RequestHeader("scenario") String scenario,
                                                                                       @RequestBody Map<String, Object> params);
    @ApiOperation(value = "根据id查询新品试制提报数据")
    @PostMapping("submissionDetail/selectSubmissionDetailByPrimaryKeys")
    List<NewProductTrialSubmissionDetailVO> selectSubmissionDetailByPrimaryKeys(@RequestBody List<String> ids);

    @ApiOperation(value = "查询最新已发布的一致性预测需求版本")
    @PostMapping("consistenceDemandForecastVersion/selectConsistenceDemandForecastVersionLatestPublished")
    ConsistenceDemandForecastVersionVO selectConsistenceDemandForecastVersionLatestPublished(@RequestHeader("scenario") String scenario);
    @ApiOperation(value = "查询最新装车需求版本")
    @GetMapping(value = "deliveryPlanVersion/selectLatestDeliveryPlanVersion")
    DeliveryPlanVersionVO selectLatestDeliveryPlanVersion(@RequestHeader("scenario") String scenario);
    
    @ApiOperation(value = "查询零件映射关系")
    @PostMapping(value = "partRelationMap/selectPartRelationMapByParams")
    List<PartRelationMapVO> selectPartRelationMapByParams(@RequestHeader("scenario") String scenario,
                                                                                         @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询主机厂风险等级")
    @PostMapping("oemRiskLevel/selectOemRiskLevelByParams")
    List<OemRiskLevelVO> selectOemRiskLevelByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "同步零件号")
    @PostMapping("/syncPartNum")
    void syncPartNumFromErp(@RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询滚动预测")
    @PostMapping("cleanForecastData/selectCleanForecastDataVOListByParams")
    List<CleanForecastDataVO> selectCleanForecastDataVOListByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询滚动预测明细")
    @PostMapping("cleanForecastDataDetail/selectCleanForecastDataDetailVOListByParams")
    List<CleanForecastDataDetailVO> selectCleanForecastDataDetailVOListByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "查询需求版本")
    @PostMapping("demandVersion/selectDemandVersionVOListByParams")
    List<DemandVersionVO> selectDemandVersionVOListByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "同步量产移交数据")
    @GetMapping("productionHandover/syncCreateProductionHandover")
    BaseResponse<Void> syncCreateProductionHandover(@RequestHeader("scenario")String scenario, @RequestHeader("ids")String ids, @RequestHeader("oaId")String oaId);

    @ApiOperation(value = "同步量产移交状态数据")
    @PostMapping("productionHandover/syncProductionHandoverStatus")
    BaseResponse<Void> syncProductionHandoverStatus(@RequestHeader("scenario")String scenario, @RequestBody List<MassProductionHandoverDTO> MassProductionHandoverDTOS);

    @ApiOperation(value = "查询高风险产品订单")
    @PostMapping("highRiskProductOrder/selectHighRiskProductOrderVOListByParams")
    List<HighRiskProductOrderVO> selectHighRiskProductOrderVOListByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

    @ApiOperation(value = "更新并发布发布货计划")
    @PostMapping("/api/deliveryPlanPublished/updateDemandQuantityAndPublish")
    BaseResponse<Void> updateDemandQuantityAndPublish(@RequestHeader("scenario") String scenario,
                                        @RequestParam("planner") String planner,
                                        @RequestBody List<DeliveryPlanPublishedDTO> publishedList);

    @PostMapping("/api/deliveryPlanPublished/updateCoordinationQuantity")
    void updateCoordinationQuantity(@RequestHeader("scenario") String scenario,
                                    @RequestParam(value = "set2Null") boolean set2Null,
                                    @RequestBody List<DeliveryPlanPublishedDTO> publishedList);

    @PostMapping("/api/deliveryPlan/inventoryMovementCheck")
    BaseResponse<Void> inventoryMovementCheck(@RequestHeader("scenario") String scenario,
                                   @RequestBody List<DeliveryPlanPublishedDTO> publishedList);

    @ApiOperation(value = "变更通知查询")
    @PostMapping("deliveryPlanPublishedCompare/deliveryPlanPublishedCompareDayView")
    List<DeliveryPlanPublishedCompareDayVO2> deliveryPlanPublishedCompareDayView(@RequestBody List<String> productCodeList,
                                                                                 @RequestHeader("scenario") String scenario,
                                                                                 @RequestParam(value = "userId", required = false) String userId);

    @ApiOperation(value = "查询仓库发货至中转库")
    @PostMapping("warehouseReleaseToWarehouse/selectWarehouseReleaseToWarehouseVOListByParams")
    List<WarehouseReleaseToWarehouseVO> selectWarehouseReleaseToWarehouseVOListByParams(@RequestHeader("scenario") String scenario, @RequestBody Map<String, Object> params);

}
