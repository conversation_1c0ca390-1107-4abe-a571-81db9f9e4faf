package com.yhl.scp.dfp.sale.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <code>SaleOrganizeDTO</code>
 * <p>
 * 销售组织DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:14:35
 */
@ApiModel(value = "销售组织DTO")
@Data
public class SaleOrganizeDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 980656971046380453L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 销售组织编码
     */
    @ApiModelProperty(value = "销售组织编码")
    private String saleOrgCode;
    /**
     * 销售组织名称
     */
    @ApiModelProperty(value = "销售组织名称")
    private String saleOrgName;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;

}
