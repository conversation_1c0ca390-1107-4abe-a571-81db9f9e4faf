package com.yhl.scp.dfp.enums;

import com.yhl.platform.common.enums.CommonEnum2;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ObjectTypeEnum</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 16:17:02
 */
public enum ObjectTypeEnum implements CommonEnum2 {
    /**
     * 场景
     */
    ANNUAL_FORECAST_SUBMISSION("annual_forecast_submission", "年度预测提报", "com.yhl.scp.dfp.annual.vo.AnnualForecastSubmissionVO"),
    BOX_INFO("box_info", "箱体信息", "com.yhl.scp.dfp.box.vo.BoxInfoVO"),
    CLEAN_ALGORITHM_DATA("clean_algorithm_data", "预测算法数据", "com.yhl.scp.dfp.clean.vo.CleanAlgorithmDataVO"),
    CLEAN_DEMAND_DATA("clean_demand_data", "日需求数据", "com.yhl.scp.dfp.clean.vo.CleanDemandDataVO"),
    CLEAN_FORECAST_DATA("clean_forecast_data", "滚动预测数据", "com.yhl.scp.dfp.clean.vo.CleanForecastDataVO"),
    CONSISTENCE_DEMAND_FORECAST_DATA("consistence_demand_forecast_data", "一致性业务预测数据", "com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO"),
    CONSISTENCE_DEMAND_FORECAST_VERSION("consistence_demand_forecast_version", "一致性业务预测版本", "com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO"),
    DELIVERY_PLAN("delivery_plan", "发货计划表", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO"),
    DEMAND_FORECAST_VERSION("demand_forecast_version", "业务预测版本", "com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO"),
    DEMAND_VERSION("demand_version", "需求版本", "com.yhl.scp.dfp.demand.vo.DemandVersionVO"),
    GLOBAL_CAR_SALE("global_car_sale", "全球汽车销量", "com.yhl.scp.dfp.global.vo.GlobalCarSaleVO"),
    GLOBAL_CAR_SALE_DETAIL("global_car_sale_detail", "全球汽车销量详情", "com.yhl.scp.dfp.global.vo.GlobalCarSaleDetailVO"),
    LOADING_DEMAND_SUBMISSION("loading_demand_submission", "装车需求提报", "com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO"),
    LOADING_DEMAND_SUBMISSION_DETAIL("loading_demand_submission_detail", "装车需求提报详情", "com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO"),
    MARKET_INFORMATION("market_information", "市场信息", "com.yhl.scp.dfp.market.vo.MarketInformationVO"),
    MARKET_SHARE("market_share", "市场占有率", "com.yhl.scp.dfp.market.vo.MarketShareVO"),
    MATERIAL_RISK_LEVEL("material_risk_level", "零件风险等级", "com.yhl.scp.dfp.material.vo.MaterialRiskLevelVO"),
    MATERIAL_RISK_LEVEL_DETAIL("material_risk_level_detail", "零件风险等级详情", "com.yhl.scp.dfp.material.vo.MaterialRiskLevelDetailVO"),
    NEW_PRODUCT_TRIAL_SUBMISSION("new_product_trial_submission", "新品试制提报", "com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionVO"),
    NEW_PRODUCT_TRIAL_SUBMISSION_DETAIL("new_product_trial_submission_detail", "新品试制提报详情", "com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionDetailVO"),
    NEW_PROJECT_SUBMISSION("new_project_submission", "新项目提报", "com.yhl.scp.dfp.newProject.vo.NewProjectSubmissionVO"),
    NEW_PROJECT_SUBMISSION_DETAIL("new_project_submission_detail", "新项目提报详情", "com.yhl.scp.dfp.newProject.vo.NewProjectSubmissionDetailVO"),
    OEM_RISK_LEVEL("oem_risk_level", "主机厂风险等级", "com.yhl.scp.dfp.oem.vo.OemRiskLevelVO"),
    ORIGIN_DEMAND_VERSION("origin_demand_version", "原始需求版本", "com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO"),
    PART_RELATION_MAP("part_relation_map", "零件映射关系", "com.yhl.scp.dfp.part.vo.PartRelationMapVO"),
    PASSENGER_CAR_SALE("passenger_car_sale", "乘用车市场信息", "com.yhl.scp.passenger.oem.vo.PassengerCarSaleVO"),
    PRODUCT_BOX_RELATION("product_box_relation", "产品与成品箱关系", "com.yhl.scp.mds.product.vo.ProductBoxRelationVO"),
    QUEUE_PLAN("queue_plan", "排车计划", "com.yhl.scp.dfp.queue.vo.QueuePlanVO"),
    QUEUE_PLAN_BATCH("queue_plan_batch", "排车计划批次", "com.yhl.scp.dfp.queue.vo.QueuePlanBatchVO"),
    RISK_LEVEL_RULE("risk_level_rule", "风险等级规则", "com.yhl.scp.dfp.risk.vo.RiskLevelRuleVO"),
    RISK_LEVEL_RULE_DETAIL("risk_level_rule_detail", "风险等级规则详情", "com.yhl.scp.dfp.risk.vo.RiskLevelRuleDetailVO"),
    SAFETY_STOCK_LEVEL("safety_stock_level", "安全库存水位", "com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO"),
    VEHICLE_CONFIGURATION("vehicle_configuration", "车型配置信息", "com.yhl.scp.dfp.vehicle.vo.VehicleConfigurationVO"),
    VEHICLE_CONFIGURATION_DETAIL("vehicle_configuration_detail", "车型配置信息详情", "com.yhl.scp.dfp.vehicle.vo.VehicleConfigurationDetailVO"),
    OEM("oem", "主机厂档案", "com.yhl.scp.dfp.oem.vo.OemVO"),
    OEM_PRODUCT_LINE("oem_product_line", "主机厂产线资源", "com.yhl.scp.dfp.oem.vo.OemProductLineVO"),
    OEM_PRODUCT_LINE_MAP("oem_product_line_map", "产线映射关系", "com.yhl.scp.dfp.oem.vo.OemProductLineMapVO"),
    OEM_STOCK_POINT_MAP("oem_stock_point_map", "主机厂库存点关联关系", "com.yhl.scp.dfp.oem.vo.OemStockPointMapVO"),
    OEM_VEHICLE_MODEL("oem_vehicle_model", "主机厂车型信息", "com.yhl.scp.dfp.oem.vo.OemVehicleModelVO"),
    OEM_VEHICLE_MODEL_MAP("oem_vehicle_model_map", "主机厂车型映射关系", "com.yhl.scp.dfp.oem.vo.OemVehicleModelMapVO"),
    PRODUCT_STOCK_POINT("product_stock_point", "物品", "com.yhl.scp.dfp.product.vo.ProductStockPointVO"),
    PRODUCTION_ORGANIZE("production_organize", "销售组织", "com.yhl.scp.dfp.production.vo.ProductionOrganizeVO"),
    SALE_ORGANIZE("sale_organize", "销售组织", "com.yhl.scp.dfp.sale.vo.SaleOrganizeVO"),
    STOCK_POINT("stock_point", "库存点", "com.yhl.scp.dfp.stock.vo.StockPointVO"),
    RESOURCE_GROUP("resource_group", "资源组", "com.yhl.scp.dfp.resource.vo.ResourceGroupVO"),
    INDUSTRY_INFO("industry_info", "行业资讯", "com.yhl.scp.dfp.industry.vo.IndustryInfoVO"),
    WAREHOUSE_RELEASE_RECORD("warehouse_release_record", "库存发货记录", "com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO"),
    OEM_CALENDAR_RULE("oem_calendar_rule", "日历规则", "com.yhl.scp.dfp.calendar.vo.OemCalendarRuleVO"),
    OEM_CAL_SHIFT("oem_cal_shift", "主机厂班次", "com.yhl.scp.dfp.calendar.vo.OemCalShiftVO"),
    RESOURCE("resource", "资源", "com.yhl.scp.dfp.resource.vo.ResourceVO"),
    OEM_RESOURCE_CALENDAR("oem_resource_calendar","主机厂装车日历" , "com.yhl.scp.dfp.calendar.vo.OemResourceCalendar"),
    PROJECT_FORECAST_PRESENTATION("project_forecast_presentation","项目预测提报" ,"com.yhl.scp.dfp.newProject.vo.ProjectForecastPresentationVO"),
    PROJECT_FORECAST_PRESENTATION_DETAIL("project_forecast_presentation_detail","项目预测提报详情" ,"com.yhl.scp.dfp.newProject.vo.ProjectForecastPresentationDetailVO"),
    PROJECT_FORECAST_VERSION("PROJECT_FORECAST_VERSION", "项目预测版本", "com.yhl.scp.dfp.demand.vo.ProjectForecastVersionVO"),
    OEM_INVENTORY("OEM_INVENTORY", "主机厂库存", "com.yhl.scp.dfp.oem.vo.OemInventoryVO"),
    CLEAN_ALGORITHM_ANALYSIS("CLEAN_ALGORITHM_ANALYSIS", "算法结果-预测因子分析", "com.yhl.scp.dfp.clean.vo.CleanAlgorithmAnalysisVO"),
    COMPREHENSIVE_EVALUATION_COEFFICIENT("COMPREHENSIVE_EVALUATION_COEFFICIENT", "需求预测评审产线综合系数", "com.yhl.scp.dfp.demand.vo.ComprehensiveEvaluationCoefficientVO"),
    DELIVERY_PLAN_PUBLISHED_COMPARE("DELIVERY_PLAN_PUBLISHED_COMPARE", "发货计划发布数量变化对比", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareVO"),
    PASSENGER_CAR_SALE_IMPORT("passenger_car_sale_import", "乘用车市场信息导入临时数据", "com.yhl.scp.dfp.passenger.vo.PassengerCarSaleImportVO"),
    CAR_PRICE_LIST_IMPORT("car_price_list_import","汽车价格导入临时表" ,"com.yhl.scp.dfp.car.vo.CarPriceListImportVO" ),
    OEM_LLM_PROMPT("oem_llm_prompt", "主机厂提示词配置", "com.yhl.scp.dfp.oem.vo.OemLlmPromptVO"),

    MASS_PRODUCTION_HANDOVER("fdp_mass_production_handover","量产移交信息" ,"com.yhl.scp.dfp.massProduction.vo.MassProductionHandoverVO" ),
    MASS_PRODUCTION_HANDOVER_DETAIL("fdp_mass_production_handover_detail","量产移交信息详情" ,"com.yhl.scp.dfp.massProduction.vo.MassProductionHandoverDetailVO" ),
    MASS_PRODUCTION_HANDOVER_LOG("fdp_mass_production_handover_log","量产移交信息日志" ,"com.yhl.scp.dfp.massProduction.vo.MassProductionHandoverLogVO" ),
    ;

    ObjectTypeEnum(String code, String desc, String mappingValue) {
        this.code = code;
        this.desc = desc;
        this.mappingValue = mappingValue;
    }

    /**
     * 表名 / 视图名
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 映射类
     */
    private String mappingValue;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getMappingValue() {
        return mappingValue;
    }

    void setMappingValue(String mappingValue) {
        this.mappingValue = mappingValue;
    }

    /**
     * 对象类型表名映射
     */
    public static final Map<String, String> OBJECT_TABLE_MAP = Arrays.stream(ObjectTypeEnum.values()).sequential()
            .collect(Collectors.toMap(ObjectTypeEnum::name, ObjectTypeEnum::getCode, (t1, t2) -> t2));

}
