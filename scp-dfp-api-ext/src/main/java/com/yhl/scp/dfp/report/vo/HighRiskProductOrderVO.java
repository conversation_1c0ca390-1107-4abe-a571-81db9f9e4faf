package com.yhl.scp.dfp.report.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <code>HighRiskProductOrderVO</code>
 * <p>
 * HighRiskProductOrderVO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-14 21:34:46
 */
@Data
@ApiModel("高风险产品订单VO")
public class HighRiskProductOrderVO implements Serializable {

    private static final long serialVersionUID = -5285109227806437695L;

    private String versionId;

    @ApiModelProperty("主机厂编码")
    private String oemCode;

    @ApiModelProperty("主机厂名称")
    private String oemName;

    @ApiModelProperty("本厂编码")
    private String productCode;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("车型")
    private String vehicleModelCode;

    private String stockPointCode;

    @ApiModelProperty("零件号")
    private String partNumber;

    @ApiModelProperty("产品风险等级")
    private String riskLevel;

    @ApiModelProperty("客户15天需求量")
    private BigDecimal demand15Days;

    @ApiModelProperty("客户30天需求量")
    private BigDecimal demand30Days;

    @ApiModelProperty("厂外设定标准安全库存天数")
    private BigDecimal outsideStandardSafetyStockDays;

    @ApiModelProperty("厂内设定标准安全库存天数")
    private BigDecimal insideStandardSafetyStockDays;

    @ApiModelProperty("中转库成品库存")
    private BigDecimal transferFgInventory;

    @ApiModelProperty("中转库半品库存")
    private BigDecimal transferSemiInventory;

    @ApiModelProperty("发运库库存")
    private BigDecimal shippingInventory;

    @ApiModelProperty("厂内成品库存量")
    private BigDecimal insideFgInventory;

    @ApiModelProperty("半品库存")
    private List<BigDecimal> stepInventories;

    @ApiModelProperty("本票需求缺口")
    private BigDecimal demandGap;

    @ApiModelProperty("标准单件包装量")
    private BigDecimal standardPackagingQty;

    private BigDecimal demandQuantity;

    private String demandTime;

}