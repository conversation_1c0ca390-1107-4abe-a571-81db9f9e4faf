package com.yhl.scp.dfp.warehouse.service;

import java.util.List;
import java.util.Map;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseToWarehouseDTO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseDayVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;

/**
 * <code>WarehouseReleaseToWarehouseService</code>
 * <p>
 * 仓库发货至中转库应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10 09:57:22
 */
public interface WarehouseReleaseToWarehouseService extends BaseService<WarehouseReleaseToWarehouseDTO, WarehouseReleaseToWarehouseVO> {

    /**
     * 查询所有
     *
     * @return list {@link WarehouseReleaseToWarehouseVO}
     */
    List<WarehouseReleaseToWarehouseVO> selectAll();

    List<WarehouseReleaseToWarehouseVO> getInRoad(List<String> demandProductCodeList,
                                                         List<String> shipmentLocatorCodes);

    List<String> selectTargetStockLocation();

	List<WarehouseReleaseToWarehouseMonthVO> selectMonthVOByParams(Map<String, Object> params);
	
	List<WarehouseReleaseToWarehouseDayVO> selectDayVOByParams(Map<String, Object> params);

}
