package com.yhl.scp.dfp.warehouse.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>WarehouseReleaseToWarehouseVO</code>
 * <p>
 * 仓库发货至中转库VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10 09:57:22
 */
@ApiModel(value = "仓库发货至中转库VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseReleaseToWarehouseVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -95996370385197129L;

    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @FieldInterpretation(value = "工厂")
    private String plantCode;
    /**
     * 是否已接收
     */
    @ApiModelProperty(value = "是否已接收")
    @FieldInterpretation(value = "是否已接收")
    private String isReceive;
    /**
     * 发货清单号
     */
    @ApiModelProperty(value = "发货清单号")
    @FieldInterpretation(value = "发货清单号")
    private String listNum;
    /**
     * 发货计划单号
     */
    @ApiModelProperty(value = "发货计划单号")
    @FieldInterpretation(value = "发货计划单号")
    private String reqNum;
    /**
     * 条码号
     */
    @ApiModelProperty(value = "条码号")
    @FieldInterpretation(value = "条码号")
    private String barNum;
    /**
     * 箱号
     */
    @ApiModelProperty(value = "箱号")
    @FieldInterpretation(value = "箱号")
    private String boxNum;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String itemCode;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @FieldInterpretation(value = "描述")
    private String descriptions;
    /**
     * 发货数量
     */
    @ApiModelProperty(value = "发货数量")
    @FieldInterpretation(value = "发货数量")
    private BigDecimal sumQty;
    /**
     * 柜号
     */
    @ApiModelProperty(value = "柜号")
    @FieldInterpretation(value = "柜号")
    private String containerNum;
    /**
     * 提单号
     */
    @ApiModelProperty(value = "提单号")
    @FieldInterpretation(value = "提单号")
    private String billOfLadingNum;
    /**
     * 船公司
     */
    @ApiModelProperty(value = "船公司")
    @FieldInterpretation(value = "船公司")
    private String shipCompany;
    /**
     * 进仓工厂
     */
    @ApiModelProperty(value = "进仓工厂")
    @FieldInterpretation(value = "进仓工厂")
    private String instockSource;
    /**
     * 来源仓库
     */
    @ApiModelProperty(value = "来源仓库")
    @FieldInterpretation(value = "来源仓库")
    private String attribute1;
    /**
     * 来源货位
     */
    @ApiModelProperty(value = "来源货位")
    @FieldInterpretation(value = "来源货位")
    private String attribute2;
    /**
     * 目标仓库
     */
    @ApiModelProperty(value = "目标仓库")
    @FieldInterpretation(value = "目标仓库")
    private String shipmentWarehouseCode;
    /**
     * 目标货位
     */
    @ApiModelProperty(value = "目标货位")
    @FieldInterpretation(value = "目标货位")
    private String shipmentLocatorCode;
    /**
     * 计划单号
     */
    @ApiModelProperty(value = "计划单号")
    @FieldInterpretation(value = "计划单号")
    private String req;
    /**
     * 行号
     */
    @ApiModelProperty(value = "行号")
    @FieldInterpretation(value = "行号")
    private String lineNum;
    /**
     * 物流器具小类
     */
    @ApiModelProperty(value = "物流器具小类")
    @FieldInterpretation(value = "物流器具小类")
    private String typeCoode;
    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    @FieldInterpretation(value = "发货时间")
    private Date creationDate;
    /**
     * 发货人
     */
    @ApiModelProperty(value = "发货人")
    @FieldInterpretation(value = "发货人")
    private String consigner;
    /**
     * 入仓时间
     */
    @ApiModelProperty(value = "入仓时间")
    @FieldInterpretation(value = "入仓时间")
    private Date inWarehouseTime;
    /**
     * 单片面积
     */
    @ApiModelProperty(value = "单片面积")
    @FieldInterpretation(value = "单片面积")
    private BigDecimal acreage;
    /**
     * 总面积
     */
    @ApiModelProperty(value = "总面积")
    @FieldInterpretation(value = "总面积")
    private BigDecimal acreageSum;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    /**
     * mes数据ID
     */
    @ApiModelProperty(value = "mes数据ID")
    @FieldInterpretation(value = "mes数据ID")
    private String kid;
    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @FieldInterpretation(value = "批次")
    private String lotNumber;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    @FieldInterpretation(value = "客户编码")
    private String customerNumber;
    /**
     * 地址ID
     */
    @ApiModelProperty(value = "地址ID")
    @FieldInterpretation(value = "地址ID")
    private String ebsSiteId;
    /**
     * 客户零件号
     */
    @ApiModelProperty(value = "客户零件号")
    @FieldInterpretation(value = "客户零件号")
    private String custPart;
    /**
     * 客户po号
     */
    @ApiModelProperty(value = "客户po号")
    @FieldInterpretation(value = "客户po号")
    private String custPo;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    @FieldInterpretation(value = "来源")
    private String sourceType;
    /**
     * 预计到港时间
     */
    @ApiModelProperty(value = "预计到港时间")
    @FieldInterpretation(value = "预计到港时间")
    private Date estimatedArrivePortTime;
    /**
     * 实际到港时间
     */
    @ApiModelProperty(value = "实际到港时间")
    @FieldInterpretation(value = "实际到港时间")
    private Date actualArrivePortTime;
    /**
     * 预计完成时间
     */
    @ApiModelProperty(value = "预计完成时间")
    @FieldInterpretation(value = "预计完成时间")
    private Date estimatedCompletionTime;
    /**
     * 实际完成时间
     */
    @ApiModelProperty(value = "实际完成时间")
    @FieldInterpretation(value = "实际完成时间")
    private Date actualCompletionTime;
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    @FieldInterpretation(value = "车牌号")
    private String carNum;

    @Override
    public void clean() {

    }

}
