package com.yhl.scp.dfp.oem.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.scp.common.vo.SimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>OemVO</code>
 * <p>
 * 主机厂档案VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:10:38
 */
@ApiModel(value = "主机厂档案VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OemVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -36954993165013071L;

    /**
     * 销售组织ID
     */
    @ApiModelProperty(value = "销售组织ID")
    @FieldInterpretation(value = "销售组织ID")
    private String saleOrgId;
    /**
     * 生产组织编码
     */
    @ApiModelProperty(value = "销售组织编码")
    @FieldInterpretation(value = "销售组织编码")
    private String saleOrgCode;
    /**
     * 生产组织名称
     */
    @ApiModelProperty(value = "销售组织名称")
    @FieldInterpretation(value = "销售组织名称")
    private String saleOrgName;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;
    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    @FieldInterpretation(value = "客户编码")
    private String customerCode;
    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    @FieldInterpretation(value = "客户名称")
    private String customerName;
    /**
     * 地点编码
     */
    @ApiModelProperty(value = "地点编码")
    @FieldInterpretation(value = "地点编码")
    private String locationCode;
    /**
     * 地址1
     */
    @ApiModelProperty(value = "地址1")
    @FieldInterpretation(value = "地址1")
    private String locationArea1;
    /**
     * 地址2
     */
    @ApiModelProperty(value = "地址2")
    @FieldInterpretation(value = "地址2")
    private String locationArea2;
    /**
     * 地址3
     */
    @ApiModelProperty(value = "地址3")
    @FieldInterpretation(value = "地址3")
    private String locationArea3;
    /**
     * 地址4
     */
    @ApiModelProperty(value = "地址4")
    @FieldInterpretation(value = "地址4")
    private String locationArea4;
    /**
     * 付款条件
     */
    @ApiModelProperty(value = "付款条件")
    @FieldInterpretation(value = "付款条件")
    private String paymentTerm;
    /**
     * 运输条款
     */
    @ApiModelProperty(value = "运输条款")
    @FieldInterpretation(value = "运输条款")
    private String transitClause;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @FieldInterpretation(value = "业务类型")
    private String businessType;
    /**
     * 市场属性
     */
    @ApiModelProperty(value = "市场属性")
    @FieldInterpretation(value = "市场属性")
    private String marketType;
    /**
     * 目标货位
     */
    @ApiModelProperty(value = "目标货位")
    @FieldInterpretation(value = "目标货位")
    private String targetStockLocation;
    /**
     * ediLocation
     */
    @ApiModelProperty(value = "ediLocation")
    @FieldInterpretation(value = "EDI位置")
    private String ediLocation;
    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @FieldInterpretation(value = "工厂")
    private String plantCode;
    /**
     * erp客户id
     */
    @ApiModelProperty(value = "erp客户id")
    @FieldInterpretation(value = "erp客户id")
    private String ebsCustomerId;
    /**
     * erp地点id
     */
    @ApiModelProperty(value = "erp地点id")
    @FieldInterpretation(value = "erp地点id")
    private String ebsSiteId;
    /**
     * erp地址用途id
     */
    @ApiModelProperty(value = "erp地址用途id")
    @FieldInterpretation(value = "erp地址用途id")
    private String shipToSiteUseId;
    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    @FieldInterpretation(value = "国家")
    private String siteCountry;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private Date lastUpdateTime;
    /**
     * edi标志
     */
    @ApiModelProperty(value = "edi标志")
    @FieldInterpretation(value = "edi标志")
    private String ediFlag;

    /**
     * 理货单模式，MES，GRP
     */
    @ApiModelProperty(value = "理货单模式，MES，GRP")
    @FieldInterpretation(value = "理货单模式，MES，GRP")
    private String tallyOrderMode;

    /**
     * 自提类型：自提，非自提
     */
    @ApiModelProperty(value = "自提类型：自提，非自提")
    @FieldInterpretation(value = "自提类型：自提，非自提")
    private String pickUpType;

    /**
     * ERP地点编码
     */
    @ApiModelProperty(value = "ERP地点编码")
    @FieldInterpretation(value = "ERP地点编码")
    private String erpSiteCode;

    /**
     * ERP地点地址
     */
    @ApiModelProperty(value = "自提类型：自提，非自提")
    @FieldInterpretation(value = "ERP地点地址")
    private String erpSiteAddress;

    /**
     * ERP地址ID
     */
    @ApiModelProperty(value = "ERP地址ID")
    private String erpCustomerAddressId;
    /**
     * ERP收货方地点ID
     */
    @ApiModelProperty(value = "ERP收货方地点ID")
    private String erpSiteId;
    /**
     * ERP收货方地点用途ID
     */
    @ApiModelProperty(value = "ERP收货方地点用途ID")
    private String erpShipToSiteUseId;
    /**
     * ERP客户ID
     */
    @ApiModelProperty(value = "ERP客户ID")
    private String erpCustomerId;
    /**
     * ERP客户编码
     */
    @ApiModelProperty(value = "ERP客户编码")
    private String erpCustomerCode;
    /**
     * ERP卸货点代码
     */
    @ApiModelProperty(value = "ERP卸货点代码")
    private String erpEdiLocation;
    /**
     * ERP工厂代码
     */
    @ApiModelProperty(value = "ERP工厂代码")
    private String erpPlantCode;
    /**
     * 物料Edi标识
     */
    @ApiModelProperty(value = "物料Edi标识")
    @FieldInterpretation(value = "物料Edi标识")
    private String productEdiFlag;

    @Override
    public void clean() {

    }

    public SimpleVO toSimpleVO() {
        return SimpleVO.builder()
                .id(this.getOemCode())
                .code(this.getOemCode())
                .name(this.getOemName())
                .build();
    }

}
