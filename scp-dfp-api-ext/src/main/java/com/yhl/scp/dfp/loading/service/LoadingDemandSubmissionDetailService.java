package com.yhl.scp.dfp.loading.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailDTO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <code>LoadingDemandSubmissionDetailService</code>
 * <p>
 * 装车需求提报详情应用接口
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 14:29:12
 */
public interface LoadingDemandSubmissionDetailService extends BaseService<LoadingDemandSubmissionDetailDTO, LoadingDemandSubmissionDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link LoadingDemandSubmissionDetailVO}
     */
    List<LoadingDemandSubmissionDetailVO> selectAll();

    /**
     * 批量删除
     *
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 根据版本需求类型删除历史旧数据
     *
     * @param submissionId
     * @param contentType
     * @return
     */
    int deleteByVersionIdAndSubmissionType(String submissionId, String contentType);

    void updateQuantityById(String id, BigDecimal demandQuantity);

    List<String> selectByOriginIdAndOemCode(String originVersionId, String oemCode, String contentType);

    int deleteBySubmissionIds(List<String> submissionIds);

	List<LoadingDemandSubmissionDetailVO> selectVOByParams(Map<String, Object> param);
}
