package com.yhl.scp.dfp.delivery.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDetailDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanDetailVO;

import java.util.List;

/**
 * <code>DeliveryPlanDetailService</code>
 * <p>
 * 发货计划明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:52
 */
public interface DeliveryPlanDetailService extends BaseService<DeliveryPlanDetailDTO, DeliveryPlanDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link DeliveryPlanDetailVO}
     */
    List<DeliveryPlanDetailVO> selectAll();

    /**
     * 批量删除
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    List<DeliveryPlanDetailVO> selectByPlanDetailIdList(List<String> deliveryPlanIdList);

	void doRemoveEditSign(List<String> deliveryPlanDataIds);

	void doUpdateDeliveryPlanDetail(List<DeliveryPlanPublishedDTO> publishedList);
}
