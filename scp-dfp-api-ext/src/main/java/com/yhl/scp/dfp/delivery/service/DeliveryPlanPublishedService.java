package com.yhl.scp.dfp.delivery.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedParamsDTO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;

import java.util.List;
import java.util.Map;

/**
 * <code>DeliveryPlanPublishedService</code>
 * <p>
 * 发货计划发布表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-17 15:59:51
 */
public interface DeliveryPlanPublishedService extends BaseService<DeliveryPlanPublishedDTO, DeliveryPlanPublishedVO> {

    /**
     * 查询所有
     *
     * @return list {@link DeliveryPlanPublishedVO}
     */
    List<DeliveryPlanPublishedVO> selectAll();

    BaseResponse<String> publish();

	List<DeliveryPlanPublishedVO> selectSumDeliveryPlanPublished(Map<String, Object> params);

    List<DeliveryPlanPublishedVO> selectDeliveryPlanPublishedByParamOnDynamicColumns(List<String> dynamicColumnParam, Map<String, Object> queryParam);

    PageInfo<DeliveryPlanPublishedVO> selectDateByParams(DeliveryPlanPublishedParamsDTO paramsDTO);

    List<LabelValue<String>> getOem();

    void doUpdateCoordinationQuantity2Null(List<String> idList);

    void doUpdateCoordinationQuantity(List<DeliveryPlanPublishedDTO> list);

    void doUpdateDemandQuantity(List<DeliveryPlanPublishedDTO> list, String scenario, String planner);

}
