package com.yhl.scp.dfp.common.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <code>GranularityEnum</code>
 * <p>
 * 需求类型
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-13 15:06:58
 */
public enum DemandTypeEnum implements CommonEnum {

    LOADING_DEMAND("LOADING_DEMAND", "装车需求");
    // PROJECT_FORECAST("PROJECT_FORECAST", "项目需求");

    private String code;
    private String desc;

    DemandTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

}