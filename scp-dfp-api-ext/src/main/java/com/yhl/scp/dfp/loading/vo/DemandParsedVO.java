package com.yhl.scp.dfp.loading.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>GeminiLLMHandler</code>
 * <p>
 * Google Gemini多模态大模型处理器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-01 11:28:55
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemandParsedVO implements Serializable {

    @ExcelProperty("主机厂编码")
    private String oemCode;

    @ExcelProperty("地点")
    private String location;

    @ExcelProperty("客户零件号")
    private String custItemNum;

    @ExcelProperty("有效日期起")
    private String demandDateFrom;

    @ExcelProperty("有效日期止")
    private String demandDateTo;

    @ExcelProperty("数量")
    private BigDecimal quantity;

    @ExcelProperty("状态")
    private String status;
}
