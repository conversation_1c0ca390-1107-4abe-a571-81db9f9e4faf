package com.yhl.scp.dfp.demand.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <code>DemandForecastEstablishmentProductVO</code>
 * <p>
 * 业务预测试制物品返回vo
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-14 14:43:19
 */
@ApiModel(value = "业务预测编制车型物品物品返回vo")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DemandForecastEstablishmentProductVO implements Serializable {

    @ApiModelProperty(value = "需求类型")
    private String demandType;

    @ApiModelProperty(value = "本厂编码")
    private String productCode;

    @ApiModelProperty(value = "零件风险等级")
    private String riskLevel;

    @ApiModelProperty(value = "取数位置")
    private String accessPosition;

    @ApiModelProperty(value = "动态列详情")
    private List<EstablishmentDynamicDataDetailVO> detailVOList;
}
