package com.yhl.scp.dfp.loading.handler;

import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.loading.vo.LoadingProductVO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <code>LoadingDataHolder</code>
 * <p>
 * LoadingDataHolder
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-12 16:58:22
 */
@Data
@Builder
public class LoadingDataHolder implements Serializable {

    private static final long serialVersionUID = 3258457313555065660L;

    private Map<String, List<String>> productPartMap;

    private Map<String, String> partEnabledMap;

    private Map<String, List<OemVO>> oemMap;

    private Map<String, LoadingDemandSubmissionVO> submissionVOMap;

    private List<OemStockPointMapVO> oemStockPointMapVOS;

    private List<LoadingProductVO> productStockPointVOS;
    
    Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailVOMap;

    private Map<String, List<String>> productCodeVehicleCodeMap;

}