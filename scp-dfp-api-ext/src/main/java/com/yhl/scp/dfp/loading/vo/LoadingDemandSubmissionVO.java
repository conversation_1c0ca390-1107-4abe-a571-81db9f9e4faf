package com.yhl.scp.dfp.loading.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <code>LoadingDemandSubmissionVO</code>
 * <p>
 * 装车需求提报VO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 10:11:42
 */
@ApiModel(value = "装车需求提报VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoadingDemandSubmissionVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 897687609277841253L;

    /**
     * 原始需求版本ID
     */
    @ApiModelProperty(value = "原始需求版本ID")
    @FieldInterpretation(value = "原始需求版本ID")
    private String versionId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    @FieldInterpretation(value = "需求类型")
    private String demandCategory;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemName;
    /**
     * 主机厂零件号
     */
    @ApiModelProperty(value = "主机厂零件号")
    @FieldInterpretation(value = "主机厂零件号")
    private String partNumber;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;

    /**
     * 日需求
     */
    @ApiModelProperty(value = "日需求")
    @FieldInterpretation(value = "日需求")
    private List<LoadingDemandSubmissionDetailVO> dayData;

    /**
     * 日需求汇总
     */
    @ApiModelProperty(value = "日需求汇总")
    @FieldInterpretation(value = "日需求汇总")
    private List<LoadingDemandSubmissionDetailVO> daySummaryData;

    /**
     * 预测数据
     */
    @ApiModelProperty(value = "预测数据")
    @FieldInterpretation(value = "预测数据")
    private List<LoadingDemandSubmissionDetailVO> monthData;

    @Override
    public void clean() {

    }
}
