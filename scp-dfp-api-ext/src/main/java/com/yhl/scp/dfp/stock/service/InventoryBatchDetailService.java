package com.yhl.scp.dfp.stock.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesInventoryBatchDetail;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.vo.RealTimeInventoryVO;
import com.yhl.scp.dfp.stock.dto.InventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.extension.product.vo.StockPointVO;

import java.util.List;
import java.util.Map;

/**
 * <code>InventoryBatchDetailService</code>
 * <p>
 * 库存批次明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 09:53:03
 */
public interface InventoryBatchDetailService extends BaseService<InventoryBatchDetailDTO, InventoryBatchDetailVO> {

    /**
     * 查询所有
     *
     * @return list {@link InventoryBatchDetailVO}
     */
    List<InventoryBatchDetailVO> selectAll();
    int doDeleteAll(String sourceType);
    int doDeleteAllByOrgId(String sourceType,String originalOrgId);
    int doDeleteAllByOrgIds(String sourceType,List<String> orgIds);

    BaseResponse<Void> syncStockBatchDetail(Scenario scenario, String stockPoints);
    BaseResponse<Void> syncErpStockBatchDetail(Scenario scenario,String stockPoints);

    BaseResponse<Void> syncMesStockBatchDetail(Scenario scenario, String stockPoints);
    BaseResponse<Void> syncMesStockBatchDetail(Scenario scenario, List<String> stockPoints);

    List<InventoryBatchDetailVO> selectByProductCodes(List<String> productCodes, String stockPointType);

	List<InventoryBatchDetailVO> selectByParamMap(Map<String, Object> params);

    int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS);

    Map<String, Integer> getRealTimeInventory(List<String> productScope);

    BaseResponse<Void> syncMes(String scenario,List<MesInventoryBatchDetail> mesInventoryBatchDetails,List<String> orgIds);
    BaseResponse<Void> syncErp(String scenario,List<ErpInventoryBatchDetail> erpInventoryBatchDetails,String orgId);

	List<InventoryBatchDetailVO> selectCollectByGroupType(String groupTyep);

    PageInfo<InventoryBatchDetailVO> selectByParamsPage(Map<String, Object> params);

    List<InventoryBatchDetailVO> selectByVOParams(Map<String, Object> params);

    PageInfo<InventoryBatchDetailVO> selectInventoryBatchDetailPageCustomize(Pagination pagination, String sortParam, String queryCriteriaParam, String overdueSort);

    List<InventoryBatchDetailVO> selectVOByParams(Map<String, Object> params);
    List<InventoryBatchDetailVO> selectAllGlassInventoryBatch();
    void doProcessSupply(String scenario);
}
