package com.yhl.scp.dfp.common.enums;

import com.yhl.platform.common.enums.CommonEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * <code>VersionTypeEnum</code>
 * <p>
 * 版本类型枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 10:09:01
 */
public enum VersionTypeEnum implements CommonEnum {
    // OD
    ORIGIN_DEMAND("ORIGIN_DEMAND", "原始需求"),
    // STD DAILY_DEMAND DD
    CLEAN_DEMAND("CLEAN_DEMAND", "日需求"),
    // LTD ROLLING_FORECAST RF
    CLEAN_FORECAST("C<PERSON>AN_FORECAST", "滚动预测"),
    // FC ALGORITHM_FORECAST AF
    CLEAN_ALGORITHM("CLEAN_ALGORITHM", "算法预测"),
    // DFC DF
    DEMAND_FORECAST("DEMAND_FORECAST", "需求预测"),
    // DP CDF
    CONSISTENCE_DEMAND_FORECAST("CONSISTENCE_DEMAND_FORECAST", "一致性需求预测"),
    // PF ??
    PROJECT_FORECAST("PROJECT_FORECAST", "项目预测"),
    // DLP DP
    DELIVERY_PLAN("DELIVERY_PLAN", "发货计划");

    private String code;

    private String desc;

    VersionTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * 获取枚举所有的值
     */

    public static List<String> getAllValues() {
        VersionTypeEnum[] values = VersionTypeEnum.values();
        List<String> list = new ArrayList<>();
        for (VersionTypeEnum value : values) {
            list.add(value.getCode());
        }
        return list;
    }

    /**
     * 根据code获取枚举
     */
    public static VersionTypeEnum getByCode(String code) {
        VersionTypeEnum[] values = VersionTypeEnum.values();
        for (VersionTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getDescByCode(String code) {
        VersionTypeEnum[] values = VersionTypeEnum.values();
        for (VersionTypeEnum value : values) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

}