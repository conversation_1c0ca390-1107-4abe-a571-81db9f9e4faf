<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.originalFilm.infrastructure.dao.OriginalFilmDemandConsultDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultDetailPO">
        <!--@Table mrp_original_film_demand_consult_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="consult_summary_id" jdbcType="VARCHAR" property="consultSummaryId"/>
        <result column="demand_source_type" jdbcType="VARCHAR" property="demandSourceType"/>
        <result column="demand_source_id" jdbcType="VARCHAR" property="demandSourceId"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="original_film_product_id" jdbcType="VARCHAR" property="originalFilmProductId"/>
        <result column="demanded_quantity" jdbcType="VARCHAR" property="demandedQuantity"/>
        <result column="demanded_date" jdbcType="TIMESTAMP" property="demandedDate"/>
        <result column="float_inventory_quantity" jdbcType="VARCHAR" property="floatInventoryQuantity"/>
        <result column="port_road_inventory" jdbcType="VARCHAR" property="portRoadInventory"/>
        <result column="port_inventory" jdbcType="VARCHAR" property="portInventory"/>
        <result column="factory_inventory" jdbcType="VARCHAR" property="factoryInventory"/>
        <result column="standard_demanded_quantity" jdbcType="VARCHAR" property="standardDemandedQuantity"/>
        <result column="used_as_replace_quantity" jdbcType="VARCHAR" property="usedAsReplaceQuantity"/>
        <result column="use_replace_quantity" jdbcType="VARCHAR" property="useReplaceQuantity"/>
        <result column="net_demanded_quantity" jdbcType="VARCHAR" property="netDemandedQuantity"/>
        <result column="special_requirements" jdbcType="VARCHAR" property="specialRequirements"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="mpbl_spec" jdbcType="VARCHAR" property="mpblSpec"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultDetailVO">
    	<result column="product_name" jdbcType="VARCHAR" property="productName"/>
    	<result column="original_film_product_name" jdbcType="VARCHAR" property="originalFilmProductName"/>
    	<result column="original_film_product_code" jdbcType="VARCHAR" property="originalFilmProductCode"/>
    	<result column="original_film_product_length" jdbcType="DECIMAL" property="originalFilmProductLength"/>
    	<result column="original_film_product_width" jdbcType="DECIMAL" property="originalFilmProductWidth"/>
    	<result column="original_film_product_thickness" jdbcType="DECIMAL" property="originalFilmProductThickness"/>
    	<result column="original_film_product_color" jdbcType="VARCHAR" property="originalFilmProductColor"/>
    	<result column="area" jdbcType="VARCHAR" property="area"/>
    	<result column="weight" jdbcType="VARCHAR" property="weight"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
		consult_summary_id,
		demand_source_type,
		demand_source_id,
		vehicle_model_code,
		product_id,
        product_code,
		original_film_product_id,
		demanded_quantity,
		demanded_date,
		float_inventory_quantity,
		port_road_inventory,
		port_inventory,
		factory_inventory,
		standard_demanded_quantity,
		used_as_replace_quantity,
		use_replace_quantity,
		net_demanded_quantity,
        special_requirements,
        area,
        weight,
        mpbl_spec,
		remark,
		enabled,
		creator,
		create_time,
		modifier,
		modify_time,
		version_value
    </sql>
    <sql id="VO_Column_List">
		id,
		consult_summary_id,
		demand_source_type,
		demand_source_id,
		vehicle_model_code,
		product_id,
		original_film_product_id,
		demanded_quantity,
		demanded_date,
		float_inventory_quantity,
		port_road_inventory,
		port_inventory,
		factory_inventory,
		standard_demanded_quantity,
		used_as_replace_quantity,
		use_replace_quantity,
		net_demanded_quantity,
		special_requirements,
		version_value,
        product_name,
        original_film_product_name,
        original_film_product_code,
        original_film_product_length,
        original_film_product_width,
        original_film_product_thickness,
        original_film_product_color,
		area,
		weight,
		mpbl_spec
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.consultSummaryId != null and params.consultSummaryId != ''">
                and consult_summary_id = #{params.consultSummaryId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandSourceType != null and params.demandSourceType != ''">
                and demand_source_type = #{params.demandSourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.demandSourceId != null and params.demandSourceId != ''">
                and demand_source_id = #{params.demandSourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_id = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_code = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.originalFilmProductId != null and params.originalFilmProductId != ''">
                and original_film_product_id = #{params.originalFilmProductId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandedQuantity != null">
                and demanded_quantity = #{params.demandedQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.demandedDate != null">
                and demanded_date = #{params.demandedDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.floatInventoryQuantity != null">
                and float_inventory_quantity = #{params.floatInventoryQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.portRoadInventory != null">
                and port_road_inventory = #{params.portRoadInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.portInventory != null">
                and port_inventory = #{params.portInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.factoryInventory != null">
                and factory_inventory = #{params.factoryInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.standardDemandedQuantity != null">
                and standard_demanded_quantity = #{params.standardDemandedQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.usedAsReplaceQuantity != null">
                and used_as_replace_quantity = #{params.usedAsReplaceQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.useReplaceQuantity != null">
                and use_replace_quantity = #{params.useReplaceQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.netDemandedQuantity != null">
                and net_demanded_quantity = #{params.netDemandedQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.specialRequirements != null">
                and special_requirements = #{params.specialRequirements,jdbcType=VARCHAR}
            </if>
            <if test="params.area != null">
                and area = #{params.area,jdbcType=VARCHAR}
            </if>
            <if test="params.weight != null">
                and weight = #{params.weight,jdbcType=VARCHAR}
            </if>
            <if test="params.mpblSpec != null and params.mpblSpec != ''">
                and mpbl_spec = #{params.mpblSpec,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.consultSummaryIds != null and params.consultSummaryIds.size() > 0">
            	and consult_summary_id in
            	<foreach collection="params.consultSummaryIds" item="item" index="index" open="(" separator="," close=")">
                	#{item}
            	</foreach>
        	</if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_original_film_demand_consult_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_original_film_demand_consult_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mrp_original_film_demand_consult_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_original_film_demand_consult_detail
        <include refid="Base_Where_Condition" />
        order by demanded_date
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mrp_original_film_demand_consult_detail
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_original_film_demand_consult_detail(
        id,
        consult_summary_id,
        demand_source_type,
        demand_source_id,
        vehicle_model_code,
        product_id,
        product_code,
        original_film_product_id,
        demanded_quantity,
        demanded_date,
        float_inventory_quantity,
        port_road_inventory,
        port_inventory,
        factory_inventory,
        standard_demanded_quantity,
        used_as_replace_quantity,
        use_replace_quantity,
        net_demanded_quantity,
        special_requirements,
        area,
        weight,
        mpbl_spec,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{consultSummaryId,jdbcType=VARCHAR},
        #{demandSourceType,jdbcType=VARCHAR},
        #{demandSourceId,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{originalFilmProductId,jdbcType=VARCHAR},
        #{demandedQuantity,jdbcType=VARCHAR},
        #{demandedDate,jdbcType=TIMESTAMP},
        #{floatInventoryQuantity,jdbcType=VARCHAR},
        #{portRoadInventory,jdbcType=VARCHAR},
        #{portInventory,jdbcType=VARCHAR},
        #{factoryInventory,jdbcType=VARCHAR},
        #{standardDemandedQuantity,jdbcType=VARCHAR},
        #{usedAsReplaceQuantity,jdbcType=VARCHAR},
        #{useReplaceQuantity,jdbcType=VARCHAR},
        #{netDemandedQuantity,jdbcType=VARCHAR},
        #{specialRequirements,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR},
        #{weight,jdbcType=VARCHAR},
        #{mpblSpec,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultDetailPO">
        insert into mrp_original_film_demand_consult_detail(
        id,
        consult_summary_id,
        demand_source_type,
        demand_source_id,
        vehicle_model_code,
        product_id,
        product_code,
        original_film_product_id,
        demanded_quantity,
        demanded_date,
        float_inventory_quantity,
        port_road_inventory,
        port_inventory,
        factory_inventory,
        standard_demanded_quantity,
        used_as_replace_quantity,
        use_replace_quantity,
        net_demanded_quantity,
        special_requirements,
        area,
        weight,
        mpbl_spec,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{consultSummaryId,jdbcType=VARCHAR},
        #{demandSourceType,jdbcType=VARCHAR},
        #{demandSourceId,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{originalFilmProductId,jdbcType=VARCHAR},
        #{demandedQuantity,jdbcType=VARCHAR},
        #{demandedDate,jdbcType=TIMESTAMP},
        #{floatInventoryQuantity,jdbcType=VARCHAR},
        #{portRoadInventory,jdbcType=VARCHAR},
        #{portInventory,jdbcType=VARCHAR},
        #{factoryInventory,jdbcType=VARCHAR},
        #{standardDemandedQuantity,jdbcType=VARCHAR},
        #{usedAsReplaceQuantity,jdbcType=VARCHAR},
        #{useReplaceQuantity,jdbcType=VARCHAR},
        #{netDemandedQuantity,jdbcType=VARCHAR},
        #{specialRequirements,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR},
        #{weight,jdbcType=VARCHAR},
        #{mpblSpec,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_original_film_demand_consult_detail(
        id,
        consult_summary_id,
        demand_source_type,
        demand_source_id,
        vehicle_model_code,
        product_id,
        product_code,
        original_film_product_id,
        demanded_quantity,
        demanded_date,
        float_inventory_quantity,
        port_road_inventory,
        port_inventory,
        factory_inventory,
        standard_demanded_quantity,
        used_as_replace_quantity,
        use_replace_quantity,
        net_demanded_quantity,
        special_requirements,
        area,
        weight,
        mpbl_spec,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.consultSummaryId,jdbcType=VARCHAR},
        #{entity.demandSourceType,jdbcType=VARCHAR},
        #{entity.demandSourceId,jdbcType=VARCHAR},
        #{entity.vehicleModelCode,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.originalFilmProductId,jdbcType=VARCHAR},
        #{entity.demandedQuantity,jdbcType=VARCHAR},
        #{entity.demandedDate,jdbcType=TIMESTAMP},
        #{entity.floatInventoryQuantity,jdbcType=VARCHAR},
        #{entity.portRoadInventory,jdbcType=VARCHAR},
        #{entity.portInventory,jdbcType=VARCHAR},
        #{entity.factoryInventory,jdbcType=VARCHAR},
        #{entity.standardDemandedQuantity,jdbcType=VARCHAR},
        #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
        #{entity.useReplaceQuantity,jdbcType=VARCHAR},
        #{entity.netDemandedQuantity,jdbcType=VARCHAR},
        #{entity.specialRequirements,jdbcType=VARCHAR},
        #{entity.area,jdbcType=VARCHAR},
        #{entity.weight,jdbcType=VARCHAR},
        #{entity.mpblSpec,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_original_film_demand_consult_detail(
        id,
        consult_summary_id,
        demand_source_type,
        demand_source_id,
        vehicle_model_code,
        product_id,
        product_code,
        original_film_product_id,
        demanded_quantity,
        demanded_date,
        float_inventory_quantity,
        port_road_inventory,
        port_inventory,
        factory_inventory,
        standard_demanded_quantity,
        used_as_replace_quantity,
        use_replace_quantity,
        net_demanded_quantity,
        special_requirements,
        area,
        weight,
        mpbl_spec,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.consultSummaryId,jdbcType=VARCHAR},
        #{entity.demandSourceType,jdbcType=VARCHAR},
        #{entity.demandSourceId,jdbcType=VARCHAR},
        #{entity.vehicleModelCode,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.originalFilmProductId,jdbcType=VARCHAR},
        #{entity.demandedQuantity,jdbcType=VARCHAR},
        #{entity.demandedDate,jdbcType=TIMESTAMP},
        #{entity.floatInventoryQuantity,jdbcType=VARCHAR},
        #{entity.portRoadInventory,jdbcType=VARCHAR},
        #{entity.portInventory,jdbcType=VARCHAR},
        #{entity.factoryInventory,jdbcType=VARCHAR},
        #{entity.standardDemandedQuantity,jdbcType=VARCHAR},
        #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
        #{entity.useReplaceQuantity,jdbcType=VARCHAR},
        #{entity.netDemandedQuantity,jdbcType=VARCHAR},
        #{entity.specialRequirements,jdbcType=VARCHAR},
        #{entity.area,jdbcType=VARCHAR},
        #{entity.weight,jdbcType=VARCHAR},
        #{entity.mpblSpec,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultDetailPO">
        update mrp_original_film_demand_consult_detail set
        consult_summary_id = #{consultSummaryId,jdbcType=VARCHAR},
        demand_source_type = #{demandSourceType,jdbcType=VARCHAR},
        demand_source_id = #{demandSourceId,jdbcType=VARCHAR},
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
        product_id = #{productId,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        original_film_product_id = #{originalFilmProductId,jdbcType=VARCHAR},
        demanded_quantity = #{demandedQuantity,jdbcType=VARCHAR},
        demanded_date = #{demandedDate,jdbcType=TIMESTAMP},
        float_inventory_quantity = #{floatInventoryQuantity,jdbcType=VARCHAR},
        port_road_inventory = #{portRoadInventory,jdbcType=VARCHAR},
        port_inventory = #{portInventory,jdbcType=VARCHAR},
        factory_inventory = #{factoryInventory,jdbcType=VARCHAR},
        standard_demanded_quantity = #{standardDemandedQuantity,jdbcType=VARCHAR},
        used_as_replace_quantity = #{usedAsReplaceQuantity,jdbcType=VARCHAR},
        use_replace_quantity = #{useReplaceQuantity,jdbcType=VARCHAR},
        net_demanded_quantity = #{netDemandedQuantity,jdbcType=VARCHAR},
        special_requirements = #{specialRequirements,jdbcType=VARCHAR},
        area = #{area,jdbcType=VARCHAR},
        weight = #{weight,jdbcType=VARCHAR},
        mpbl_spec = #{mpblSpec,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
        and version_value = #{versionValue,jdbcType=INTEGER};
        update mrp_original_film_demand_consult_detail set
        version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultDetailPO">
        update mrp_original_film_demand_consult_detail
        <set>
            <if test="item.consultSummaryId != null and item.consultSummaryId != ''">
                consult_summary_id = #{item.consultSummaryId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandSourceType != null and item.demandSourceType != ''">
                demand_source_type = #{item.demandSourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.demandSourceId != null and item.demandSourceId != ''">
                demand_source_id = #{item.demandSourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.originalFilmProductId != null and item.originalFilmProductId != ''">
                original_film_product_id = #{item.originalFilmProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandedQuantity != null">
                demanded_quantity = #{item.demandedQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.demandedDate != null">
                demanded_date = #{item.demandedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.floatInventoryQuantity != null">
                float_inventory_quantity = #{item.floatInventoryQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.portRoadInventory != null">
                port_road_inventory = #{item.portRoadInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.portInventory != null">
                port_inventory = #{item.portInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.factoryInventory != null">
                factory_inventory = #{item.factoryInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.standardDemandedQuantity != null">
                standard_demanded_quantity = #{item.standardDemandedQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.usedAsReplaceQuantity != null">
                used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.useReplaceQuantity != null">
                use_replace_quantity = #{item.useReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.netDemandedQuantity != null">
                net_demanded_quantity = #{item.netDemandedQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.specialRequirements != null">
                special_requirements = #{item.specialRequirements,jdbcType=VARCHAR},
            </if>
            <if test="item.area != null">
                area = #{item.area,jdbcType=VARCHAR},
            </if>
            <if test="item.weight != null">
                weight = #{item.weight,jdbcType=VARCHAR},
            </if>
            <if test="item.mpblSpec != null and item.mpblSpec != ''">
                mpbl_spec = #{item.mpblSpec,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER};
        update mrp_original_film_demand_consult_detail set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_original_film_demand_consult_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="consult_summary_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.consultSummaryId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_source_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandSourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_source_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandSourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="original_film_product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalFilmProductId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demanded_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandedQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demanded_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandedDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="float_inventory_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.floatInventoryQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="port_road_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.portRoadInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="port_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.portInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="factory_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.factoryInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_demanded_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardDemandedQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="used_as_replace_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.usedAsReplaceQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="use_replace_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.useReplaceQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="net_demanded_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.netDemandedQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="special_requirements = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.specialRequirements,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.area,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.weight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="mpbl_spec = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.mpblSpec,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
         and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update mrp_original_film_demand_consult_detail set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mrp_original_film_demand_consult_detail 
        <set>
            <if test="item.consultSummaryId != null and item.consultSummaryId != ''">
                consult_summary_id = #{item.consultSummaryId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandSourceType != null and item.demandSourceType != ''">
                demand_source_type = #{item.demandSourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.demandSourceId != null and item.demandSourceId != ''">
                demand_source_id = #{item.demandSourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.originalFilmProductId != null and item.originalFilmProductId != ''">
                original_film_product_id = #{item.originalFilmProductId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandedQuantity != null">
                demanded_quantity = #{item.demandedQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.demandedDate != null">
                demanded_date = #{item.demandedDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.floatInventoryQuantity != null">
                float_inventory_quantity = #{item.floatInventoryQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.portRoadInventory != null">
                port_road_inventory = #{item.portRoadInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.portInventory != null">
                port_inventory = #{item.portInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.factoryInventory != null">
                factory_inventory = #{item.factoryInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.standardDemandedQuantity != null">
                standard_demanded_quantity = #{item.standardDemandedQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.usedAsReplaceQuantity != null">
                used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.useReplaceQuantity != null">
                use_replace_quantity = #{item.useReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.netDemandedQuantity != null">
                net_demanded_quantity = #{item.netDemandedQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.specialRequirements != null">
                special_requirements = #{item.specialRequirements,jdbcType=VARCHAR},
            </if>
            <if test="item.area != null">
                area = #{item.area,jdbcType=VARCHAR},
            </if>
            <if test="item.weight != null">
                weight = #{item.weight,jdbcType=VARCHAR},
            </if>
            <if test="item.mpblSpec != null and item.mpblSpec != ''">
                mpbl_spec = #{item.mpblSpec,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}   
         and version_value = #{item.versionValue,jdbcType=INTEGER};
            update mrp_original_film_demand_consult_detail set
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER} 
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_original_film_demand_consult_detail where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_original_film_demand_consult_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    <!-- 通过原片需求汇总ids删除原片需求汇总明细数据 -->
    <delete id="deleteByConsultSummaryIds" parameterType="java.util.List">
        delete from mrp_original_film_demand_consult_detail where consult_summary_id in
        <foreach collection="consultSummaryIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    <select id="selectByDemandSourceIds" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mrp_original_film_demand_consult_detail
        where 1=1 
        and consult_summary_id in
		<foreach collection="consultSummaryIds" item="item" index="index" open="(" separator="," close=")">
        	#{item}
    	</foreach>
        order by demanded_date
    </select>
</mapper>
