package com.yhl.scp.mrp.material.purchase.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesReturnedPurchase;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.enums.ArrivalTrackingDataSourceEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.purchase.convertor.MaterialReturnedPurchaseConvertor;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialReturnedPurchaseDO;
import com.yhl.scp.mrp.material.purchase.domain.service.MaterialReturnedPurchaseDomainService;
import com.yhl.scp.mrp.material.purchase.dto.MaterialReturnedPurchaseDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialReturnedPurchaseDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialReturnedPurchasePO;
import com.yhl.scp.mrp.material.purchase.service.MaterialReturnedPurchaseService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialReturnedPurchaseVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.order.service.PurchaseOrderInfoService;
import com.yhl.scp.mrp.order.vo.PurchaseOrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialReturnedPurchaseServiceImpl</code>
 * <p>
 * 采购退货应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-08 15:17:25
 */
@Slf4j
@Service
public class MaterialReturnedPurchaseServiceImpl extends AbstractService implements MaterialReturnedPurchaseService {

    @Resource
    private MaterialReturnedPurchaseDao materialReturnedPurchaseDao;

    @Resource
    private MaterialReturnedPurchaseDomainService materialReturnedPurchaseDomainService;

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private PurchaseOrderInfoService purchaseOrderInfoService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialReturnedPurchaseDTO materialReturnedPurchaseDTO) {
        // 0.数据转换
        MaterialReturnedPurchaseDO materialReturnedPurchaseDO = MaterialReturnedPurchaseConvertor.INSTANCE.dto2Do(materialReturnedPurchaseDTO);
        MaterialReturnedPurchasePO materialReturnedPurchasePO = MaterialReturnedPurchaseConvertor.INSTANCE.dto2Po(materialReturnedPurchaseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialReturnedPurchaseDomainService.validation(materialReturnedPurchaseDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialReturnedPurchasePO);
        materialReturnedPurchaseDao.insert(materialReturnedPurchasePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialReturnedPurchaseDTO materialReturnedPurchaseDTO) {
        // 0.数据转换
        MaterialReturnedPurchaseDO materialReturnedPurchaseDO = MaterialReturnedPurchaseConvertor.INSTANCE.dto2Do(materialReturnedPurchaseDTO);
        MaterialReturnedPurchasePO materialReturnedPurchasePO = MaterialReturnedPurchaseConvertor.INSTANCE.dto2Po(materialReturnedPurchaseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialReturnedPurchaseDomainService.validation(materialReturnedPurchaseDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialReturnedPurchasePO);
        materialReturnedPurchaseDao.update(materialReturnedPurchasePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialReturnedPurchaseDTO> list) {
        List<MaterialReturnedPurchasePO> newList = MaterialReturnedPurchaseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialReturnedPurchaseDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialReturnedPurchaseDTO> list) {
        List<MaterialReturnedPurchasePO> newList = MaterialReturnedPurchaseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialReturnedPurchaseDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialReturnedPurchaseDTO> list) {
        List<MaterialReturnedPurchasePO> newList = MaterialReturnedPurchaseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialReturnedPurchaseDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialReturnedPurchaseDao.deleteBatch(idList);
        }
        return materialReturnedPurchaseDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialReturnedPurchaseVO selectByPrimaryKey(String id) {
        MaterialReturnedPurchasePO po = materialReturnedPurchaseDao.selectByPrimaryKey(id);
        return MaterialReturnedPurchaseConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MATERIAL_RETURNED_PURCHASE")
    public List<MaterialReturnedPurchaseVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MATERIAL_RETURNED_PURCHASE")
    public List<MaterialReturnedPurchaseVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialReturnedPurchaseVO> dataList = materialReturnedPurchaseDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialReturnedPurchaseServiceImpl target = springBeanUtils.getBean(MaterialReturnedPurchaseServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialReturnedPurchaseVO> selectByParams(Map<String, Object> params) {
        List<MaterialReturnedPurchasePO> list = materialReturnedPurchaseDao.selectByParams(params);
        return MaterialReturnedPurchaseConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialReturnedPurchaseVO> selectVOByParams(Map<String, Object> params) {
        return materialReturnedPurchaseDao.selectVOByParams(params);
    }

    @Override
    public List<MaterialReturnedPurchaseVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> handleReturnedPurchase(List<MesReturnedPurchase> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success();
        }
        List<MaterialReturnedPurchaseDTO> insertDtoS = new ArrayList<>();
        List<MaterialReturnedPurchaseDTO> updateDtoS = new ArrayList<>();
        List<MaterialReturnedPurchasePO> oldPos = materialReturnedPurchaseDao.selectReturnedPurchase(list);
        Map<String, MaterialReturnedPurchasePO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getOrgId() + "_" + t.getProductCode()+"_"+t.getDocumentNum()+"_"+t.getLineNum(),
                        Function.identity(), (v1, v2) -> v1));
        for (MesReturnedPurchase mesReturnedPurchase : list) {
            MaterialReturnedPurchaseDTO dto =  new MaterialReturnedPurchaseDTO();
            String id= mesReturnedPurchase.getPlantId() + "_" + mesReturnedPurchase.getItemCode()+"_"+mesReturnedPurchase.getDocumentNum()+"_"+mesReturnedPurchase.getLineNum();
            if (oldPosMap.containsKey(id)) {
                MaterialReturnedPurchasePO oldPo = oldPosMap.get(id);
                org.springframework.beans.BeanUtils.copyProperties(oldPo, dto);
                generateDto(dto, mesReturnedPurchase);
                updateDtoS.add(dto);
            } else {
                generateDto(dto, mesReturnedPurchase);
                insertDtoS.add(dto);
            }

        }
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            doCreateBatch(insertDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateDtoS)) {
            doUpdateBatch(updateDtoS);
        }

        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Void> syncReturnedPurchase(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.RETURNED_PURCHASE.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public void doReturnedJob(Integer moveMinute) {
        log.info("材料到货跟踪退货开始");
        // 36小时前
        Date startModifyTime = DateUtils.moveHour(new Date(), -36);
        // 获取采购入库记录数据
        List<MaterialReturnedPurchaseVO> materialReturnedPurchaseVOList = this.selectVOByParams(ImmutableMap.of("startModifyTime", startModifyTime));
        materialReturnedPurchaseVOList = materialReturnedPurchaseVOList.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getLineNum())
                        && StringUtils.isNotEmpty(e.getPoNumber())
                        && StringUtils.isNotEmpty(e.getProductCode())
                        && !StringUtils.equals(e.getWhetherMatches(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toList());

        // 只需要处理非原片
        materialReturnedPurchaseVOList = materialReturnedPurchaseVOList.stream()
                .filter(data -> !data.getProductClassify().equals("RA.A"))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(materialReturnedPurchaseVOList)) {
            log.info("无退货数据");
            return;
        }

        // 合并相同的退货，并累加数量
        materialReturnedPurchaseVOList = new ArrayList<>(materialReturnedPurchaseVOList.stream()
                .collect(Collectors.toMap(
                        data -> String.join(",", data.getPoNumber(), data.getLineNum(), data.getProductCode()),
                        Function.identity(),
                        (v1, v2) -> {
                            v1.setReturnQty(String.valueOf(
                                    Integer.parseInt(v1.getReturnQty()) + Integer.parseInt(v2.getReturnQty())
                            ));
                            return v1;
                        }
                )).values());

        // 根据 PO + PO行 + 物料编码 分组汇总
        List<String> combineKeys02Map = materialReturnedPurchaseVOList.stream()
                .map(data -> String.join(",", data.getPoNumber(), data.getLineNum(), data.getProductCode()))
                .distinct().collect(Collectors.toList());

        // 获取采购订单（用于校验PO状态）
        List<PurchaseOrderInfoVO> purchaseOrderInfoVOList =
                purchaseOrderInfoService.selectByParams(ImmutableMap.of("combineKeys02Map", combineKeys02Map));
        Map<String, PurchaseOrderInfoVO>  purchaseOrderInfoMap = purchaseOrderInfoVOList.stream()
                .collect(Collectors.toMap(data -> String.join(",",
                                data.getOrderHeader(), data.getOrderLine(), data.getProductCode()),
                        Function.identity(), (v1, v2) -> v1));

        // 需要关闭得订单
        List<MaterialReturnedPurchaseVO> closeDataList = new ArrayList<>();

        // 过滤退货，在订单中行状态非关闭的才可退货
        materialReturnedPurchaseVOList = materialReturnedPurchaseVOList.stream().filter(data -> {
            String key = String.join(",", data.getPoNumber(), data.getLineNum(), data.getProductCode());
            PurchaseOrderInfoVO purchaseOrderInfoVO = purchaseOrderInfoMap.get(key);
            if (Objects.nonNull(purchaseOrderInfoVO) && purchaseOrderInfoVO.getLineStatus().equals("OPEN")) {
                return true;
            }
            // 订单表没有 或 状态不为OPEN 都需要关闭
            closeDataList.add(data);
            return false;
        }).collect(Collectors.toList());

        // 收集物料编码
        List<String> productCodes = materialReturnedPurchaseVOList.stream().map(MaterialReturnedPurchaseVO::getProductCode).distinct().collect(Collectors.toList());
        Map<String, NewProductStockPointVO> productStockPointVOMap = new HashMap<>();
        // 获取SJG库存点
        List<NewStockPointVO> stockPointVOList =
                newMdsFeign.selectStockPointByParams(DynamicDataSourceContextHolder.getDataSource(),
                        ImmutableMap.of("organizeType", StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode()));
        String stockPointCode = stockPointVOList.get(0).getStockPointCode();

        if (CollectionUtils.isNotEmpty(productCodes)) {
            // 获取物料
            FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("id", "product_code", "product_name", "stock_point_code"))
                    .queryParam(ImmutableMap.of("productCodes", productCodes,"stockPointCode",stockPointCode))
                    .build();
            List<NewProductStockPointVO> productStockPointVOList =
                    newMdsFeign.selectProductListByParamOnDynamicColumns(DynamicDataSourceContextHolder.getDataSource(), feignDynamicParam);

            productStockPointVOMap = productStockPointVOList.stream()
                    .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));
        }

        // 收集 采购订单号 + 采购订单行号 + 物料编码
        List<String> combineKeys = materialReturnedPurchaseVOList.stream().map(data -> String.join("_",
                data.getPoNumber(), data.getLineNum(), data.getProductCode())).distinct().collect(Collectors.toList());

        // 获取对应到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList =
                materialArrivalTrackingService.selectByParams(ImmutableMap.of("combineKeys", combineKeys));
        Map<String, MaterialArrivalTrackingVO> materialArrivalTrackingMap = materialArrivalTrackingVOList.stream()
                .filter(data -> data.getArrivalStatus().equals(ArrivalStatusEnum.PLAN_PRUCHASE.getCode()))
                .filter(data -> null != data.getWaitDeliveryQuantity())
                .collect(Collectors.toMap(data -> String.join("_",
                                data.getPurchaseOrderCode(), data.getPurchaseOrderLineCode(), data.getMaterialCode()),
                        Function.identity(), (v1, v2) -> v1));

        List<MaterialArrivalTrackingDTO> addList = new ArrayList<>();
        List<MaterialArrivalTrackingDTO> updateList = new ArrayList<>();
        List<MaterialReturnedPurchaseDTO> updateWhetherMatchesList = new ArrayList<>();

        // 匹配退货记录后，根据采购订单号、采购订单行号、物料编码，将退货数量加到到货跟踪的计划采购状态的待发货数量中，如果已没有计划采购状态的数据，则新增一行
        for (MaterialReturnedPurchaseVO vo : materialReturnedPurchaseVOList) {
            String key = String.join("_", vo.getPoNumber(), vo.getLineNum(), vo.getProductCode());

            // 获取对应的到货跟踪
            MaterialArrivalTrackingVO materialArrivalTrackingVO = materialArrivalTrackingMap.get(key);

            MaterialArrivalTrackingDTO materialArrivalTrackingDTO = new MaterialArrivalTrackingDTO();

            if (Objects.nonNull(materialArrivalTrackingVO)) {
                materialArrivalTrackingDTO.setWaitDeliveryQuantity(materialArrivalTrackingVO.getWaitDeliveryQuantity()
                        .add(new BigDecimal(vo.getReturnQty())));
                materialArrivalTrackingDTO.setId(materialArrivalTrackingVO.getId());
                materialArrivalTrackingDTO.setRemark("退货");
                updateList.add(materialArrivalTrackingDTO);
            } else {
                materialArrivalTrackingDTO.setRequireDate(vo.getCreationDate());
                materialArrivalTrackingDTO.setRequireQuantity(new BigDecimal(vo.getReturnQty()));
                materialArrivalTrackingDTO.setPurchaseOrderCode(vo.getPoNumber());
                materialArrivalTrackingDTO.setPurchaseOrderLineCode(vo.getLineNum());
                materialArrivalTrackingDTO.setDataSource(ArrivalTrackingDataSourceEnum.MANUAL_ADDITION.getCode());
                materialArrivalTrackingDTO.setWaitDeliveryQuantity(new BigDecimal(vo.getReturnQty()));
                materialArrivalTrackingDTO.setArrivalStatus(ArrivalStatusEnum.PLAN_PRUCHASE.getCode());

                NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(vo.getProductCode());
                if (Objects.nonNull(newProductStockPointVO)) {
                    materialArrivalTrackingDTO.setProductId(newProductStockPointVO.getId());
                    materialArrivalTrackingDTO.setStockPointCode(stockPointCode);
                    materialArrivalTrackingDTO.setMaterialCode(newProductStockPointVO.getProductCode());
                    materialArrivalTrackingDTO.setMaterialName(newProductStockPointVO.getProductName());
                } else {
                    log.error("物料编码{}未获取", vo.getProductCode());
                }
                materialArrivalTrackingDTO.setRemark("退货");
                addList.add(materialArrivalTrackingDTO);
            }

            // 修改是否匹配为 是
            MaterialReturnedPurchaseDTO materialReturnedPurchaseDTO = new MaterialReturnedPurchaseDTO();
            materialReturnedPurchaseDTO.setId(vo.getId());
            materialReturnedPurchaseDTO.setWhetherMatches(YesOrNoEnum.YES.getCode());
            updateWhetherMatchesList.add(materialReturnedPurchaseDTO);
        }

        // 处理需要关闭的到货跟踪
        if (CollectionUtils.isNotEmpty(closeDataList)){
            for (MaterialReturnedPurchaseVO materialReturnedPurchaseVO : closeDataList) {
                String key = String.join("_",
                        materialReturnedPurchaseVO.getPoNumber(), materialReturnedPurchaseVO.getLineNum(), materialReturnedPurchaseVO.getProductCode());

                // 获取对应的到货跟踪
                MaterialArrivalTrackingVO materialArrivalTrackingVO = materialArrivalTrackingMap.get(key);
                if (Objects.nonNull(materialArrivalTrackingVO)){
                    MaterialArrivalTrackingDTO materialArrivalTrackingDTO = new MaterialArrivalTrackingDTO();
                    materialArrivalTrackingDTO.setId(materialArrivalTrackingVO.getId());
                    materialArrivalTrackingDTO.setArrivalStatus(ArrivalStatusEnum.CLOSE.getCode());
                    materialArrivalTrackingDTO.setRemark("退货匹配不到PO 或 状态不为OPEN");
                    updateList.add(materialArrivalTrackingDTO);

                    // 修改是否匹配为 是
                    MaterialReturnedPurchaseDTO materialReturnedPurchaseDTO = new MaterialReturnedPurchaseDTO();
                    materialReturnedPurchaseDTO.setId(materialReturnedPurchaseVO.getId());
                    materialReturnedPurchaseDTO.setWhetherMatches(YesOrNoEnum.YES.getCode());
                    updateWhetherMatchesList.add(materialReturnedPurchaseDTO);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(addList)) {
            Lists.partition(addList, 500).forEach(materialArrivalTrackingService::doCreateBatch);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            Lists.partition(updateList, 500).forEach(materialArrivalTrackingService::doUpdateBatchSelective);
        }
        if (CollectionUtils.isNotEmpty(updateWhetherMatchesList)) {
            Lists.partition(updateWhetherMatchesList, 500).forEach(this::doUpdateBatchSelective);
        }
        log.info("材料到货跟踪退货结束");
    }
    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_RETURNED_PURCHASE.getCode();
    }

    @Override
    public List<MaterialReturnedPurchaseVO> invocation(List<MaterialReturnedPurchaseVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }
    private void generateDto(MaterialReturnedPurchaseDTO dto, MesReturnedPurchase mesReturnedPurchase) {
        dto.setReturnQty(mesReturnedPurchase.getReturnQty());
        dto.setProductCode(mesReturnedPurchase.getItemCode());
        dto.setDocumentNum(mesReturnedPurchase.getDocumentNum());
        dto.setLineNum(mesReturnedPurchase.getLineNum());
        dto.setPoNumber(mesReturnedPurchase.getPoNumber());
        dto.setOrgId(mesReturnedPurchase.getPlantId());
        dto.setCreationDate(mesReturnedPurchase.getCreationDate());
    }
}
