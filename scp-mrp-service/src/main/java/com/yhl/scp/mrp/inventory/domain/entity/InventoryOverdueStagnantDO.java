package com.yhl.scp.mrp.inventory.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>InventoryOverdueStagnantDO</code>
 * <p>
 * 超期呆滞库存（历史）DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-15 16:48:27
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryOverdueStagnantDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 418741874594335061L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 版本id
     */
    private String versionId;
    /**
     * 实时库存id
     */
    private String fdpInventoryBatchDetailId;
    /**
     * 工艺类型
     */
    private String itemType;
    /**
     * 库龄超期界定天数
     */
    private BigDecimal overDeadlineDay;
    /**
     * 库龄天数
     */
    private BigDecimal stockAgeDay;
    /**
     * 超期天数
     */
    private BigDecimal overdueDays;
    /**
     * 筛选范围内需求数量
     */
    private BigDecimal scopeDemandQuantity;
    /**
     * 库存耗尽日期
     */
    private Date inventoryDepletionDate;
    /**
     * 版本
     */
    private Integer versionValue;

}
