package com.yhl.scp.mrp.material.plan.allocate;

import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.mrp.material.plan.domain.entity.MrpAllocateResultDO;
import com.yhl.scp.mrp.material.plan.dto.AllocateDTO;
import com.yhl.scp.mrp.material.plan.dto.MaterialDayTotalDemandDTO;
import com.yhl.scp.mrp.material.plan.dto.MrpDemandDTO;
import com.yhl.scp.mrp.material.plan.dto.MrpSupplyDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryOccupyPO;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import com.yhl.scp.mrp.transport.vo.TransportRoutingVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>InventoryAllocator</code>
 * <p>
 * 供需分配器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-02 11:16:03
 */
@Slf4j
public class InventoryAllocator {


    public static MrpAllocateResultDO allocate(AllocateDTO allocateDTO) {
        // 需求
        MrpDemandDTO mrpDemandDTO = allocateDTO.getMrpDemand();
        boolean whetherUseSubstitution = allocateDTO.isWhetherUseSubstitution();
        BigDecimal demandQuantity = mrpDemandDTO.getUnFulfillmentQuantity();
        List<MrpSupplyDTO> mrpSupplyAllList = allocateDTO.getMrpSupplyList();
        mrpSupplyAllList.sort(Comparator.comparing(MrpSupplyDTO::getSupplyTime));
        TreeSet<String> stockPointCodes = new TreeSet<>();
        mrpSupplyAllList.forEach(mrpSupplyDTO -> stockPointCodes.add(mrpSupplyDTO.getStockPointCode()));
        // 获取最后一个元素
        String lastStockPointCode = "";
        if (CollectionUtils.isNotEmpty(stockPointCodes)){
            lastStockPointCode = stockPointCodes.last();
        }

        // 供应
        Map<String, List<MrpSupplyDTO>> mrpSupplyGroup = mrpSupplyAllList.stream()
                .filter(mrpSupplyDTO -> mrpDemandDTO.getDemandProductCodeList().contains(mrpSupplyDTO.getProductCode()))
                .collect(Collectors.groupingBy(MrpSupplyDTO::getStockPointCode));

        // 库存占用信息
        List<MaterialPlanInventoryOccupyPO> materialPlanInventoryOccupyList = new ArrayList<>();
        BigDecimal fulfillmentQuantity = BigDecimal.ZERO;
        Calendar cal = Calendar.getInstance();

        //被替代使用量
        Map<String, BigDecimal> usedAsReplaceQuantityMap = allocateDTO.getUsedAsReplaceQuantityMap();
        // 获取单耗数据
        Map<String, GlassSubstitutionRelationshipVO> substituteProductInputFactorMap = allocateDTO.getSubstituteProductInputFactorMap();

        for (String originStockPointCode : stockPointCodes) {
            List<TransportRoutingVO> transportRoutingList = allocateDTO.getTransportRoutingList().stream()
                    .filter(item -> originStockPointCode.equals(item.getOriginStockPointCode()))
                    .sorted(Comparator.comparing(TransportRoutingVO::getPriority))
                    .collect(Collectors.toList());
            TransportRoutingVO transportRouting = null;
            Date transportDateStart = null;
            // 备选运输路径，其他条件满足，运输时间不足的数据
            List<TransportRoutingVO> alternativeTransportRoutingVOS = new ArrayList<>();
            for (TransportRoutingVO transportRoutingVO : transportRoutingList) {
                if (allocateDTO.isBcDestination() && !"ROAD".equals(transportRoutingVO.getTransportType())) {
                    continue;
                }
                if (!allocateDTO.isBcDestination() && "ROAD".equals(transportRoutingVO.getTransportType())) {
                    continue;
                }
                cal.setTime(mrpDemandDTO.getDemandTime());
                cal.add(Calendar.DAY_OF_YEAR, -transportRoutingVO.getTransportDate().intValue());
                transportDateStart = cal.getTime();
//                transportDateStart = DateUtils.moveDay(mrpDemandDTO.getDemandTime(), -transportRoutingVO.getTransportDate().intValue());
                if (!transportDateStart.before(allocateDTO.getMrpCalDateStart())) {
                    transportRouting = transportRoutingVO;
                    break;
                }else {
                    alternativeTransportRoutingVOS.add(transportRoutingVO);
                }
            }
            // 原片运输路径为空，并且不为最后一个元素
            if (transportRouting == null &&
                    (!lastStockPointCode.equals(originStockPointCode) ||
                            CollectionUtils.isEmpty(alternativeTransportRoutingVOS))) {
                continue;
            }

            if (null == transportRouting){
                transportRouting = alternativeTransportRoutingVOS.get(0);
                transportDateStart = allocateDTO.getMrpCalDateStart();
            }
            List<MrpSupplyDTO> mrpSupplyList = mrpSupplyGroup.get(originStockPointCode);
            if (CollectionUtils.isEmpty(mrpSupplyList)){
                continue;
            }
            // 对供应进行排序
            mrpSupplyList = sortMrpSupply(mrpSupplyList, allocateDTO.getDemandGroupByProduct(), allocateDTO.getMrpSupplyAllList());
            for (MrpSupplyDTO mrpSupply : mrpSupplyList) {
                if (mrpSupply.getSupplyTime().after(mrpDemandDTO.getDemandTime())) {
                    continue;
                }
                if (demandQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                if (mrpSupply.getUnAllocatedQuantity().compareTo(BigDecimal.ZERO) <= 0){
                    continue;
                }
                // 原片单耗
                BigDecimal inputFactor = BigDecimal.ONE;
                // 生产单耗
                BigDecimal productionInputFactor = BigDecimal.ONE;
                GlassSubstitutionRelationshipVO glassSubstitutionRelationshipVO = substituteProductInputFactorMap.get(mrpSupply.getProductCode());
                if (null != glassSubstitutionRelationshipVO){
                    inputFactor = glassSubstitutionRelationshipVO.getGlassInputFactor() == null ||
                            glassSubstitutionRelationshipVO.getGlassInputFactor().compareTo(BigDecimal.ZERO) == 0 ?
                            BigDecimal.ONE : glassSubstitutionRelationshipVO.getGlassInputFactor();

                    productionInputFactor = glassSubstitutionRelationshipVO.getProductionInputFactor() == null ||
                            glassSubstitutionRelationshipVO.getProductionInputFactor().compareTo(BigDecimal.ZERO) == 0 ?
                            BigDecimal.ONE : glassSubstitutionRelationshipVO.getProductionInputFactor();;
                }
//                BigDecimal substituteInputFactor = substituteProductInputFactorMap.getOrDefault(mrpSupply.getProductCode(), BigDecimal.ONE);

                // 库存未分配数量
                BigDecimal unAllocatedQuantity = mrpSupply.getUnAllocatedQuantity();
                // 可分配数量

                // 分配数量
                BigDecimal occupyQuantity;
                // 替代数量Key
                String joinKey = String.join("#", mrpSupply.getProductCode(), DateUtils.dateToString(mrpDemandDTO.getDemandTime()));

                // 需求数量 * 原片单耗 / 生产单耗
                BigDecimal demandQuantityInputFactor = demandQuantity.multiply(inputFactor).divide(productionInputFactor, RoundingMode.HALF_UP);
                if (unAllocatedQuantity.compareTo(demandQuantityInputFactor) >= 0) {
                    BigDecimal possibleOccupyQuantity = demandQuantityInputFactor
                            .divide(mrpSupply.getUnitUsageQuantity(), 0, RoundingMode.UP)
                            .multiply(mrpSupply.getUnitUsageQuantity());

                    if (possibleOccupyQuantity.compareTo(unAllocatedQuantity) > 0) {
                        occupyQuantity  = unAllocatedQuantity;
                    } else {
                        occupyQuantity = possibleOccupyQuantity;
                    }
                    demandQuantity = BigDecimal.ZERO;
                    if (null != usedAsReplaceQuantityMap && whetherUseSubstitution){
                        // 替代料供应
                        usedAsReplaceQuantityMap.put(joinKey,
                                usedAsReplaceQuantityMap.getOrDefault(joinKey, BigDecimal.ZERO).add(occupyQuantity));
                    }
                } else {
                    occupyQuantity = unAllocatedQuantity;

                    BigDecimal occupyQuantityInputFactor = occupyQuantity.multiply(productionInputFactor).divide(inputFactor, RoundingMode.HALF_UP);
//                    BigDecimal occupyQuantityInputFactor = occupyQuantity.divide(substituteInputFactor, RoundingMode.HALF_UP);
                    demandQuantity = demandQuantity.subtract(occupyQuantityInputFactor);
                    if (null != usedAsReplaceQuantityMap && whetherUseSubstitution){
                        // 替代料供应
                        usedAsReplaceQuantityMap.put(joinKey,
                                usedAsReplaceQuantityMap.getOrDefault(joinKey, BigDecimal.ZERO).add(occupyQuantity));
                    }
                }

                fulfillmentQuantity = fulfillmentQuantity.add(occupyQuantity);
                // 已分配数量需要使用实际需求数量
                fulfillmentQuantity = fulfillmentQuantity.multiply(productionInputFactor).divide(inputFactor, RoundingMode.HALF_UP);
//                fulfillmentQuantity = fulfillmentQuantity.divide(substituteInputFactor, RoundingMode.HALF_UP);
                mrpSupply.setUnAllocatedQuantity(unAllocatedQuantity.subtract(occupyQuantity));
                materialPlanInventoryOccupyList.add(createInventoryOccupy(mrpDemandDTO, mrpSupply,
                        occupyQuantity,transportDateStart,transportRouting));
            }
        }
        mrpDemandDTO.setUnFulfillmentQuantity(mrpDemandDTO.getDemandQuantity().subtract(fulfillmentQuantity));
        MrpAllocateResultDO mrpAllocateResultDO = new MrpAllocateResultDO();
        mrpAllocateResultDO.setFulfillmentQuantity(fulfillmentQuantity);
        mrpAllocateResultDO.setMaterialPlanInventoryOccupyList(materialPlanInventoryOccupyList);
        if (CollectionUtils.isNotEmpty(mrpSupplyAllList) &&
                whetherUseSubstitution &&
                CollectionUtils.isNotEmpty(materialPlanInventoryOccupyList)){
            // 使用的替代料
            BigDecimal occupyQuantitySum = materialPlanInventoryOccupyList.stream()
                    .map(MaterialPlanInventoryOccupyPO::getOccupyQuantity)
                    .filter(Objects::nonNull) // 过滤掉null值
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            mrpAllocateResultDO.setUseReplaceQuantity(occupyQuantitySum);
        }
        return mrpAllocateResultDO;
    }

    private static List<MrpSupplyDTO> sortMrpSupply(List<MrpSupplyDTO> mrpSupplyListOfProductCode,
                                                    Map<String, List<MaterialDayTotalDemandDTO>> demandGroupByProduct,
                                                    List<MrpSupplyDTO> mrpSupplyAllList) {

        // 按柜号分组，value 是原片编码
        Map<String, Set<String>> mrpSupplyGroupToContainerNumber = mrpSupplyAllList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getContainerNumber()))
                .collect(Collectors.groupingBy(
                        MrpSupplyDTO::getContainerNumber,
                        Collectors.mapping(MrpSupplyDTO::getProductCode, Collectors.toSet())
                ));
        // 排序规则一，按照柜号含有其他需求的原片
        for (MrpSupplyDTO mrpSupplyDTO : mrpSupplyListOfProductCode) {
            // 设置默认排序号
            mrpSupplyDTO.setPriority(2);
            // 获取该柜号下的原片
            Set<String> productCodeList = mrpSupplyGroupToContainerNumber.get(mrpSupplyDTO.getContainerNumber());
            // 柜子下的原片为空，或者该柜子下的原片只有自己
            if (CollectionUtils.isEmpty(productCodeList) ||
                    (productCodeList.size() == 1 && productCodeList.contains(mrpSupplyDTO.getProductCode()))){
                continue;
            }

            // 获取柜号下原片是否有需求
            for (String productCode : productCodeList) {
                if (!StringUtils.equals(productCode, mrpSupplyDTO.getProductCode()) && demandGroupByProduct.containsKey(productCode)) {
                    // 有原片需求，并且不是本身的需求
                    mrpSupplyDTO.setPriority(1);
                    break;
                }
            }
        }

        // 对mrpSupplyListOfProductCode集合先按照priority排序，再按照SupplyTime排序
        mrpSupplyListOfProductCode = mrpSupplyListOfProductCode.stream()
                .sorted(Comparator.comparing(MrpSupplyDTO::getPriority).thenComparing(MrpSupplyDTO::getSupplyTime))
                .collect(Collectors.toList());
        return mrpSupplyListOfProductCode;
    }


    private static MaterialPlanInventoryOccupyPO createInventoryOccupy(MrpDemandDTO mrpDemandDTO,
                                                                       MrpSupplyDTO mrpSupplyDTO,
                                                                       BigDecimal availableQuantity,
                                                                       Date transportDateStart,
                                                                       TransportRoutingVO transportRouting) {

        MaterialPlanInventoryOccupyPO materialPlanInventoryOccupyPO = new MaterialPlanInventoryOccupyPO();
        materialPlanInventoryOccupyPO.setOccupyQuantity(availableQuantity);
        materialPlanInventoryOccupyPO.setInventoryId(mrpSupplyDTO.getSupplyId());
        materialPlanInventoryOccupyPO.setTransportDateStart(transportDateStart);
        materialPlanInventoryOccupyPO.setTransportDateEnd(DateUtils.getDayFirstTime(mrpDemandDTO.getDemandTime()));
        materialPlanInventoryOccupyPO.setStockPointCodeFrom(null != transportRouting ? transportRouting.getOriginStockPointCode() : null);
        materialPlanInventoryOccupyPO.setStockPointCodeTo(null != transportRouting ? transportRouting.getDestinStockPointCode() : null);
        materialPlanInventoryOccupyPO.setDemandProductCode(mrpDemandDTO.getProductCode());
        materialPlanInventoryOccupyPO.setSupplyProductCode(mrpSupplyDTO.getOriginProductCode());
        materialPlanInventoryOccupyPO.setInventoryType(mrpSupplyDTO.getSupplySource());
        materialPlanInventoryOccupyPO.setOccupyDate(DateUtils.getDayFirstTime(transportDateStart));
        materialPlanInventoryOccupyPO.setTransferRoutingId(null != transportRouting ? transportRouting.getId() : null);
        materialPlanInventoryOccupyPO.setContainerNumber(mrpSupplyDTO.getContainerNumber());
        return materialPlanInventoryOccupyPO;
    }
}
