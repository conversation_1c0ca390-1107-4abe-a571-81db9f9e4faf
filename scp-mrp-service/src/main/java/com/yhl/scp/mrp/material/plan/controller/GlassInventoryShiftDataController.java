package com.yhl.scp.mrp.material.plan.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.mrp.inventory.dto.GlassInventoryShiftQueryParamDTO;
import com.yhl.scp.mrp.material.plan.dto.GlassInventoryShiftDataDTO;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftPublishService;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>GlassInventoryShiftDataController</code>
 * <p>
 * 物料库存推移控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 11:29:27
 */
@Slf4j
@Api(tags = "原片推移物料控制器")
@RestController
@RequestMapping("glassInventoryShiftData")
public class GlassInventoryShiftDataController extends BaseController {

    @Resource
    GlassInventoryShiftPublishService glassInventoryShiftPublishService;
    @Resource
    private GlassInventoryShiftDataService glassInventoryShiftDataService;
    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<GlassInventoryShiftDataVO>> page() {
        List<GlassInventoryShiftDataVO> glassInventoryShiftDataList = glassInventoryShiftDataService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<GlassInventoryShiftDataVO> pageInfo = new PageInfo<>(glassInventoryShiftDataList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "分页查询-自定义")
    @PostMapping(value = "pageCustom")
    public BaseResponse<PageInfo<GlassInventoryShiftPageVO>> pageCustom(@RequestBody GlassInventoryShiftQueryParamDTO dto) {
        PageInfo<GlassInventoryShiftPageVO> glassInventoryShiftDataList = glassInventoryShiftDataService.pageCustom(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, glassInventoryShiftDataList);
    }


    @ApiOperation(value = "发布")
    @GetMapping(value = "publish")
    public BaseResponse<Void> publish() {

        try {
            if (redisUtil.hasKey(RedisKeyManageEnum.GLASS_MRP_PUBLISHED_KEY.getKey())) {
                return BaseResponse.error("当前已有推移结果正在发布，请等待发布完成");
            }
            redisUtil.set(RedisKeyManageEnum.GLASS_MRP_PUBLISHED_KEY.getKey(), SystemHolder.getUserId(), 60 * 60);
            glassInventoryShiftPublishService.doPublish();
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("发布失败", e);
            throw new BusinessException("发布失败,{0}", e.getLocalizedMessage());
        } finally {
            // 释放key
            redisUtil.delete(RedisKeyManageEnum.GLASS_MRP_PUBLISHED_KEY.getKey());
        }
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody GlassInventoryShiftDataDTO glassInventoryShiftDataDTO) {
        return glassInventoryShiftDataService.doCreate(glassInventoryShiftDataDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody GlassInventoryShiftDataDTO glassInventoryShiftDataDTO) {
        return glassInventoryShiftDataService.doUpdate(glassInventoryShiftDataDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        glassInventoryShiftDataService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<GlassInventoryShiftDataVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, glassInventoryShiftDataService.selectByPrimaryKey(id));
    }


}
