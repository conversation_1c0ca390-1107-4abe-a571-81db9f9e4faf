package com.yhl.scp.mrp.material.plan.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mrp.enums.*;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialPlanNeedVO;
import com.yhl.scp.mrp.inventory.controller.FulfillmentManualController;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.dto.SyncMaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.convertor.MaterialPlanNeedConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.MaterialPlanNeedDO;
import com.yhl.scp.mrp.material.plan.domain.service.MaterialPlanNeedDomainService;
import com.yhl.scp.mrp.material.plan.dto.*;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanNeedDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedPO;
import com.yhl.scp.mrp.material.plan.result.InsertRollPredictionDayResult;
import com.yhl.scp.mrp.material.plan.result.UpdateLineStatusResult;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedIssueDetailsService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanNeedIssueDetailsVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialPlanNeedServiceImpl</code>
 * <p>
 * 要货计划应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:16:04
 */
@Slf4j
@Service
public class MaterialPlanNeedServiceImpl extends AbstractService implements MaterialPlanNeedService {

    @Resource
    private MaterialPlanNeedDao materialPlanNeedDao;

    @Resource
    private MaterialPlanNeedDomainService materialPlanNeedDomainService;

    @Resource
    private MaterialPlanNeedIssueDetailsService materialPlanNeedIssueDetailsService;

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private RedisUtil redisUtil;

    @Autowired
    private FulfillmentManualController fulfillmentManualController;

    @Resource
    private NoGlassMrpServiceImpl noGlassMrpService;

    @Resource
    private NoGlassInventoryShiftDataService noGlassInventoryShiftDataService;

    public static void main(String[] args) {
        String queryCondition = String.format(" find_in_set(%s, material_planner) > 0", "aaabbbccc");
        System.out.println("queryCondition = " + queryCondition);

        BigDecimal quantity = new BigDecimal(2006);
        BigDecimal unit = new BigDecimal(2000);
        BigDecimal computeValue = quantity;
        if (quantity.compareTo(BigDecimal.ZERO) < 0) {
            computeValue = quantity.abs();
        }

        // 需求数量 / 产品包装数量
        BigDecimal boxCountTemp = computeValue.divide(unit, 0, RoundingMode.DOWN);
        BigDecimal remainder = computeValue.remainder(unit);
        if (remainder.compareTo(BigDecimal.ZERO) == 0) {
            // 为整数则不处理
            System.out.println("quantity1 = " + quantity);
        }
        // 余数凑成整，向上取整
        BigDecimal boxCount = boxCountTemp.add(BigDecimal.ONE);
        BigDecimal result = boxCount.multiply(unit);
        if (quantity.compareTo(BigDecimal.ZERO) < 0) {
            result = result.negate();
        }
        System.out.println("quantity2 = " + result);
    }

    @Override
    public BaseResponse<Void> doCreate(MaterialPlanNeedDTO materialPlanNeedDTO) {
        // 0.数据转换
        MaterialPlanNeedDO materialPlanNeedDO = MaterialPlanNeedConvertor.INSTANCE.dto2Do(materialPlanNeedDTO);
        MaterialPlanNeedPO materialPlanNeedPO = MaterialPlanNeedConvertor.INSTANCE.dto2Po(materialPlanNeedDTO);
        // 1.数据校验
        materialPlanNeedDomainService.validation(materialPlanNeedDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPlanNeedPO);
        materialPlanNeedDao.insertWithPrimaryKey(materialPlanNeedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialPlanNeedDTO materialPlanNeedDTO) {
        // 0.数据转换
        MaterialPlanNeedDO materialPlanNeedDO = MaterialPlanNeedConvertor.INSTANCE.dto2Do(materialPlanNeedDTO);
        MaterialPlanNeedPO materialPlanNeedPO = MaterialPlanNeedConvertor.INSTANCE.dto2Po(materialPlanNeedDTO);
        // 1.数据校验
        materialPlanNeedDomainService.validation(materialPlanNeedDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPlanNeedPO);
        if (PlanNeedPublishStatusEnum.UN_PUBLISHED.getCode().equals(materialPlanNeedPO.getPublishStatus())) {
            materialPlanNeedPO.setExpectedArrivalTime(materialPlanNeedPO.getNeedDate());
        }
        materialPlanNeedDao.update(materialPlanNeedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPlanNeedDTO> list) {
        List<MaterialPlanNeedPO> newList = MaterialPlanNeedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPlanNeedDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPlanNeedDTO> list) {
        List<MaterialPlanNeedPO> newList = MaterialPlanNeedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPlanNeedDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialPlanNeedDTO> list) {
        List<MaterialPlanNeedPO> newList = MaterialPlanNeedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPlanNeedDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        List<String> validIds = new ArrayList<>();
        List<String> invalidIds = new ArrayList<>();
        List<String> materialPlanNeedNoList = new ArrayList<>();
        for (String id : idList) {
            MaterialPlanNeedPO materialPlanNeedPO = materialPlanNeedDao.selectByPrimaryKey(id);
            if (materialPlanNeedPO != null && (PlanNeedPublishStatusEnum.PUBLISHED.getCode().equals(materialPlanNeedPO.getPublishStatus()) || PlanNeedPublishStatusEnum.TO_BE_CONFIRMED.getCode().equals(materialPlanNeedPO.getPublishStatus()) || PlanNeedPublishStatusEnum.AGREEMENT_REACHED.getCode().equals(materialPlanNeedPO.getPublishStatus()))) {
                invalidIds.add(id);
                materialPlanNeedNoList.add(materialPlanNeedPO.getMaterialPlanNeedNo());
            } else {
                validIds.add(id);
            }
        }
        if (!invalidIds.isEmpty()) {
            StringBuilder errorMessage = new StringBuilder("已发布、待计划员确认或已达成一致，不能删除:");
            for (int i = 0; i < invalidIds.size(); i++) {
                errorMessage.append("要货计划号: ").append(materialPlanNeedNoList.get(i));
                if (i < invalidIds.size() - 1) {
                    errorMessage.append("; ");
                }
            }
            throw new BusinessException(errorMessage.toString());
        }
        if (validIds.size() > 1) {
            return materialPlanNeedDao.deleteBatch(validIds);
        } else if (validIds.size() == 1) {
            return materialPlanNeedDao.deleteByPrimaryKey(validIds.get(0));
        }
        return 0;
    }

    @Override
    public MaterialPlanNeedVO selectByPrimaryKey(String id) {
        MaterialPlanNeedPO po = materialPlanNeedDao.selectByPrimaryKey(id);
        return MaterialPlanNeedConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_plan_need")
    public List<MaterialPlanNeedVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
//        List<Role> roleList = ipsNewFeign.selectRoleListByUserId(SystemHolder.getUserId());
//        List<String> roleNameList = roleList.stream().map(Role::getRoleName).distinct().collect(Collectors.toList());
//        // 含有材料计划员、PVB计划员则需要加查询条件
//        if (roleNameList.contains("材料计划员") || roleNameList.contains("B/PVB计划员")) {
//            // 过滤材料计划员权限
//            String queryCondition = String.format(" find_in_set('%s', material_planner) > 0", SystemHolder.getUserId());
//            if (StringUtils.isEmpty(queryCriteriaParam)) {
//                queryCriteriaParam = queryCondition;
//            } else {
//                queryCriteriaParam = queryCriteriaParam + " and" + queryCondition;
//            }
//        }
//        System.out.println("queryCriteriaParam = " + queryCriteriaParam);
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_plan_need")
    public List<MaterialPlanNeedVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPlanNeedVO> dataList = materialPlanNeedDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPlanNeedServiceImpl target = SpringBeanUtils.getBean(MaterialPlanNeedServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPlanNeedVO> selectByParams(Map<String, Object> params) {
        List<MaterialPlanNeedPO> list = materialPlanNeedDao.selectByParams(params);
        return MaterialPlanNeedConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPlanNeedVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
//        return ObjectTypeEnum.MATERIAL_PLAN_NEED.getCode();
        return "";
    }

    @Override
    public List<MaterialPlanNeedVO> invocation(List<MaterialPlanNeedVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    /**
     * 考虑计划批量
     *
     * @param quantity 数量
     * @param unit     批量单位
     * @return 圆整之后的数量
     */
    @Override
    public BigDecimal roundUpwards(BigDecimal quantity, BigDecimal unit) {
        if (null == unit || unit.compareTo(BigDecimal.ZERO) == 0) {
            return quantity;
        }
        BigDecimal computeValue = quantity;
        if (quantity.compareTo(BigDecimal.ZERO) < 0) {
            computeValue = computeValue.abs();
        }

        // 需求数量 / 产品包装数量
        BigDecimal boxCountTemp = computeValue.divide(unit, 0, RoundingMode.DOWN);
        BigDecimal remainder = computeValue.remainder(unit);
        if (remainder.compareTo(BigDecimal.ZERO) == 0) {
            // 为整数则不处理
            return quantity;
        }
        // 余数凑成整，向上取整
        BigDecimal boxCount = boxCountTemp.add(BigDecimal.ONE);
        BigDecimal result = boxCount.multiply(unit);
        if (quantity.compareTo(BigDecimal.ZERO) < 0) {
            result = result.negate();
        }
        return result;
    }

    @Override
    public BaseResponse<Void> doReceiveData(String scenario, List<MaterialPlanNeedFeedbackDTO> list) {
        log.info("数据源{}", DynamicDataSourceContextHolder.getDataSource());

        // 收集要货计划id
        Set<String> ids = list.stream().map(MaterialPlanNeedFeedbackDTO::getMaterialPlanNeedId).collect(Collectors.toSet());

        // 收集拆批的要货计划id
        Set<String> dismantlingBatchesIdSet = new HashSet<>();

        // 供应商要货计划根据要货计划id分组
        Map<String, List<MaterialPlanNeedFeedbackDTO>> materialPlanNeedFeedbackMap = list.stream().collect(Collectors.groupingBy(MaterialPlanNeedFeedbackDTO::getMaterialPlanNeedId));

        List<MaterialPlanNeedVO> materialPlanNeedVOList = this.selectByCondition(null, null);
        // 当前的要货计划（根据主键id分组）
        Map<String, MaterialPlanNeedVO> materialPlanNeedIdMap = materialPlanNeedVOList.stream().collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v1));

        // 添加（处理拆批后剩余未处理数据）
        List<MaterialPlanNeedDTO> addList = new ArrayList<>();
        // 修改（正常修改要货计划供应商反馈）
        List<MaterialPlanNeedDTO> updateList = new ArrayList<>();

        for (Map.Entry<String, List<MaterialPlanNeedFeedbackDTO>> entity : materialPlanNeedFeedbackMap.entrySet()) {
            // 获取此次供应商反馈数据
            List<MaterialPlanNeedFeedbackDTO> value = entity.getValue();
            String id = value.get(0).getMaterialPlanNeedId();

            // 查看当前要货计划中是否存在
            MaterialPlanNeedVO materialPlanNeedVO = materialPlanNeedIdMap.get(entity.getKey());
            if (Objects.isNull(materialPlanNeedVO)) {
                log.error("{}此要货id不存在", entity.getKey());
                continue;
            } else {
                MaterialPlanNeedDTO materialPlanNeedDTO = new MaterialPlanNeedDTO();
                BeanUtils.copyProperties(materialPlanNeedVO, materialPlanNeedDTO);

                // 获取供应商反馈数据（默认未1个未拆批）
                MaterialPlanNeedFeedbackDTO materialPlanNeedFeedbackDTO = value.get(0);
                // 承诺到货时间
                materialPlanNeedDTO.setExpectedArrivalTime(materialPlanNeedFeedbackDTO.getExpectedArrivalTime());
                // 承诺到货数量
                materialPlanNeedDTO.setExpectedArrivalQuantity(materialPlanNeedFeedbackDTO.getExpectedArrivalQuantity());
                // 生成PO
                if (StringUtils.isNotEmpty(materialPlanNeedDTO.getPurchaseOrderCode())) {
                    materialPlanNeedDTO.setPurchaseOrderCode(materialPlanNeedFeedbackDTO.getPurchaseOrderCode());
                }
                // 发布状态（根据供应商反馈状态区分）
                if (StringUtils.isNotEmpty(materialPlanNeedFeedbackDTO.getLineStatus())) {
                    if (materialPlanNeedFeedbackDTO.getLineStatus().equals("Y")) {
                        materialPlanNeedDTO.setPublishStatus(PlanNeedPublishStatusEnum.AGREEMENT_REACHED.getCode());
                    } else if (materialPlanNeedFeedbackDTO.getLineStatus().equals("N")) {
                        materialPlanNeedDTO.setPublishStatus(PlanNeedPublishStatusEnum.TO_BE_CONFIRMED.getCode());
                    }
                }
                // 添加后就删除防止拆批重复
                value.remove(materialPlanNeedFeedbackDTO);
                updateList.add(materialPlanNeedDTO);
            }

            // 如果有拆批数据，需要新增一条要货计划
            if (CollectionUtils.isNotEmpty(value)) {
                for (MaterialPlanNeedFeedbackDTO materialPlanNeedFeedbackDTO : value) {
                    MaterialPlanNeedDTO materialPlanNeedDTO = new MaterialPlanNeedDTO();
                    BeanUtils.copyProperties(materialPlanNeedVO, materialPlanNeedDTO);

                    // 承诺到货时间
                    materialPlanNeedDTO.setExpectedArrivalTime(materialPlanNeedFeedbackDTO.getExpectedArrivalTime());
                    // 承诺到货数量
                    materialPlanNeedDTO.setExpectedArrivalQuantity(materialPlanNeedFeedbackDTO.getExpectedArrivalQuantity());
                    // 生成PO
                    if (StringUtils.isNotEmpty(materialPlanNeedDTO.getPurchaseOrderCode())) {
                        materialPlanNeedDTO.setPurchaseOrderCode(materialPlanNeedFeedbackDTO.getPurchaseOrderCode());
                    }
                    materialPlanNeedDTO.setId(UUID.randomUUID().toString());
                    materialPlanNeedDTO.setRemark("拆批数据");
                    materialPlanNeedDTO.setPublishUser(SystemHolder.getUserId());
                    // 发布状态（根据供应商反馈状态区分）
                    if (StringUtils.isNotEmpty(materialPlanNeedFeedbackDTO.getLineStatus())) {
                        if (materialPlanNeedFeedbackDTO.getLineStatus().equals("Y")) {
                            materialPlanNeedDTO.setPublishStatus(PlanNeedPublishStatusEnum.AGREEMENT_REACHED.getCode());
                        } else if (materialPlanNeedFeedbackDTO.getLineStatus().equals("N")) {
                            materialPlanNeedDTO.setPublishStatus(PlanNeedPublishStatusEnum.TO_BE_CONFIRMED.getCode());
                        }
                    }
                    materialPlanNeedDTO.setParentId(id);
                    // 添加拆批后剩余得数据
                    addList.add(materialPlanNeedDTO);

                    // 收集拆批后的id
                    dismantlingBatchesIdSet.add(materialPlanNeedDTO.getId());
                }
            }
        }

        //批量新增
//        log.info("要货新增{}", JSON.toJSONString(addList));
        if (CollectionUtils.isNotEmpty(addList)) {
            Lists.partition(addList, 500).forEach(this::doCreateBatch);
        }
        //批量修改
//        log.info("要货修改{}", JSON.toJSONString(updateList));
        if (CollectionUtils.isNotEmpty(updateList)) {
            Lists.partition(updateList, 500).forEach(this::updateBatchSelective);
        }

        // 生成到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS = materialArrivalTrackingService.selectAll();
        Map<String, MaterialArrivalTrackingVO> sourceIdMap = materialArrivalTrackingVOS.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getArrivalStatus()))
                .filter(data -> data.getArrivalStatus().equals(ArrivalStatusEnum.PLAN_PRUCHASE.getCode()))
                .collect(Collectors.toMap(MaterialArrivalTrackingVO::getSourceId, Function.identity(), (v1, v2) -> v1));

        List<SyncMaterialArrivalTrackingDTO> addMaterialArrivalTrackingDTOS = new ArrayList<>();
        List<SyncMaterialArrivalTrackingDTO> updateMaterialArrivalTrackingDTOS = new ArrayList<>();

        // 补充拆批数据id，避免遗漏拆批数据
        if (CollectionUtils.isNotEmpty(dismantlingBatchesIdSet)) {
            ids.addAll(dismantlingBatchesIdSet);
        }
        // 过滤出本次要发布的要货计划数据
        List<MaterialPlanNeedVO> publishedList = this.selectByVOParams(ImmutableMap.of("ids", ids));

        // 生成供应商要货反馈
        for (MaterialPlanNeedVO materialPlanNeedVO : publishedList) {
            SyncMaterialArrivalTrackingDTO materialArrivalTrackingDTO = SyncMaterialArrivalTrackingDTO.builder()
                    .sourceId(materialPlanNeedVO.getId())
                    .dataSource(ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode())
                    .purchaseOrderCode(materialPlanNeedVO.getPurchaseOrderCode())
                    .stockPointCode(materialPlanNeedVO.getStockPointCode())
                    .materialCode(materialPlanNeedVO.getProductCode())
                    .materialName(materialPlanNeedVO.getProductName())
                    .requireDate(materialPlanNeedVO.getNeedDate())
                    .requireQuantity(materialPlanNeedVO.getExpectedArrivalQuantity())
                    .productUnit(materialPlanNeedVO.getMeasurementUnit())
                    .supplierCode(materialPlanNeedVO.getSupplierCode())
                    .supplierName(materialPlanNeedVO.getSupplierName())
                    .publishStatus(materialPlanNeedVO.getPublishStatus())
                    .creator(SystemHolder.getUserId())
                    .modifier(SystemHolder.getUserId())
                    .build();

            MaterialArrivalTrackingVO materialArrivalTrackingVO = sourceIdMap.get(materialPlanNeedVO.getId());
            if (Objects.isNull(materialArrivalTrackingVO)) {
                addMaterialArrivalTrackingDTOS.add(materialArrivalTrackingDTO);
            } else {
                materialArrivalTrackingDTO.setId(materialArrivalTrackingVO.getId());
                updateMaterialArrivalTrackingDTOS.add(materialArrivalTrackingDTO);
            }
        }

        List<String> computeProductCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addMaterialArrivalTrackingDTOS)) {
            computeProductCodeList.addAll(addMaterialArrivalTrackingDTOS.stream()
                    .map(SyncMaterialArrivalTrackingDTO::getMaterialCode)
                    .distinct().collect(Collectors.toList()));
            List<List<SyncMaterialArrivalTrackingDTO>> partition = Lists.partition(addMaterialArrivalTrackingDTOS, 500);
            partition.forEach(materialArrivalTrackingService::doSyncMaterialArrivalTracking);
        }

        if (CollectionUtils.isNotEmpty(updateMaterialArrivalTrackingDTOS)) {
            computeProductCodeList.addAll(updateMaterialArrivalTrackingDTOS.stream()
                    .map(SyncMaterialArrivalTrackingDTO::getMaterialCode)
                    .distinct().collect(Collectors.toList()));
            List<List<SyncMaterialArrivalTrackingDTO>> partition = Lists.partition(updateMaterialArrivalTrackingDTOS, 500);
            partition.forEach(materialArrivalTrackingService::doUpdateSyncMaterialArrivalTracking);
            computeProductCodeList = computeProductCodeList.stream().distinct().collect(Collectors.toList());
        }

        // 需要刷新mrp结果数据
        List<String> finalComputeProductCodeList = computeProductCodeList;
        CompletableFuture.runAsync(() -> {
            try {
                String executionSequence = UUIDUtil.getUUID();
                log.info("$$$$$$$$$$$$$$$$$$$$$ 触发要货计划反馈，开始刷新MRP计算结果, 执行序列编码:{}", executionSequence);
                noGlassMrpService.doRunMrp(new MrpParamDTO(MrpCalcTypeEnum.NO_GLASS.getCode(), SystemHolder.getScenario(),
                        null, DateUtils.getDayFirstTime(new Date()), null, null, Boolean.TRUE, finalComputeProductCodeList));
                log.info("$$$$$$$$$$$$$$$$$$$$$ 触发要货计划反馈，刷新MRP计算结果结束, 执行序列编码：{}", executionSequence);
            } catch (Exception e) {
                log.error("MRP材料推移失败", e);
            }
        });

        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                //需要提交之后执行的代码
                log.info("MRP材料推移");
            }
        });

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }


    @Override
    public BaseResponse<Void> splitTheOrder(List<MaterialPlanNeedDTO> materialPlanNeedDTOList) {
        if (CollectionUtils.isEmpty(materialPlanNeedDTOList)) {
            throw new BusinessException("拆单的要货计划不能为空");
        }

        String needId = materialPlanNeedDTOList.stream().map(MaterialPlanNeedDTO::getId).findFirst().orElse(null);
        MaterialPlanNeedVO materialPlanNeedVO = this.selectByPrimaryKey(needId);
        BigDecimal needQuantity = materialPlanNeedVO.getNeedQuantity();

        BigDecimal needQuantitySum = materialPlanNeedDTOList.stream().map(MaterialPlanNeedDTO::getNeedQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (needQuantitySum.compareTo(needQuantity) != 0) {
            throw new BusinessException("拆单数量之和必须等于原要货数量");
        }

        List<MaterialPlanNeedDTO> addList = new ArrayList<>();
        // 处理拆批数据
        for (MaterialPlanNeedDTO needDTO : materialPlanNeedDTOList) {
            MaterialPlanNeedDTO materialPlanNeedDTO = new MaterialPlanNeedDTO();
            BeanUtils.copyProperties(needDTO, materialPlanNeedDTO);
            materialPlanNeedDTO.setId(UUID.randomUUID().toString());
            materialPlanNeedDTO.setRemark("拆单数据（原数量" + needQuantity + "）");
            materialPlanNeedDTO.setParentId(needId);

            // 添加拆批数据
            addList.add(materialPlanNeedDTO);
        }

        // 删除原本要货计划
        materialPlanNeedDao.deleteByPrimaryKey(needId);

        // 新增拆批要货计划
        if (CollectionUtils.isNotEmpty(addList)) {
            Lists.partition(addList, 500).forEach(this::doCreateBatch);
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> issueData(List<String> ids, boolean isSummary) {
        List<MaterialPlanNeedVO> materialPlanNeedVOList;
        if (CollectionUtils.isNotEmpty(ids)) {
            materialPlanNeedVOList = this.selectByVOParams(ImmutableMap.of("ids", ids));

            if (isSummary) {
                // 把要货计划号下已发布的内容全部下发
                List<String> materialPlanNeedNos = materialPlanNeedVOList.stream()
                        .map(MaterialPlanNeedVO::getMaterialPlanNeedNo)
                        .distinct().collect(Collectors.toList());

                materialPlanNeedVOList = this.selectByVOParams(ImmutableMap.of("materialPlanNeedNos", materialPlanNeedNos));

                materialPlanNeedVOList = materialPlanNeedVOList.stream()
                        .filter(data -> data.getPublishStatus().equals(PlanNeedPublishStatusEnum.PUBLISHED.getCode()))
                        .collect(Collectors.toList());
            }
        } else {
            // 获取全部要货计划（视图）
            materialPlanNeedVOList = this.selectByCondition(null, null);
        }

        if (CollectionUtils.isEmpty(materialPlanNeedVOList)) return null;

        // 处理创建人，收集工号
        List<String> creatorList = materialPlanNeedVOList.stream().map(BaseVO::getCreator).distinct().collect(Collectors.toList());
        List<User> userList = ipsNewFeign.selectUserByParams(SystemHolder.getScenario(), ImmutableMap.of("ids", creatorList));
        Map<String, String> staffCodeMap = userList.stream().collect(Collectors.toMap(User::getId, User::getStaffCode, (v1, v2) -> v1));

        // 根据要货计划号分组
        Map<String, List<MaterialPlanNeedVO>> materialPlanNeedVOMaterialPlanNeedNoMap = materialPlanNeedVOList.stream()
                .collect(Collectors.groupingBy(MaterialPlanNeedVO::getMaterialPlanNeedNo));
        // 获取要货计划下发明细
        List<MaterialPlanNeedIssueDetailsVO> materialPlanNeedIssueDetailsVOList = materialPlanNeedIssueDetailsService.selectByParams(
                ImmutableMap.of("materialPlanNeedNos", materialPlanNeedVOMaterialPlanNeedNoMap.keySet()));

        // 根据采购计划单号分组
        Map<String, List<MaterialPlanNeedVO>> materialPlanNeedMap = materialPlanNeedVOList.stream()
                .collect(Collectors.groupingBy(MaterialPlanNeedVO::getMaterialPlanNeedNo));

        // 下发30天要货计划（需要递增版本）
        List<InsertRollPredictionDayResult> insertRollPredictionResultDayList =
                fetchInsertRollPredictionResultsDay(materialPlanNeedMap, materialPlanNeedIssueDetailsVOList, staffCodeMap);
        // 根据状态分组看有无异常状态
        Map<Integer, List<InsertRollPredictionDayResult>> statusDatMap =
                insertRollPredictionResultDayList.stream().collect(Collectors.groupingBy(InsertRollPredictionDayResult::getStatus));

        // 记录下发失败的30天要货数据日志
        processMaterialPlanDay(statusDatMap.getOrDefault(0, new ArrayList<>()), materialPlanNeedMap, Boolean.FALSE.toString(), NeedDimensionEnum.DAY.getCode());
        // 记录下发成功的30天要货数据日志
        processMaterialPlanDay(statusDatMap.getOrDefault(1, new ArrayList<>()), materialPlanNeedMap, Boolean.TRUE.toString(), NeedDimensionEnum.DAY.getCode());

        // 需要修改下发时间、状态、要货计划单号的数据id
        List<MaterialPlanNeedDTO> updateList = new ArrayList<>();
        for (MaterialPlanNeedVO materialPlanNeedVO : materialPlanNeedVOList) {
            MaterialPlanNeedDTO dto = new MaterialPlanNeedDTO();
            dto.setId(materialPlanNeedVO.getId());
            dto.setPublishTime(new Date());
            dto.setPublishStatus(PlanNeedPublishStatusEnum.PUBLISHED.getCode());
            dto.setMaterialPlanNeedNo(materialPlanNeedVO.getMaterialPlanNeedNo());
            dto.setPublishUser(SystemHolder.getUserId());
            updateList.add(dto);
        }
        // 修改要货计划的下发时间
        materialPlanNeedDao.updateBatchSelective(MaterialPlanNeedConvertor.INSTANCE.dto2Pos(updateList));

        // 生成到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS = materialArrivalTrackingService.selectAll();
        Map<String, MaterialArrivalTrackingVO> sourceIdMap = materialArrivalTrackingVOS.stream()
                .collect(Collectors.toMap(MaterialArrivalTrackingVO::getSourceId, Function.identity(), (v1, v2) -> v1));

        List<SyncMaterialArrivalTrackingDTO> addMaterialArrivalTrackingDTOS = new ArrayList<>();
        List<SyncMaterialArrivalTrackingDTO> updateMaterialArrivalTrackingDTOS = new ArrayList<>();
        // 生成供应商要货反馈
        for (MaterialPlanNeedVO materialPlanNeedVO : materialPlanNeedVOList) {
            SyncMaterialArrivalTrackingDTO materialArrivalTrackingDTO = SyncMaterialArrivalTrackingDTO.builder()
                    .sourceId(materialPlanNeedVO.getId())
                    .dataSource(ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode())
                    .purchaseOrderCode(materialPlanNeedVO.getPurchaseOrderCode())
                    .stockPointCode(materialPlanNeedVO.getStockPointCode())
                    .materialCode(materialPlanNeedVO.getProductCode())
                    .materialName(materialPlanNeedVO.getProductName())
                    .requireDate(materialPlanNeedVO.getNeedDate())
                    .requireQuantity(materialPlanNeedVO.getExpectedArrivalQuantity())
                    .productUnit(materialPlanNeedVO.getMeasurementUnit())
                    .supplierCode(materialPlanNeedVO.getSupplierCode())
                    .supplierName(materialPlanNeedVO.getSupplierName())
                    .publishStatus(ArrivalTrackingPublishStatusEnum.PUBLISHED.getCode())
                    .creator(SystemHolder.getUserId()).modifier(SystemHolder.getUserId()).build();

            MaterialArrivalTrackingVO materialArrivalTrackingVO = sourceIdMap.get(materialPlanNeedVO.getId());
            if (Objects.isNull(materialArrivalTrackingVO)) {
                addMaterialArrivalTrackingDTOS.add(materialArrivalTrackingDTO);
            } else {
                materialArrivalTrackingDTO.setId(materialArrivalTrackingVO.getId());
                updateMaterialArrivalTrackingDTOS.add(materialArrivalTrackingDTO);
            }
        }
        if (CollectionUtils.isNotEmpty(addMaterialArrivalTrackingDTOS)) {
            List<List<SyncMaterialArrivalTrackingDTO>> partition = Lists.partition(addMaterialArrivalTrackingDTOS, 500);
            partition.forEach(materialArrivalTrackingService::doSyncMaterialArrivalTracking);
        }

        if (CollectionUtils.isNotEmpty(updateMaterialArrivalTrackingDTOS)) {
            List<List<SyncMaterialArrivalTrackingDTO>> partition = Lists.partition(updateMaterialArrivalTrackingDTOS, 500);
            partition.forEach(materialArrivalTrackingService::doUpdateSyncMaterialArrivalTracking);
        }

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 记录30天要货计划下发日志
     *
     * @param predictionDayResults 预测结果列表（即状态为下发失败或下发成功的30天数据）
     * @param materialPlanNeedMap  存放物料计划所需数据的Map
     * @param status               当前状态（下发失败或下发成功）
     */
    private void processMaterialPlanDay(List<InsertRollPredictionDayResult> predictionDayResults, Map<String, List<MaterialPlanNeedVO>> materialPlanNeedMap, String status, String dimension) {
        if (CollectionUtils.isNotEmpty(predictionDayResults)) {
            List<MaterialPlanNeedIssueDetailsDTO> materialPlanNeedIssueDetailsAddList = new ArrayList<>();
            for (InsertRollPredictionDayResult predictionDayResult : predictionDayResults) {
                List<MaterialPlanNeedVO> materialPlanNeedVOList = materialPlanNeedMap.get(predictionDayResult.getPlanCode());
                if (CollectionUtils.isNotEmpty(materialPlanNeedVOList)) {
                    for (MaterialPlanNeedVO materialPlanNeedVO : materialPlanNeedVOList) {
                        MaterialPlanNeedIssueDetailsDTO materialPlanNeedIssueDetailsDTO = new MaterialPlanNeedIssueDetailsDTO();
                        BeanUtils.copyProperties(materialPlanNeedVO, materialPlanNeedIssueDetailsDTO);
                        materialPlanNeedIssueDetailsDTO.setMaterialPlanNeedNo(predictionDayResult.getPlanCode());
                        materialPlanNeedIssueDetailsDTO.setIssueVersion(predictionDayResult.getVersionNo());
                        materialPlanNeedIssueDetailsDTO.setPlanNeedId(materialPlanNeedVO.getId());
                        materialPlanNeedIssueDetailsDTO.setStatus(status);
                        materialPlanNeedIssueDetailsDTO.setDimension(dimension);
                        materialPlanNeedIssueDetailsAddList.add(materialPlanNeedIssueDetailsDTO);
                    }
                }
            }
            Lists.partition(materialPlanNeedIssueDetailsAddList, 1000).forEach(materialPlanNeedIssueDetailsService::doCreateBatch);
        }
    }

    /**
     * 下发要货计划（天）
     *
     * @param materialPlanNeedMap                要货计划
     * @param materialPlanNeedIssueDetailsVOList 要货计划下发明细
     * @param staffCodeMap                       工号
     * @return 下发结果
     */
    public List<InsertRollPredictionDayResult> fetchInsertRollPredictionResultsDay(Map<String, List<MaterialPlanNeedVO>> materialPlanNeedMap,
                                                                                   List<MaterialPlanNeedIssueDetailsVO> materialPlanNeedIssueDetailsVOList,
                                                                                   Map<String, String> staffCodeMap) {
        try {
            // 填充30天月度要货数据
            MaterialPlanNeedReleaseDayDTO materialPlanNeedReleaseDayDTO =
                    fillMaterialPlanNeedReleaseDayDTO(materialPlanNeedMap, materialPlanNeedIssueDetailsVOList, staffCodeMap);
            // 填充参数调用dcp服务下发30天要货计划
            HashMap<String, Object> params = new HashMap<>();
            params.put("Service", materialPlanNeedReleaseDayDTO.getService());
            BaseResponse<String> response = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(),
                    ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_PRODUCT_NEED_DAY.getCode(), params);
            log.info("30天要货计划下发的数据为{}", materialPlanNeedReleaseDayDTO);
            if (response.getSuccess().equals(false)) {
                throw new BusinessException("服务接口调用失败");
            }
            // 解析嵌套的 JSON 数组
            return JSON.parseArray(response.getData(), InsertRollPredictionDayResult.class);
        } catch (Exception e) {
            log.error("下发30天要货计划失败", e);
            throw new BusinessException("下发30天要货计划失败,{0}", e.getLocalizedMessage());
        }
    }

    /**
     * 转换 MaterialPlanNeedReleaseDayDTO
     *
     * @param materialPlanNeedMap                要货计划
     * @param materialPlanNeedIssueDetailsVOList 要货计划下发明细
     * @param staffCodeMap                       工号
     * @return MaterialPlanNeedReleaseDayDTO 30天要货计划请求参数DTO
     */
    private MaterialPlanNeedReleaseDayDTO fillMaterialPlanNeedReleaseDayDTO(Map<String, List<MaterialPlanNeedVO>> materialPlanNeedMap,
                                                                            List<MaterialPlanNeedIssueDetailsVO> materialPlanNeedIssueDetailsVOList,
                                                                            Map<String, String> staffCodeMap) {

        Map<String, List<MaterialPlanNeedIssueDetailsVO>> materialPlanNeedIssueDetailsMap = materialPlanNeedIssueDetailsVOList.stream()
                .filter(data -> data.getDimension().equals(NeedDimensionEnum.DAY.getCode()))
                .collect(Collectors.groupingBy(MaterialPlanNeedIssueDetailsVO::getMaterialPlanNeedNo));

        // 初始化发布服务DTO
        MaterialPlanNeedReleaseDayDTO.Service service = createDayDTOService();

        // 使用Map按照planCode对PurchasingPlan进行分组
        Map<String, MaterialPlanNeedReleaseDayDTO.PurchasingPlan> planCodeMap = new HashMap<>();

        for (Map.Entry<String, List<MaterialPlanNeedVO>> entry : materialPlanNeedMap.entrySet()) {
            List<MaterialPlanNeedVO> materialPlanNeedVOlist = entry.getValue();

            int version = 1;
            // 处理物料数据
            for (MaterialPlanNeedVO materialPlanNeedVO : materialPlanNeedVOlist) {
                // 获取对应下发明细
                List<MaterialPlanNeedIssueDetailsVO> materialPlanNeedIssueDetailsVOS =
                        materialPlanNeedIssueDetailsMap.get(materialPlanNeedVO.getMaterialPlanNeedNo());
                if (CollectionUtils.isNotEmpty(materialPlanNeedIssueDetailsVOS)) {

                    // 获取明细里的版本号
                    version = materialPlanNeedIssueDetailsVOS.stream()
                            .sorted((vo1, vo2) -> Integer.compare(vo2.getIssueVersion(), vo1.getIssueVersion())).collect(Collectors.toList())
                            .get(0).getIssueVersion() + 1;
                }
                // 创建采购计划并设置基本信息
                MaterialPlanNeedReleaseDayDTO.PurchasingPlan purchasingPlan = createPurchasingPlan(materialPlanNeedVO, version, staffCodeMap);

                // 获取当前planCode对应的PurchasingPlan，如果不存在则创建一个新的
                MaterialPlanNeedReleaseDayDTO.PurchasingPlan existingPurchasingPlan = planCodeMap.get(purchasingPlan.getPlanCode());
                if (existingPurchasingPlan == null) {
                    planCodeMap.put(purchasingPlan.getPlanCode(), purchasingPlan);
                    existingPurchasingPlan = purchasingPlan;
                }

                // 设置物料数据
                MaterialPlanNeedReleaseDayDTO.Lines lines = existingPurchasingPlan.getLines();
                if (lines == null) {
                    lines = new MaterialPlanNeedReleaseDayDTO.Lines();
                    lines.setPurchasingPlanLine(new ArrayList<>());
                    existingPurchasingPlan.setLines(lines);
                }
                MaterialPlanNeedReleaseDayDTO.PurchasingPlanLine purchasingPlanLine = this.createPurchasingPlanLine(materialPlanNeedVO);
                lines.getPurchasingPlanLine().add(purchasingPlanLine);
            }
        }

        // 将分组后的PurchasingPlan添加到列表中
        List<MaterialPlanNeedReleaseDayDTO.PurchasingPlan> purchasingPlanList = new ArrayList<>(planCodeMap.values());

        MaterialPlanNeedReleaseDayDTO.list list = new MaterialPlanNeedReleaseDayDTO.list();
        list.setPurchasingPlanList(purchasingPlanList);

        // 设置要货数据
        MaterialPlanNeedReleaseDayDTO.Request request = new MaterialPlanNeedReleaseDayDTO.Request();
        request.setList(list);

        MaterialPlanNeedReleaseDayDTO.Data data = new MaterialPlanNeedReleaseDayDTO.Data();
        data.setRequest(request);
        data.setControl(null);

        // 将数据设置到服务中
        service.setData(data);

        // 创建最终的 DTO 对象
        MaterialPlanNeedReleaseDayDTO materialPlanNeedReleaseDayDTO = new MaterialPlanNeedReleaseDayDTO();
        materialPlanNeedReleaseDayDTO.setService(service);
        return materialPlanNeedReleaseDayDTO;
    }

    /**
     * 转换 MaterialPlanNeedReleaseDayDTO
     *
     * @param materialPlanNeedVOList             要货计划
     * @param materialPlanNeedIssueDetailsVOList
     * @return MaterialPlanNeedReleaseDayDTO 滚动预测要货计划请求参数DTO
     */
    private MaterialPlanNeedReleaseMonthDTO fillMaterialPlanNeedReleaseMonthDTO(List<MaterialPlanNeedVO> materialPlanNeedVOList, List<MaterialPlanNeedIssueDetailsVO> materialPlanNeedIssueDetailsVOList) {

        // 按月份和物料+库存点分组进行分组和累加
        Map<String, Map<String, MaterialPlanNeedVO>> materialPlanNeedMap = materialPlanNeedVOList.stream()
                // 按照日期（年-月）分组
                .collect(Collectors.groupingBy(plan -> {
                    // 增加对日期长度的检查，避免 substring 报错
                    String needDateStr = DateUtils.dateToString(plan.getNeedDate());
                    return plan.getNeedDate() != null && needDateStr.length() >= 7 ? needDateStr.substring(0, 7) : "";
                }, Collectors.toMap(plan -> plan.getStockPointCode() + "&" + plan.getProductCode(), plan -> plan, (plan1, plan2) -> {
                    // 对相同的 key 合并 MaterialPlanNeedVO 对象
                    BigDecimal totalNeedQuantity = (plan1.getNeedQuantity() != null ? plan1.getNeedQuantity() : BigDecimal.ZERO)
                            .add(plan2.getNeedQuantity() != null ? plan2.getNeedQuantity() : BigDecimal.ZERO);
                    plan1.setNeedQuantity(totalNeedQuantity);
                    // 返回合并后的新对象
                    return plan1;
                })));


        // 初始化发布服务DTO
        MaterialPlanNeedReleaseMonthDTO.Service service = createMonthDTOService();
        // 采购计划列表
        MaterialPlanNeedReleaseMonthDTO.RollPredictionList rollPredictionList = new MaterialPlanNeedReleaseMonthDTO.RollPredictionList();
        List<MaterialPlanNeedReleaseMonthDTO.RollPrediction> rollPredictions = new ArrayList<>();

        // 根据要货计划下发明细获取版本号
        Map<String, List<MaterialPlanNeedIssueDetailsVO>> materialPlanNeedNoMap = materialPlanNeedIssueDetailsVOList.stream()
                .filter(data -> data.getDimension().equals(NeedDimensionEnum.MONTH.getCode()))
                // 这里时间取创建时间 因为传递给SRM的下发时间是new Date()
                .collect(Collectors.groupingBy(data ->
                        data.getMaterialPlanNeedNo() + "&" + DateUtils.formatDate(data.getCreateTime(), "yyyy-MM-dd")));

        int version = 1;
        for (Map.Entry<String, Map<String, MaterialPlanNeedVO>> entity : materialPlanNeedMap.entrySet()) {
            Map<String, MaterialPlanNeedVO> value = entity.getValue();
            for (Map.Entry<String, MaterialPlanNeedVO> productEntity : value.entrySet()) {
                MaterialPlanNeedVO materialPlanNeedVO = productEntity.getValue();
                // 获取对应下发明细
                List<MaterialPlanNeedIssueDetailsVO> materialPlanNeedIssueDetailsVOS = materialPlanNeedNoMap.get(
                        materialPlanNeedVO.getMaterialPlanNeedNo() + "&" +
                                DateUtils.formatDate(materialPlanNeedVO.getCreateTime(), "yyyy-MM-dd"));
                if (CollectionUtils.isNotEmpty(materialPlanNeedIssueDetailsVOS)) {
                    // 获取明细里的版本号并+1
                    version = materialPlanNeedIssueDetailsVOS.stream()
                            .sorted((vo1, vo2) -> Integer.compare(vo2.getIssueVersion(), vo1.getIssueVersion()))
                            .collect(Collectors.toList()).get(0).getIssueVersion() + 1;
                }
                MaterialPlanNeedReleaseMonthDTO.RollPrediction rollPrediction = createRollPrediction(materialPlanNeedVO, version);
                rollPredictions.add(rollPrediction);
            }
        }
        rollPredictionList.setRollPrediction(rollPredictions);

        // 设置要货数据
        MaterialPlanNeedReleaseMonthDTO.Request request = new MaterialPlanNeedReleaseMonthDTO.Request();
        request.setRollPredictionList(rollPredictionList);

        MaterialPlanNeedReleaseMonthDTO.Data data = new MaterialPlanNeedReleaseMonthDTO.Data();
        data.setRequest(request);
        // 将数据设置到服务中
        service.setData(data);

        // 创建最终的 DTO 对象
        MaterialPlanNeedReleaseMonthDTO materialPlanNeedReleaseMonthDTO = new MaterialPlanNeedReleaseMonthDTO();
        materialPlanNeedReleaseMonthDTO.setService(service);
        return materialPlanNeedReleaseMonthDTO;
    }


    /**
     * 创建并返回一个已赋固定值的Service对象
     *
     * @return MaterialPlanNeedReleaseDayDTO.Service 返回一个初始化了Route信息的Service对象
     */
    private MaterialPlanNeedReleaseDayDTO.Service createDayDTOService() {
        MaterialPlanNeedReleaseDayDTO.Service service = new MaterialPlanNeedReleaseDayDTO.Service();

        MaterialPlanNeedReleaseDayDTO.Route route = new MaterialPlanNeedReleaseDayDTO.Route();
        route.setSerialNo("2024121203018000001");
        route.setServiceId("02003000000008");
        route.setSourceSysId("03018");
        route.setServiceTime("20241212152805");
        service.setRoute(route);

        return service;
    }

    /**
     * 创建并返回一个已赋固定值的Service对象
     *
     * @return MaterialPlanNeedReleaseMonthDTO.Service 返回一个初始化了Route信息的Service对象
     */
    private MaterialPlanNeedReleaseMonthDTO.Service createMonthDTOService() {
        MaterialPlanNeedReleaseMonthDTO.Service service = new MaterialPlanNeedReleaseMonthDTO.Service();

        MaterialPlanNeedReleaseMonthDTO.Route route = new MaterialPlanNeedReleaseMonthDTO.Route();
        route.setSerialNO("2024121203018000001");
        route.setServiceID("02003000000009");
        route.setSourceSysID("03018");
        route.setServiceTime("20241212152805");
        service.setRoute(route);

        return service;
    }

    /**
     * 创建并返回一个已赋固定值的Service对象
     *
     * @return MaterialPlanNeedPlannerConfirmDTO.Service 返回一个初始化了Route信息的Service对象
     */
    private MaterialPlanNeedPlannerConfirmDTO.Service createPlannerConfirmDTOService() {
        MaterialPlanNeedPlannerConfirmDTO.Service service = new MaterialPlanNeedPlannerConfirmDTO.Service();

        MaterialPlanNeedPlannerConfirmDTO.Route route = new MaterialPlanNeedPlannerConfirmDTO.Route();
        route.setSerialNo("2024121203018000001");
        route.setServiceId("02003000000010");
        route.setSourceSysId("03018");
        route.setServiceTime("20241212152805");
        service.setRoute(route);

        return service;
    }

    /**
     * 根据传入的MaterialPlanNeedVO和版本号version创建并返回一个新的PurchasingPlan对象
     *
     * @param materialPlanNeedVO 包含物料计划信息的VO对象
     * @param version            采购计划的版本号
     * @param staffCodeMap       工号
     * @return MaterialPlanNeedReleaseDayDTO.PurchasingPlan 返回初始化的PurchasingPlan对象
     */
    private MaterialPlanNeedReleaseDayDTO.PurchasingPlan createPurchasingPlan(MaterialPlanNeedVO materialPlanNeedVO,
                                                                              int version, Map<String, String> staffCodeMap) {
        MaterialPlanNeedReleaseDayDTO.PurchasingPlan purchasingPlan = new MaterialPlanNeedReleaseDayDTO.PurchasingPlan();

        // 设置物料计划的相关信息
        if (materialPlanNeedVO != null) {
            purchasingPlan.setPlanCode(materialPlanNeedVO.getMaterialPlanNeedNo() != null ? materialPlanNeedVO.getMaterialPlanNeedNo() : "");
            purchasingPlan.setOrgId(materialPlanNeedVO.getOrganizeId() != null ? materialPlanNeedVO.getOrganizeId() : "");
            purchasingPlan.setSupplierCode(materialPlanNeedVO.getSupplierCode() != null ? materialPlanNeedVO.getSupplierCode() : "");
            purchasingPlan.setSupplierName(materialPlanNeedVO.getSupplierName() != null ? materialPlanNeedVO.getSupplierName() : "");
            purchasingPlan.setMaterialPlanNeedId(materialPlanNeedVO.getId());
            purchasingPlan.setVersionNo(version);
            purchasingPlan.setPublisherCode(staffCodeMap.get(materialPlanNeedVO.getCreator()) != null ?
                    staffCodeMap.get(materialPlanNeedVO.getCreator()) :
                    "默认");
            purchasingPlan.setPublishDate(DateUtils.dateToString(new Date(), "yyyy-MM-dd"));
            purchasingPlan.setSupplierSouce("");
        }

        return purchasingPlan;
    }


    /**
     * 根据传入的MaterialPlanNeedVO对象创建并返回一个新的PurchasingPlanLine对象
     *
     * @param planNeedVO 包含物料计划行信息的VO对象
     * @return MaterialPlanNeedPlannerConfirmDTO.PurchasingPlanLine
     */
    private MaterialPlanNeedPlannerConfirmDTO.PurchasingPlanLine createPurchasingPlanLine02(MaterialPlanNeedVO planNeedVO) {
        MaterialPlanNeedPlannerConfirmDTO.PurchasingPlanLine purchasingPlanLine = new MaterialPlanNeedPlannerConfirmDTO.PurchasingPlanLine();

        // 设置物料行的相关信息
        if (planNeedVO != null) {
            purchasingPlanLine.setItemCode(planNeedVO.getProductCode() != null ? planNeedVO.getProductCode() : "");
            purchasingPlanLine.setLineStatus("Y");
            purchasingPlanLine.setPromiseQty(planNeedVO.getExpectedArrivalQuantity() != null ? planNeedVO.getExpectedArrivalQuantity().doubleValue() : 0);
            // 有父级ID说明是拆单  取id要取父级ID
            if (StringUtils.isNotEmpty(planNeedVO.getParentId())) {
                purchasingPlanLine.setMaterialPlanNeedId(planNeedVO.getParentId());
            } else {
                purchasingPlanLine.setMaterialPlanNeedId(planNeedVO.getId());
            }
        }
        return purchasingPlanLine;
    }

    /**
     * 根据传入的MaterialPlanNeedVO对象创建并返回一个新的PurchasingPlanLine对象
     *
     * @param planNeedVO 包含物料计划行信息的VO对象
     * @return MaterialPlanNeedReleaseDayDTO.PurchasingPlanLine 返回初始化的PurchasingPlanLine对象
     */
    private MaterialPlanNeedReleaseDayDTO.PurchasingPlanLine createPurchasingPlanLine(MaterialPlanNeedVO planNeedVO) {
        MaterialPlanNeedReleaseDayDTO.PurchasingPlanLine purchasingPlanLine = new MaterialPlanNeedReleaseDayDTO.PurchasingPlanLine();

        // 设置物料行的相关信息
        if (planNeedVO != null) {
            purchasingPlanLine.setItemCode(planNeedVO.getProductCode() != null ? planNeedVO.getProductCode() : "");
            purchasingPlanLine.setItemName(planNeedVO.getProductName() != null ? planNeedVO.getProductName() : "");
            purchasingPlanLine.setLockDays(planNeedVO.getRequestCargoPlanLockDay() != null ? planNeedVO.getRequestCargoPlanLockDay().intValue() : 0);
            purchasingPlanLine.setRequireDate(planNeedVO.getExpectedArrivalTime() != null ? DateUtils.dateToString(planNeedVO.getExpectedArrivalTime()) : "");
            purchasingPlanLine.setRequireQty(planNeedVO.getNeedQuantity() != null ? planNeedVO.getNeedQuantity().intValue() : 0);
            purchasingPlanLine.setMatchedPo(planNeedVO.getOrderNo() != null ? planNeedVO.getOrderNo() : "");
            purchasingPlanLine.setMatchedQty(planNeedVO.getOrderQuantity() != null ? planNeedVO.getOrderQuantity().intValue() : 0);
            purchasingPlanLine.setMaterialPlanNeedId(planNeedVO.getId());
            if (StringUtils.isNotEmpty(planNeedVO.getPublishStatus()) && planNeedVO.getPublishStatus().equals(PlanNeedPublishStatusEnum.AGREEMENT_REACHED.getCode())) {
                purchasingPlanLine.setLineStatus("Y");
            } else {
                purchasingPlanLine.setLineStatus("N");
            }
            purchasingPlanLine.setAgentCode(SystemHolder.getStaffCode() != null ? SystemHolder.getStaffCode() : "默认");
        }

        return purchasingPlanLine;
    }

    /**
     * 根据传入的MaterialPlanNeedVO对象创建并返回一个新的RollPrediction对象
     *
     * @param planNeedVO 包含物料计划行信息的VO对象
     * @return MaterialPlanNeedReleaseMonthDTO.RollPrediction  返回初始化的RollPrediction对象
     */
    private MaterialPlanNeedReleaseMonthDTO.RollPrediction createRollPrediction(MaterialPlanNeedVO planNeedVO, int version) {
        MaterialPlanNeedReleaseMonthDTO.RollPrediction rollPrediction = new MaterialPlanNeedReleaseMonthDTO.RollPrediction();
        if (Objects.nonNull(planNeedVO)) {
            rollPrediction.setPredictionNo(planNeedVO.getMaterialPlanNeedNo());
            rollPrediction.setOrgCode(planNeedVO.getOrganizeId());
            rollPrediction.setOrgName(planNeedVO.getStockPointName());
            rollPrediction.setSupplierCode(planNeedVO.getSupplierCode());
            rollPrediction.setSupplierName(planNeedVO.getSupplierName());
            rollPrediction.setItemCode(planNeedVO.getProductCode());
            rollPrediction.setItemName(planNeedVO.getProductName());
            rollPrediction.setRequireDate(planNeedVO.getNeedDate() != null ? DateUtils.dateToString(planNeedVO.getNeedDate()) : DateUtils.dateToString(new Date()));
            rollPrediction.setRequireNum(planNeedVO.getNeedQuantity() != null ? String.valueOf(planNeedVO.getNeedQuantity()) : "0");
            rollPrediction.setVersion(String.valueOf(version));
            rollPrediction.setPublisher("");
            rollPrediction.setPublishDate(DateUtils.dateToString(new Date(), "yyyy-MM-dd"));
        }
        return rollPrediction;
    }

    @Override
    public List<MaterialPlanNeedVO> selectByVOParams(Map<String, Object> params) {
        return materialPlanNeedDao.selectVOByParams(params);
    }

    @Override
    public BaseResponse<Void> updateBatchSelective(List<MaterialPlanNeedDTO> materialPlanNeedDTOList) {
        List<MaterialPlanNeedPO> newList = MaterialPlanNeedConvertor.INSTANCE.dto2Pos(materialPlanNeedDTOList);
        BasePOUtils.updateBatchFiller(newList);
        materialPlanNeedDao.updateBatchSelective(newList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doDeleteByPublishStatus(String publishStatus, String userId) {
        materialPlanNeedDao.deleteByPublishStatus(publishStatus, userId);
    }

    @Override
    public BaseResponse<Void> plannerConfirm(List<MaterialPlanNeedDTO> materialPlanNeedDTOList) {
        if (CollectionUtils.isEmpty(materialPlanNeedDTOList)) {
            throw new BusinessException("计划员确认数据不可为空");
        }
        List<String> ids = materialPlanNeedDTOList.stream().map(MaterialPlanNeedDTO::getId).collect(Collectors.toList());
        List<MaterialPlanNeedVO> materialPlanNeedVOList = this.selectByVOParams(ImmutableMap.of("ids", ids));

        // 获取到货跟踪数据
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = materialArrivalTrackingService.selectByParams(ImmutableMap.of("sourceIds", ids));
        // 收集id
        List<String> materialArrivalTrackingIdList = materialArrivalTrackingVOList.stream().map(BaseVO::getId).collect(Collectors.toList());

        try {
            // 填充计划员确认数据
            MaterialPlanNeedPlannerConfirmDTO needPlannerConfirmDTO = fillMaterialPlanNeedPlannerConfirmDTO(materialPlanNeedVOList);
            // 填充参数调用dcp服务下发计划员确认
            HashMap<String, Object> params = new HashMap<>();
            params.put("Service", needPlannerConfirmDTO.getService());
            BaseResponse<String> response = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_PRODUCT_NEED_PLANNER_CONFIRM.getCode(), params);
            log.info("计划员确认的数据为{}", needPlannerConfirmDTO);
            if (response.getSuccess().equals(false)) {
                throw new BusinessException("服务接口调用失败");
            }
            // 解析嵌套的 JSON 数组
            List<UpdateLineStatusResult> updateLineStatusResults = JSON.parseArray(response.getData(), UpdateLineStatusResult.class);

//            log.info("计划员确认返回的数据{}", JSON.toJSONString(updateLineStatusResults));

            // 修改要货计划发布状态
            this.updateBatchSelective(materialPlanNeedDTOList);

            // 修改到货跟踪发布状态
            if (CollectionUtils.isNotEmpty(materialArrivalTrackingIdList)) {
                List<MaterialArrivalTrackingDTO> updateListMaterialArrivalTracking = materialArrivalTrackingIdList.stream().map(data -> MaterialArrivalTrackingDTO.builder().id(data).publishStatus(PlanNeedPublishStatusEnum.AGREEMENT_REACHED.getCode()).build()).collect(Collectors.toList());

                materialArrivalTrackingService.doUpdateBatchSelective(updateListMaterialArrivalTracking);
            }
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("计划员确认失败", e);
            throw new BusinessException("计划员确认失败,{0}", e.getLocalizedMessage());
        }
    }

    private MaterialPlanNeedPlannerConfirmDTO fillMaterialPlanNeedPlannerConfirmDTO(List<MaterialPlanNeedVO> materialPlanNeedVOList) {
        MaterialPlanNeedPlannerConfirmDTO.Service plannerConfirmDTOService = createPlannerConfirmDTOService();

        List<MaterialPlanNeedPlannerConfirmDTO.PurchasingPlanLine> purchasingPlanLines = new ArrayList<>();
        for (MaterialPlanNeedVO materialPlanNeedVO : materialPlanNeedVOList) {
            MaterialPlanNeedPlannerConfirmDTO.PurchasingPlanLine purchasingPlanLine = createPurchasingPlanLine02(materialPlanNeedVO);
            purchasingPlanLines.add(purchasingPlanLine);
        }

        MaterialPlanNeedPlannerConfirmDTO.list list = new MaterialPlanNeedPlannerConfirmDTO.list();
        list.setPurchasingPlanLine(purchasingPlanLines);

        MaterialPlanNeedPlannerConfirmDTO.Request request = new MaterialPlanNeedPlannerConfirmDTO.Request();
        request.setList(list);

        MaterialPlanNeedPlannerConfirmDTO.Data data = new MaterialPlanNeedPlannerConfirmDTO.Data();
        data.setRequest(request);
        data.setControl(new MaterialPlanNeedPlannerConfirmDTO.Control());

        // 将数据设置到服务中
        plannerConfirmDTOService.setData(data);

        // 创建最终的 DTO 对象
        MaterialPlanNeedPlannerConfirmDTO needPlannerConfirmDTO = new MaterialPlanNeedPlannerConfirmDTO();
        needPlannerConfirmDTO.setService(plannerConfirmDTOService);

        return needPlannerConfirmDTO;
    }

    private void generatePlanNeedNo(List<MaterialPlanNeedDTO> parentMaterialPlanNeedDTOList, Map<String, Integer> supplierSerialNumberMap, Date nowDate) {
        Map<String, List<MaterialPlanNeedDTO>> materialPlanNeedDtoGroup = parentMaterialPlanNeedDTOList.stream()
                .collect(Collectors.groupingBy(MaterialPlanNeedDTO::getSupplierCode));
        for (Map.Entry<String, List<MaterialPlanNeedDTO>> entry : materialPlanNeedDtoGroup.entrySet()) {
            String supplierCode = entry.getKey();
            List<MaterialPlanNeedDTO> planNeedDTOList = entry.getValue();
            int serialNumber = 1;
            // 获取供应商序列号
            if (supplierSerialNumberMap.containsKey(supplierCode)) {
                serialNumber = supplierSerialNumberMap.get(supplierCode);
            } else {
                // 从缓存里获取
                if (redisUtil.hasKey(String.join("#", RedisKeyManageEnum.PLAN_NEED_SERIAL_NUMBER_KEY.getKey(), supplierCode))) {
                    serialNumber = (int) redisUtil.get(String.join("#", RedisKeyManageEnum.PLAN_NEED_SERIAL_NUMBER_KEY.getKey(), supplierCode));
                }
            }
            String planNeedNo = "P" + supplierCode + DateUtils.dateToString(nowDate, "yyyyMMdd") + String.format("%03d", serialNumber);
            for (MaterialPlanNeedDTO materialPlanNeedDTO : planNeedDTOList) {
                materialPlanNeedDTO.setMaterialPlanNeedNo(planNeedNo);
            }
            serialNumber++;
            supplierSerialNumberMap.put(supplierCode, serialNumber);
            redisUtil.set(String.join("#", RedisKeyManageEnum.PLAN_NEED_SERIAL_NUMBER_KEY.getKey(), supplierCode), serialNumber);
        }
    }

    @Override
    public void doDeleteByParams(Map<String, Object> params) {
        materialPlanNeedDao.deleteByParams(params);
    }

    @Override
    public BaseResponse<Void> arrived(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) throw new BusinessException("下发到货数据不可为空");

        List<MaterialPlanNeedVO> materialPlanNeedVOList = this.selectByVOParams(ImmutableMap.of("ids", ids));

        try {
            List<MaterialPlanNeedVO> planNeedVOListSupplierNotNull = materialPlanNeedVOList.stream()
                    .filter(item -> org.apache.commons.lang3.StringUtils.isBlank(item.getSupplierCode())).collect(Collectors.toList());

            // 填充下发到货数据
            MaterialPlanNeedArrivedDTO materialPlanNeedArrivedDTO = fillMaterialPlanNeedArrivedDTO(planNeedVOListSupplierNotNull);

            // 填充参数调用dcp服务下发计划员确认
            HashMap<String, Object> params = new HashMap<>();
            params.put("Service", materialPlanNeedArrivedDTO.getService());
            BaseResponse<String> response = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_PRODUCT_NEED_ARRIVED.getCode(), params);
            log.info("下发到货的数据为{}", materialPlanNeedArrivedDTO);
            if (response.getSuccess().equals(false)) {
                throw new BusinessException("服务接口调用失败");
            }

            // 进行mrp推移
//            log.info("要货计划下发完成,进行材料mrp计算");
//            MrpParamDTO mrpParamDTO = new MrpParamDTO();
//            mrpParamDTO.setDemandSource("DELIVERY_PLAN_DEMAND");
//            mrpParamDTO.setCalcStartTime(DateUtils.getDayFirstTime(new Date()));
//            mrpParamDTO.setWhetherAutomatic(Boolean.FALSE);
//            List<String> computeProductCodeList = materialPlanNeedVOList.stream()
//                    .map(MaterialPlanNeedVO::getProductCode)
//                    .distinct().collect(Collectors.toList());
//            log.info("要货计划下发,mrp需要计算的物料数据:{}", String.join(",", computeProductCodeList));
//            mrpParamDTO.setComputeProductCodeList(computeProductCodeList);
//            noGlassMrpService.doRunMrp(mrpParamDTO);

            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("下发到货失败", e);
            throw new BusinessException("下发到货失败,{0}", e.getLocalizedMessage());
        }
    }

    /**
     * 转换 MaterialPlanNeedArrivedDTO
     *
     * @param materialPlanNeedVOList 要货计划
     * @return MaterialPlanNeedArrivedDTO  下发到货DTO
     */
    private MaterialPlanNeedArrivedDTO fillMaterialPlanNeedArrivedDTO(List<MaterialPlanNeedVO> materialPlanNeedVOList) {

        // 获取到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = materialArrivalTrackingService.selectAll();
        // 到货跟踪根据采购单号+要货单号分组
        Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingMap = materialArrivalTrackingVOList.stream().collect(Collectors.groupingBy(data -> String.join("&", data.getPurchaseOrderCode(), data.getMaterialPlanNeedNo())));

        MaterialPlanNeedArrivedDTO.Service arrivedDTOService = createArrivedDTOService();

        List<MaterialPlanNeedArrivedDTO.PurchasingPlanLine> purchasingPlanLines = new ArrayList<>();
        for (MaterialPlanNeedVO materialPlanNeedVO : materialPlanNeedVOList) {
            MaterialPlanNeedArrivedDTO.PurchasingPlanLine purchasingPlanLine = new MaterialPlanNeedArrivedDTO.PurchasingPlanLine();

            // 获取对应的到货跟踪数据
            List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS = materialArrivalTrackingMap.get(String.join("&", materialPlanNeedVO.getPurchaseOrderCode(), materialPlanNeedVO.getMaterialPlanNeedNo()));
            BigDecimal delivered = BigDecimal.ZERO;
            BigDecimal alreadyInStock = BigDecimal.ZERO;
            BigDecimal planProcurement = BigDecimal.ZERO;
            if (CollectionUtils.isNotEmpty(materialArrivalTrackingVOS)) {
                // 已送货数量
                delivered = materialArrivalTrackingVOS.stream().filter(data -> data.getArrivalStatus().equals(ArrivalStatusEnum.DELIVERED.getCode())).map(MaterialArrivalTrackingVO::getRequireQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 已入库数量
                alreadyInStock = materialArrivalTrackingVOS.stream().filter(data -> data.getArrivalStatus().equals(ArrivalStatusEnum.ALREADY_IN_STOCK.getCode())).map(MaterialArrivalTrackingVO::getRequireQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 采购数量
                planProcurement = materialArrivalTrackingVOS.stream().filter(data -> data.getArrivalStatus().equals(ArrivalStatusEnum.PLAN_PRUCHASE.getCode())).map(MaterialArrivalTrackingVO::getRequireQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);

            }
            purchasingPlanLine.setOnroadQty(delivered.toString());
            purchasingPlanLine.setReceivedQty(alreadyInStock.toString());
            purchasingPlanLine.setWaitingQty(planProcurement.toString());
            purchasingPlanLine.setMaterialPlanNeedId(materialPlanNeedVO.getId());
            purchasingPlanLines.add(purchasingPlanLine);
        }

        MaterialPlanNeedArrivedDTO.ListWrapper list = new MaterialPlanNeedArrivedDTO.ListWrapper();
        list.setPurchasingPlanLine(purchasingPlanLines);

        MaterialPlanNeedArrivedDTO.Request request = new MaterialPlanNeedArrivedDTO.Request();
        request.setList(list);

        MaterialPlanNeedArrivedDTO.Data data = new MaterialPlanNeedArrivedDTO.Data();
        data.setRequest(request);
        data.setControl(new MaterialPlanNeedArrivedDTO.Control());

        // 将数据设置到服务中
        arrivedDTOService.setData(data);

        // 创建最终的 DTO 对象
        MaterialPlanNeedArrivedDTO needPlannerConfirmDTO = new MaterialPlanNeedArrivedDTO();
        needPlannerConfirmDTO.setService(arrivedDTOService);

        return needPlannerConfirmDTO;
    }

    private MaterialPlanNeedArrivedDTO.Service createArrivedDTOService() {
        MaterialPlanNeedArrivedDTO.Service service = new MaterialPlanNeedArrivedDTO.Service();

        MaterialPlanNeedArrivedDTO.Route route = new MaterialPlanNeedArrivedDTO.Route();
        route.setSerialNo("2024121203018000001");
        route.setServiceId("02003000000011");
        route.setSourceSysId("03018");
        route.setServiceTime("20241212152805");
        route.setSoapenv("http://schemas.xmlsoap.org/soap/envelope/");

        MaterialPlanNeedArrivedDTO.ServiceResponse serviceResponse = new MaterialPlanNeedArrivedDTO.ServiceResponse();
        serviceResponse.setStatus("COMPLETE");
        route.setServiceResponse(serviceResponse);

        service.setRoute(route);

        return service;
    }

    private MaterialPlanNeedCancelIssueDTO.Service createCancelIssueDTOService() {
        MaterialPlanNeedCancelIssueDTO.Service service = new MaterialPlanNeedCancelIssueDTO.Service();

        MaterialPlanNeedCancelIssueDTO.Route route = new MaterialPlanNeedCancelIssueDTO.Route();
        route.setSerialNo("2024121203018000001");
        route.setServiceId("02003000000014");
        route.setSourceSysId("03018");
        route.setServiceTime("20241212152805");
        service.setRoute(route);

        return service;
    }

    /**
     * 转换 MaterialPlanNeedCancelIssueDTO
     *
     * @param materialPlanNeedVOList 要货计划
     * @return MaterialPlanNeedArrivedDTO  下发到货DTO
     */
    private MaterialPlanNeedCancelIssueDTO fillMaterialPlanNeedCancelIssueDTO(List<MaterialPlanNeedVO> materialPlanNeedVOList) {
        MaterialPlanNeedCancelIssueDTO.Service arrivedDTOService = createCancelIssueDTOService();

        List<MaterialPlanNeedCancelIssueDTO.PurchasingPlanLine> purchasingPlanLines = new ArrayList<>();
        for (MaterialPlanNeedVO materialPlanNeedVO : materialPlanNeedVOList) {
            MaterialPlanNeedCancelIssueDTO.PurchasingPlanLine purchasingPlanLine = new MaterialPlanNeedCancelIssueDTO.PurchasingPlanLine();
            purchasingPlanLine.setMaterialPlanNeedId(materialPlanNeedVO.getId());
            purchasingPlanLines.add(purchasingPlanLine);
        }

        MaterialPlanNeedCancelIssueDTO.ListWrapper list = new MaterialPlanNeedCancelIssueDTO.ListWrapper();
        list.setPurchasingPlanLine(purchasingPlanLines);

        MaterialPlanNeedCancelIssueDTO.Request request = new MaterialPlanNeedCancelIssueDTO.Request();
        request.setList(list);

        MaterialPlanNeedCancelIssueDTO.Data data = new MaterialPlanNeedCancelIssueDTO.Data();
        data.setRequest(request);
        data.setControl(new MaterialPlanNeedCancelIssueDTO.Control());

        // 将数据设置到服务中
        arrivedDTOService.setData(data);

        // 创建最终的 DTO 对象
        MaterialPlanNeedCancelIssueDTO needPlannerConfirmDTO = new MaterialPlanNeedCancelIssueDTO();
        needPlannerConfirmDTO.setService(arrivedDTOService);

        return needPlannerConfirmDTO;
    }

    @Override
    public void doSyncPlanNeedData(List<NoGlassInventoryShiftDataVO> shiftDataList, List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftListOfPlanNeed,
                                   Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMap, Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
                                   Map<String, SupplierVO> supplierVOMapOfId, String versionCode, List<String> permissionCodeList, List<String> addNeedIdList) {

        List<MaterialPlanNeedDTO> insertMaterialPlanNeedDTOList = new ArrayList<>();

        Map<String, NoGlassInventoryShiftDataVO> shiftDataVOMap = shiftDataList
                .stream().collect(Collectors.toMap(NoGlassInventoryShiftDataVO::getId, Function.identity()));

        Map<String, List<NoGlassInventoryShiftDetailVO>> noGlassShiftDetailGroup = noGlassInventoryShiftListOfPlanNeed
                .stream().collect(Collectors.groupingBy(NoGlassInventoryShiftDetailVO::getNoGlassInventoryShiftDataId));
        for (Map.Entry<String, List<NoGlassInventoryShiftDetailVO>> entry : noGlassShiftDetailGroup.entrySet()) {
            NoGlassInventoryShiftDataVO shiftDataVO = shiftDataVOMap.get(entry.getKey());
            // 获取材料与供应商数据
            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMap.get(shiftDataVO.getProductCode());
            if (null == materialSupplierPurchaseVO) {
                continue;
            }
            // 获取采购比例数据
            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup.get(materialSupplierPurchaseVO.getId());
            if (CollectionUtils.isEmpty(supplierPurchaseRatioVOList)) {
                continue;
            }
            // 订单下达提前期
            int orderPlacementLeadTimeDay = materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay() == null ? 0 : materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay().intValue();

            List<NoGlassInventoryShiftDetailVO> detailVOS = entry.getValue();
            for (NoGlassInventoryShiftDetailVO noGlassInventoryShiftVO : detailVOS) {
                if (null == noGlassInventoryShiftVO.getPlanPurchase() || noGlassInventoryShiftVO.getPlanPurchase().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                // 剩余计划采购量
                BigDecimal remainderPlanPurchase = noGlassInventoryShiftVO.getPlanPurchase();
                // 按照供应商采购比例生成要货计划
                for (int i = 0; i < supplierPurchaseRatioVOList.size(); i++) {
                    SupplierPurchaseRatioVO supplierPurchaseRatioVO = supplierPurchaseRatioVOList.get(i);
                    // 采购比例
                    BigDecimal purchaseRatio;
                    if (null == supplierPurchaseRatioVO.getPurchaseRatio() || supplierPurchaseRatioVO.getPurchaseRatio().compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    purchaseRatio = supplierPurchaseRatioVO.getPurchaseRatio();
                    // 获取供应商
                    SupplierVO supplierVO = supplierVOMapOfId.get(supplierPurchaseRatioVO.getSupplierId());
                    if (null == supplierVO) {
                        continue;
                    }
                    BigDecimal planPurchase = noGlassInventoryShiftVO.getPlanPurchase();
                    planPurchase = planPurchase.multiply(purchaseRatio);
                    // 最后一个供应商取剩余量，为0则并不生成
                    if ((i == supplierPurchaseRatioVOList.size() - 1 && remainderPlanPurchase.compareTo(BigDecimal.ZERO) == 0) || remainderPlanPurchase.compareTo(BigDecimal.ZERO) < 0) {
                        continue;
                    }
                    if (null != materialSupplierPurchaseVO.getPackageLot() && i != supplierPurchaseRatioVOList.size() - 1) {
                        // 按照包装批量向上取整
                        planPurchase = this.roundUpwards(planPurchase, materialSupplierPurchaseVO.getPackageLot());
                    }
                    if (i == supplierPurchaseRatioVOList.size() - 1) {
                        // 最后一家取剩余量
                        planPurchase = remainderPlanPurchase;
                    }
                    remainderPlanPurchase = remainderPlanPurchase.subtract(planPurchase);
                    // 生成要货计划
                    MaterialPlanNeedDTO mergeMaterialPlanNeedDTO = getMergeMaterialPlanNeedDTO(noGlassInventoryShiftVO.getInventoryDate(), planPurchase, supplierVO, orderPlacementLeadTimeDay);
                    mergeMaterialPlanNeedDTO.setMaterialPlanInventoryShiftId(noGlassInventoryShiftVO.getId());
                    mergeMaterialPlanNeedDTO.setProductCode(shiftDataVO.getProductCode());
                    mergeMaterialPlanNeedDTO.setInventoryShiftVersionCode(versionCode);
                    mergeMaterialPlanNeedDTO.setWhetherLockPeriod(noGlassInventoryShiftVO.getWhetherLockPeriod());
                    mergeMaterialPlanNeedDTO.setRequestCargoPlanLockDay(materialSupplierPurchaseVO.getRequestCargoPlanLockDay());
                    insertMaterialPlanNeedDTOList.add(mergeMaterialPlanNeedDTO);
                }
            }
        }

        //持久化数据
        if (CollectionUtils.isNotEmpty(insertMaterialPlanNeedDTOList)) {
            // 生成要货计划号
            Date nowDate = new Date();
            Map<String, Integer> supplierSerialNumberMap = new HashMap<>();
            generatePlanNeedNo(insertMaterialPlanNeedDTOList, supplierSerialNumberMap, nowDate);

            // 汇总当前发布的物料
            List<String> planNeedProductCodeList = insertMaterialPlanNeedDTOList.stream()
                    .map(MaterialPlanNeedDTO::getProductCode).distinct()
                    .collect(Collectors.toList());

            // 根据物料删除未发布的要货计划（改为权限下所有未发布的物料）
            this.doDeleteByParams(ImmutableMap.of(
                    "publishStatus", PlanNeedPublishStatusEnum.UN_PUBLISHED.getCode(),
                    "productCodeList", permissionCodeList));

//            materialPlanNeedService.doDeleteByParams(ImmutableMap.of(
//                    "publishStatus", PlanNeedPublishStatusEnum.UN_PUBLISHED.getCode(),
//                    "userId", SystemHolder.getUserId()));

            List<MaterialPlanNeedDTO> updateList = new ArrayList<>();
            List<MaterialPlanNeedDTO> insertList = new ArrayList<>();

            // 筛选出目前生成的要货计划
            List<MaterialPlanNeedVO> dataBaseMaterialPlanNeedDTOList = this.selectByVOParams(ImmutableMap.of("productCodeList", planNeedProductCodeList));

            if (CollectionUtils.isNotEmpty(dataBaseMaterialPlanNeedDTOList)) {
                Map<String, MaterialPlanNeedVO> databaseMaterialPlanNeedVOMapOfJoinKey = dataBaseMaterialPlanNeedDTOList.stream()
                        .collect(Collectors.toMap(item -> String.join("#",
                                item.getProductCode(),
                                item.getSupplierCode(),
                                DateUtils.dateToString(item.getNeedDate())), Function.identity(), (k1, k2) -> k2));

                for (MaterialPlanNeedDTO materialPlanNeedDTO : insertMaterialPlanNeedDTOList) {
                    MaterialPlanNeedVO materialPlanNeedVO = databaseMaterialPlanNeedVOMapOfJoinKey.get(String.join("#",
                            materialPlanNeedDTO.getProductCode(),
                            materialPlanNeedDTO.getSupplierCode(),
                            DateUtils.dateToString(materialPlanNeedDTO.getNeedDate())));
                    if (null != materialPlanNeedVO) {
                        // 判断是否在锁定期内
                        if (StringUtils.equals(YesOrNoEnum.YES.getCode(), materialPlanNeedDTO.getWhetherLockPeriod())) {
                            // 在锁定期内，需要判断是否生成了PO
                            if (org.apache.commons.lang3.StringUtils.isNotBlank(materialPlanNeedVO.getPurchaseOrderCode())) {
                                // 生成了PO，新增一条数据
                                insertList.add(materialPlanNeedDTO);
                            } else {
                                // 未生成PO，数量叠加
                                MaterialPlanNeedDTO updateMaterialPlanNeedDTO = MaterialPlanNeedConvertor.INSTANCE.vo2Dto(materialPlanNeedVO);
                                updateMaterialPlanNeedDTO.setNeedQuantity(materialPlanNeedVO.getNeedQuantity().add(materialPlanNeedDTO.getNeedQuantity()));
                                updateMaterialPlanNeedDTO.setPublishStatus(PlanNeedPublishStatusEnum.CHANGED.getCode());
                                updateMaterialPlanNeedDTO.setExpectedArrivalTime(updateMaterialPlanNeedDTO.getNeedDate());
                                updateMaterialPlanNeedDTO.setExpectedArrivalQuantity(updateMaterialPlanNeedDTO.getNeedQuantity());
                                // 获取材料与供应商数据
                                MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMap.get(materialPlanNeedVO.getProductCode());
                                updateMaterialPlanNeedDTO.setRequestCargoPlanLockDay(materialSupplierPurchaseVO.getRequestCargoPlanLockDay());
                                updateList.add(updateMaterialPlanNeedDTO);
                            }
                        } else {
                            // 不在锁定期内，同一时间保留最新发布的数据
                            MaterialPlanNeedDTO updateMaterialPlanNeedDTO = MaterialPlanNeedConvertor.INSTANCE.vo2Dto(materialPlanNeedVO);
                            updateMaterialPlanNeedDTO.setNeedQuantity(materialPlanNeedDTO.getNeedQuantity());
                            updateMaterialPlanNeedDTO.setPublishStatus(PlanNeedPublishStatusEnum.CHANGED.getCode());
                            updateMaterialPlanNeedDTO.setExpectedArrivalTime(updateMaterialPlanNeedDTO.getNeedDate());
                            updateMaterialPlanNeedDTO.setExpectedArrivalQuantity(updateMaterialPlanNeedDTO.getNeedQuantity());
                            // 获取材料与供应商数据
                            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMap.get(materialPlanNeedVO.getProductCode());
                            updateMaterialPlanNeedDTO.setRequestCargoPlanLockDay(materialSupplierPurchaseVO.getRequestCargoPlanLockDay());
                            updateList.add(updateMaterialPlanNeedDTO);
                        }
                    } else {
                        insertList.add(materialPlanNeedDTO);
                    }
                }
            } else {
                insertList = insertMaterialPlanNeedDTOList;
            }

            if (CollectionUtils.isNotEmpty(insertList)) {
                insertList.forEach(item -> item.setPublishStatus(PlanNeedPublishStatusEnum.UN_PUBLISHED.getCode()));
                addNeedIdList.addAll(insertList.stream().map(MaterialPlanNeedDTO::getId).collect(Collectors.toList()));
                this.doCreateBatch(insertList);
            }

            if (CollectionUtils.isNotEmpty(updateList)) {
                addNeedIdList.addAll(updateList.stream().map(MaterialPlanNeedDTO::getId).collect(Collectors.toList()));
                this.doUpdateBatch(updateList);
            }
        }
    }

    @Override
    public void issueJob() {
        // 每次获取 1 天
        Date startModifyTime = DateUtils.moveDay(new Date(), -1);
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS =
                materialArrivalTrackingService.selectVOByParams(ImmutableMap.of(
                        "startModifyTime", startModifyTime,
                        "dataSource", ArrivalTrackingDataSourceEnum.PURCHASING_PLAN.getCode()));
        if (CollectionUtils.isNotEmpty(materialArrivalTrackingVOS)) {
            List<String> sourceIds = materialArrivalTrackingVOS.stream()
                    .map(MaterialArrivalTrackingVO::getSourceId).collect(Collectors.toList());

            List<MaterialPlanNeedVO> materialPlanNeedVOList = this.selectByParams(ImmutableMap.of("ids", sourceIds));

            if (CollectionUtils.isNotEmpty(materialPlanNeedVOList)) {
                // 调用下发接口
                this.issueData(materialPlanNeedVOList.stream().map(BaseVO::getId).collect(Collectors.toList()), true);
            }
        }
    }

    @Override
    public BaseResponse<Void> cancelIssue(List<String> ids) {

        if (CollectionUtils.isEmpty(ids)) throw new BusinessException("取消下发数据不可为空");

        List<MaterialPlanNeedVO> materialPlanNeedVOList = this.selectByVOParams(new HashMap<>());

        // 不在此次MRP发布的数据中，并且没有PO、数据源是要货预测
        List<MaterialPlanNeedVO> fileList = materialPlanNeedVOList.stream()
                .filter(data -> !ids.contains(data.getId()))
                .filter(data -> null == data.getPurchaseOrderCode() && null == data.getPurchaseOrderLineCode())
                .filter(data -> data.getDataSource().equals(ArrivalTrackingDataSourceEnum.PURCHASING_FORECAST.getCode()))
                .filter(data -> !data.getPublishStatus().equals(PlanNeedPublishStatusEnum.CANCEL.getCode()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(fileList)) throw new BusinessException("没有满足的取消下发数据");

        List<String> needIdList = fileList.stream().map(BaseVO::getId).collect(Collectors.toList());

        // 取消要货计划数据
        List<MaterialPlanNeedDTO> updateNeedList = fileList.stream().map(data -> {
            MaterialPlanNeedDTO materialPlanNeedDTO = new MaterialPlanNeedDTO();
            materialPlanNeedDTO.setId(data.getId());
            materialPlanNeedDTO.setPublishStatus(PlanNeedPublishStatusEnum.CANCEL.getCode());
            return materialPlanNeedDTO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateNeedList)) {
            Lists.partition(updateNeedList, 500).forEach(this::updateBatchSelective);
        }

        // 取消到货跟踪数据
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = materialArrivalTrackingService.selectByParams(ImmutableMap.of("sourceIds", needIdList));
        List<MaterialArrivalTrackingDTO> updateTrackingList = materialArrivalTrackingVOList.stream().map(data -> {
            MaterialArrivalTrackingDTO materialArrivalTrackingDTO = new MaterialArrivalTrackingDTO();
            materialArrivalTrackingDTO.setId(data.getId());
            materialArrivalTrackingDTO.setArrivalStatus(ArrivalStatusEnum.CANCEL.getCode());
            return materialArrivalTrackingDTO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateTrackingList)) {
            Lists.partition(updateTrackingList, 500).forEach(materialArrivalTrackingService::doUpdateBatchSelective);
        }

        try {
            // 填充下发到货数据
            MaterialPlanNeedCancelIssueDTO materialPlanNeedCancelIssueDTO = fillMaterialPlanNeedCancelIssueDTO(fileList);

            // 填充参数调用dcp服务下发计划员确认
            HashMap<String, Object> params = new HashMap<>();
            params.put("Service", materialPlanNeedCancelIssueDTO.getService());
            BaseResponse<String> response = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_PRODUCT_NEED_CANCEL_ISSUE.getCode(), params);
            log.info("取消下发的数据为{}", materialPlanNeedCancelIssueDTO);
            if (response.getSuccess().equals(false)) {
                throw new BusinessException("服务接口调用失败");
            }

            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("取消下发失败", e);
            throw new BusinessException("取消下发失败,{0}", e.getLocalizedMessage());
        }
    }

    @Override
    public BaseResponse<Void> cancelIssue02(List<String> ids) {

        if (CollectionUtils.isEmpty(ids)) throw new BusinessException("取消下发数据不可为空");

        List<MaterialPlanNeedVO> materialPlanNeedVOList = this.selectByVOParams(ImmutableMap.of("ids", ids));

        // 取消要货计划数据
        List<MaterialPlanNeedDTO> updateNeedList = ids.stream().map(data -> {
            MaterialPlanNeedDTO materialPlanNeedDTO = new MaterialPlanNeedDTO();
            materialPlanNeedDTO.setId(data);
            materialPlanNeedDTO.setPublishStatus(PlanNeedPublishStatusEnum.CANCEL.getCode());
            return materialPlanNeedDTO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateNeedList)) {
            Lists.partition(updateNeedList, 500).forEach(this::updateBatchSelective);
        }

        // 取消到货跟踪数据
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = materialArrivalTrackingService.selectByParams(ImmutableMap.of("sourceIds", ids));
        List<MaterialArrivalTrackingDTO> updateTrackingList = materialArrivalTrackingVOList.stream().map(data -> {
            MaterialArrivalTrackingDTO materialArrivalTrackingDTO = new MaterialArrivalTrackingDTO();
            materialArrivalTrackingDTO.setId(data.getId());
            materialArrivalTrackingDTO.setArrivalStatus(ArrivalStatusEnum.CANCEL.getCode());
            return materialArrivalTrackingDTO;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(updateTrackingList)) {
            Lists.partition(updateTrackingList, 500).forEach(materialArrivalTrackingService::doUpdateBatchSelective);
        }

        try {
            // 填充下发到货数据
            MaterialPlanNeedCancelIssueDTO materialPlanNeedCancelIssueDTO = fillMaterialPlanNeedCancelIssueDTO(materialPlanNeedVOList);

            // 填充参数调用dcp服务下发计划员确认
            HashMap<String, Object> params = new HashMap<>();
            params.put("Service", materialPlanNeedCancelIssueDTO.getService());
            BaseResponse<String> response = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_PRODUCT_NEED_CANCEL_ISSUE.getCode(), params);
            log.info("取消下发的数据为{}", materialPlanNeedCancelIssueDTO);
            if (response.getSuccess().equals(false)) {
                throw new BusinessException("服务接口调用失败");
            }

            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("取消下发失败", e);
            throw new BusinessException("取消下发失败,{0}", e.getLocalizedMessage());
        }
    }


    private MaterialPlanNeedDTO getMergeMaterialPlanNeedDTO(Date inventoryDate, BigDecimal needQuantitySum, SupplierVO supplierVO, int orderPlacementLeadTimeDay) {
        MaterialPlanNeedDTO mergeMaterialPlanNeedDTO = new MaterialPlanNeedDTO();
        mergeMaterialPlanNeedDTO.setNeedQuantity(needQuantitySum);
        mergeMaterialPlanNeedDTO.setNeedDate(inventoryDate);
        mergeMaterialPlanNeedDTO.setSupplierCode(supplierVO.getSupplierCode());
        mergeMaterialPlanNeedDTO.setSupplierName(supplierVO.getSupplierName());
        mergeMaterialPlanNeedDTO.setRequirementReleaseDate(DateUtils.moveDay(inventoryDate, -orderPlacementLeadTimeDay));
        mergeMaterialPlanNeedDTO.setId(UUIDUtil.getUUID());
        // 默认承诺到货时间 和 数量，为要货日期 和 数量
        mergeMaterialPlanNeedDTO.setExpectedArrivalTime(inventoryDate);
        mergeMaterialPlanNeedDTO.setExpectedArrivalQuantity(needQuantitySum);
        return mergeMaterialPlanNeedDTO;
    }

}
