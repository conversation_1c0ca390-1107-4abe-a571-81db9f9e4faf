package com.yhl.scp.mrp.originalFilm.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultDetailPO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>OriginalFilmDemandConsultDetailDao</code>
 * <p>
 * 原片需求征询明细DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 10:05:38
 */
public interface OriginalFilmDemandConsultDetailDao extends BaseDao<OriginalFilmDemandConsultDetailPO, OriginalFilmDemandConsultDetailVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link OriginalFilmDemandConsultDetailVO}
     */
    List<OriginalFilmDemandConsultDetailVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 通过原片需求汇总ids删除原片需求汇总明细数据
     * @param consultSummaryIds
     */
	void deleteByConsultSummaryIds(@Param("consultSummaryIds")List<String> consultSummaryIds);

	List<OriginalFilmDemandConsultDetailVO> selectByDemandSourceIds(@Param("consultSummaryIds") List<String> consultSummaryIds);

}
