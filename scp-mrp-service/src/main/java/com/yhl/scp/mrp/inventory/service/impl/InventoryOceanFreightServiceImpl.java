package com.yhl.scp.mrp.inventory.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPoCreate;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.scss.ScssInventoryOceanFreight;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.supplier.vo.SupplierAddressVO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.freestorage.infrastructure.dao.ProFreeStorageDao;
import com.yhl.scp.mrp.freestorage.vo.ProFreeStorageVO;
import com.yhl.scp.mrp.inventory.convertor.InventoryOceanFreightConvertor;
import com.yhl.scp.mrp.inventory.domain.entity.InventoryOceanFreightDO;
import com.yhl.scp.mrp.inventory.domain.service.InventoryOceanFreightDomainService;
import com.yhl.scp.mrp.inventory.dto.InventoryFloatGlassShippedDetailDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryOceanFreightDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryOceanFreightPlacesDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryQuayDetailDTO;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryFloatGlassShippedDetailDao;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryOceanFreightDao;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryOceanFreightPlacesDao;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryQuayDetailDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPO;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPlacesPO;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryQuayDetailPO;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassShippedDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryOceanFreightPlacesService;
import com.yhl.scp.mrp.inventory.service.InventoryOceanFreightService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightShippedMapVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>InventoryOceanFreightServiceImpl</code>
 * <p>
 * 浮法海运_提单号应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04 10:52:16
 */
@Slf4j
@Service
public class InventoryOceanFreightServiceImpl extends AbstractService implements InventoryOceanFreightService {

    Calendar calendar = Calendar.getInstance();
    @Resource
    private InventoryOceanFreightDao inventoryOceanFreightDao;
    @Resource
    private InventoryOceanFreightDomainService inventoryOceanFreightDomainService;
    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private InventoryOceanFreightPlacesDao inventoryOceanFreightPlacesDao;
    @Resource
    private InventoryOceanFreightPlacesService inventoryOceanFreightPlacesService;
    @Resource
    private InventoryFloatGlassShippedDetailDao inventoryFloatGlassShippedDetailDao;
    @Resource
    private InventoryQuayDetailDao inventoryQuayDetailsDao;
    @Resource
    private InventoryQuayDetailService inventoryQuayDetailService;
    @Resource
    private InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;
    @Resource
    private MrpFeign mrpFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private NewMdsFeign mdsFeign;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private InventoryOceanFreightService inventoryOceanFreightService;
    @Resource
    private ProFreeStorageDao proFreeStorageDao;


    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(InventoryOceanFreightDTO inventoryOceanFreightDTO) {
        // 0.数据转换
        InventoryOceanFreightDO inventoryOceanFreightDO = InventoryOceanFreightConvertor.INSTANCE.dto2Do(inventoryOceanFreightDTO);
        InventoryOceanFreightPO inventoryOceanFreightPO = InventoryOceanFreightConvertor.INSTANCE.dto2Po(inventoryOceanFreightDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryOceanFreightDomainService.validation(inventoryOceanFreightDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(inventoryOceanFreightPO);
        inventoryOceanFreightDao.insert(inventoryOceanFreightPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(InventoryOceanFreightDTO inventoryOceanFreightDTO) {
        // 0.数据转换
        InventoryOceanFreightDO inventoryOceanFreightDO = InventoryOceanFreightConvertor.INSTANCE.dto2Do(inventoryOceanFreightDTO);
        InventoryOceanFreightPO inventoryOceanFreightPO = InventoryOceanFreightConvertor.INSTANCE.dto2Po(inventoryOceanFreightDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryOceanFreightDomainService.validation(inventoryOceanFreightDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(inventoryOceanFreightPO);
        inventoryOceanFreightDao.update(inventoryOceanFreightPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<InventoryOceanFreightDTO> list) {
        List<InventoryOceanFreightPO> newList = InventoryOceanFreightConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        inventoryOceanFreightDao.insertBatch(newList);
    }

    private void doCreateBatchWithPrimaryKey(List<InventoryOceanFreightDTO> list) {
        List<InventoryOceanFreightPO> newList = InventoryOceanFreightConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        inventoryOceanFreightDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<InventoryOceanFreightDTO> list) {
        List<InventoryOceanFreightPO> newList = InventoryOceanFreightConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        inventoryOceanFreightDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return inventoryOceanFreightDao.deleteBatch(idList);
        }
        return inventoryOceanFreightDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public InventoryOceanFreightVO selectByPrimaryKey(String id) {
        InventoryOceanFreightPO po = inventoryOceanFreightDao.selectByPrimaryKey(id);
        return InventoryOceanFreightConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "INVENTORY_OCEAN_FREIGHT")
    public List<InventoryOceanFreightVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "INVENTORY_OCEAN_FREIGHT")
    public List<InventoryOceanFreightVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<InventoryOceanFreightVO> dataList = inventoryOceanFreightDao.selectByCondition(sortParam, queryCriteriaParam);
        InventoryOceanFreightServiceImpl target = springBeanUtils.getBean(InventoryOceanFreightServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<InventoryOceanFreightVO> selectByParams(Map<String, Object> params) {
        List<InventoryOceanFreightPO> list = inventoryOceanFreightDao.selectByParams(params);
        return InventoryOceanFreightConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<InventoryOceanFreightVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> handleInventoryOceanFreight(List<InventoryOceanFreightShippedMapVO> o) {
        if (CollectionUtils.isEmpty(o)) {
            return BaseResponse.success();
        }
        List<InventoryOceanFreightShippedMapVO> onlyList = o.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                vo -> {
                                    ScssInventoryOceanFreight scss = vo.getScssInventoryOceanFreight();
                                    return scss.getBillNo() + "_" + scss.getContainerNo() + "_" + scss.getCarrier().getCode();
                                },
                                Function.identity(),
                                (existing, replacement) -> existing // 如果遇到重复的键，保留第一个出现的元素
                        ),
                        map -> new ArrayList<>(map.values())
                ));

        List<InventoryOceanFreightDTO> insertDtoS = new ArrayList<>();
        List<InventoryOceanFreightDTO> updateDtoS = new ArrayList<>();
        List<InventoryFloatGlassShippedDetailDTO> inventoryFloatGlassShippedDtoS = new ArrayList<>();
        Map<String, InventoryOceanFreightDTO> oldDTOOceanFreight = new HashMap<>();
        Map<String, InventoryOceanFreightPlacesDTO> oldDTOOceanFreightPlacesMap = new HashMap<>();
        List<InventoryOceanFreightPO> oldPos = inventoryOceanFreightDao.selectByOceanFreightIds(onlyList);

        Map<String, InventoryOceanFreightPO> oceanFreightPOMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getBlNum() + "_" + t.getBoxNum() + "_" + t.getCarrierCode(),
                        Function.identity(), (v1, v2) -> v1));

        //插入子表
        List<InventoryOceanFreightPlacesDTO> insertPlacesDtoS = new ArrayList<>();
        List<InventoryOceanFreightPlacesDTO> updatePlacesDtoS = new ArrayList<>();
        Set<String> blIds = oldPos.stream().map(InventoryOceanFreightPO::getId).collect(Collectors.toSet());
        HashMap<String, Object> mapInventoryOceanFreightPlaces = MapUtil.newHashMap();
        mapInventoryOceanFreightPlaces.put("blIds", blIds);
        List<InventoryOceanFreightPlacesPO> oldPlacesPos = inventoryOceanFreightPlacesDao.selectByParams(mapInventoryOceanFreightPlaces);
        Map<String, InventoryOceanFreightPlacesPO> oceanFreightPlacesPOMap = CollectionUtils.isEmpty(oldPlacesPos) ?
                MapUtil.newHashMap() :
                oldPlacesPos.stream().collect(Collectors.toMap(t -> t.getPlacesType() + "_" + t.getAddressCode() + "_" + t.getBlId(),
                        Function.identity(), (v1, v2) -> v1));
        //插入港口表
        List<InventoryQuayDetailPO> inventoryQuayDetailPOS = inventoryQuayDetailsDao.selectByInventoryQuayDetailIds(o);
        Map<String, InventoryQuayDetailPO> inventoryQuayDetailMap = CollectionUtils.isEmpty(inventoryQuayDetailPOS) ?
                MapUtil.newHashMap() :
                inventoryQuayDetailPOS.stream().collect(Collectors.toMap(t -> t.getLotNumber() + "_" + t.getPlanNumber() + "_" + t.getLineId(),
                        Function.identity(), (v1, v2) -> v1));
        HashMap<String, Object> freeStorageMap = MapUtil.newHashMap(3);
        freeStorageMap.put("enabled", YesOrNoEnum.YES.getCode());
        List<ProFreeStorageVO> proFreeStorageVOS = proFreeStorageDao.selectVOByParams(freeStorageMap);
        Map<String, ProFreeStorageVO> proFreeStorageVOMap = CollectionUtils.isEmpty(proFreeStorageVOS) ?
                MapUtil.newHashMap() :
                proFreeStorageVOS.stream().collect(Collectors.toMap(t -> t.getCompanyCode() + "_" + t.getPortName() + "_" + t.getCarrierCode(),
                        Function.identity(), (v1, v2) -> v1));

        List<InventoryQuayDetailDTO> insertQuayDetailDtoS = new ArrayList<>();
        List<InventoryQuayDetailDTO> updateQuayDetailDtoS = new ArrayList<>();

        for (InventoryOceanFreightShippedMapVO mapVO : o) {
            ScssInventoryOceanFreight scssInventoryOceanFreight = mapVO.getScssInventoryOceanFreight();
            InventoryFloatGlassShippedDetailVO detailVO = mapVO.getDetailVO();
            InventoryOceanFreightDTO dto = new InventoryOceanFreightDTO();
            String id =
                    scssInventoryOceanFreight.getBillNo() + "_" + scssInventoryOceanFreight.getContainerNo() + "_" + scssInventoryOceanFreight.getCarrier().getCode();


            if (oceanFreightPOMap.containsKey(id)) {
                InventoryOceanFreightPO oldPo = oceanFreightPOMap.get(id);
                BeanUtils.copyProperties(oldPo, dto);
                generateDto(dto, scssInventoryOceanFreight, detailVO,
                        inventoryFloatGlassShippedDtoS, oceanFreightPlacesPOMap, insertPlacesDtoS, updatePlacesDtoS, inventoryQuayDetailMap
                        , insertQuayDetailDtoS, updateQuayDetailDtoS, proFreeStorageVOMap, oldDTOOceanFreightPlacesMap);
                if (!oldDTOOceanFreight.containsKey(id)) {
                    oldDTOOceanFreight.put(id, dto);
                    updateDtoS.add(dto);
                }

            } else {
                dto.setId(UUIDUtil.getUUID());
                generateDto(dto, scssInventoryOceanFreight, detailVO,
                        inventoryFloatGlassShippedDtoS, oceanFreightPlacesPOMap, insertPlacesDtoS, updatePlacesDtoS, inventoryQuayDetailMap
                        , insertQuayDetailDtoS, updateQuayDetailDtoS, proFreeStorageVOMap, oldDTOOceanFreightPlacesMap);
                if (!oldDTOOceanFreight.containsKey(id)) {
                    oldDTOOceanFreight.put(id, dto);
                    insertDtoS.add(dto);
                }
            }

        }

        quayDetailDTO(insertQuayDetailDtoS, updateQuayDetailDtoS);
        Executor executor = Executors.newFixedThreadPool(5); // 根据需求调整线程数
        CompletableFuture.runAsync(() -> {
            try {
                if (CollectionUtils.isNotEmpty(inventoryFloatGlassShippedDtoS)) {
                    inventoryFloatGlassShippedDetailService.doUpdateBatch(inventoryFloatGlassShippedDtoS);
                }
                if (CollectionUtils.isNotEmpty(insertDtoS)) {
                    doCreateBatchWithPrimaryKey(insertDtoS);
                }
                if (CollectionUtils.isNotEmpty(updateDtoS)) {
                    doUpdateBatch(updateDtoS);
                }

                if (CollectionUtils.isNotEmpty(insertPlacesDtoS)) {
                    inventoryOceanFreightPlacesService.doCreateBatch(insertPlacesDtoS);
                }
                if (CollectionUtils.isNotEmpty(updatePlacesDtoS)) {
                    inventoryOceanFreightPlacesService.doUpdateBatch(updatePlacesDtoS);
                }
            } catch (Exception e) {
                log.error("海运中间表存储失败", e);
            }
        }, executor);
        return BaseResponse.success();
    }

    private void quayDetailDTO(List<InventoryQuayDetailDTO> insertQuayDetailDtoS, List<InventoryQuayDetailDTO> updateQuayDetailDtoS) {
        if (CollectionUtils.isNotEmpty(insertQuayDetailDtoS)) {
            inventoryQuayDetailService.doCreateBatch(insertQuayDetailDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateQuayDetailDtoS)) {
            inventoryQuayDetailService.doUpdateBatch(updateQuayDetailDtoS);
        }
    }

    @Override
    public BaseResponse<Void> syncInventoryOceanFreight(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        HashMap<String, Object> map = MapUtil.newHashMap(3);
        map.put("enabled", YesOrNoEnum.YES.getCode());
        List<InventoryFloatGlassShippedDetailVO> inventoryFloatGlassShippedDetailPOS =
                inventoryFloatGlassShippedDetailDao.selectVOByParams(map);
        List<InventoryFloatGlassShippedDetailVO> filteredList = inventoryFloatGlassShippedDetailPOS.stream()
                .filter(po -> po.getActualArrivalTime() == null)
                .filter(po -> "出口海运".equals(po.getDeliveryMethod()) || "国内船运".equals(po.getDeliveryMethod()) || "集装箱直运".equals(po.getDeliveryMethod()))
                .collect(Collectors.toList());
        String filteredListJson = JSON.toJSONString(filteredList);
        params.put("list", filteredListJson);
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.SCSS.getCode(),
                ApiCategoryEnum.INVENT_ORY_OCEAN_FREIGHT.getCode(), params);
        return BaseResponse.success("同步成功");
    }


    @Override
    public BaseResponse<Void> handlePoCreate(String scenario, List<ErpPoCreate> o) {
        if (CollectionUtils.isEmpty(o)) {
            log.error("接口返回PO创建为空");
            return BaseResponse.error("接口返回PO创建为空");
        }
        log.info("po创建返回数据大小{}", o.size());
        List<String> ids = o.stream()
                .map(ErpPoCreate::getBpimId)
                .distinct()
                .collect(Collectors.toList());
        List<InventoryQuayDetailVO> inventoryQuayDetailVOList =
                inventoryQuayDetailsDao.selectVOByParams(ImmutableMap.of("ids", ids));

        Map<String, ErpPoCreate> erpPoCreateMap = o.stream()
                .collect(Collectors.toMap(ErpPoCreate::getBpimId, Function.identity(),
                        (value1, value2) -> value1));

        List<InventoryQuayDetailDTO> updateList = inventoryQuayDetailVOList.stream()
                .map(e -> {
                    ErpPoCreate erpPoCreate = erpPoCreateMap.get(e.getId());
                    if (erpPoCreate == null) {
                        return null;
                    }

                    return InventoryQuayDetailDTO.builder()
                            .id(erpPoCreate.getBpimId())
                            .po(erpPoCreate.getPoNo())
                            .poNumber(erpPoCreate.getLines().get(0).getLineNum())
                            .build();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        BasePOUtils.updateBatchFiller(updateList);

        if (CollectionUtils.isNotEmpty(updateList)) {
            Lists.partition(updateList, 1000).forEach(list -> inventoryQuayDetailService.doUpdateBatchSelective(list));
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> syncPoCreate(String tenantId) {
        Map<String, Object> map = MapUtil.newHashMap(3);
        map.put("type", ObjectTypeEnum.INVENTORY_OCEAN_FREIGHT.getCode());
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.PO_CREATE.getCode(), map);
        return BaseResponse.success("同步成功");
    }

    @Override
    public String syncAutoCreatPo(String tenantId, Map<String, Object> params) {
        if (Boolean.TRUE.equals(redisUtil.hasKey(RedisKeyManageEnum.INVENTORY_OCEAN_FREIGHT_ISSUE.getKey()))) {
            throw new BusinessException("浮法已发运正在创建PO，请稍后操作");
        }
        List<InventoryQuayDetailPO> chooseIssueList = inventoryQuayDetailsDao.selectByParams(params);
        chooseIssueList = chooseIssueList.stream()
                .filter(item -> item.getPo() == null && item.getContainerDeliveryTime() != null)
                .collect(Collectors.toList());
        return methodCreatPo(chooseIssueList, tenantId);
    }

    @Override
    public String doBatchCreatPo(List<String> ids) {
        if (Boolean.TRUE.equals(redisUtil.hasKey(RedisKeyManageEnum.INVENTORY_OCEAN_FREIGHT_ISSUE.getKey()))) {
            throw new BusinessException("浮法已发运正在创建PO，请稍后操作");
        }
        List<InventoryQuayDetailPO> chooseIssueList = inventoryQuayDetailsDao.selectByPrimaryKeys(ids);
        List<InventoryQuayDetailPO> chooseIssueErrorList = chooseIssueList.stream()
                .filter(item -> item.getPo() != null || item.getContainerDeliveryTime() == null)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(chooseIssueErrorList)) {
            throw new BusinessException("勾选的码头数据需要同时满足不存在PO且是送货时间不为空");
        }


        return methodCreatPo(chooseIssueList, SystemHolder.getTenantCode());
    }

    private String methodCreatPo(List<InventoryQuayDetailPO> chooseIssueList, String tenantId) {
        if (CollectionUtils.isEmpty(chooseIssueList))
            return null;
        // 锁定5分钟
        List<String> unIssuedIds = chooseIssueList.stream().map(InventoryQuayDetailPO::getId)
                .collect(Collectors.toList());
        log.info("当前id{},getKey{},join{}", JSONObject.toJSONString(unIssuedIds),
                RedisKeyManageEnum.INVENTORY_OCEAN_FREIGHT_ISSUE.getKey(),String.join(",", unIssuedIds));
        redisUtil.set(RedisKeyManageEnum.INVENTORY_OCEAN_FREIGHT_ISSUE.getKey(), String.join(",", unIssuedIds), 300);
        log.info("redis当前值{}", redisUtil.get(RedisKeyManageEnum.INVENTORY_OCEAN_FREIGHT_ISSUE.getKey()).toString());
        redisUtil.set(RedisKeyManageEnum.INVENTORY_OCEAN_FREIGHT_USER.getKey(), SystemHolder.getUserId(), 300);
        try {
            // 已发运创建PO数据
            List<ErpPoCreate> erpPoCreateList = new ArrayList<>();
            String poErrorMessage = initErpPoCreateList(chooseIssueList, erpPoCreateList);
            if (StringUtils.isNotEmpty(poErrorMessage)) {
                poErrorMessage = ",数据问题:" + poErrorMessage;
            }else {
                poErrorMessage = "";
            }
            String filteredListJson = JSON.toJSONString(erpPoCreateList);
            // 已发运创建PO数据ERP
            Map<String, Object> params = MapUtil.newHashMap();
            params.put("type", ObjectTypeEnum.INVENTORY_OCEAN_FREIGHT.getCode());
            params.put("createList", filteredListJson);
            log.info("po创建参数为{}", JSON.toJSONString(params));
            BaseResponse<String> baseResponse = newDcpFeign.callExternalApi(tenantId,
                    ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.PO_CREATE.getCode(), params);
            log.info("po创建响应：" + JSONObject.toJSONString(baseResponse));
            // 结果解析
            if (Boolean.TRUE.equals(baseResponse.getSuccess()) && baseResponse.getData() != null) {
                // 创建PR成功后，更新状态
                String data = baseResponse.getData();
                log.info("po创建,获取接口日志请求:{}", data);
                ExtApiLogVO extApiLog = newDcpFeign.getExtApiLog(data);
                log.info("po创建,获取接口日志响应:{}", JSONObject.toJSONString(extApiLog));
                Integer resolveCount = extApiLog.getResolveCount();
                Integer applyCount = extApiLog.getApplyCount();
                String remark = extApiLog.getRemark();
                if (StringUtils.isEmpty(remark)) {
                    remark = "";
                }
                log.info("po创建, applyCount数据值：{}", applyCount);
                log.info("po创建, remark数据值：{}", remark);
                String result = "共%d条数据，成功%d条，失败%d条";
                result = String.format(result, resolveCount, applyCount, resolveCount - (applyCount == null ? 0 : applyCount));
                if (applyCount != resolveCount) {
                    result = result + remark;
                }
                return result + remark + poErrorMessage;
            } else {
                log.error("po创建下发报错，请查看最新对外接口日志");
                throw new BusinessException("po创建下发错误:" + baseResponse.getMsg()+poErrorMessage);
            }
        } catch (Exception e) {
            log.error("po创建下发报错", e);
            throw new BusinessException("po创建下发报错：" + e.getMessage());
        } finally {
            redisUtil.delete(RedisKeyManageEnum.INVENTORY_OCEAN_FREIGHT_ISSUE.getKey());
            redisUtil.delete(RedisKeyManageEnum.INVENTORY_OCEAN_FREIGHT_USER.getKey());

        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.INVENTORY_OCEAN_FREIGHT.getCode();
    }

    @Override
    public List<InventoryOceanFreightVO> invocation(List<InventoryOceanFreightVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    private void generateDto(InventoryOceanFreightDTO dto, ScssInventoryOceanFreight scssInventoryOceanFreight
            , InventoryFloatGlassShippedDetailVO detailVO, List<InventoryFloatGlassShippedDetailDTO> inventoryFloatGlassShippedDtoS,
                             Map<String, InventoryOceanFreightPlacesPO> oceanFreightPlacesPOMap,
                             List<InventoryOceanFreightPlacesDTO> insertPlacesDtoS,
                             List<InventoryOceanFreightPlacesDTO> updatePlacesDtoS,
                             Map<String, InventoryQuayDetailPO> inventoryQuayDetailMap, List<InventoryQuayDetailDTO> insertQuayDetailDtoS,
                             List<InventoryQuayDetailDTO> updateQuayDetailDtoS,
                             Map<String, ProFreeStorageVO> proFreeStorageVOMap, Map<String, InventoryOceanFreightPlacesDTO> oldDTOOceanFreightPlacesMap) {
        dto.setBlNum(scssInventoryOceanFreight.getBillNo());
        dto.setBoxNum(scssInventoryOceanFreight.getContainerNo());
        dto.setCarrierCode(scssInventoryOceanFreight.getCarrier().getCode());
        dto.setEndTime(scssInventoryOceanFreight.getEndTime());
        for (ScssInventoryOceanFreight.Places places : scssInventoryOceanFreight.getPlaces()) {
            String id = places.getType() + "_" + places.getCode() + "_" + dto.getId();
            InventoryOceanFreightPlacesDTO placesDTO = new InventoryOceanFreightPlacesDTO();
            if (oceanFreightPlacesPOMap.containsKey(id)) {
                InventoryOceanFreightPlacesPO oldPo = oceanFreightPlacesPOMap.get(id);
                BeanUtils.copyProperties(oldPo, placesDTO);
                generatePlacesDto(dto, detailVO, inventoryFloatGlassShippedDtoS, places, placesDTO,
                        inventoryQuayDetailMap, insertQuayDetailDtoS, updateQuayDetailDtoS, proFreeStorageVOMap);
                if (!oldDTOOceanFreightPlacesMap.containsKey(id)) {
                    oldDTOOceanFreightPlacesMap.put(id, placesDTO);
                    updatePlacesDtoS.add(placesDTO);
                }
            } else {
                generatePlacesDto(dto, detailVO, inventoryFloatGlassShippedDtoS, places, placesDTO,
                        inventoryQuayDetailMap, insertQuayDetailDtoS, updateQuayDetailDtoS, proFreeStorageVOMap);
                if (!oldDTOOceanFreightPlacesMap.containsKey(id)) {
                    oldDTOOceanFreightPlacesMap.put(id, placesDTO);
                    insertPlacesDtoS.add(placesDTO);
                }

            }
        }

    }

    private void generatePlacesDto(InventoryOceanFreightDTO dto, InventoryFloatGlassShippedDetailVO detailVO,
                                   List<InventoryFloatGlassShippedDetailDTO> inventoryFloatGlassShippedDtoS, ScssInventoryOceanFreight.Places places, InventoryOceanFreightPlacesDTO placesDTO, Map<String, InventoryQuayDetailPO> inventoryQuayDetailMap, List<InventoryQuayDetailDTO> insertQuayDetailDtoS, List<InventoryQuayDetailDTO> updateQuayDetailDtoS, Map<String, ProFreeStorageVO> proFreeStorageVOMap) {
        placesDTO.setBlId(dto.getId());
        String nameCN = places.getNameCN();
        if ("上海".equals(places.getNameCN())) {
            nameCN = "宝山";
        }
        placesDTO.setAddressName(nameCN);
        placesDTO.setAddressCode(places.getCode());
        placesDTO.setNameCn(nameCN);
        placesDTO.setNameOrigin(places.getNameOrigin());
        placesDTO.setPlacesType(places.getType());
        placesDTO.setLatitude(places.getLat());
        placesDTO.setLongitude(places.getLon());
        placesDTO.setVoyage(places.getPlan().getVoyage());
        placesDTO.setShippingHouse(places.getPlan().getVessel());
        placesDTO.setMmsi(places.getPlan().getMmsi());
        placesDTO.setEtdTime(places.getPlan().getEtd());
        placesDTO.setEtaTime(places.getPlan().getEta());
        placesDTO.setEtaLsTime(places.getPlan().getEta_ls());
        placesDTO.setAtaTime(places.getPlan().getAta());
        placesDTO.setAtdTime(places.getPlan().getAtd());
        placesDTO.setStartTime(places.getPlan().getStartTime());
        placesDTO.setEndTime(places.getPlan().getEndTime());
        InventoryFloatGlassShippedDetailDTO inventoryFloatGlassShippedDetailDTO =
                new InventoryFloatGlassShippedDetailDTO();
        BeanUtils.copyProperties(detailVO, inventoryFloatGlassShippedDetailDTO);

        if (placesDTO.getAtaTime() != null) {
            if (places.getType().equals("4")) {
                // 确保 detailVO.getDeliveryTime() 比 placesDTO.getAtaTime() 早
                if (detailVO.getDeliveryTime() != null && placesDTO.getAtaTime().after(detailVO.getDeliveryTime())) {
                    InventoryQuayDetailDTO quayDetailDTO = new InventoryQuayDetailDTO();
                    String id = detailVO.getLotNumber() + "_" + detailVO.getPlanNumber() + "_" + detailVO.getLineId();
                    if (inventoryQuayDetailMap.containsKey(id)) {
                        InventoryQuayDetailPO oldPo = inventoryQuayDetailMap.get(id);
                        BeanUtils.copyProperties(oldPo, quayDetailDTO);
                        generateQuayDetailDto(detailVO, placesDTO, quayDetailDTO, proFreeStorageVOMap);
                        updateQuayDetailDtoS.add(quayDetailDTO);
                    } else {
                        generateQuayDetailDto(detailVO, placesDTO, quayDetailDTO, proFreeStorageVOMap);
                        insertQuayDetailDtoS.add(quayDetailDTO);
                    }
                    if (Objects.nonNull(placesDTO.getAtaTime())) {
                        inventoryFloatGlassShippedDetailDTO.setEnabled(YesOrNoEnum.NO.getCode());
                        inventoryFloatGlassShippedDetailDTO.setActualArrivalTime(placesDTO.getAtaTime());
                        inventoryFloatGlassShippedDetailDTO.setPortName(placesDTO.getAddressName());
                        inventoryFloatGlassShippedDtoS.add(inventoryFloatGlassShippedDetailDTO);
                    }
                }
            }
        } else {
            if ("4".equals(places.getType())) {
                inventoryFloatGlassShippedDetailDTO.setEstimatedArrivalTime(placesDTO.getEtaTime());
                inventoryFloatGlassShippedDetailDTO.setPortName(placesDTO.getAddressName());
                inventoryFloatGlassShippedDtoS.add(inventoryFloatGlassShippedDetailDTO);
            }
        }
    }

    private void generateQuayDetailDto(InventoryFloatGlassShippedDetailVO detailVO,
                                       InventoryOceanFreightPlacesDTO placesDTO, InventoryQuayDetailDTO quayDetailDTO, Map<String, ProFreeStorageVO> proFreeStorageVOMap) {
        quayDetailDTO.setActualArrivalTime(placesDTO.getAtaTime());
        quayDetailDTO.setActualSentQuantity(detailVO.getActualSentQuantity());
        quayDetailDTO.setArea(detailVO.getArea());
        quayDetailDTO.setBox(detailVO.getBox());
        quayDetailDTO.setCarrier(detailVO.getCarrier());
        quayDetailDTO.setContainerNumber(detailVO.getContainerNumber());
        quayDetailDTO.setCuttingRate(detailVO.getCuttingRate());
        quayDetailDTO.setLotLevelCode(detailVO.getLotLevelCode());
        quayDetailDTO.setLotNumber(detailVO.getLotNumber());
        quayDetailDTO.setPackageType(detailVO.getPackageType());
        quayDetailDTO.setPerBox(detailVO.getPerBox());
        quayDetailDTO.setProductCode(detailVO.getProductCode());
        quayDetailDTO.setProductId(detailVO.getProductId());
        quayDetailDTO.setProductSpec(detailVO.getProductSpec());
        quayDetailDTO.setStockPointCode(detailVO.getStockPointCode());
        quayDetailDTO.setWeight(detailVO.getWeight());
        quayDetailDTO.setEnabled(detailVO.getEnabled());
        quayDetailDTO.setLineId(detailVO.getLineId());
        quayDetailDTO.setPlanNumber(detailVO.getPlanNumber());
        quayDetailDTO.setPo(detailVO.getPo());
        quayDetailDTO.setManufacturer(detailVO.getManufacturer());
        quayDetailDTO.setOrderPrice(detailVO.getOrderPrice());
        quayDetailDTO.setPortName(placesDTO.getAddressName());
        quayDetailDTO.setBillNo(detailVO.getBillNo());
        quayDetailDTO.setPo(detailVO.getPo());
        quayDetailDTO.setPoNumber(detailVO.getPoNumber());
        quayDetailDTO.setStorageFlag(YesOrNoEnum.NO.getCode());
        quayDetailDTO.setDeliveryTime(detailVO.getDeliveryTime());
        String portName = detailVO.getPortName();
        Date overdueTime = null;
        String freeId = quayDetailDTO.getStockPointCode() + "_" + portName + "_" + detailVO.getCarrier();
        if (proFreeStorageVOMap.containsKey(freeId)) {
            ProFreeStorageVO proFreeStorageVO = proFreeStorageVOMap.get(freeId);
            int freeStorageDays = Integer.parseInt(proFreeStorageVO.getFreeStorage());

            calendar.setTime(quayDetailDTO.getActualArrivalTime());
            // 加上 freeStorage 天数，再减去 1 天
            calendar.add(Calendar.DAY_OF_MONTH, freeStorageDays - 1);
            overdueTime = calendar.getTime();
        }
        quayDetailDTO.setOverdueTime(overdueTime);
    }

    private String initErpPoCreateList(List<InventoryQuayDetailPO> chooseIssueList,
                                       List<ErpPoCreate> erpPoCreateList) {
        List<String> stockPointCodes = chooseIssueList.stream().map(InventoryQuayDetailPO::getStockPointCode)
                .distinct().collect(Collectors.toList());
        List<String> productCodes = chooseIssueList.stream()
                .map(item -> item.getProductCode())
                .distinct()
                .collect(Collectors.toList());
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        List<NewStockPointVO> stockPointList = newMdsFeign.selectStockPointByParams(scenario.getData(),
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "stockPointCodes", stockPointCodes));
        List<String> supplierIds = stockPointList.stream()
                .map(item -> item.getSupplierId())
                .distinct()
                .collect(Collectors.toList());
        Map<String, NewStockPointVO> stockPointMap = stockPointList.stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(),
                        (value1, value2) -> value1));

        List<NewStockPointVO> stockPointVOList =
                newMdsFeign.selectStockPointByParams(scenario.getData(),
                        ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), "organizeType", StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode()));
        Map<String, NewStockPointVO> stockPointVOListMap = stockPointVOList.stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity(),
                        (value1, value2) -> value1));
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario.getData(), StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode(), "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        NewStockPointVO newStockPointVO = stockPointVOListMap.get(rangeData);
        String orgId = newStockPointVO.getOrganizeId();

        Map<String, NewProductStockPointVO> productCodeMap = getProductMapByCodes(productCodes, scenario.getData(),
                newStockPointVO.getStockPointCode());
        Map<String, SupplierAddressVO> supplierAddressMap = getSupplierAddressMapByCodes(supplierIds, scenario.getData());
        Map<String, User> userCodeMap = getUserMapByCodes();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        String errorMessage = chooseIssueList.stream()
                .map(e -> {
                    ErpPoCreate erpPoCreate = new ErpPoCreate();
                    if (!stockPointMap.containsKey(e.getStockPointCode())) {
                        return e.getProductCode() + "未找到对应公司";
                    }
                    NewStockPointVO newStockPointVO1 = stockPointMap.get(e.getStockPointCode());
                    if (!productCodeMap.containsKey(e.getProductCode())) {
                        return e.getProductCode() + "该编码未在物料表中存在";
                    }
                    NewProductStockPointVO newProductStockPointVO = productCodeMap.get(e.getProductCode());
                    if (StringUtils.isEmpty(newProductStockPointVO.getPurchasePlanner())) {
                        return e.getProductCode() + "采购计划员为空";
                    }
                    if (!userCodeMap.containsKey(newProductStockPointVO.getPurchasePlanner())) {
                        return e.getProductCode() + "未找到对应采购计划员:" + newProductStockPointVO.getPurchasePlanner();
                    }
                    User user = userCodeMap.get(newProductStockPointVO.getPurchasePlanner());
                    if (StringUtils.isEmpty(user.getErpUser())) {
                        return "请先维护" + e.getProductCode() + "对应的计划员ERP账号";
                    }
                    if (StringUtils.isEmpty(newStockPointVO1.getSupplierId())) {
                        return "请先维护" + e.getStockPointCode() + "对应的供应商关系";
                    }
                    if (StringUtils.isEmpty(newStockPointVO1.getCompanyName())) {
                        return "请先维护" + e.getStockPointCode() + "对应的公司名称";
                    }
                    if (StringUtils.isEmpty(supplierAddressMap.get(newStockPointVO1.getSupplierId()))) {
                        return "该" + e.getStockPointCode() + "对应的供应商没有采购的地址";
                    }
                    if (e.getOrderPrice() == null) {
                        return "该" + e.getProductCode() + "价格为空";
                    }
                    if (e.getArea() == null) {
                        return "该" + e.getProductCode() + "面积为空";
                    }
                    erpPoCreate.setOrgId(orgId);
                    erpPoCreate.setVendorName(newStockPointVO1.getCompanyName());
                    erpPoCreate.setVendorSite(supplierAddressMap.get(newStockPointVO1.getSupplierId()).getAddress());
                    erpPoCreate.setPoType("标准采购订单");
                    erpPoCreate.setShipTo(newStockPointVO.getStockPointName());
                    erpPoCreate.setBillTo(newStockPointVO.getStockPointName());
                    erpPoCreate.setBuyer(user.getErpUser());
                    erpPoCreate.setCurrency("CNY");
                    erpPoCreate.setIsAutoApprave("Y");
                    erpPoCreate.setBpimId(e.getId());
                    erpPoCreate.setBpimStockPointCode(e.getStockPointCode());
                    erpPoCreate.setBpimContainerNumber(e.getContainerNumber());
                    List<ErpPoCreate.PoLines> lines = new ArrayList<>();
                    ErpPoCreate.PoLines poLines = new ErpPoCreate.PoLines();
                    poLines.setItemCode(e.getProductCode());
                    poLines.setUom("平方米");
                    BigDecimal orderPrice = e.getOrderPrice();
                    BigDecimal divisor = new BigDecimal("1.13");
                    BigDecimal calculatedPrice = orderPrice.divide(divisor, 4, BigDecimal.ROUND_HALF_UP);
                    poLines.setPrice(calculatedPrice);
                    poLines.setQty(e.getArea());
                    Date deliveryTime = e.getContainerDeliveryTime();
                    LocalDate localDate = deliveryTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    LocalDate newDeliveryTime = localDate.plusDays(3);
                    String formattedDate = newDeliveryTime.format(formatter);
                    poLines.setNeedByDate(formattedDate);
                    poLines.setNote("BPIM");
                    lines.add(poLines);
                    erpPoCreate.setLines(lines);
                    erpPoCreateList.add(erpPoCreate);
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.joining(", "));

        return errorMessage;
    }

    private Map<String, NewProductStockPointVO> getProductMapByCodes(List<String> productCodes, String scenario, String plantCode) {

        List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointVOByParams(scenario,
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "productCodes", productCodes));
        Map<String, NewProductStockPointVO> productMap = productList.stream()
                .filter(item -> item.getStockPointCode().equals(plantCode))
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(),
                        (value1, value2) -> value1));
        return productMap;
    }

    private Map<String, SupplierAddressVO> getSupplierAddressMapByCodes(List<String> supplierAddressList, String scenario) {

        List<SupplierAddressVO> productList = newMdsFeign.selectSupplierAddressByParams(scenario,
                ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
                        "purchasingFlag", YesOrNoEnum.YES.getCode(),
                        "supplierIds", supplierAddressList));
        Map<String, SupplierAddressVO> supplierAddressMap = productList.stream()
                .collect(Collectors.toMap(SupplierAddressVO::getSupplierId, Function.identity(),
                        (value1, value2) -> value1));
        return supplierAddressMap;
    }

    private Map<String, User> getUserMapByCodes() {
        List<User> userList = ipsNewFeign.userList();
        Map<String, User> userMap = userList.stream()
                .collect(Collectors.toMap(User::getId, Function.identity(),
                        (value1, value2) -> value1));
        return userMap;
    }
}
