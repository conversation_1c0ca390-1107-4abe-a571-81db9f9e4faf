package com.yhl.scp.mrp.originalFilm.domain.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.ddd.BaseDO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>OriginalFilmDemandConsultDetailDO</code>
 * <p>
 * 原片需求征询明细DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 10:05:38
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OriginalFilmDemandConsultDetailDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 917623127626756271L;

    /**
     * 主键
     */
    private String id;
        
    /**
     * 汇总ID
     */
    private String consultSummaryId;
        
    /**
     * 需求来源类型（产能平衡转入，生产计划需求, 人工新增）
     */
    private String demandSourceType;
        
    /**
     * 需求来源的ID
     */
    private String demandSourceId;
        
    /**
     * 车型
     */
    private String vehicleModelCode;

    /**
     * 产品物料id
     */
    private String productId;

    /**
     * 原片物料编码
     */
    private String productCode;
        
    /**
     * 原片物料id
     */
    private String originalFilmProductId;
        
    /**
     * 需求数量
     */
    private BigDecimal demandedQuantity;
        
    /**
     * 需求日期
     */
    private Date demandedDate;
        
    /**
     * 浮法厂库存
     */
    private BigDecimal floatInventoryQuantity;
    
    /**
     * 港口在途库存（原片在途）
     */
    private BigDecimal portRoadInventory;
    
    /**
     * 港口库存(码头库存)
     */
    private BigDecimal portInventory;
    
    /**
     * 本厂库存
     */
    private BigDecimal factoryInventory;
    
    /**
     * 标准规格需求量（毛需求）
     */
    private BigDecimal standardDemandedQuantity;
    
    /**
     * 替代规格需求量（被作为替代料消耗量）
     */
    private BigDecimal usedAsReplaceQuantity;
    
    /**
     * 被替代量（消耗替代料量）
     */
    private BigDecimal useReplaceQuantity;
    
    /**
     * 净需求量=毛需求+替代规格需求量-库存数据(浮法厂库存,原片在途,码头库存,本厂库存,被替代量)
     */
    private BigDecimal netDemandedQuantity;

    /**
     * 毛胚规格
     */
    private String mpblSpec;
        
    /**
     * 版本号
     */
    private Integer versionValue;

    /**
     * 特殊要求
     */
    private String specialRequirements;

    /**
     * 面积
     */
    private BigDecimal area;

    /**
     * 重量
     */
    private BigDecimal weight;

}
