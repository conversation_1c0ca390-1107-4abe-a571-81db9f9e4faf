package com.yhl.scp.mrp.originalFilm.service.impl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductDemandSupplyCalculateVO;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.enums.OriginalFilmDemandSourceTypeEnum;
import com.yhl.scp.mrp.originalFilm.convertor.OriginalFilmDemandConsultDetailConvertor;
import com.yhl.scp.mrp.originalFilm.domain.entity.OriginalFilmDemandConsultDetailDO;
import com.yhl.scp.mrp.originalFilm.domain.service.OriginalFilmDemandConsultDetailDomainService;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultDetailDTO;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultSummaryDTO;
import com.yhl.scp.mrp.originalFilm.infrastructure.dao.OriginalFilmDemandConsultDetailDao;
import com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultDetailPO;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultDetailService;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultSummaryService;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultDetailVO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultSummaryVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>OriginalFilmDemandConsultDetailServiceImpl</code>
 * <p>
 * 原片需求征询明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 10:05:38
 */
@Slf4j
@Service
public class OriginalFilmDemandConsultDetailServiceImpl extends AbstractService implements OriginalFilmDemandConsultDetailService {

    @Resource
    private OriginalFilmDemandConsultDetailDao originalFilmDemandConsultDetailDao;

    @Resource
    private OriginalFilmDemandConsultDetailDomainService originalFilmDemandConsultDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    
    @Resource
    private OriginalFilmDemandConsultSummaryService originalFilmDemandConsultSummaryService;
    
    @Resource
    private IpsNewFeign ipsNewFeign;
    
    @Resource
    private NewMdsFeign newMdsFeign;
    
    @Resource
    private DfpFeign dfpFeign;
    
    @Resource
    private OriginalFilmDemandConsultSummaryService OriginalFilmDemandConsultSummaryService;
    

    @Override
    public BaseResponse<Void> doCreate(OriginalFilmDemandConsultDetailDTO dto) {
        // 0.数据转换
        OriginalFilmDemandConsultDetailDO originalFilmDemandConsultDetailDO = OriginalFilmDemandConsultDetailConvertor
        		.INSTANCE.dto2Do(dto);
        OriginalFilmDemandConsultDetailPO originalFilmDemandConsultDetailPO = OriginalFilmDemandConsultDetailConvertor
        		.INSTANCE.dto2Po(dto);
        // 1.数据校验
        originalFilmDemandConsultDetailDomainService.validation(originalFilmDemandConsultDetailDO);
        // 2.维护对应的原片需求征询汇总数据, 根据厚度，颜色，月份进行查询
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), 
        		TenantCodeEnum.FYQB.getCode());
        List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointByIds(defaultScenario.getData(), 
        		Arrays.asList(dto.getOriginalFilmProductId()));
        if(CollUtil.isEmpty(productList)) {
        	throw new BusinessException("原片编码不能为空!");
        }
        NewProductStockPointVO productVO = productList.get(0);
        if(Objects.isNull(productVO.getProductThickness())) {
        	throw new BusinessException("物料【 " + productVO.getProductCode() + "】未维护厚度!");
        }
        if(StringUtils.isEmpty(productVO.getProductColor())) {
        	throw new BusinessException("物料【 " + productVO.getProductCode() + "】未维护颜色!");
        }
        addSummary(dto, originalFilmDemandConsultDetailPO, productVO.getProductThickness(), productVO.getProductColor());
        // 3.数据持久化
        BasePOUtils.insertFiller(originalFilmDemandConsultDetailPO);
        originalFilmDemandConsultDetailPO.setDemandSourceType(OriginalFilmDemandSourceTypeEnum.MANUAL_ADDITION_DEMAND.getCode());
        originalFilmDemandConsultDetailDao.insertWithPrimaryKey(originalFilmDemandConsultDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OriginalFilmDemandConsultDetailDTO dto) {
        // 0.数据转换
        OriginalFilmDemandConsultDetailDO originalFilmDemandConsultDetailDO = OriginalFilmDemandConsultDetailConvertor
        		.INSTANCE.dto2Do(dto);
        OriginalFilmDemandConsultDetailPO originalFilmDemandConsultDetailPO = OriginalFilmDemandConsultDetailConvertor
        		.INSTANCE.dto2Po(dto);
        // 1.数据校验
        originalFilmDemandConsultDetailDomainService.validation(originalFilmDemandConsultDetailDO);
        //原数量
        BigDecimal sourceDemandedQty = originalFilmDemandConsultDetailDao.selectByPrimaryKey(dto.getId()).getDemandedQuantity();
        BigDecimal varianceQty = dto.getDemandedQuantity().subtract(sourceDemandedQty);
        // 2.维护对应的原片需求征询汇总数据，数量扣减，需求日期调整
        OriginalFilmDemandConsultSummaryVO summary = originalFilmDemandConsultSummaryService.selectByPrimaryKey(dto.getConsultSummaryId());
    	//判断是否是同一个月份
        if(!Objects.equals(summary.getDemandedMonth(), DateUtils.dateToString(dto.getDemandedDate(), DateUtils.YEAR_MONTH))) {
        	//非同一个月份，当前月份进行需求数量加减，数量维护到新的汇总
        	addSummary(dto, originalFilmDemandConsultDetailPO, summary.getProductThickness(), summary.getProductColor());
        	varianceQty = sourceDemandedQty.negate();
        }
        //原汇总数据处理
        OriginalFilmDemandConsultSummaryDTO updateSummary = OriginalFilmDemandConsultSummaryDTO.builder()
    			.id(summary.getId())
    			.versionValue(summary.getVersionValue())
    			.demandedQuantity(summary.getDemandedQuantity().add(varianceQty))
    			.build();
    	originalFilmDemandConsultSummaryService.doUpdate(updateSummary);
        
        // 3.数据持久化
        BasePOUtils.updateFiller(originalFilmDemandConsultDetailPO);
        originalFilmDemandConsultDetailDao.updateSelective(originalFilmDemandConsultDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 维护统计
     * @param dto
     * @param originalFilmDemandConsultDetailPO
     * @param productThickness
     * @param productColor
     */
	private void addSummary(OriginalFilmDemandConsultDetailDTO dto,
			OriginalFilmDemandConsultDetailPO originalFilmDemandConsultDetailPO,
			BigDecimal productThickness, String productColor) {
		Map<String, Object> queryParam = MapUtil.newHashMap();
		queryParam.put("productThickness", productThickness);
		queryParam.put("productColor", productColor);
		queryParam.put("demandedMonth", DateUtils.dateToString(dto.getDemandedDate(), DateUtils.YEAR_MONTH));
		queryParam.put("enabled", YesOrNoEnum.YES.getCode());
		List<OriginalFilmDemandConsultSummaryVO> summaryList = originalFilmDemandConsultSummaryService.selectByParams(queryParam);
		if(CollUtil.isEmpty(summaryList)) {
			//新增统计
			OriginalFilmDemandConsultSummaryDTO addSummary = OriginalFilmDemandConsultSummaryDTO.builder()
					.id(UUIDUtil.getUUID())
					.productThickness(productThickness)
					.productColor(productColor)
					.demandedQuantity(dto.getDemandedQuantity())
					.demandedMonth(DateUtils.dateToString(dto.getDemandedDate(), DateUtils.YEAR_MONTH))
					.build();
			originalFilmDemandConsultSummaryService.insertWithPrimaryKey(addSummary);
			originalFilmDemandConsultDetailPO.setConsultSummaryId(addSummary.getId());
		}else {
			//修改统计
			OriginalFilmDemandConsultSummaryVO sourceVO = summaryList.get(0);
			OriginalFilmDemandConsultSummaryDTO updateSummary = OriginalFilmDemandConsultSummaryDTO.builder()
					.id(sourceVO.getId())
					.versionValue(sourceVO.getVersionValue())
					.demandedQuantity(dto.getDemandedQuantity()
							.add(sourceVO.getDemandedQuantity()))
					.build();
			originalFilmDemandConsultSummaryService.doUpdate(updateSummary);
			originalFilmDemandConsultDetailPO.setConsultSummaryId(sourceVO.getId());
		}
	}

    @Override
    public void doCreateBatch(List<OriginalFilmDemandConsultDetailDTO> list) {
        List<OriginalFilmDemandConsultDetailPO> newList = OriginalFilmDemandConsultDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        originalFilmDemandConsultDetailDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<OriginalFilmDemandConsultDetailDTO> list) {
        List<OriginalFilmDemandConsultDetailPO> newList = OriginalFilmDemandConsultDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        originalFilmDemandConsultDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        //处理汇总
        List<OriginalFilmDemandConsultDetailPO> detailList = originalFilmDemandConsultDetailDao.selectByPrimaryKeys(idList);
        BigDecimal demandedQuantity = BigDecimal.ZERO;
        for (OriginalFilmDemandConsultDetailPO po : detailList) {
        	demandedQuantity = demandedQuantity.add(po.getDemandedQuantity());
		}
        OriginalFilmDemandConsultSummaryVO summary = originalFilmDemandConsultSummaryService.selectByPrimaryKey(detailList.get(0).getConsultSummaryId());
        OriginalFilmDemandConsultSummaryDTO updateSummary = OriginalFilmDemandConsultSummaryDTO.builder()
				.id(summary.getId())
				.versionValue(summary.getVersionValue())
				.demandedQuantity(summary.getDemandedQuantity()
						.subtract(demandedQuantity))
				.build();
		originalFilmDemandConsultSummaryService.doUpdate(updateSummary);
		//删除统计
        return originalFilmDemandConsultDetailDao.deleteBatch(idList);
    }

    @Override
    public OriginalFilmDemandConsultDetailVO selectByPrimaryKey(String id) {
        OriginalFilmDemandConsultDetailPO po = originalFilmDemandConsultDetailDao.selectByPrimaryKey(id);
        return OriginalFilmDemandConsultDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_original_film_demand_consult_detail")
    public List<OriginalFilmDemandConsultDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<OriginalFilmDemandConsultDetailVO> pageList = this.selectByCondition(sortParam, queryCriteriaParam);
        pageList.forEach( e -> {
        	if(e.getOriginalFilmProductLength() != null && e.getOriginalFilmProductWidth()!= null) {
        		e.setOriginalFilmProductSpec(e.getOriginalFilmProductLength().stripTrailingZeros().toPlainString()
        				+ "*" + e.getOriginalFilmProductWidth().stripTrailingZeros().toPlainString());
        	}
        });
        return pageList;
    }

    @Override
    @Expression(value = "v_mrp_original_film_demand_consult_detail")
    public List<OriginalFilmDemandConsultDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OriginalFilmDemandConsultDetailVO> dataList = originalFilmDemandConsultDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        OriginalFilmDemandConsultDetailServiceImpl target = springBeanUtils.getBean(OriginalFilmDemandConsultDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OriginalFilmDemandConsultDetailVO> selectByParams(Map<String, Object> params) {
        List<OriginalFilmDemandConsultDetailPO> list = originalFilmDemandConsultDetailDao.selectByParams(params);
        return OriginalFilmDemandConsultDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OriginalFilmDemandConsultDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.ORIGINAL_FILM_DEMAND_CONSULT_DETAIL.getCode();
    }

    @Override
    public List<OriginalFilmDemandConsultDetailVO> invocation(List<OriginalFilmDemandConsultDetailVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

	@Override
	public void deleteByConsultSummaryIds(List<String> idList) {
		originalFilmDemandConsultDetailDao.deleteByConsultSummaryIds(idList);
	}

	@Override
	public List<LabelValue<String>> selectVehicleModels() {
		BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
		return dfpFeign.selectAllVehicleModelCodes(defaultScenario.getData());
	}

	@Override
	public List<LabelValue<String>> selectFinishProducts(String vehicleModelCode) {
		//获取工艺路径中对应车型的成品物料信息
		BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), 
				TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
		Map<String, Object> params = new HashMap<>();
		params.put("vehicleModelCode", vehicleModelCode);
		params.put("productType", "FG");
		params.put("stockPointCode", rangeData);
		List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointVOByParams(scenario.getData(), params);
		return productList.stream()
                .map(x -> new LabelValue<>(x.getProductCode(), x.getId()))
                .collect(Collectors.toList());
	}

	@Override
	public List<LabelValue<String>> selectOrginalFilmProducts(String productId) {
		//获取指定物料下所有的原料
		BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
		ProductDemandSupplyCalculateVO pdscVO = newMdsFeign.selectRoutingStepInputForSupplyCalculate(defaultScenario.getData(), "", 
				productId, Lists.newArrayList());
		List<RoutingStepInputVO> routingStepInputVOs = pdscVO.getRoutingStepInputVOs();
		return routingStepInputVOs.stream()
                .map(x -> new LabelValue<>(x.getProductCode(), x.getInputProductId()))
                .collect(Collectors.toList());
	}

	@Override
	public List<OriginalFilmDemandConsultDetailVO> selectByDemandSourceIds(List<String> consultSummaryIds) {
		return originalFilmDemandConsultDetailDao.selectByDemandSourceIds(consultSummaryIds);
	}

}
