package com.yhl.scp.mrp.transport.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mrp.transport.dto.TransportRoutingDTO;
import com.yhl.scp.mrp.transport.service.TransportRoutingService;
import com.yhl.scp.mrp.transport.vo.TransportRoutingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>TransportRoutingController</code>
 * <p>
 * 运输路径控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-28 14:38:51
 */
@Slf4j
@Api(tags = "运输路径控制器")
@RestController
@RequestMapping("transportRouting")
public class TransportRoutingController extends BaseController {

    @Resource
    private TransportRoutingService transportRoutingService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<TransportRoutingVO>> page() {
        List<TransportRoutingVO> transportRoutingList = transportRoutingService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<TransportRoutingVO> pageInfo = new PageInfo<>(transportRoutingList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody TransportRoutingDTO transportRoutingDTO) {
        return transportRoutingService.doCreate(transportRoutingDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody TransportRoutingDTO transportRoutingDTO) {
        return transportRoutingService.doUpdate(transportRoutingDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        transportRoutingService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<TransportRoutingVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, transportRoutingService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "路径编码下拉")
    @GetMapping(value = "routingCode/dropDown")
    public BaseResponse<List<LabelValue<String>>> routingCodeDropDown(
            @RequestParam(value = "originStockPointId",required = false)String originStockPointId,
            @RequestParam(value = "destinStockPointId",required = false)String destinStockPointId
    ) {
        List<LabelValue<String>> result = transportRoutingService.routingCodeDropDown(originStockPointId,destinStockPointId);
        return BaseResponse.success(result);
    }
}
