package com.yhl.scp.mrp.material.arrival.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalFeedbackRecordDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalFeedbackRecordService;
import com.yhl.scp.mrp.material.arrival.vo.MaterialArrivalFeedbackRecordVO;
import com.yhl.scp.mrp.material.plan.service.MrpService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialArrivalFeedbackRecordController</code>
 * <p>
 * 材料到货反馈记录控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-30 11:05:36
 */
@Slf4j
@Api(tags = "材料到货反馈记录控制器")
@RestController
@RequestMapping("materialArrivalFeedbackRecord")
public class MaterialArrivalFeedbackRecordController extends BaseController {

    @Resource
    private MaterialArrivalFeedbackRecordService materialArrivalFeedbackRecordService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MaterialArrivalFeedbackRecordVO>> page() {
        List<MaterialArrivalFeedbackRecordVO> materialArrivalFeedbackRecordList = materialArrivalFeedbackRecordService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialArrivalFeedbackRecordVO> pageInfo = new PageInfo<>(materialArrivalFeedbackRecordList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialArrivalFeedbackRecordDTO materialArrivalFeedbackRecordDTO) {
        return materialArrivalFeedbackRecordService.doCreate(materialArrivalFeedbackRecordDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialArrivalFeedbackRecordDTO materialArrivalFeedbackRecordDTO) {
        return materialArrivalFeedbackRecordService.doUpdate(materialArrivalFeedbackRecordDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialArrivalFeedbackRecordService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialArrivalFeedbackRecordVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialArrivalFeedbackRecordService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "接收到货反馈数据")
    @PostMapping(value = "receiveArrivalData")
    public BaseResponse<Void> receiveArrivalData(List<MaterialArrivalFeedbackRecordVO> dataList) {
        try {
            materialArrivalFeedbackRecordService.receiveArrivalData(dataList);
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("接收到货反馈数据失败", e);
            throw new BusinessException("接收到货反馈数据失败,{0}", e.getLocalizedMessage());
        }
    }
}
