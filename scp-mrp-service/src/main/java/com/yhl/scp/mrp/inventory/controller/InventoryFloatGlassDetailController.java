package com.yhl.scp.mrp.inventory.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.inventory.dto.InventoryAlternativeRelationshipDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryFloatGlassDetailDTO;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>InventoryFloatGlassDetailController</code>
 * <p>
 * 原片浮法库存批次明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 20:25:15
 */
@Slf4j
@Api(tags = "原片浮法库存批次明细控制器")
@RestController
@RequestMapping("inventoryFloatGlassDetail")
public class InventoryFloatGlassDetailController extends BaseController {

    @Resource
    private InventoryFloatGlassDetailService inventoryFloatGlassDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<InventoryFloatGlassDetailVO>> page(
            @RequestParam(value = "overdueSort", required = false, defaultValue = "NO") String overdueSort
    ) {
        List<InventoryFloatGlassDetailVO> inventoryFloatGlassDetailList =
                inventoryFloatGlassDetailService.selectByPage(getPagination(), getSortParam(),
                        getQueryCriteriaParam(), overdueSort);
        PageInfo<InventoryFloatGlassDetailVO> pageInfo = new PageInfo<>(inventoryFloatGlassDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody InventoryFloatGlassDetailDTO inventoryFloatGlassDetailDTO) {
        return inventoryFloatGlassDetailService.doCreate(inventoryFloatGlassDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody InventoryFloatGlassDetailDTO inventoryFloatGlassDetailDTO) {
        return inventoryFloatGlassDetailService.doUpdate(inventoryFloatGlassDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        inventoryFloatGlassDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<InventoryFloatGlassDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, inventoryFloatGlassDetailService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "可用查找")
    @GetMapping(value = "availableSearch")
    public BaseResponse<List<InventoryAlternativeRelationshipVO>> availableSearch(@RequestParam("productCode") String productCode,
                                                                                  @RequestParam("alternativeType") String alternativeType,
                                                                                  @RequestParam(value = "cuttingRatePercentage", required = false, defaultValue = "0.8") String cuttingRatePercentage) {
        List<InventoryAlternativeRelationshipVO> availableSearch = inventoryFloatGlassDetailService.availableSearch(productCode, alternativeType, cuttingRatePercentage);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, availableSearch);
    }

    @ApiOperation(value = "原片替代（浮法）")
    @PostMapping(value = "alternative")
    public BaseResponse<Void> alternative(@RequestBody List<InventoryAlternativeRelationshipDTO> list) {
        return inventoryFloatGlassDetailService.alternative(list);
    }
    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncInventoryFloatGlassDetail() {
        return inventoryFloatGlassDetailService.syncOriginalFilmFFInventory(SystemHolder.getTenantCode());
    }

    @ApiOperation(value = "模板导出（外购）")
    @GetMapping(value = "exportTemplateOutsourcing")
    public void exportTemplateOutsourcing(HttpServletResponse response) {
        inventoryFloatGlassDetailService.exportTemplateOutsourcing(response);
    }

    @ApiOperation(value = "导入（外购）")
    @PostMapping(value = "uploadOutsourcing")
    public BaseResponse<Void> uploadOutsourcing(@RequestPart MultipartFile file) {
        inventoryFloatGlassDetailService.uploadOutsourcing(file);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
}
