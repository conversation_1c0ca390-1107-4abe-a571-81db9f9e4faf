package com.yhl.scp.mrp.order.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPurchaseOrder;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialPlanNeedVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedDTO;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseRequestVO;
import com.yhl.scp.mrp.material.purchase.vo.MaterialReturnedPurchaseVO;
import com.yhl.scp.mrp.order.convertor.PurchaseOrderInfoConvertor;
import com.yhl.scp.mrp.order.convertor.PurchaseOrderInfoConvertorImpl;
import com.yhl.scp.mrp.order.domain.entity.PurchaseOrderInfoDO;
import com.yhl.scp.mrp.order.domain.service.PurchaseOrderInfoDomainService;
import com.yhl.scp.mrp.order.dto.PurchaseOrderInfoDTO;
import com.yhl.scp.mrp.order.infrastructure.dao.PurchaseOrderInfoDao;
import com.yhl.scp.mrp.order.infrastructure.po.PurchaseOrderInfoPO;
import com.yhl.scp.mrp.order.service.PurchaseOrderInfoService;
import com.yhl.scp.mrp.order.vo.PurchaseOrderInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>PurchaseOrderInfoServiceImpl</code>
 * <p>
 * 应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-27 14:10:22
 */
@Slf4j
@Service
public class PurchaseOrderInfoServiceImpl extends AbstractService implements PurchaseOrderInfoService {

    @Resource
    private PurchaseOrderInfoDao purchaseOrderInfoDao;

    @Resource
    private PurchaseOrderInfoDomainService purchaseOrderInfoDomainService;

    @Resource
    private MaterialPlanNeedService materialPlanNeedService;

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private NewMdsFeign mdsFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Override
    public BaseResponse<Void> doCreate(PurchaseOrderInfoDTO purchaseOrderInfoDTO) {
        // 0.数据转换
        PurchaseOrderInfoDO purchaseOrderInfoDO = PurchaseOrderInfoConvertor.INSTANCE.dto2Do(purchaseOrderInfoDTO);
        PurchaseOrderInfoPO purchaseOrderInfoPO = PurchaseOrderInfoConvertor.INSTANCE.dto2Po(purchaseOrderInfoDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        purchaseOrderInfoDomainService.validation(purchaseOrderInfoDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(purchaseOrderInfoPO);
        purchaseOrderInfoDao.insert(purchaseOrderInfoPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(PurchaseOrderInfoDTO purchaseOrderInfoDTO) {
        // 0.数据转换
        PurchaseOrderInfoDO purchaseOrderInfoDO = PurchaseOrderInfoConvertor.INSTANCE.dto2Do(purchaseOrderInfoDTO);
        PurchaseOrderInfoPO purchaseOrderInfoPO = PurchaseOrderInfoConvertor.INSTANCE.dto2Po(purchaseOrderInfoDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        purchaseOrderInfoDomainService.validation(purchaseOrderInfoDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(purchaseOrderInfoPO);
        purchaseOrderInfoDao.update(purchaseOrderInfoPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<PurchaseOrderInfoDTO> list) {
        List<PurchaseOrderInfoPO> newList = PurchaseOrderInfoConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        purchaseOrderInfoDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<PurchaseOrderInfoDTO> list) {
        List<PurchaseOrderInfoPO> newList = PurchaseOrderInfoConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        purchaseOrderInfoDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return purchaseOrderInfoDao.deleteBatch(idList);
        }
        return purchaseOrderInfoDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public PurchaseOrderInfoVO selectByPrimaryKey(String id) {
        PurchaseOrderInfoPO po = purchaseOrderInfoDao.selectByPrimaryKey(id);
        return PurchaseOrderInfoConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "PURCHASE_ORDER_INFO")
    public List<PurchaseOrderInfoVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PURCHASE_ORDER_INFO")
    public List<PurchaseOrderInfoVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<PurchaseOrderInfoVO> dataList = purchaseOrderInfoDao.selectByCondition(sortParam, queryCriteriaParam);
        PurchaseOrderInfoServiceImpl target = SpringBeanUtils.getBean(PurchaseOrderInfoServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<PurchaseOrderInfoVO> selectByParams(Map<String, Object> params) {
        List<PurchaseOrderInfoPO> list = purchaseOrderInfoDao.selectByParams(params);
        return PurchaseOrderInfoConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<PurchaseOrderInfoVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<PurchaseOrderInfoVO> selectByHeaderOrLine(Map<String, Object> params) {
        return PurchaseOrderInfoConvertor.INSTANCE.po2Vos(purchaseOrderInfoDao.selectByHeaderOrLine(params));
    }

    @Override
    public BaseResponse<Void> syncPurchaseOrder(String tenantId) {
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(), tenantId);

        Map stockParams = MapUtil.builder().put("organizeType", StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode())
                .put("enabled", YesOrNoEnum.YES.getCode()).build();
        List<NewStockPointVO> newStockPointVOS =mdsFeign.selectStockPointByParams(scenario.getData(), stockParams);
        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return BaseResponse.error("库存点信息为空");
        }
        Map<String, List<NewStockPointVO>> stockPointMap = newStockPointVOS.stream().collect(Collectors.groupingBy(NewStockPointVO::getStockPointCode));
        for (String stockPoint : stockPointMap.keySet()) {
            // 调用远程的销售组织信息
            Map<String, Object> newStockPoingMap = new HashMap<>(4);
            newStockPoingMap.put("stockPointCode", stockPoint);
            newStockPoingMap.put("orgId", stockPointMap.get(stockPoint).get(0).getOrganizeId());
            newStockPoingMap.put("triggerType", DcpConstants.TASKS_MANUAL_TRIGGER);
            newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.PURCHASE_ORDER.getCode(), newStockPoingMap);
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> sync(String scenario, List<ErpPurchaseOrder> o) {
        if (CollectionUtils.isEmpty(o)) {
            return BaseResponse.success();
        }
        List<String> headerCodes = o.stream().map(ErpPurchaseOrder::getOrderHeader).distinct().collect(Collectors.toList());
        List<String> lineCodes = o.stream().map(ErpPurchaseOrder::getOrderLine).distinct().collect(Collectors.toList());
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("headerCodes", headerCodes);
        params.put("lineCodes", lineCodes);
        List<PurchaseOrderInfoPO> oldPOList = purchaseOrderInfoDao.selectByHeaderOrLine(params);
        Map<String, PurchaseOrderInfoPO> oldPOMap = oldPOList.stream().collect(Collectors.toMap(t -> t.getOrderHeader() + "|" + t.getOrderLine(), Function.identity(), (v1, v2) -> v1));
        List<PurchaseOrderInfoDTO> insertPurchaseOrderInfoDTOS = Lists.newArrayList();
        List<PurchaseOrderInfoDTO> updatePurchaseOrderInfoDTOS = Lists.newArrayList();

        for (ErpPurchaseOrder erpPurchaseOrder : o) {
            PurchaseOrderInfoDTO purchaseOrderInfoDTO = new PurchaseOrderInfoDTO();
            String dataKey = erpPurchaseOrder.getOrderHeader() + "|" + erpPurchaseOrder.getOrderLine();
            if(oldPOMap.containsKey(dataKey)){
                PurchaseOrderInfoPO oldPO = oldPOMap.get(dataKey);
                purchaseOrderInfoDTO = PurchaseOrderInfoConvertorImpl.INSTANCE.po2Dto(oldPO);
                purchaseOrderInfoDTO.setStockPointCode(erpPurchaseOrder.getOrgCode());
                purchaseOrderInfoDTO.setOrderHeader(erpPurchaseOrder.getOrderHeader());
                purchaseOrderInfoDTO.setOrderLine(erpPurchaseOrder.getOrderLine());
                purchaseOrderInfoDTO.setProductCode(erpPurchaseOrder.getItem());
                purchaseOrderInfoDTO.setProductName(erpPurchaseOrder.getItemDesc());
                purchaseOrderInfoDTO.setMeasurementUnit(erpPurchaseOrder.getItemUnit());
                purchaseOrderInfoDTO.setNeedDate(erpPurchaseOrder.getNeedDate());
                purchaseOrderInfoDTO.setPromisedDate(erpPurchaseOrder.getPromisedDate());
                purchaseOrderInfoDTO.setApprovalStatus(erpPurchaseOrder.getApprovalStatus());
                purchaseOrderInfoDTO.setHeaderStatus(erpPurchaseOrder.getHeaderStatus());
                purchaseOrderInfoDTO.setLineStatus(erpPurchaseOrder.getLineStatus());
                purchaseOrderInfoDTO.setCreationDate(erpPurchaseOrder.getCreationDate());
                purchaseOrderInfoDTO.setLastUpdateDate(erpPurchaseOrder.getLastUpdateDate());
                purchaseOrderInfoDTO.setComments(erpPurchaseOrder.getComments());
                purchaseOrderInfoDTO.setAgentName(erpPurchaseOrder.getAgentName());
                purchaseOrderInfoDTO.setSupplierCode(erpPurchaseOrder.getVendorCode());
                purchaseOrderInfoDTO.setSupplierName(erpPurchaseOrder.getVendorName());
                purchaseOrderInfoDTO.setSupplierSiteCode(erpPurchaseOrder.getVendorSiteCode());
                purchaseOrderInfoDTO.setNote(erpPurchaseOrder.getNote());
                purchaseOrderInfoDTO.setQtyBilled(new BigDecimal(erpPurchaseOrder.getQtyBilled()));
                if(Objects.nonNull(erpPurchaseOrder.getQuantity())){
                    purchaseOrderInfoDTO.setQuantity(new BigDecimal(erpPurchaseOrder.getQuantity()));
                }
                if(Objects.nonNull(erpPurchaseOrder.getQuantityReceived())){
                    purchaseOrderInfoDTO.setQuantityReceived(new BigDecimal(erpPurchaseOrder.getQuantityReceived()));
                }
                updatePurchaseOrderInfoDTOS.add(purchaseOrderInfoDTO);
            }else{
                purchaseOrderInfoDTO.setStockPointCode(erpPurchaseOrder.getOrgCode());
                purchaseOrderInfoDTO.setOrderHeader(erpPurchaseOrder.getOrderHeader());
                purchaseOrderInfoDTO.setOrderLine(erpPurchaseOrder.getOrderLine());
                purchaseOrderInfoDTO.setProductCode(erpPurchaseOrder.getItem());
                purchaseOrderInfoDTO.setProductName(erpPurchaseOrder.getItemDesc());
                purchaseOrderInfoDTO.setMeasurementUnit(erpPurchaseOrder.getItemUnit());
                purchaseOrderInfoDTO.setNeedDate(erpPurchaseOrder.getNeedDate());
                purchaseOrderInfoDTO.setPromisedDate(erpPurchaseOrder.getPromisedDate());
                purchaseOrderInfoDTO.setApprovalStatus(erpPurchaseOrder.getApprovalStatus());
                purchaseOrderInfoDTO.setHeaderStatus(erpPurchaseOrder.getHeaderStatus());
                purchaseOrderInfoDTO.setLineStatus(erpPurchaseOrder.getLineStatus());
                purchaseOrderInfoDTO.setCreationDate(erpPurchaseOrder.getCreationDate());
                purchaseOrderInfoDTO.setLastUpdateDate(erpPurchaseOrder.getLastUpdateDate());
                purchaseOrderInfoDTO.setComments(erpPurchaseOrder.getComments());
                purchaseOrderInfoDTO.setAgentName(erpPurchaseOrder.getAgentName());
                purchaseOrderInfoDTO.setSupplierCode(erpPurchaseOrder.getVendorCode());
                purchaseOrderInfoDTO.setSupplierName(erpPurchaseOrder.getVendorName());
                purchaseOrderInfoDTO.setSupplierSiteCode(erpPurchaseOrder.getVendorSiteCode());
                purchaseOrderInfoDTO.setNote(erpPurchaseOrder.getNote());
                purchaseOrderInfoDTO.setQtyBilled(new BigDecimal(erpPurchaseOrder.getQtyBilled()));
                if(Objects.nonNull(erpPurchaseOrder.getQuantity())){
                    purchaseOrderInfoDTO.setQuantity(new BigDecimal(erpPurchaseOrder.getQuantity()));
                }
                if(Objects.nonNull(erpPurchaseOrder.getQuantityReceived())){
                    purchaseOrderInfoDTO.setQuantityReceived(new BigDecimal(erpPurchaseOrder.getQuantityReceived()));
                }
                insertPurchaseOrderInfoDTOS.add(purchaseOrderInfoDTO);
            }

        }
        if(CollectionUtils.isNotEmpty(insertPurchaseOrderInfoDTOS)){
            List<List<PurchaseOrderInfoDTO>> partition = com.google.common.collect.Lists.partition(insertPurchaseOrderInfoDTOS, 3000);
            for (List<PurchaseOrderInfoDTO> poDtos : partition) {
                this.doCreateBatch(poDtos);
            }
            log.info("ERP采购订单同步完成，新增数据条数：{}", insertPurchaseOrderInfoDTOS.size());
        }
        if(CollectionUtils.isNotEmpty(updatePurchaseOrderInfoDTOS)){
            List<List<PurchaseOrderInfoDTO>> partition = com.google.common.collect.Lists.partition(updatePurchaseOrderInfoDTOS, 3000);
            for (List<PurchaseOrderInfoDTO> poDtos : partition) {
                this.doUpdateBatch(poDtos);
            }
            log.info("ERP采购订单同步完成，更新数据条数：{}", updatePurchaseOrderInfoDTOS.size());
        }

        return BaseResponse.success("同步成功");
    }

    @Override
    public void doPurchaseOrderInfoJob(Integer moveMinute) {
        log.info("根据采购订单更新要货PO数据开始");
        //每三十分钟跑一次，每次获取40分钟前变更的数据维护到货跟踪对应数据的预计发货时间和数量
        Date startModifyTime = DateUtils.moveMinute(new Date(), moveMinute);
        List<PurchaseOrderInfoVO> purchaseOrderInfoVOList = this.selectByParams(ImmutableMap.of(
                "enabled", YesOrNoEnum.YES.getCode(),
                "startModifyTime", startModifyTime));

        // note是要货计划的id，根据id更新PO
        purchaseOrderInfoVOList = purchaseOrderInfoVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getNote()))
                .filter(data -> StringUtils.isNotEmpty(data.getOrderLine()))
                .filter(data -> StringUtils.isNotEmpty(data.getOrderHeader()))
                .collect(Collectors.toList());

        // 根据note（要货计划号）分组
        Map<String, PurchaseOrderInfoVO> purchaseOrderInfoMap = purchaseOrderInfoVOList.stream()
                .collect(Collectors.toMap(PurchaseOrderInfoVO::getNote, Function.identity(), (v1, v2) -> v1));

        Set<String> ids = purchaseOrderInfoMap.keySet();

        // 获取对应要货计划
        List<MaterialPlanNeedVO> materialPlanNeedVOList =
                materialPlanNeedService.selectByParams(ImmutableMap.of("ids",ids ));

        // 获取对应的到货跟踪
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList =
                materialArrivalTrackingService.selectByParams(ImmutableMap.of("sourceIds", ids));

        List<MaterialPlanNeedDTO> updateNeedList = new ArrayList<>();
        List<MaterialArrivalTrackingDTO> updateTrackingList = new ArrayList<>();

        // 赋值 PO号 和 PO行号
        for (MaterialPlanNeedVO materialPlanNeedVO : materialPlanNeedVOList) {
            PurchaseOrderInfoVO purchaseOrderInfoVO = purchaseOrderInfoMap.get(materialPlanNeedVO.getId());
            if (Objects.nonNull(purchaseOrderInfoVO)){
                updateNeedList.add(MaterialPlanNeedDTO.builder()
                        .id(materialPlanNeedVO.getId())
                        .purchaseOrderCode(purchaseOrderInfoVO.getOrderHeader())
                        .purchaseOrderLineCode(purchaseOrderInfoVO.getOrderLine())
                        .build());
            }
        }

        // 赋值 PO号 和 PO行号
        for (MaterialArrivalTrackingVO materialArrivalTrackingVO : materialArrivalTrackingVOList) {
            PurchaseOrderInfoVO purchaseOrderInfoVO = purchaseOrderInfoMap.get(materialArrivalTrackingVO.getSourceId());
            if (Objects.nonNull(purchaseOrderInfoVO)){
                updateTrackingList.add(MaterialArrivalTrackingDTO.builder()
                        .id(materialArrivalTrackingVO.getId())
                        .purchaseOrderCode(purchaseOrderInfoVO.getOrderHeader())
                        .purchaseOrderLineCode(purchaseOrderInfoVO.getOrderLine())
                        .build());
            }
        }

        if (CollectionUtils.isNotEmpty(updateNeedList)){
            com.google.common.collect.Lists.partition(updateNeedList, 500).forEach(materialPlanNeedService::doUpdateBatchSelective);
        }

        if (CollectionUtils.isNotEmpty(updateTrackingList)){
            com.google.common.collect.Lists.partition(updateTrackingList, 500).forEach(materialArrivalTrackingService::doUpdateBatchSelective);
        }

    }

    @Override
    public void doClosePoJob(Integer moveMinute) {
        log.info("PO关闭开始");
        // 36小时前
        Date startModifyTime = DateUtils.moveHour(new Date(), -36);
        // 获取采购订单数据
        List<PurchaseOrderInfoVO> purchaseOrderList = this.selectByParams(ImmutableMap.of("startModifyTime", startModifyTime));
        // 过滤出需要关闭的数据
        purchaseOrderList = purchaseOrderList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getOrderHeader()) && StringUtils.isNotEmpty(data.getOrderLine()))
                .filter(data -> data.getLineStatus().contains("CLOSED"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(purchaseOrderList)) {
            log.info("无可用关闭数据");
            return;
        }
        // 收集 PO + PO行
        List<String> combineKeys03 = purchaseOrderList.stream()
                .map(data -> String.join("_", data.getOrderHeader(), data.getOrderLine()))
                .distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(combineKeys03)){
            return;
        }

        // 获取需要关闭的到货跟踪数据
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS =
                materialArrivalTrackingService.selectByParams(ImmutableMap.of("combineKeys03", combineKeys03));

        // 赋值到货跟踪状态
        List<MaterialArrivalTrackingDTO> updateList = materialArrivalTrackingVOS.stream()
                .map(data -> MaterialArrivalTrackingDTO.builder()
                        .id(data.getId())
                        .arrivalStatus(ArrivalStatusEnum.CLOSE.getCode())
                        .remark("PO关闭")
                        .build()).collect(Collectors.toList());

        // 修改到货跟踪
        if (CollectionUtils.isNotEmpty(updateList)){
            com.google.common.collect.Lists.partition(updateList, 500).forEach(materialArrivalTrackingService::doUpdateBatchSelective);
        }
    }



    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<PurchaseOrderInfoVO> invocation(List<PurchaseOrderInfoVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
