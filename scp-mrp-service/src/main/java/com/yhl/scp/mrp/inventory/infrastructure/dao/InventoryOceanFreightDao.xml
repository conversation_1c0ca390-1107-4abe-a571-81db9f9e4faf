<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryOceanFreightDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPO">
        <!--@Table mrp_inventory_ocean_freight-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="bl_num" jdbcType="VARCHAR" property="blNum"/>
        <result column="box_num" jdbcType="VARCHAR" property="boxNum"/>
        <result column="carrier_code" jdbcType="VARCHAR" property="carrierCode"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,bl_num,box_num,creator,create_time,modifier,version_value,modify_time,enabled,end_time,carrier_code
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.blNum != null and params.blNum != ''">
                and bl_num = #{params.blNum,jdbcType=VARCHAR}
            </if>
            <if test="params.boxNum != null and params.boxNum != ''">
                and box_num = #{params.boxNum,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.carrierCode != null and params.carrierCode != ''">
                and carrier_code = #{params.carrierCode,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_inventory_ocean_freight
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_inventory_ocean_freight
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mrp_inventory_ocean_freight
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_inventory_ocean_freight
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectByOceanFreightIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_inventory_ocean_freight
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                <if test="item.scssInventoryOceanFreight.billNo != null and item.scssInventoryOceanFreight.billNo != ''">
                    bl_num = #{item.scssInventoryOceanFreight.billNo,jdbcType=VARCHAR} and
                </if>
                <if test="item.scssInventoryOceanFreight.containerNo != null and item.scssInventoryOceanFreight.containerNo != ''">
                    box_num = #{item.scssInventoryOceanFreight.containerNo,jdbcType=VARCHAR} and
                </if>
                 carrier_code = #{item.scssInventoryOceanFreight.carrier.code,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_inventory_ocean_freight(
        id,
        bl_num,
        box_num,
        carrier_code,
        creator,
        create_time,
        modifier,
        version_value,
        modify_time,
        enabled,
        end_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{blNum,jdbcType=VARCHAR},
        #{boxNum,jdbcType=VARCHAR},
        #{carrierCode,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{endTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPO">
        insert into mrp_inventory_ocean_freight(
        id,
        bl_num,
        box_num,
        carrier_code,
        creator,
        create_time,
        modifier,
        version_value,
        modify_time,
        enabled,
        end_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{blNum,jdbcType=VARCHAR},
        #{boxNum,jdbcType=VARCHAR},
        #{carrierCode,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{endTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_inventory_ocean_freight(
        id,
        bl_num,
        box_num,
        carrier_code,
        creator,
        create_time,
        modifier,
        version_value,
        modify_time,
        enabled,
        end_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.blNum,jdbcType=VARCHAR},
        #{entity.boxNum,jdbcType=VARCHAR},
        #{entity.carrierCode,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.endTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_inventory_ocean_freight(
        id,
        bl_num,
        box_num,
        carrier_code,
        creator,
        create_time,
        modifier,
        version_value,
        modify_time,
        enabled,
        end_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.blNum,jdbcType=VARCHAR},
        #{entity.boxNum,jdbcType=VARCHAR},
        #{entity.carrierCode,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.endTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPO">
        update mrp_inventory_ocean_freight set
        bl_num = #{blNum,jdbcType=VARCHAR},
        box_num = #{boxNum,jdbcType=VARCHAR},
        carrier_code = #{carrierCode,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        enabled = #{enabled,jdbcType=VARCHAR},
        end_time = #{endTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOceanFreightPO">
        update mrp_inventory_ocean_freight
        <set>
            <if test="item.blNum != null and item.blNum != ''">
                bl_num = #{item.blNum,jdbcType=VARCHAR},
            </if>
            <if test="item.boxNum != null and item.boxNum != ''">
                box_num = #{item.boxNum,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierCode != null and item.carrierCode != ''">
                carrier_code = #{item.carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_inventory_ocean_freight
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="bl_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.blNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.boxNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carrier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carrierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mrp_inventory_ocean_freight 
        <set>
            <if test="item.blNum != null and item.blNum != ''">
                bl_num = #{item.blNum,jdbcType=VARCHAR},
            </if>
            <if test="item.boxNum != null and item.boxNum != ''">
                box_num = #{item.boxNum,jdbcType=VARCHAR},
            </if>
            <if test="item.carrierCode != null and item.carrierCode != ''">
                carrier_code = #{item.carrierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_inventory_ocean_freight where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_inventory_ocean_freight where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
