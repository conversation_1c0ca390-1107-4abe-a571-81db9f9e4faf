package com.yhl.scp.mrp.supplier.convertor;

import com.yhl.scp.mds.extension.supplier.domain.entity.SupplierPurchaseRatioDO;
import com.yhl.scp.mds.extension.supplier.dto.SupplierPurchaseRatioDTO;
import com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPurchaseRatioPO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>SupplierPurchaseRatioConvertor</code>
 * <p>
 * 供应商采购比例转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-26 00:14:49
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, builder = @org.mapstruct.Builder(disableBuilder = true))
public interface SupplierPurchaseRatioConvertor {

    SupplierPurchaseRatioConvertor INSTANCE = Mappers.getMapper(SupplierPurchaseRatioConvertor.class);

    SupplierPurchaseRatioDO dto2Do(SupplierPurchaseRatioDTO obj);

    SupplierPurchaseRatioDTO do2Dto(SupplierPurchaseRatioDO obj);

    List<SupplierPurchaseRatioDO> dto2Dos(List<SupplierPurchaseRatioDTO> list);

    List<SupplierPurchaseRatioDTO> do2Dtos(List<SupplierPurchaseRatioDO> list);

    SupplierPurchaseRatioVO do2Vo(SupplierPurchaseRatioDO obj);

    SupplierPurchaseRatioVO po2Vo(SupplierPurchaseRatioPO obj);

    List<SupplierPurchaseRatioVO> po2Vos(List<SupplierPurchaseRatioPO> list);

    SupplierPurchaseRatioPO do2Po(SupplierPurchaseRatioDO obj);

    SupplierPurchaseRatioDO po2Do(SupplierPurchaseRatioPO obj);

    SupplierPurchaseRatioPO dto2Po(SupplierPurchaseRatioDTO obj);

    List<SupplierPurchaseRatioPO> dto2Pos(List<SupplierPurchaseRatioDTO> obj);

    List<SupplierPurchaseRatioDTO> pos2Dtos(List<SupplierPurchaseRatioPO> obj);

}
