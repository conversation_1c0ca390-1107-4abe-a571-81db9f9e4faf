package com.yhl.scp.mrp.selfSufficient.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialSelfSufficientRequirementDetailDO</code>
 * <p>
 * 自给件需求明细DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-11 09:39:50
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialSelfSufficientRequirementDetailDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 413214888999669350L;

    /**
     * 主键
     */
    private String id;
    /**
     * 组织
     */
    private String stockPointCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 物料名称
     */
    private String productName;
    /**
     * 供应商ID
     */
    private String supplierId;
    /**
     * 物料分类
     */
    private String productClassify;
    /**
     * 要货日期
     */
    private Date needDate;
    /**
     * 要货数量
     */
    private BigDecimal needQuantity;
    /**
     * 单位
     */
    private String unit;
    /**
     * 版本
     */
    private Integer versionValue;

}
