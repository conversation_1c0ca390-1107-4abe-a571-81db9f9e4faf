package com.yhl.scp.mrp.material.plan.service.impl;

import cn.hutool.core.date.StopWatch;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.enums.TransferStatusEnum;
import com.yhl.scp.mrp.material.plan.domain.service.GlassInventoryShiftDataPublishedDomainService;
import com.yhl.scp.mrp.material.plan.domain.service.GlassInventoryShiftDetailPublishedDomainService;
import com.yhl.scp.mrp.material.plan.enums.MaterialTypeEnum;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.*;
import com.yhl.scp.mrp.material.plan.infrastructure.po.*;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftPublishService;
import com.yhl.scp.mrp.material.plan.service.utils.GlassMrpUtil;
import com.yhl.scp.mrp.published.domain.service.MaterialPlanInventoryOccupyPublishedDomainService;
import com.yhl.scp.mrp.published.domain.service.MaterialPlanReplacePublishedDomainService;
import com.yhl.scp.mrp.published.infrastructure.dao.MaterialPlanInventoryOccupyPublishedDao;
import com.yhl.scp.mrp.published.infrastructure.dao.MaterialPlanPublishedVersionDao;
import com.yhl.scp.mrp.published.infrastructure.dao.MaterialPlanReplacePublishedDao;
import com.yhl.scp.mrp.published.infrastructure.po.MaterialPlanInventoryOccupyPublishedPO;
import com.yhl.scp.mrp.published.infrastructure.po.MaterialPlanPublishedVersionPO;
import com.yhl.scp.mrp.published.infrastructure.po.MaterialPlanReplacePublishedPO;
import com.yhl.scp.mrp.published.service.MaterialPlanPublishedVersionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <code>MaterialPlanPublishServiceImpl</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-21 23:03:34
 */
@Service
public class GlassInventoryShiftPublishServiceImpl implements GlassInventoryShiftPublishService {
    private static final Logger log = LoggerFactory.getLogger(GlassInventoryShiftPublishServiceImpl.class);
    @Resource
    GlassInventoryShiftDataDao glassInventoryShiftDataDao;
    @Resource
    GlassInventoryShiftDetailDao glassInventoryShiftDetailDao;
    @Resource
    MaterialPlanInventoryOccupyDao materialPlanInventoryOccupyDao;
    @Resource
    MaterialPlanReplaceDao materialPlanReplaceDao;
    @Resource
    MaterialPlanTransferDao materialPlanTransferDao;


    @Resource
    MaterialPlanPublishedVersionService materialPlanPublishedVersionService;
    @Resource
    GlassInventoryShiftDataPublishedDao glassInventoryShiftDataPublishedDao;
    @Resource
    GlassInventoryShiftDetailPublishedDao glassInventoryShiftDetailPublishedDao;
    @Resource
    MaterialPlanPublishedVersionDao materialPlanPublishedVersionDao;
    @Resource
    MaterialPlanReplacePublishedDao materialPlanReplacePublishedDao;
    @Resource
    MaterialPlanInventoryOccupyPublishedDao materialPlanInventoryOccupyPublishedDao;

    @Resource
    private GlassInventoryShiftDataPublishedDomainService glassInventoryShiftDataPublishedDomainService;
    @Resource
    private GlassInventoryShiftDetailPublishedDomainService glassInventoryShiftDetailPublishedDomainService;
    @Resource
    private MaterialPlanInventoryOccupyPublishedDomainService materialPlanInventoryOccupyPublishedDomainService;
    @Resource
    private MaterialPlanReplacePublishedDomainService materialPlanReplacePublishedDomainService;


    @Resource
    NewMdsFeign mdsFeign;

    @Override
    public void doPublish() {
        StopWatch stopWatch = new StopWatch("原片发布");
        stopWatch.start("查询人员权限下的物料");
        Date now = new Date();
        // 查询计划员下的物料
        List<String> dynamicColumnParam = Lists.newArrayList("id", "product_code");
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("materialPlanner", SystemHolder.getUserId());
//        queryParams.put("materialPlanner", "8d62ab0f-018d-72b118fe-8a87cdd8-0029");
        queryParams.put("productType", ProductTypeEnum.P.getCode());
        queryParams.put("productClassify", "RA.A");
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(dynamicColumnParam)
                .queryParam(queryParams).build();

        List<NewProductStockPointVO> newProductStockPointVOList =
                mdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(newProductStockPointVOList)){
            throw new BusinessException("用户没有物料权限");
        }

        stopWatch.start("新增发布版本");
        List<String> originProductCodeList = newProductStockPointVOList.stream()
                .map(NewProductStockPointVO::getProductCode).distinct()
                .collect(Collectors.toList());
        List<String> productCodeList = new ArrayList<>(originProductCodeList);
        for (String productCode : originProductCodeList) {
            // productCode 第三位替换为*
            productCodeList.add(productCode.substring(0, 2) + "*" + productCode.substring(3));
        }


        MaterialPlanPublishedVersionPO planPublishedVersionPO = new MaterialPlanPublishedVersionPO();
        planPublishedVersionPO.setId(UUIDUtil.getUUID());
        String versionCode = materialPlanPublishedVersionService.getVersionCode(MaterialTypeEnum.GLASS.getCode());
        planPublishedVersionPO.setVersionCode(versionCode);
        planPublishedVersionPO.setMaterialType(MaterialTypeEnum.GLASS.getCode());
        BasePOUtils.insertFiller(planPublishedVersionPO, now);
        stopWatch.stop();

        // 库存推移物料数据
        stopWatch.start("查询并组装推移data数据");
        List<GlassInventoryShiftDataPO> glassInventoryShiftDataList = glassInventoryShiftDataDao.selectByParams(
                ImmutableMap.of("productCodeList", productCodeList));
        Map<String, String> glassInventoryShiftDataIdMap = new HashMap<>(glassInventoryShiftDataList.size());
        List<GlassInventoryShiftDataPublishedPO> glassInventoryShiftDataPublishedList = new ArrayList<>(glassInventoryShiftDataList.size());
        glassInventoryShiftDataList.forEach(t -> {
            GlassInventoryShiftDataPublishedPO inventoryShiftDataPublished = glassInventoryShiftDataPublishedDomainService.getGlassInventoryShiftDataPublishedPO(t);
            inventoryShiftDataPublished.setId(UUIDUtil.getUUID());
            inventoryShiftDataPublished.setMaterialPlanPublishedVersionId(planPublishedVersionPO.getId());
            glassInventoryShiftDataIdMap.put(t.getId(), inventoryShiftDataPublished.getId());
            glassInventoryShiftDataPublishedList.add(inventoryShiftDataPublished);
        });
        BasePOUtils.insertBatchFiller(glassInventoryShiftDataPublishedList, now);
        stopWatch.stop();

        // 物料库存明细数据
        stopWatch.start("查询并组装推移detail数据");
        List<String> inventoryShiftDataIds = glassInventoryShiftDataList.stream().map(GlassInventoryShiftDataPO::getId)
                .collect(Collectors.toList());
        List<GlassInventoryShiftDetailPO> glassInventoryShiftDetailList = glassInventoryShiftDetailDao.selectByParams(
                ImmutableMap.of("inventoryShiftDataIds", inventoryShiftDataIds));
        List<GlassInventoryShiftDetailPublishedPO> glassInventoryShiftDetailPublishedList = new ArrayList<>(glassInventoryShiftDetailList.size());
        Map<String, String> glassInventoryShiftDetailIdMap = new HashMap<>(glassInventoryShiftDataList.size());
        glassInventoryShiftDetailList.forEach(t -> {
            GlassInventoryShiftDetailPublishedPO inventoryShiftDetailPublishedPO = glassInventoryShiftDetailPublishedDomainService.getGlassInventoryShiftDetailPublishedPO(t);
            inventoryShiftDetailPublishedPO.setId(UUIDUtil.getUUID());
            glassInventoryShiftDetailIdMap.put(t.getId(), inventoryShiftDetailPublishedPO.getId());
            inventoryShiftDetailPublishedPO.setInventoryShiftDataId(glassInventoryShiftDataIdMap.get(t.getInventoryShiftDataId()));
            glassInventoryShiftDetailPublishedList.add(inventoryShiftDetailPublishedPO);
        });
        BasePOUtils.insertBatchFiller(glassInventoryShiftDetailPublishedList, now);
        stopWatch.stop();

        // 获取库存占用信息
        stopWatch.start("查询并组装库存占用信息数据");
        List<MaterialPlanInventoryOccupyPO> occupyList = materialPlanInventoryOccupyDao.selectByParams(ImmutableMap.of(
                "supplyProductCodeList", productCodeList));
        List<MaterialPlanInventoryOccupyPublishedPO> inventoryOccupyPublishedList = new ArrayList<>(occupyList.size());
        occupyList.forEach(t -> {
            MaterialPlanInventoryOccupyPublishedPO inventoryOccupyPublishedPO = materialPlanInventoryOccupyPublishedDomainService.getMaterialPlanInventoryOccupyPublishedPO(t);
            inventoryOccupyPublishedPO.setInventoryShiftDetailId(glassInventoryShiftDetailIdMap.get(t.getInventoryShiftDetailId()));
            inventoryOccupyPublishedPO.setMaterialPlanPublishedVersionId(planPublishedVersionPO.getId());
            inventoryOccupyPublishedList.add(inventoryOccupyPublishedPO);
        });
        BasePOUtils.insertBatchFiller(inventoryOccupyPublishedList, now);
        stopWatch.stop();

        // 替代计划
        stopWatch.start("查询并组装替代计划数据");
        List<MaterialPlanReplacePO> materialPlanReplacePOList = materialPlanReplaceDao.selectByProductCodes(productCodeList);
        List<MaterialPlanReplacePublishedPO> replacePlanList = new ArrayList<>(materialPlanReplacePOList.size());
        if (CollectionUtils.isEmpty(materialPlanReplacePOList)) {
            materialPlanReplacePOList.forEach(t -> {
                MaterialPlanReplacePublishedPO materialPlanReplacePublishedPO = materialPlanReplacePublishedDomainService.getMaterialPlanReplacePublishedPO(t);
                materialPlanReplacePublishedPO.setMaterialPlanPublishedVersionId(planPublishedVersionPO.getId());
                replacePlanList.add(materialPlanReplacePublishedPO);
            });
            BasePOUtils.insertBatchFiller(replacePlanList, now);
        }
        stopWatch.stop();

        stopWatch.start("查询库存点数据");
        Map<String, NewStockPointVO> stockPointVOMap = mdsFeign.selectAllStockPoint(SystemHolder.getScenario()).stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity()));
        stopWatch.stop();

        // 根据库存占用信息产生调拨计划
        // 未来时间的调拨计划：状态已发布调拨计划
        stopWatch.start("根据库存占用信息产生调拨计划");
        Map<String,MaterialPlanTransferPO> materialPlanTransferMap = Maps.newHashMap();
        List<MaterialPlanTransferPO> materialPlanTransferList = materialPlanTransferDao
                .selectByParams(ImmutableMap.of("transferDateDepartStart", DateUtils.getDayFirstTime(new Date()),
                        "transferStatus", TransferStatusEnum.PUBLISHED.getCode()));
        if (CollectionUtils.isNotEmpty(materialPlanTransferList)) {
            materialPlanTransferMap.putAll( materialPlanTransferList.stream()
                    .collect(Collectors.toMap(t -> String.join("&&", t.getProductCode(),
                            StringUtils.isEmpty(t.getCabinetNo()) ? "null" : t.getCabinetNo(),
                            DateUtils.dateToString(t.getTransferDateDepart()),t.getTransferRoutingId()), Function.identity())));
        }
        List<MaterialPlanTransferPO> materialPlanTransferCreateList = Lists.newArrayList();
        List<MaterialPlanTransferPO> materialPlanTransferUpdateList = Lists.newArrayList();
        // 从码头发出的调拨
        Predicate<MaterialPlanInventoryOccupyPublishedPO> portOccupyPredicate = t -> StockPointTypeEnum.MT.getCode()
                .equals(stockPointVOMap.get(t.getStockPointCodeFrom()).getStockPointType());
        List<MaterialPlanInventoryOccupyPublishedPO> portOccupyList =inventoryOccupyPublishedList.stream()
                .filter(portOccupyPredicate).collect(Collectors.toList());
        assembleTransfer(portOccupyList, materialPlanTransferMap, materialPlanTransferCreateList, materialPlanTransferUpdateList);
        stopWatch.stop();

        // 从浮法发出的调拨
        stopWatch.start("从浮法发出的调拨");
        Predicate<MaterialPlanInventoryOccupyPublishedPO> floatPredicate = t -> StockPointTypeEnum.FF.getCode()
                .equals(stockPointVOMap.get(t.getStockPointCodeFrom()).getStockPointType());
        List<MaterialPlanInventoryOccupyPublishedPO> floatOccupyList = inventoryOccupyPublishedList.stream().filter(floatPredicate).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(floatOccupyList)) {
            // 根据供货商配置重置浮法厂调拨日期
            resetTransferDateBySupplierConfig(stockPointVOMap, now, floatOccupyList);
            assembleTransfer(floatOccupyList, materialPlanTransferMap, materialPlanTransferCreateList, materialPlanTransferUpdateList);
        }
        stopWatch.stop();

        // 删除未发布的调拨计划
        stopWatch.start("持久化原片发布数据");
        materialPlanTransferDao.deleteUnpublishedBatch();
        if (CollectionUtils.isNotEmpty(materialPlanTransferCreateList)) {
            BasePOUtils.insertBatchFiller(materialPlanTransferCreateList, now);
            Lists.partition(materialPlanTransferCreateList, 1000).forEach(t -> materialPlanTransferDao.insertBatchWithPrimaryKey(t));
        }
        if (CollectionUtils.isNotEmpty(materialPlanTransferUpdateList)) {
            BasePOUtils.updateBatchFiller(materialPlanTransferUpdateList, now);
            Lists.partition(materialPlanTransferUpdateList, 1000).forEach(t -> materialPlanTransferDao.updateBatch(t));
        }

        materialPlanInventoryOccupyDao.updateBatch(occupyList);
        materialPlanPublishedVersionDao.insert(planPublishedVersionPO);

        Lists.partition(glassInventoryShiftDataPublishedList, 1000)
                .forEach(t->glassInventoryShiftDataPublishedDao.insertBatchWithPrimaryKey(t));

        Lists.partition(glassInventoryShiftDetailPublishedList, 1000)
                .forEach(t->glassInventoryShiftDetailPublishedDao.insertBatchWithPrimaryKey(t));

        Lists.partition(inventoryOccupyPublishedList, 1000)
                .forEach(list->materialPlanInventoryOccupyPublishedDao.insertBatch(list));
        if (CollectionUtils.isNotEmpty(replacePlanList)) {
            Lists.partition(replacePlanList, 1000)
                    .forEach(list->materialPlanReplacePublishedDao.insertBatch(list));
        }
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    private void resetTransferDateBySupplierConfig(Map<String, NewStockPointVO> stockPointVOMap,
                                                   Date now,
                                                   List<MaterialPlanInventoryOccupyPublishedPO> floatOccupyList) {
        // 浮法厂供应日历设置
        List<String> supplierIdList = stockPointVOMap.values().stream()
                .filter(t -> StockPointTypeEnum.FF.getCode().equals(t.getStockPointType()))
                .map(NewStockPointVO::getSupplierId)
                .collect(Collectors.toList());
        // 获取承运商数据
        List<SupplierVO> supplierList = mdsFeign.selectSupplierByPrimaryKeys(supplierIdList);
        Map<String, SupplierVO> supplierMap = supplierList.stream().collect(Collectors.toMap(SupplierVO::getId, Function.identity()));

        // 浮法厂的发货日期
        Map<String, List<Date>> ffTransferDateList = new HashMap<>();
        stockPointVOMap.values().stream().filter(t -> StockPointTypeEnum.FF.getCode().equals(t.getStockPointType())).forEach(t -> {
            String supplierId = t.getSupplierId();
            SupplierVO supplier = supplierMap.get(supplierId);
            if (null == supplier){
                return;
            }
            List<Date> transferDateList = new ArrayList<>();
            Date firstTransferDate = DateUtils.getDayFirstTime(now);
            addTransferDate(supplier, transferDateList, firstTransferDate, true);
            for (int i = 1; i < 365; i++) {
                Date transferDate = DateUtils.moveDay(firstTransferDate, i);
                addTransferDate(supplier, transferDateList, transferDate, false);
            }
            ffTransferDateList.put(t.getStockPointCode(), transferDateList);
        });

        ffTransferDateList.forEach((stockPointCode, transferDateList) -> {
            for (int i = 0; i < transferDateList.size()-1; i++) {
                Date transferDate = transferDateList.get(i);
                int nextIndex = i + 1;
                if (nextIndex >= transferDateList.size()) {
                    break;
                }
                Date transferDateNext = transferDateList.get(i + 1);
                for (MaterialPlanInventoryOccupyPublishedPO materialPlanInventoryOccupyPO : floatOccupyList) {
                    if (!stockPointCode.equals(materialPlanInventoryOccupyPO.getStockPointCodeFrom())) {
                        continue;
                    }
                    if (materialPlanInventoryOccupyPO.getTransportDateStart().before(transferDate)) {
                        continue;
                    }
                    if (!materialPlanInventoryOccupyPO.getTransportDateStart().before(transferDateNext)) {
                        continue;
                    }
                    int days = DateUtils.getDateInterval(materialPlanInventoryOccupyPO.getTransportDateStart(),
                            materialPlanInventoryOccupyPO.getTransportDateEnd()).intValue();
                    materialPlanInventoryOccupyPO.setTransportDateStart(transferDate);
                    materialPlanInventoryOccupyPO.setTransportDateEnd(DateUtils.moveDay(transferDate, days));
                }
            }
        });
    }

    private void addTransferDate(SupplierVO supplier, List<Date> transferDateList, Date firstTransferDate, boolean isFirst) {
        if (isFirst) {
            transferDateList.add(firstTransferDate);
        } else {
            if (StringUtils.isNotEmpty(supplier.getReceivingWeekDay())
                    && supplier.getReceivingWeekDay().contains(String.valueOf(DateUtils.getDayOfWeek(firstTransferDate)))){
                transferDateList.add(firstTransferDate);
            }
            if (StringUtils.isNotEmpty(supplier.getReceivingMonthDay())
                    && supplier.getReceivingMonthDay().contains(String.valueOf(DateUtils.getDayOfMonth(firstTransferDate)))) {
                transferDateList.add(firstTransferDate);
            }
        }
    }


    /**
     * 封装调拨计划
     *
     * @param occupyList
     * @param materialPlanTransferMap
     * @param materialPlanTransferCreateList
     * @param materialPlanTransferUpdateList
     */
    private void assembleTransfer(List<MaterialPlanInventoryOccupyPublishedPO> occupyList,
                                  Map<String, MaterialPlanTransferPO> materialPlanTransferMap,
                                  List<MaterialPlanTransferPO> materialPlanTransferCreateList,
                                  List<MaterialPlanTransferPO> materialPlanTransferUpdateList) {
        // 根据物料编码，调拨时间，运输路径分组
        Map<String, List<MaterialPlanInventoryOccupyPublishedPO>> inventoryOccupyGroup = occupyList.stream()
                .collect(Collectors.groupingBy(t -> String.join("&&", t.getSupplyProductCode(),
                        StringUtils.isEmpty(t.getContainerNumber()) ? "null" : t.getContainerNumber(),
                        DateUtils.dateToString(t.getTransportDateStart()), t.getTransferRoutingId())));

        inventoryOccupyGroup.forEach((k,v)->{
            // 调拨数量
            // 已存在的调拨计划
            MaterialPlanTransferPO materialPlanTransferExist = materialPlanTransferMap.get(k);
            // 如果挑拨计划不存在，则创建调拨计划
            if (materialPlanTransferExist == null) {
                createMaterialTransferPO(materialPlanTransferCreateList, v);
                v.forEach(t->t.setMaterialPlanTransferId(materialPlanTransferCreateList.get(materialPlanTransferCreateList.size()-1).getId()));
            } else {
                materialPlanTransferExist.setHistoryQuantity(materialPlanTransferExist.getTransferQuantity());
                materialPlanTransferExist.setAdjustQuantity(v.stream()
                        .map(MaterialPlanInventoryOccupyPublishedPO::getOccupyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                materialPlanTransferUpdateList.add(materialPlanTransferExist);
                v.forEach(t->t.setMaterialPlanTransferId(materialPlanTransferExist.getId()));
            }
        });
    }





    private void createMaterialTransferPO(List<MaterialPlanTransferPO> materialPlanTransferCreateList,
                                          List<MaterialPlanInventoryOccupyPublishedPO> occupyList) {
        MaterialPlanTransferPO materialPlanTransferPO = new MaterialPlanTransferPO();
        materialPlanTransferPO.setId(UUIDUtil.getUUID());
        materialPlanTransferPO.setInventoryType(occupyList.get(0).getInventoryType());
        materialPlanTransferPO.setStockPointCodeFrom(occupyList.get(0).getStockPointCodeFrom());
        materialPlanTransferPO.setStockPointCodeTo(occupyList.get(0).getStockPointCodeTo());
        materialPlanTransferPO.setProductCode(occupyList.get(0).getSupplyProductCode());
        materialPlanTransferPO.setDemandProductCode(occupyList.get(0).getDemandProductCode());
        materialPlanTransferPO.setTransferDateDepart(occupyList.get(0).getTransportDateStart());
        materialPlanTransferPO.setTransferDateArrive(occupyList.get(0).getTransportDateEnd());
        materialPlanTransferPO.setTransferQuantity(occupyList.stream()
                .map(MaterialPlanInventoryOccupyPublishedPO::getOccupyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
        materialPlanTransferPO.setTransferStatus(TransferStatusEnum.UN_PUBLISHED.getCode());
        materialPlanTransferPO.setTransferRoutingId(occupyList.get(0).getTransferRoutingId());
        materialPlanTransferPO.setStorageFlag(YesOrNoEnum.NO.getCode());
        materialPlanTransferPO.setCabinetNo(occupyList.get(0).getContainerNumber());
        materialPlanTransferPO.setPlanTransferNo(String.join("-",
                materialPlanTransferPO.getProductCode(),
                materialPlanTransferPO.getStockPointCodeFrom(),
                DateUtils.dateToString(materialPlanTransferPO.getTransferDateDepart(), "yyyy-MM-dd")));

        materialPlanTransferCreateList.add(materialPlanTransferPO);
    }
}

