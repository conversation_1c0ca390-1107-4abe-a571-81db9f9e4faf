package com.yhl.scp.mrp.originalFilm.infrastructure.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.ddd.BasePO;

/**
 * <code>OriginalFilmDemandConsultDetailPO</code>
 * <p>
 * 原片需求征询明细PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 10:05:38
 */
public class OriginalFilmDemandConsultDetailPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -65800944249233765L;

    /**
     * 汇总ID
     */
        
    private String consultSummaryId;
        
    /**
     * 需求来源类型（产能平衡转入，生产计划需求, 人工新增）
     */
    private String demandSourceType;
        
    /**
     * 需求来源的ID
     */
    private String demandSourceId;
        
    /**
     * 车型
     */
    private String vehicleModelCode;
        
    /**
     * 产品物料id
     */
    private String productId;

    /**
     * 原片物料编码
     */
    private String productCode;
        
    /**
     * 原片物料id
     */
    private String originalFilmProductId;
        
    /**
     * 需求数量
     */
    private BigDecimal demandedQuantity;
    
    /**
     * 需求日期
     */
    private Date demandedDate;
        
    /**
     * 浮法厂库存
     */
    private BigDecimal floatInventoryQuantity;
    
    /**
     * 港口在途库存（原片在途）
     */
    private BigDecimal portRoadInventory;
    
    /**
     * 港口库存(码头库存)
     */
    private BigDecimal portInventory;
    
    /**
     * 本厂库存
     */
    private BigDecimal factoryInventory;
    
    /**
     * 标准规格需求量（毛需求）
     */
    private BigDecimal standardDemandedQuantity;
    
    /**
     * 替代规格需求量（被作为替代料消耗量）
     */
    private BigDecimal usedAsReplaceQuantity;
    
    /**
     * 被替代量（消耗替代料量）
     */
    private BigDecimal useReplaceQuantity;
    
    /**
     * 净需求量=毛需求+替代规格需求量-库存数据(浮法厂库存,原片在途,码头库存,本厂库存,被替代量)
     */
    private BigDecimal netDemandedQuantity;

    /**
     * 毛胚规格
     */
    private String mpblSpec;
        
    /**
     * 版本号
     */
    private Integer versionValue;

    /**
     * 特殊要求
     */
    private String specialRequirements;

    /**
     * 面积
     */
    private BigDecimal area;

    /**
     * 重量
     */
    private BigDecimal weight;

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getConsultSummaryId() {
        return consultSummaryId;
    }

    public void setConsultSummaryId(String consultSummaryId) {
        this.consultSummaryId = consultSummaryId;
    }

    public String getDemandSourceType() {
        return demandSourceType;
    }

    public void setDemandSourceType(String demandSourceType) {
        this.demandSourceType = demandSourceType;
    }

    public String getDemandSourceId() {
        return demandSourceId;
    }

    public void setDemandSourceId(String demandSourceId) {
        this.demandSourceId = demandSourceId;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getOriginalFilmProductId() {
        return originalFilmProductId;
    }

    public void setOriginalFilmProductId(String originalFilmProductId) {
        this.originalFilmProductId = originalFilmProductId;
    }

    public BigDecimal getDemandedQuantity() {
        return demandedQuantity;
    }

    public void setDemandedQuantity(BigDecimal demandedQuantity) {
        this.demandedQuantity = demandedQuantity;
    }

    public Date getDemandedDate() {
        return demandedDate;
    }

    public void setDemandedDate(Date demandedDate) {
        this.demandedDate = demandedDate;
    }

    public BigDecimal getFloatInventoryQuantity() {
        return floatInventoryQuantity;
    }

    public void setFloatInventoryQuantity(BigDecimal floatInventoryQuantity) {
        this.floatInventoryQuantity = floatInventoryQuantity;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

	public BigDecimal getPortRoadInventory() {
		return portRoadInventory;
	}

	public void setPortRoadInventory(BigDecimal portRoadInventory) {
		this.portRoadInventory = portRoadInventory;
	}

	public BigDecimal getPortInventory() {
		return portInventory;
	}

	public void setPortInventory(BigDecimal portInventory) {
		this.portInventory = portInventory;
	}

	public BigDecimal getFactoryInventory() {
		return factoryInventory;
	}

	public void setFactoryInventory(BigDecimal factoryInventory) {
		this.factoryInventory = factoryInventory;
	}

	public BigDecimal getStandardDemandedQuantity() {
		return standardDemandedQuantity;
	}

	public void setStandardDemandedQuantity(BigDecimal standardDemandedQuantity) {
		this.standardDemandedQuantity = standardDemandedQuantity;
	}

	public BigDecimal getUsedAsReplaceQuantity() {
		return usedAsReplaceQuantity;
	}

	public void setUsedAsReplaceQuantity(BigDecimal usedAsReplaceQuantity) {
		this.usedAsReplaceQuantity = usedAsReplaceQuantity;
	}

	public BigDecimal getUseReplaceQuantity() {
		return useReplaceQuantity;
	}

	public void setUseReplaceQuantity(BigDecimal useReplaceQuantity) {
		this.useReplaceQuantity = useReplaceQuantity;
	}

	public BigDecimal getNetDemandedQuantity() {
		return netDemandedQuantity;
	}

	public void setNetDemandedQuantity(BigDecimal netDemandedQuantity) {
		this.netDemandedQuantity = netDemandedQuantity;
	}

    public String getSpecialRequirements() {
        return specialRequirements;
    }

    public void setSpecialRequirements(String specialRequirements) {
        this.specialRequirements = specialRequirements;
    }

    public String getMpblSpec() {
        return mpblSpec;
    }

    public void setMpblSpec(String mpblSpec) {
        this.mpblSpec = mpblSpec;
    }

    public BigDecimal getArea() {
        return area;
    }

    public void setArea(BigDecimal area) {
        this.area = area;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }
}
