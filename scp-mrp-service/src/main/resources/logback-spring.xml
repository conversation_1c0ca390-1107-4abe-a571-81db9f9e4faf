<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <!--<springProperty scope="context" name="APP_NAME" source="spring.application.name"/>-->
    <property name="APP_NAME" value="app"/>
    <!-- 基础配置 -->
    <property name="LOG_HOME" value="./logs"/>
    <property name="MAX_HISTORY" value="30"/>
    <property name="MAX_FILE_SIZE" value="50MB"/>
    <!-- 优化的日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([%t: ${PID:- }]){faint} %L - %clr(%logger){cyan}%clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <property name="FILE_LOG_PATTERN"
              value="%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} [%t: ${PID:- }] %L - %logger: %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <charset>UTF-8</charset>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

     <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}.log</file>
        <!-- 添加立即刷新 -->
        <immediateFlush>true</immediateFlush>
        <!-- 添加安全写入 -->
        <prudent>true</prudent>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>${FILE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 修改异步配置 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 降低队列大小 -->
        <queueSize>256</queueSize>
        <!-- 不丢弃日志 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 如果队列满了，阻塞而不是丢弃 -->
        <neverBlock>false</neverBlock>
        <!-- 包含调用者数据 -->
        <includeCallerData>true</includeCallerData>
        <!-- 设置最大刷新时间（毫秒） -->
        <maxFlushTime>1000</maxFlushTime>
        <appender-ref ref="FILE"/>
    </appender>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>