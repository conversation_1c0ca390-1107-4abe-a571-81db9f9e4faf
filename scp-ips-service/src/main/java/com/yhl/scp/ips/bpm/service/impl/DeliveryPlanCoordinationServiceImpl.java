package com.yhl.scp.ips.bpm.service.impl;

import com.google.common.collect.Lists;
import com.yhl.scp.ips.bpm.service.DeliveryPlanCoordinationService;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.rbac.service.UserService;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.IdentityService;
import org.flowable.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <code>DeliveryPlanCoordinationServiceImpl</code>
 * <p>
 * DeliveryPlanCoordinationServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-14 17:27:55
 */
@Slf4j
@Service("deliveryPlanCoordinationService")
public class DeliveryPlanCoordinationServiceImpl implements DeliveryPlanCoordinationService {

    @Autowired
    private IdentityService identityService;

    @Autowired
    private UserService userService;

    @Autowired
    private NewMdsFeign newMdsFeign;

    @Override
    public String getAssigneeByProductPermission(DelegateExecution execution) {
        String productCode = (String)execution.getVariable("productCode");
        String scenario = (String)execution.getVariable("scenario");
        Optional<NewProductStockPointVO> first = newMdsFeign.selectByProductCode(scenario, Lists.newArrayList(productCode))
                .stream().filter(x -> StringUtils.isNotBlank(x.getOrderPlanner())).findFirst();
        if (!first.isPresent()) {
            log.error("物品【{}】未维护订单计划员", productCode);
            return null;
        }
        String orderPlanner = first.get().getOrderPlanner();
        User user = userService.selectById(orderPlanner);
        if (user == null)  {
            log.error("系统用户【{}】不存在", orderPlanner);
            return null;
        }
        String staffCode = user.getStaffCode();
        org.flowable.idm.api.User user1 = identityService.createUserQuery().userId(staffCode).singleResult();
        if (user1 == null) {
            log.error("工作流用户【{}】不存在", staffCode);
            return null;
        }
        return staffCode;
    }

}
