package com.yhl.scp.ips.rbac.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.rbac.dao.TenantDao;
import com.yhl.scp.ips.rbac.entity.*;
import com.yhl.scp.ips.rbac.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>TenantServiceImpl</code>
 * <p>
 * TenantServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
@Service
public class TenantServiceImpl implements TenantService {

    @Autowired
    private TenantDao tenantDao;

    @Autowired
    private TenantModuleService tenantModuleService;
    @Autowired
    private TenantUserService tenantUserService;

    @Autowired
    private DeptService deptService;

    @Autowired
    private UserService userService;

    @Override
    public BaseResponse<String> doCreate(Tenant tenant) {
        if (StringUtils.isEmpty(tenant.getTenantName())) {
            return BaseResponse.error("租户名称不能为空!");
        }
        if (null != selectByTenantName(tenant.getTenantName())) {
            return BaseResponse.error("租户已存在!");
        }
        if (StringUtils.isEmpty(tenant.getId())) {
            tenant.setId(UUIDUtil.getUUID());
        }
        tenant.setEnabled(YesOrNoEnum.YES.getCode());
        tenantDao.insert(tenant);

        Dept dept = new Dept();
        dept.setParentId("1");
        dept.setDeptName(tenant.getTenantName());
        dept.setTenantRoot(YesOrNoEnum.YES.getCode());
        dept.setTenantId(tenant.getId());
        dept.setTenantCode(tenant.getTenantCode());
        deptService.doCreate(dept);

        List<String> modules = tenant.getModules();
        for (String module : modules) {
            TenantModule tenantModule = new TenantModule();
            tenantModule.setModuleCode(module);
            tenantModule.setTenantId(tenant.getId());
            tenantModuleService.doCreate(tenantModule);
        }
        if (!modules.contains(SystemModuleEnum.IPS.getCode())) {
            createDefaultTenant(tenant.getId());
        }

        //		List<String> userIds = tenant.getUserIds();
        //		for (String userId : userIds) {
        //			TenantUser tenantUser = new TenantUser();
        //			tenantUser.setTenantId(tenant.getId());
        //			tenantUser.setUserId(userId);
        //			tenantUser.setUserType(UserTypeEnum.TENANT_ADMIN.getCode());
        //			tenantUserService.doCreate(tenantUser);
        //		}
        return BaseResponse.success("租户创建成功");
    }

    @Override
    public BaseResponse<String> doUpdate(Tenant tenant) {
        Tenant checkTenant = selectByTenantName(tenant.getTenantName());
        if (null != checkTenant && !checkTenant.getId().equals(tenant.getId())) {
            return BaseResponse.error("租户名称不能重复，租户已存在!");
        }

        tenantModuleService.doDeleteByTenantId(tenant.getId());
        List<TenantModule> tenantModules = tenantModuleService.selectByTenantId(tenant.getId());
        List<String> moduleCodes = tenantModules.stream().map(TenantModule::getModuleCode).collect(Collectors.toList());
        List<String> modules = tenant.getModules();
        for (String module : modules) {
            if (moduleCodes.contains(module)) {
                continue;
            }
            TenantModule tenantModule = new TenantModule();
            tenantModule.setModuleCode(module);
            tenantModule.setTenantId(tenant.getId());
            tenantModuleService.doCreate(tenantModule);
        }
        if (!modules.contains(SystemModuleEnum.IPS.getCode())) {
            createDefaultTenant(tenant.getId());
        }

        //		tenantUserService.doDeleteByTenantId(tenant.getId());
        //		List<String> userIds = tenant.getUserIds();
        //		for (String userId : userIds) {
        //			TenantUser tenantUser = new TenantUser();
        //			tenantUser.setTenantId(tenant.getId());
        //			tenantUser.setUserId(userId);
        //			tenantUserService.doCreate(tenantUser);
        //		}

        tenantDao.updateByPrimaryKey(tenant);
        return BaseResponse.success("修改成功");
    }

    public void createDefaultTenant(String tenantId) {
        TenantModule ipsTenantModule = new TenantModule();
        ipsTenantModule.setModuleCode(SystemModuleEnum.IPS.getCode());
        ipsTenantModule.setTenantId(tenantId);
        tenantModuleService.doCreate(ipsTenantModule);
    }

    @Override
    public Tenant selectByTenantName(String tenantName) {
        return tenantDao.selectByTenantName(tenantName);
    }

    @Override
    public List<Tenant> selectByUserId(String userId) {
        List<Tenant> tenantList = tenantDao.selectByUserId(userId);
        //按照租户编码过滤并各自获取一条数据
        Map<String, Optional<Tenant>> collect = tenantList.stream()
                .collect(Collectors.groupingBy(Tenant::getTenantCode,
                        Collectors.reducing((a, b) -> a)));
        Collection<Optional<Tenant>> values = collect.values();
        tenantList = values.stream().filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        return tenantList;
    }

    @Override
    public List<Tenant> selectPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<Tenant> tenants = tenantDao.selectByPage(sortParam, queryCriteriaParam);

        List<String> tenantIds = tenants.stream().map(Tenant::getId).collect(Collectors.toList());
        List<TenantUser> tenantUsers = tenantUserService.selectByTenantIds(tenantIds);
        List<User> users = userService.selectAll();
        Map<String, User> userMap = users.stream().collect(Collectors.toMap(User::getId, v -> v));

        Map<String, List<String>> tenantUserMap = tenantUsers.stream().filter(item -> !StringUtils.isEmpty(item.getTenantId()))
                .collect(Collectors.groupingBy(TenantUser::getTenantId, Collectors.mapping(TenantUser::getUserId, Collectors.toList())));

        List<TenantModule> tenantModules = tenantModuleService.selectAll();
        Map<String, List<String>> moduleMap = tenantModules.stream().filter(item -> !StringUtils.isEmpty(item.getTenantId()))
                .collect(Collectors.groupingBy(TenantModule::getTenantId, Collectors.mapping(TenantModule::getModuleCode, Collectors.toList())));
        for (Tenant tenant : tenants) {
            List<String> userNames = new ArrayList<>();
            tenant.setModules(moduleMap.get(tenant.getId()));
            List<String> userIds = tenantUserMap.get(tenant.getId());
            if (!CollectionUtils.isEmpty(userIds)) {
                for (String userId : userIds) {
                    User user = userMap.get(userId);
                    if (null != user) {
                        userNames.add(user.getUserName());
                    }
                }
            }
            tenant.setUserNames(userNames);
        }
        return tenants;
    }

    @Override
    public Tenant getById(String id) {
        Tenant tenant = tenantDao.selectByPrimaryKey(id);
//        List<TenantUser> tenantUsers = tenantUserService.selectTenantAdminByTenantId(id);
//        tenant.setUserIds(tenantUsers.stream().map(TenantUser::getUserId).distinct().collect(Collectors.toList()));
//        List<TenantModule> tenantModules = tenantModuleService.selectByTenantId(id);
//        tenant.setModules(tenantModules.stream().map(TenantModule::getModuleCode).distinct().collect(Collectors.toList()));
        return tenant;
    }

    @Override
    public List<Tenant> selectAll() {
        return tenantDao.selectAll();
    }

}