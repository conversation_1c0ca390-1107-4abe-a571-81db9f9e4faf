package com.yhl.scp.ips.log.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.log.convertor.ExcelImportLogConvertor;
import com.yhl.scp.ips.log.domain.entity.ExcelImportLogDO;
import com.yhl.scp.ips.log.domain.service.ExcelImportLogDomainService;
import com.yhl.scp.ips.log.dto.ExcelImportLogDTO;
import com.yhl.scp.ips.log.infrastructure.dao.ExcelImportLogDao;
import com.yhl.scp.ips.log.infrastructure.po.ExcelImportLogPO;
import com.yhl.scp.ips.log.service.ExcelImportLogService;
import com.yhl.scp.ips.log.vo.ExcelImportLogVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>ExcelImportLogServiceImpl</code>
 * <p>
 * Excel导入日志应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-17 13:49:15
 */
@Slf4j
@Service
public class ExcelImportLogServiceImpl extends AbstractService implements ExcelImportLogService {

    @Resource
    private ExcelImportLogDao excelImportLogDao;

    @Resource
    private ExcelImportLogDomainService excelImportLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(ExcelImportLogDTO excelImportLogDTO) {
        // 0.数据转换
        ExcelImportLogDO excelImportLogDO = ExcelImportLogConvertor.INSTANCE.dto2Do(excelImportLogDTO);
        ExcelImportLogPO excelImportLogPO = ExcelImportLogConvertor.INSTANCE.dto2Po(excelImportLogDTO);
        // 1.数据校验
        excelImportLogDomainService.validation(excelImportLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(excelImportLogPO);
        excelImportLogDao.insert(excelImportLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(ExcelImportLogDTO excelImportLogDTO) {
        // 0.数据转换
        ExcelImportLogDO excelImportLogDO = ExcelImportLogConvertor.INSTANCE.dto2Do(excelImportLogDTO);
        ExcelImportLogPO excelImportLogPO = ExcelImportLogConvertor.INSTANCE.dto2Po(excelImportLogDTO);
        // 1.数据校验
        excelImportLogDomainService.validation(excelImportLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(excelImportLogPO);
        excelImportLogDao.update(excelImportLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ExcelImportLogDTO> list) {
        List<ExcelImportLogPO> newList = ExcelImportLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        excelImportLogDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<ExcelImportLogDTO> list) {
        List<ExcelImportLogPO> newList = ExcelImportLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        excelImportLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return excelImportLogDao.deleteBatch(idList);
        }
        return excelImportLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ExcelImportLogVO selectByPrimaryKey(String id) {
        ExcelImportLogPO po = excelImportLogDao.selectByPrimaryKey(id);
        return ExcelImportLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "EXCEL_IMPORT_LOG")
    public List<ExcelImportLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "EXCEL_IMPORT_LOG")
    public List<ExcelImportLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ExcelImportLogVO> dataList = excelImportLogDao.selectByCondition(sortParam, queryCriteriaParam);
        ExcelImportLogServiceImpl target = springBeanUtils.getBean(ExcelImportLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ExcelImportLogVO> selectByParams(Map<String, Object> params) {
        List<ExcelImportLogPO> list = excelImportLogDao.selectByParams(params);
        return ExcelImportLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ExcelImportLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<ExcelImportLogVO> selectParentLog() {
        return excelImportLogDao.selectParentLog();
    }

    @Override
    public List<ExcelImportLogVO> selectByParentId(String parentId) {
        return excelImportLogDao.selectByParentId(parentId);
    }

    @Override
    public String getObjectType() {
        // return ObjectTypeEnum.EXCEL_IMPORT_LOG.getCode();
        return "EXCEL_IMPORT_LOG";
    }

    @Override
    public List<ExcelImportLogVO> invocation(List<ExcelImportLogVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

}