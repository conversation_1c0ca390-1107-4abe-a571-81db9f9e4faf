package com.yhl.scp.ips.bpm.controller;

import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.enums.MinoBusinessEnum;
import com.yhl.scp.biz.common.util.MinioUtils;
import com.yhl.scp.ips.common.SystemHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <code>BpmAttachmentController</code>
 * <p>
 * BPM流程附件管理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-05-16 18:05:17
 */

@Api(tags = "BPM流程附件管理")
@RestController
@RequestMapping(value = "bpmAttachment")
@Slf4j
public class BpmAttachmentController extends BaseController {

    @Value("${sftp.host}")
    private String host;

    @Value("${sftp.port}")
    private int port;

    @Value("${sftp.username}")
    private String username;

    @Value("${sftp.password}")
    private String password;

    @Value("${sftp.bpm-path}")
    private String bpmPath;

    @Value("${minio.bucket}")
    private String bucketName;

    @Resource
    private MinioUtils minioUtils;

    @ApiOperation("流程附件上传")
    @PostMapping("upload")
    public BaseResponse<String> upload(@RequestPart MultipartFile file,
                                       @RequestParam(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("attachment file upload ...");
            long time1 = System.currentTimeMillis();
            if (file == null) {
                return new BaseResponse<>("文件不能为空");
            }
            String filePath = minioUtils.upload(bucketName, SystemHolder.getScenario(),
                    file, SystemHolder.getCnName(), SystemHolder.getUserName() + "#" + processInstanceId,
                    MinoBusinessEnum.BPM_ATTACHMENT.getCode());
            long time2 = System.currentTimeMillis();
            log.info("attachment upload took {} ms", time2 - time1);
            return new BaseResponse<>(Boolean.TRUE, filePath);
        } catch (Exception e) {
            log.error("Fail to upload process attachment file, ", e);
            return new BaseResponse<>("上传流程附件失败");
        }
    }

    @ApiOperation("流程附件下载")
    @GetMapping("download")
    public void download(@RequestParam(value = "filePath") String filePath) {
        try {
            log.info("attachment file download ...");
            minioUtils.download(bucketName, filePath, response);
        } catch (Exception e) {
            log.error("Fail to download process attachment file, ", e);
        }
    }

    @ApiOperation("流程附件删除")
    @PostMapping("delete")
    public BaseResponse<Void> delete(@RequestParam(value = "filePath") String filePath) {
        try {
            log.info("attachment file delete ...");
            minioUtils.remove(bucketName, filePath);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to delete attachment file, ", e);
            return new BaseResponse<>("流程附件删除失败");
        }
    }

}