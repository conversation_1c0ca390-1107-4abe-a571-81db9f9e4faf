package com.yhl.scp.ips.rbac.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.common.enums.UserTypeEnum;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.config.CollectionCacheInit;
import com.yhl.scp.ips.rbac.dao.RoleDao;
import com.yhl.scp.ips.rbac.dao.RoleResourceDao;
import com.yhl.scp.ips.rbac.dao.UserRoleDao;
import com.yhl.scp.ips.rbac.entity.Role;
import com.yhl.scp.ips.rbac.entity.RoleResource;
import com.yhl.scp.ips.rbac.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <code>RoleServiceImpl</code>
 * <p>
 * RoleServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
@Service
public class RoleServiceImpl implements RoleService {

    @Autowired
    private RoleDao roleDao;
    @Autowired
    private UserRoleDao userRoleDao;
    @Autowired
    private RoleResourceDao roleResourceDao;
    @Resource
    private CollectionCacheInit collectionCacheInit;

    @Override
    public BaseResponse doCreate(Role role) {
        if (StringUtils.isEmpty(role.getRoleName())) {
            return BaseResponse.error("角色名称不能为空!");
        }
        Role checkRole = roleDao.selectByRoleName(role.getRoleName(), SystemHolder.getTenantId());
        if (null != checkRole) {
            return BaseResponse.error("角色已存在");
        }
        if (StringUtils.isEmpty(role.getId())) {
            role.setId(UUIDUtil.getUUID());
        }
        if (!UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType())) {
            role.setTenantId(SystemHolder.getTenantId());
        }
        role.setCreator(SystemHolder.getUserId());
        roleDao.insert(role);
        return BaseResponse.success("创建角色成功");
    }

    @Override
    public List<Role> selectPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return roleDao.selectByCondition(sortParam, queryCriteriaParam, SystemHolder.getTenantId());
    }

    @Override
    public void doDelete(String roleId) {
        roleDao.deleteByPrimaryKey(roleId);
        userRoleDao.deleteByRoleId(roleId);
    }

    @Override
    public void doCreateRoleResources(String roleId, List<String> resourceIdList) {
        roleResourceDao.deleteByRoleId(roleId);
        for (String resourceId : resourceIdList) {
            RoleResource roleResource = new RoleResource();
            roleResource.setRoleId(roleId);
            roleResource.setResourceId(resourceId);
            roleResourceDao.insert(roleResource);
        }

    }

    @Override
    public List<Role> selectRoles() {
        if (UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType())) {
            return roleDao.selectForAdmin();
        } else {
            return roleDao.selectByTenantId(SystemHolder.getTenantId());
        }

    }

    @Override
    public Role getById(String roleId) {
        return roleDao.selectByPrimaryKey(roleId);
    }

    @Override
    public BaseResponse doUpdate(Role role) {
        if (!UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType())) {
            role.setTenantId(SystemHolder.getTenantId());
        }
        roleDao.updateByPrimaryKey(role);
        return BaseResponse.success("修改成功");
    }

    @Override
    public List<Role> selectByIds(List<String> roleIds) {
        return roleDao.selectByIds(roleIds);
    }

    @Override
    public List<Role> selectByUserId(String userId) {
        return roleDao.selectByUserId(userId);
    }

    @Override
    public List<Role> selectByName(String name) {
        return roleDao.selectByName(name);
    }

    @Override
    public boolean whetherOpenWorkBench() {
        List<CollectionValueVO> roleTypeValues = collectionCacheInit.getByCollectionCode("ROLE_TYPE_NAME").getData();
        if (CollectionUtils.isEmpty(roleTypeValues)) {
            throw new BusinessException("ROLE_TYPE_NAME值集未配置");
        }

        List<String> allowedRoleNames = roleTypeValues.stream()
                .map(CollectionValueVO::getValueMeaning)
                .collect(Collectors.toList());

        String userId = SystemHolder.getUserId();
        List<Role> userRoles = roleDao.selectByUserId(userId);

        return userRoles.stream()
                .map(Role::getRoleName)
                .anyMatch(allowedRoleNames::contains);
    }
}
