<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.rbac.dao.UserScenarioDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.rbac.entity.UserScenario">
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="scenario_id" jdbcType="VARCHAR" property="scenarioId"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.ips.rbac.vo.UserScenarioVO">
        <result column="scenario_name" jdbcType="VARCHAR" property="scenarioName"/>
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode"/>
        <result column="data_base_name" jdbcType="VARCHAR" property="dataBaseName"/>
    </resultMap>

    <select id="selectByParams" resultMap="BaseResultMap">
        select
        user_id, scenario_id
        from auth_rbac_user_scenario
        <where>
            <if test="params.userId != null and params.userId != ''">
                and user_id = #{params.userId,jdbcType=VARCHAR}
            </if>
            <if test="params.scenarioId != null and params.scenarioId != ''">
                and scenario_id = #{params.scenarioId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="com.yhl.scp.ips.rbac.entity.UserScenario">
        insert into auth_rbac_user_scenario (user_id, scenario_id)
        values (#{userId,jdbcType=VARCHAR}, #{scenarioId,jdbcType=VARCHAR})
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into auth_rbac_user_scenario(user_id, scenario_id)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.userId,jdbcType=VARCHAR},
            #{entity.scenarioId,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="deleteByUserId" parameterType="java.lang.String">
        delete
        from auth_rbac_user_scenario
        where user_id = #{userId,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByUserIdAndModuleCode" parameterType="java.lang.String">
        delete
        from auth_rbac_user_scenario us
        where us.user_id = #{userId,jdbcType=VARCHAR}
          and us.scenario_id in (select s.id from auth_scenario s where s.module_code = #{moduleCode,jdbcType=VARCHAR})
    </delete>

    <delete id="deleteByScenarioId" parameterType="java.lang.String">
        delete
        from auth_rbac_user_scenario
        where scenario_id = #{scenarioId,jdbcType=VARCHAR}
    </delete>

    <select id="selectVOByUserIdsAndModuleCode" resultMap="VOResultMap">
        select
        us.user_id, us.scenario_id, s.scenario_name, s.module_code, s.data_base_name
        from auth_rbac_user_scenario us left join auth_scenario s on s.id = us.scenario_id
        <where>
            <if test="tenantId != null and tenantId != ''">
                and s.tenant_id = #{tenantId,jdbcType=VARCHAR}
            </if>
            <if test="moduleCode != null and moduleCode != ''">
                and s.module_code = #{moduleCode,jdbcType=VARCHAR}
            </if>
            <if test="userIds != null and userIds.size() > 0">
                and us.user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            and s.id is not null
        </where>
    </select>
</mapper>