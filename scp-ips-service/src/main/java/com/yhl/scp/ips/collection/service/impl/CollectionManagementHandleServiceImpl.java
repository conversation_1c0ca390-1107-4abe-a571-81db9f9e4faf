package com.yhl.scp.ips.collection.service.impl;

import com.yhl.scp.ips.collection.domain.service.CollectionManagementDomainService;
import com.yhl.scp.ips.collection.infrastructure.dao.CollectionManagementDao;
import com.yhl.scp.ips.collectionManagement.service.CollectionManagementHandleService;
import com.yhl.scp.ips.common.dto.RemoveVersionDTO;
import com.yhl.scp.ips.config.CollectionCacheInit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName CollectionManagementHandleServiceImpl
 * @Description TODO
 * @Date 2024-09-06 14:17:34
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Slf4j
@Service
public class CollectionManagementHandleServiceImpl implements CollectionManagementHandleService {

    @Resource
    private CollectionManagementDao collectionManagementDao;

    @Resource
    private CollectionManagementDomainService collectionManagementDomainService;

    @Resource
    private CollectionCacheInit collectionCacheInit;

    @Override
    public int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOList) {
        collectionManagementDomainService.checkDelete(removeVersionDTOList);
        if (CollectionUtils.isNotEmpty(removeVersionDTOList)) {
            collectionCacheInit.deleteByCollectionIds(removeVersionDTOList.stream().map(RemoveVersionDTO::getId).collect(Collectors.toList()));
        }
        return collectionManagementDao.deleteBatchVersion(removeVersionDTOList);
    }
}
