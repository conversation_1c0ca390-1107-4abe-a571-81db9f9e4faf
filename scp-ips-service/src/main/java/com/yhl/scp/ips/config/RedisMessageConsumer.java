package com.yhl.scp.ips.config;

import cn.hutool.json.JSONUtil;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.log.infrastructure.dao.BusinessMonitorLogDao;
import com.yhl.scp.ips.log.infrastructure.po.BusinessMonitorLogPO;
import com.yhl.scp.ips.log.service.UserTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>RedisMessageConsumer</code>
 * <p>
 * RedisMessageConsumer
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-20 21:39:51
 */
@Order(999)
@Component
@Slf4j
public class RedisMessageConsumer {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private BusinessMonitorLogDao businessMonitorLogDao;

    @Resource
    private UserTaskService userTaskService;

    @Scheduled(fixedRate = 10000)
    // 每10秒检查一次队列
    public void consumeMessage() {
        String queueName = RedisKeyManageEnum.BUSINESS_MONITOR_LOG_QUEUE.getKey();
        String message = redisTemplate.opsForList().leftPop(queueName);
        if (message != null) {
            //message 去除反斜杠
            message = message.replace("\\", "");
            //message 去除前后号
            message = message.substring(1, message.length() - 1);

            log.info("%%%%%%%%%%%%  Received message: {}", message);
            BusinessMonitorLogPO businessMonitorLogPO = JSONUtil.toBean(message, BusinessMonitorLogPO.class);
            businessMonitorLogPO.setId(UUIDUtil.getUUID());
            businessMonitorLogDao.insert(businessMonitorLogPO);
            if ("SUCCESS".equals(businessMonitorLogPO.getOperationStatus())) {
                log.info("用户任务更新开始：userId:{}, businessCode:{}", businessMonitorLogPO.getCreator(),
                        businessMonitorLogPO.getBusinessCode());
                userTaskService.doUpdateByBusinessMonitorLog(businessMonitorLogPO.getCreator(),
                        businessMonitorLogPO.getBusinessCode());
            }
        }
    }

}
