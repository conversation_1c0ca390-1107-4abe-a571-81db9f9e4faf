package com.yhl.scp.ips.rbac.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.rbac.entity.Resource;
import com.yhl.scp.ips.rbac.entity.TenantModule;
import com.yhl.scp.ips.rbac.service.ResourceService;
import com.yhl.scp.ips.rbac.service.TenantModuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <code>ResourceController</code>
 * <p>
 * ResourceController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
@Api(tags = "菜单管理")
@RestController
@RequestMapping("resource")
public class ResourceController extends BaseController {

    @Autowired
    private ResourceService resourceService;
    @Autowired
    private TenantModuleService tenantModuleService;

    @ApiOperation("新增")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public BaseResponse createOrUpdate(Resource resource) {
        return resourceService.doCreate(resource);
    }

    @ApiOperation("修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public BaseResponse update(Resource resource) {
        return resourceService.doUpdate(resource);
    }

    @ApiOperation("删除")
    @RequestMapping(value = "/delete/{resourceId}", method = RequestMethod.POST)
    public BaseResponse delete(@PathVariable("resourceId") String resourceId) {
        return resourceService.doDelete(resourceId);
    }

    @ApiOperation("根据id查询")
    @RequestMapping(value = "/{resourceId}", method = RequestMethod.GET)
    public BaseResponse getResourceById(@PathVariable("resourceId") String resourceId) {
        return new BaseResponse(true, resourceService.selectById(resourceId));
    }

    @ApiOperation("分页查询")
    @RequestMapping(value = "/page", method = RequestMethod.GET)
    public BaseResponse page() {
        List<Resource> resources = resourceService.selectPage(getPagination(), getSortParam(), getQueryCriteriaParam());
        PageInfo<Resource> pageInfo = new PageInfo<>(resources);
        return BaseResponse.success(pageInfo);
    }

    @ApiOperation("获取菜单树")
    @RequestMapping(value = "/tree/{resourceId}", method = RequestMethod.GET)
    public BaseResponse getDeptTree(@PathVariable("resourceId") String resourceId) {
        List<Resource> resources = resourceService.selectAll();
        if (!SystemHolder.isSystemAdmin()) {
            List<TenantModule> tenantModules = tenantModuleService.selectByTenantId(SystemHolder.getTenantId());
            List<String> tenantModuleCode = tenantModules.stream().map(TenantModule::getModuleCode).distinct().collect(Collectors.toList());
            resources = resources.stream().filter(item -> "1".equals(item.getId()) || tenantModuleCode.contains(item.getModuleCode())).collect(Collectors.toList());
        }
        return new BaseResponse(Boolean.TRUE, listToTree(resources));
    }

    public static List<Resource> listToTree(List<Resource> list) {
        List<Resource> treeList = new ArrayList<>();
        for (Resource tree : list) {
            if (!StringUtils.hasText(tree.getParentId())) {
                treeList.add(findChildren(tree, list));
            }
        }
        return treeList;
    }

    private static Resource findChildren(Resource tree, List<Resource> list) {
        for (Resource node : list) {
            // 系统管理员 SYSTEM_ADMIN
            if (StringUtils.isEmpty(SystemHolder.getTenantId())) {
                if (!StringUtils.isEmpty(node.getTenantId())) {
                    continue;
                }
            } else {
                // 租户
                if (!StringUtils.isEmpty(node.getTenantId()) && !node.getTenantId().equals(SystemHolder.getTenantId())) {
                    continue;
                }
            }
            if (tree.getId().equals(node.getParentId())) {
                if (tree.getSubResources() == null) {
                    tree.setSubResources(new ArrayList<>());
                }
                tree.getSubResources().add(findChildren(node, list));
            }
        }
        return tree;
    }


}