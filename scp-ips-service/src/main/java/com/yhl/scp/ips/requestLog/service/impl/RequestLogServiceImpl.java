package com.yhl.scp.ips.requestLog.service.impl;

import com.yhl.platform.common.Pagination;
import com.yhl.scp.ips.requestLog.dao.RequestLogRepository;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.dto.RequestLogQueryDTO;
import com.yhl.scp.ips.requestLog.service.RequestLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class RequestLogServiceImpl implements RequestLogService {

    @Resource
    private RequestLogRepository requestLogRepository;

    @Override
    public void saveData(RequestLogDTO requestLogDTO) {
        requestLogRepository.save(requestLogDTO);
    }

    @Override
    public Page<RequestLogDTO> selectByPage(Pagination pagination, RequestLogQueryDTO queryParams) {
        Pageable pageable = PageRequest.of(pagination.getPageNum() - 1, pagination.getPageSize(), Sort.by(Sort.Direction.DESC, "requestTime"));
        RequestLogDTO example = new RequestLogDTO();
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withIgnoreCase()
                .withStringMatcher(ExampleMatcher.StringMatcher.CONTAINING);
        if (Objects.nonNull(queryParams)) {
            if (StringUtils.isNotBlank(queryParams.getUserId())) {
                example.setUserId(queryParams.getUserId());
                matcher.withMatcher("userId", ExampleMatcher.GenericPropertyMatchers.contains());
            }
            if (StringUtils.isNotBlank(queryParams.getRequestUri())) {
                example.setRequestUri(queryParams.getRequestUri());
                matcher.withMatcher("requestUri", ExampleMatcher.GenericPropertyMatchers.contains());
            }
            if (StringUtils.isNotBlank(queryParams.getUserAgent())) {
                example.setUserAgent(queryParams.getUserAgent());
                matcher.withMatcher("userAgent", ExampleMatcher.GenericPropertyMatchers.contains());
            }
        }
        return requestLogRepository.findAll(Example.of(example, matcher), pageable);
    }
}
