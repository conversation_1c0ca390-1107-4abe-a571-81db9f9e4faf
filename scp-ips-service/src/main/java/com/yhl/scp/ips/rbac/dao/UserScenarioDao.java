package com.yhl.scp.ips.rbac.dao;

import com.yhl.scp.ips.rbac.entity.UserScenario;
import com.yhl.scp.ips.rbac.vo.UserScenarioVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>UserScenarioDao</code>
 * <p>
 * 用户场景
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 18:48:31
 */
public interface UserScenarioDao {

    int insert(UserScenario record);

    int insertBatch(@Param("list") List<UserScenario> list);

    void deleteByUserId(String userId);

    void deleteByUserIdAndModuleCode(@Param("userId") String userId, @Param("moduleCode") String moduleCode);

    void deleteByScenarioId(String scenarioId);

    List<UserScenario> selectByParams(@Param("params") Map<String, Object> params);

    List<UserScenarioVO> selectVOByUserIdsAndModuleCode(@Param("userIds") List<String> userIds,
                                                        @Param("tenantId") String tenantId,
                                                        @Param("moduleCode") String moduleCode);

}
