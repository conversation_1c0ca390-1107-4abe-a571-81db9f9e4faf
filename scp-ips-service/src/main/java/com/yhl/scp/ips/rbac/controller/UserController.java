package com.yhl.scp.ips.rbac.controller;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.PlatformUser;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.AesEncryptUtil;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.i18n.service.UserDefaultLanguageService;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.common.enums.UserTypeEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.rbac.entity.*;
import com.yhl.scp.ips.rbac.service.*;
import com.yhl.scp.ips.system.service.ScenarioService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>UserController</code>
 * <p>
 * UserController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */

@Api(tags = "用户管理")
@RestController
@RequestMapping("user")
@Slf4j
public class UserController extends BaseController {

    @javax.annotation.Resource
    private UserService userService;
    @javax.annotation.Resource
    private TenantService tenantService;
    @javax.annotation.Resource
    private ResourceService resourceService;

    @javax.annotation.Resource
    private TenantModuleService tenantModuleService;

    @javax.annotation.Resource
    private TenantUserService tenantUserService;

    @javax.annotation.Resource
    private ScenarioService scenarioService;

    @javax.annotation.Resource
    private UserDefaultLanguageService defaultLanguageService;

    @javax.annotation.Resource
    private ModuleService moduleService;

    @ApiOperation("创建用户")
    @PostMapping(value = "/create")
    public BaseResponse add(User user) {
        return userService.doCreate(user);
    }

    @ApiOperation("更新用户")
    @PostMapping(value = "/update")
    public BaseResponse update(User user) {
        return userService.doUpdate(user);
    }

    @ApiOperation("删除用户")
    @PostMapping(value = "/delete/{userId}")
    public BaseResponse delete(@PathVariable("userId") String userId) {
        userService.doDelete(userId);
        return BaseResponse.success();
    }

    @ApiOperation("禁用用户")
    @PostMapping(value = "/disable/{userId}")
    public BaseResponse disable(@PathVariable("userId") String userId) {
        userService.doDisable(userId);
        return BaseResponse.success();
    }

    @ApiOperation("启用用户")
    @PostMapping(value = "/enable/{userId}")
    public BaseResponse enable(@PathVariable("userId") String userId) {
        userService.doEnable(userId);
        return BaseResponse.success();
    }

    @ApiOperation("分页查询")
    @GetMapping(value = "/page")
    public BaseResponse page() {
        List<User> users = userService.selectPage(getPagination(), getSortParam(), getQueryCriteriaParam());
        PageInfo<User> pageInfo = new PageInfo<>(users);
        return new BaseResponse(true, pageInfo);
    }

    @ApiOperation("根据id获取用户信息")
    @GetMapping(value = "/{id}")
    public BaseResponse getUserById(@PathVariable("id") String id) {
        return new BaseResponse(true, userService.selectById(id));
    }

    @ApiOperation("用户租户列表")
    @GetMapping(value = "/tenants")
    public BaseResponse tenants() {
        List<Tenant> tenants = tenantService.selectByUserId(SystemHolder.getUserId());
        return new BaseResponse(true, tenants);
    }

    @ApiOperation("用户所在租户的模块列表")
    @GetMapping(value = "/tenant/module")
    public BaseResponse tenantModules() {
        List<TenantUser> tenantUsers = tenantUserService.selectByUserId(SystemHolder.getUserId());
        Optional<TenantUser> optional = tenantUsers.stream().filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster())).findAny();
        String tenantId = "";
        if (optional.isPresent()) {
            tenantId = optional.get().getTenantId();
        } else {
            if (tenantUsers.isEmpty()) {
                return BaseResponse.success();
            }
            tenantId = tenantUsers.get(0).getTenantId();
        }
        List<TenantModule> tenantModules = new ArrayList<>();
        if (!StringUtils.isEmpty(tenantId)) {
            tenantModules = tenantModuleService.selectByTenantId(tenantId);
        }
        return new BaseResponse(true, tenantModules.stream().map(TenantModule::getModuleCode).collect(Collectors.toList()));
    }

    @ApiOperation("用户列表")
    @GetMapping(value = "/users")
    public BaseResponse users() {
        return new BaseResponse(true, userService.selectAll());
    }

    /**
     * 登录之后选择租户
     *
     * @param tenantId
     * @return
     */
    @ApiOperation("选择租户")
    @GetMapping(value = "/tenant/switch")
    public BaseResponse switchTenant(@RequestParam("tenantId") String tenantId) {
        session.setAttribute(Constants.TENANT_ID, tenantId);
        response.setHeader("tenant", tenantId);
        response.setHeader("module", SystemModuleEnum.MDS.getCode());
        response.setHeader("scenario", scenarioService.selectDefaultScenario(tenantId, SystemModuleEnum.MDS.getCode()));
        return BaseResponse.success();
    }

    @ApiOperation("获取已登录用户信息")
    @GetMapping(value = "/userinfo")
    public BaseResponse<PlatformUser> getUser() {
        PlatformUser user = SystemHolder.getUser();
        // user.setPassword(null);
        return BaseResponse.success(user);
    }

    @ApiOperation("用户模块列表")
    @GetMapping(value = "/modules")
    public BaseResponse modules() {
        List<Resource> resources = resourceService.selectByUserId(SystemHolder.getUserId());
        List<String> modules = resources.stream().map(Resource::getModuleCode).distinct()
                .filter(item -> !StringUtils.isEmpty(item)).collect(Collectors.toList());
        return BaseResponse.success(modules);
    }

    @ApiOperation("用户默认语言")
    @GetMapping(value = "/language")
    public BaseResponse getDefaultLanguage() {
        String language = defaultLanguageService.getDefaultLanguageByUserId(SystemHolder.getUserId());
        if (StringUtils.isEmpty(language)) {
            language = request.getLocale().toString();
        }
        return new BaseResponse(true, language, null);
    }

    @ApiOperation("用户菜单(所选租户下面对应角色菜单)")
    @GetMapping(value = "/resources")
    public BaseResponse resources() {
        List<Resource> resources = resourceService.selectByUserId(SystemHolder.getUserId());
        List<Resource> result = listToTree(resources);
        if (!result.isEmpty()) {
            Resource root = result.get(0);
            return BaseResponse.success(root.getSubResources());
        }
        return BaseResponse.success(new ArrayList<>());
    }

    @ApiOperation("新-用户菜单(所选租户下面对应角色菜单)")
    @GetMapping(value = "/resourcesNew")
    public BaseResponse<Map<String, List<Resource>>> resourcesNew() {
        List<String> moduleCodes = Lists.newArrayList(SystemModuleEnum.MDS.getCode(), SystemModuleEnum.DFP.getCode(),
                SystemModuleEnum.MPS.getCode(), SystemModuleEnum.MRP.getCode(), SystemModuleEnum.CTS.getCode());
        List<Resource> resources = resourceService.selectByUserId(SystemHolder.getUserId());
        Map<String, List<Resource>> dataMap = new HashMap<>();
        for (String moduleCode : moduleCodes) {
            List<String> resourceIds = moduleService.selectByModuleCode(moduleCode).stream()
                    .map(ModuleResource::getResourceId).distinct().collect(Collectors.toList());
            List<Resource> subList = new ArrayList<>();
            for (Resource resource : resources) {
                if (resourceIds.contains(resource.getId())) {
                    if (CollectionUtils.isEmpty(resource.getGrantedModules())) {
                        resource.setGrantedModules(new HashSet<>());
                    }
                    resource.getGrantedModules().add(moduleCode);
                    subList.add(resource);
                }
            }
            List<Resource> subResult = listToTree(subList);
            if (!subResult.isEmpty()) {
                Resource root = subResult.get(0);
                dataMap.put(moduleCode, root.getSubResources());
            } else {
                dataMap.put(moduleCode, new ArrayList<>());
            }
        }
        return BaseResponse.success(dataMap);
    }

    @ApiOperation("模块列表")
    @GetMapping(value = "/moduleResources")
    public BaseResponse moduleResources() {
        if (SystemHolder.isSystemAdmin()) {
            return BaseResponse.success(new ArrayList<>());
        }
        List<ModuleResource> moduleResources;
        moduleResources = moduleService.selectByModuleCode(SystemHolder.getModuleCode());
        return BaseResponse.success(moduleResources);
    }

    @ApiOperation("批量更新用户角色")
    @PostMapping(value = "/updateBatch")
    public BaseResponse updateBatch(@RequestBody User user) {
        return userService.doUpdateBatch(user);
    }

    private List<Resource> listToTree(List<Resource> list) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> modules = moduleRelationship();
        List<Resource> treeList = new ArrayList<>();
        for (Resource tree : list) {
            if (StringUtils.isEmpty(tree.getParentId())) {
                treeList.add(findChildren(tree, list, modules));
            }
        }
        return treeList;
    }

    private List<String> moduleRelationship() {
        List<String> result = new ArrayList<>();
        result.add(SystemHolder.getModuleCode());
        result.addAll(Arrays.asList(EnumUtils.getEnumByCode(SystemModuleEnum.class, SystemHolder.getModuleCode())
                .getMappingValue().split(",")));
        return result;
    }

    private static Resource findChildren(Resource tree, List<Resource> list, List<String> modules) {
        for (Resource node : list) {
            if (tree.getId().equals(node.getParentId())) {
                if (tree.getSubResources() == null) {
                    tree.setSubResources(new ArrayList<>());
                }
                tree.getSubResources().add(findChildren(node, list, modules));
            }
        }
        return tree;
    }

    private static Resource findChildren(Resource tree, List<Resource> list) {
        for (Resource node : list) {

            if (tree.getId().equals(node.getParentId())) {
                if (tree.getSubResources() == null) {
                    tree.setSubResources(new ArrayList<>());
                }
                tree.getSubResources().add(findChildren(node, list));
            }
        }
        return tree;
    }

    @ApiOperation("用户类型下拉框")
    @GetMapping(value = "/usertype")
    public BaseResponse selectUserTypes() {
        if (UserTypeEnum.SYSTEM_ADMIN.getCode().equals(SystemHolder.getUserType())) {
            List<LabelValue> labelValues = new ArrayList<>();
            LabelValue labelValue = new LabelValue();
            labelValue.setLabel(UserTypeEnum.TENANT_ADMIN.getDesc());
            labelValue.setValue(UserTypeEnum.TENANT_ADMIN.getCode());
            labelValues.add(labelValue);
            return new BaseResponse(true, labelValues);
        } else {
            List<LabelValue> labelValues = new ArrayList<>();
            LabelValue labelValue = new LabelValue();
            labelValue.setLabel(UserTypeEnum.NORMAL.getDesc());
            labelValue.setValue(UserTypeEnum.NORMAL.getCode());
            labelValues.add(labelValue);
            LabelValue labelValue2 = new LabelValue();
            labelValue2.setLabel(UserTypeEnum.TENANT_ADMIN.getDesc());
            labelValue2.setValue(UserTypeEnum.TENANT_ADMIN.getCode());
            labelValues.add(labelValue2);
            return new BaseResponse(true, labelValues);
        }
    }

    @ApiOperation("用户名下拉查询")
    @GetMapping(value = "/cnNameDropDown")
    public BaseResponse<List<LabelValue<String>>> cnNameDropDown() {
        List<User> list = userService.selectAll();
        List<LabelValue<String>> data = list.stream().map(item -> new LabelValue<>(item.getCnName(), item.getId()))
                .collect(Collectors.toList());
        data.add(new LabelValue<>("BPIM", "bpim-bot"));
        return BaseResponse.success(data);
    }

    @ApiOperation("修改密码")
    @PostMapping(value = "/modifyPassword")
    public BaseResponse<String> modifyPassword(@RequestParam String password,
                                               @RequestParam String newPassword) {
        try {
            PlatformUser user = SystemHolder.getUser();
            if (!StringUtils.hasText(user.getId())) {
                session.removeAttribute(Constants.SESSION_USER);
                return BaseResponse.error("用户未登录");
            }
            User systemUser = userService.getUserByUserName(user.getUserName());
            if (!password.equals(systemUser.getPassword())) {
                return BaseResponse.error("旧密码不正确，请重新输入");
            }
            systemUser.setPassword(newPassword);
            userService.doUpdateById(systemUser);
        } catch (Exception e) {
            return BaseResponse.error(e.getMessage());
        }
        return BaseResponse.success();
    }

    @ApiOperation("重置密码")
    @RequestMapping(value = "/resetPwd", method = RequestMethod.POST)
    public BaseResponse resetPwd(@RequestParam("userId") String userId) {
        User user = userService.selectById(userId);
        String newPassword = AesEncryptUtil.encrypt("123456");
        user.setPassword(newPassword);
        userService.doUpdateById(user);
        log.info("{}重置了用户：{}登录密码！", SystemHolder.getCnName(), user.getCnName());
        return new BaseResponse(true, "重置成功！");
    }

    @ApiOperation(value = "用户同步")
    @GetMapping(value = "/syncUser")
    public BaseResponse<Void> syncUser(@RequestParam(value = "startTime", required = false) String startTime) {
        return userService.syncSyncUser(startTime);
    }

    @ApiOperation("用户名邮箱下拉查询")
    @GetMapping(value = "/email")
    public BaseResponse<List<LabelValue<String>>> email() {
        List<User> list = userService.selectAll();
        list = list.stream().filter(x -> StringUtils.isNotEmpty(x.getEmail())).collect(Collectors.toList());
        List<LabelValue<String>> data = list.stream().map(item -> new LabelValue<>(item.getCnName(), item.getEmail()))
                .collect(Collectors.toList());
        return BaseResponse.success(data);
    }

    @ApiOperation("查询量产计划员下拉框")
    @GetMapping(value = "/orderPlannerDropDown")
    public BaseResponse<List<LabelValue<String>>> selectOrderPlannerDropDown() {
        List<LabelValue<String>> list = userService.selectOrderPlannerDropDown();
        return BaseResponse.success(list);
    }
}