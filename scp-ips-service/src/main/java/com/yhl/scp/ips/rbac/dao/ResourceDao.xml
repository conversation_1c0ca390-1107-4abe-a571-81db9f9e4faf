<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.ips.rbac.dao.ResourceDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.ips.rbac.entity.Resource">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="resource_type" jdbcType="VARCHAR" property="resourceType"/>
        <result column="open_type" jdbcType="VARCHAR" property="openType"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="component_path" jdbcType="VARCHAR" property="componentPath"/>
        <result column="custom" jdbcType="VARCHAR" property="custom"/>
        <result column="widget_type" jdbcType="VARCHAR" property="widgetType"/>
    </resultMap>

    <resultMap id="TreeMap" type="com.yhl.scp.ips.rbac.entity.Resource">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="resource_type" jdbcType="VARCHAR" property="resourceType"/>
        <result column="open_type" jdbcType="VARCHAR" property="openType"/>
        <result column="tenant_id" jdbcType="VARCHAR" property="tenantId"/>
        <result column="module_code" jdbcType="VARCHAR" property="moduleCode"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="component_path" jdbcType="VARCHAR" property="componentPath"/>
        <result column="custom" jdbcType="VARCHAR" property="custom"/>
        <collection property="subResources" column="{id=id,tenantId=tId}" javaType="java.util.ArrayList"
                    ofType="com.yhl.scp.ips.rbac.entity.Resource"
                    select="selectByParentId">
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id, resource_name, url, parent_id, sort, icon, resource_type, open_type, tenant_id,
    module_code, enabled,component_path,custom,widget_type
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_resource
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>
        from auth_rbac_resource
        where enabled = 'YES'
    </select>
    <select id="selectWidgetResourcesByRoleIds" resultMap="BaseResultMap">
        select distinct r.*
        from auth_rbac_resource r
        left join auth_rbac_role_resource rr on r.id = rr.resource_id
        where enabled = 'YES'
        and r.resource_type = 'WIDGET'
        and rr.role_id in
        <foreach index="index" separator="," collection="list" item="item" open="(" close=")">
            #{item}
        </foreach>

    </select>
    <select id="selectWidgetResourcesByParentIds" resultMap="BaseResultMap">
        select distinct r.*
        from auth_rbac_resource r
        where enabled = 'YES'
        and r.resource_type = 'WIDGET'
        and r.parent_id in
        <foreach index="index" separator="," collection="list" item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectTreeById" parameterType="java.lang.String" resultMap="TreeMap">
        select 'false' as QUERYID,
        <include refid="Base_Column_List"/>,
        '${tenantId}' as tId
        from auth_rbac_resource
        where id = #{id,jdbcType=VARCHAR} and enabled = 'YES'
        and (tenant_id is null or tenant_id = '')
        <if test="tenantId!=null and tenantId!=''">
            or ( id = #{id,jdbcType=VARCHAR} and tenant_id = #{tenantId} and enabled = 'YES')
        </if>
        order by sort
    </select>
    <select id="selectByParentId" resultMap="TreeMap">
        select
        <include refid="Base_Column_List"/>,
        '${tId}' as tId
        from auth_rbac_resource t
        where parent_id=#{id} and t.enabled = 'YES'
        and (tenant_id is null or tenant_id = '')
        <if test="tenantId!=null and tenantId!=''">
            or ( parent_id=#{id} and tenant_id = #{tenantId} and enabled = 'YES')
        </if>
        order by t.sort
    </select>

    <select id="selectByParams" parameterType="java.util.Map" resultMap="BaseResultMap">
        select distinct
        <include refid="Base_Column_List"/>
        from auth_rbac_resource t
        where t.enabled = 'YES'
            and t.resource_type = 'MENU'
            and t.id in (
            select rr.resource_id
            from auth_rbac_role_resource rr
            join auth_rbac_user_role ur on rr.role_id = ur.role_id
            join auth_rbac_role r on r.id = ur.role_id
            where rr.resource_id = t.id
                and ur.user_id = #{params.userId,jdbcType=VARCHAR}
                and r.tenant_id = #{params.tenantId,jdbcType=VARCHAR})
            and (t.tenant_id IS NULL or t.tenant_id = '')
            or (t.resource_type = 'MENU' and t.tenant_id = #{params.tenantId,jdbcType=VARCHAR} and t.enabled='YES')
            order by t.sort
    </select>

    <select id="selectForSystemAdmin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_rbac_resource t
        where 1=1 and t.enabled = 'YES' and (t.tenant_id is null or t.tenant_id = '')
        and t.module_code = 'IPS' or t.id = '1'
        order by sort
    </select>

    <select id="selectByCondition" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from auth_rbac_resource t
        where 1=1 and t.enabled = 'YES' and (tenant_id is null or tenant_id = '')
        <if test="queryCriteriaParam!=null and queryCriteriaParam!=''">
            ${queryCriteriaParam}
        </if>
        <if test="tenantId!=null and tenantId!=''">
            or (t.tenant_id = #{tenantId} and t.enabled = 'YES')
        </if>
        <if test="sortParam!=null and sortParam!=''">
            order by ${sortParam}
        </if>
    </select>
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from auth_rbac_resource t
        where id in
        <foreach item="item" index="index" separator="," collection="resourceIds" open="(" close=")">
            #{item}
        </foreach>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from auth_rbac_resource
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.yhl.scp.ips.rbac.entity.Resource">
        insert into auth_rbac_resource (id, resource_name, url,
                                        parent_id, sort, icon,
                                        resource_type, open_type, tenant_id,
                                        module_code, enabled, component_path, custom, widget_type)
        values (#{id,jdbcType=VARCHAR}, #{resourceName,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR},
                #{parentId,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{icon,jdbcType=VARCHAR},
                #{resourceType,jdbcType=VARCHAR}, #{openType,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR},
                #{moduleCode,jdbcType=VARCHAR}, #{enabled,jdbcType=VARCHAR}, #{componentPath,jdbcType=VARCHAR},
                #{custom,jdbcType=VARCHAR}, #{widgetType,jdbcType=VARCHAR})
    </insert>


    <update id="updateByPrimaryKey" parameterType="com.yhl.scp.ips.rbac.entity.Resource">
        update auth_rbac_resource
        set resource_name  = #{resourceName,jdbcType=VARCHAR},
            url            = #{url,jdbcType=VARCHAR},
            parent_id      = #{parentId,jdbcType=VARCHAR},
            sort           = #{sort,jdbcType=INTEGER},
            icon           = #{icon,jdbcType=VARCHAR},
            resource_type  = #{resourceType,jdbcType=VARCHAR},
            open_type      = #{openType,jdbcType=VARCHAR},
            tenant_id      = #{tenantId,jdbcType=VARCHAR},
            module_code    = #{moduleCode,jdbcType=VARCHAR},
            enabled        = #{enabled,jdbcType=VARCHAR},
            component_path = #{componentPath,jdbcType=VARCHAR},
            custom         = #{custom,jdbcType=VARCHAR},
            widget_type    = #{widgetType,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>