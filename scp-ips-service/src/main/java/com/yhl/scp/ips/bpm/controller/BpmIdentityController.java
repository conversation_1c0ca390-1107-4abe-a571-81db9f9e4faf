package com.yhl.scp.ips.bpm.controller;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.ips.bpm.dto.BpmGroupDTO;
import com.yhl.scp.ips.bpm.dto.BpmUserDTO;
import com.yhl.scp.ips.bpm.vo.GroupVO;
import com.yhl.scp.ips.bpm.vo.PageResult;
import com.yhl.scp.ips.bpm.vo.SystemUserVO;
import com.yhl.scp.ips.bpm.vo.UserVO;
import com.yhl.scp.ips.rbac.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.IdentityService;
import org.flowable.idm.api.Group;
import org.flowable.idm.api.GroupQuery;
import org.flowable.idm.api.User;
import org.flowable.idm.api.UserQuery;
import org.flowable.ui.common.model.GroupRepresentation;
import org.flowable.ui.common.model.UserRepresentation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>BpmIdentityController</code>
 * <p>
 * BPM流程身份管理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021-11-21 22:27:00
 */
@Api(tags = "BPM流程身份管理")
@RestController
@RequestMapping(value = "bpmIdentity")
@Slf4j
public class BpmIdentityController extends BaseController {

    @Autowired
    private IdentityService identityService;

    @Autowired
    private UserService userService;

    public static final String ILLEGAL_CHARACTER = "-";

    @ApiOperation(value = "分页查询用户组")
    @GetMapping(value = "group/page")
    public BaseResponse<PageResult<GroupVO>> groupPage(@RequestParam(value = "groupId", required = false) String groupId,
                                                       @RequestParam(value = "name", required = false) String name) {
        try {
            log.info("group page query ...");
            // 组装查询条件
            GroupQuery groupQuery = identityService.createGroupQuery();
            if (StringUtils.isNotBlank(groupId)) {
                groupQuery.groupId(groupId);
            }
            if (StringUtils.isNotEmpty(name)) {
                groupQuery.groupNameLikeIgnoreCase("%" + name + "%");
            }
            long totalCount = groupQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<Group> list = groupQuery.orderByGroupName().asc().listPage(firstResult, getPagination().getPageSize());
            List<GroupVO> dataList = new ArrayList<>();
            // 结果数据组装
            if (CollectionUtils.isNotEmpty(list)) {
                for (Group group : list) {
                    List<User> userList = identityService.createUserQuery().memberOfGroup(group.getId()).list();
                    if (CollectionUtils.isNotEmpty(userList)) {
                        List<String> usersInGroup = userList.stream().map(item -> item.getFirstName()
                                        + (StringUtils.isNotBlank(item.getLastName()) ? item.getLastName() : ""))
                                .collect(Collectors.toList());
                        dataList.add(new GroupVO(group, true, usersInGroup));
                    } else {
                        dataList.add(new GroupVO(group));
                    }
                }
            }
            PageResult<GroupVO> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query group page, ", e);
            return new BaseResponse<>("用户组分页查询失败");
        }
    }

    @ApiOperation(value = "查询用户组列表")
    @GetMapping(value = "group/list")
    public BaseResponse<List<GroupVO>> groupList(@RequestParam(value = "userId") String userId) {
        try {
            log.info("group list query ...");
            GroupQuery groupQuery = identityService.createGroupQuery();
            List<Group> list = groupQuery.orderByGroupName().asc().list();
            List<GroupVO> dataList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (Group group : list) {
                    GroupVO groupRepresentationX = new GroupVO(group);
                    List<User> userList = identityService.createUserQuery().memberOfGroup(group.getId()).list();
                    if (CollectionUtils.isNotEmpty(userList)) {
                        List<String> collect = userList.stream().map(User::getId).collect(Collectors.toList());
                        if (collect.contains(userId)) {
                            groupRepresentationX = new GroupVO(group, true, Lists.newArrayList());
                        }
                    }
                    dataList.add(groupRepresentationX);
                }
            }
            return new BaseResponse<>(Boolean.TRUE, dataList);
        } catch (Exception e) {
            log.error("Fail to query group list, ", e);
            return new BaseResponse<>("用户组列表查询失败");
        }
    }

    @ApiOperation(value = "创建用户组")
    @PostMapping(value = "group/create")
    public BaseResponse<Void> groupAdd(BpmGroupDTO bpmGroupDTO) {
        try {
            log.info("group create ...");
            String groupId = bpmGroupDTO.getGroupId();
            String groupName = bpmGroupDTO.getGroupName();
            String groupType = bpmGroupDTO.getGroupType();
            List<String> userIds = bpmGroupDTO.getUserIds();
            if (StringUtils.isEmpty(groupId) || StringUtils.isEmpty(groupName) || StringUtils.isEmpty(groupType)) {
                return new BaseResponse<>("用户组ID, 组名，组类型不能为空");
            }
            if (StringUtils.isNotBlank(groupName) && groupName.contains(ILLEGAL_CHARACTER)) {
                return new BaseResponse<>("用户组名称不能包含‘-’");
            }
            List<Group> list = identityService.createGroupQuery().groupName(groupName).list();
            if (CollectionUtils.isNotEmpty(list)) {
                return new BaseResponse<>("用户组名称重复");
            }
            Group newGroup = identityService.newGroup(groupId);
            newGroup.setName(groupName);
            newGroup.setType(groupType);
            identityService.saveGroup(newGroup);
            if (CollectionUtils.isNotEmpty(userIds)) {
                for (String userId : userIds) {
                    identityService.createMembership(userId, groupId);
                }
            }
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to create group, ", e);
            return new BaseResponse<>("用户组创建失败");
        }
    }

    @ApiOperation(value = "删除用户组")
    @PostMapping(value = "group/delete")
    public BaseResponse<Void> groupDelete(@RequestParam(value = "groupId") String groupId) {
        try {
            log.info("group delete ...");
            identityService.deleteGroup(groupId);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to delete group, ", e);
            return new BaseResponse<>("用户组删除失败");
        }
    }

    @ApiOperation(value = "修改用户组")
    @GetMapping(value = "group/update")
    public BaseResponse<Group> groupUpdate(BpmGroupDTO bpmGroupDTO) {
        try {
            log.info("group update ...");
            String groupId = bpmGroupDTO.getGroupId();
            String groupName = bpmGroupDTO.getGroupName();
            String groupType = bpmGroupDTO.getGroupType();
            List<String> userIds = bpmGroupDTO.getUserIds();
            if (StringUtils.isEmpty(groupId) || StringUtils.isEmpty(groupName) || StringUtils.isEmpty(groupType)) {
                return new BaseResponse<>("用户组ID, 组名，组类型不能为空");
            }
            if (StringUtils.isNotBlank(groupName) && groupName.contains(ILLEGAL_CHARACTER)) {
                return new BaseResponse<>("用户组名称不能包含’-‘");
            }
            Group group = identityService.createGroupQuery().groupName(groupName).singleResult();
            if (group != null && (!groupId.equals(group.getId()))) {
                return new BaseResponse<>("用户组名称重复");
            }

            Group oldGroup = identityService.createGroupQuery().groupId(groupId).singleResult();
            oldGroup.setName(groupName);
            oldGroup.setType(groupType);
            identityService.saveGroup(oldGroup);

            if (CollectionUtils.isNotEmpty(userIds)) {
                for (String userId : userIds) {
                    identityService.createMembership(userId, groupId);
                }
            }
            return new BaseResponse<>(Boolean.TRUE, oldGroup);
        } catch (Exception e) {
            log.error("Fail to delete group, ", e);
            return new BaseResponse<>("用户组修改失败");
        }
    }

    @ApiOperation(value = "用户组下所有用户")
    @GetMapping(value = "usersOfGroup/list/{groupId}")
    public BaseResponse<List<UserRepresentation>> usersOfGroupList(@PathVariable(value = "groupId") String groupId) {
        try {
            log.info("users of a certain group list query ...");
            UserQuery userQuery = identityService.createUserQuery().memberOfGroup(groupId);
            List<User> list = userQuery.orderByUserId().asc().list();
            List<UserRepresentation> dataList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (User user : list) {
                    dataList.add(new UserRepresentation(user));
                }
            }
            return new BaseResponse<>(Boolean.TRUE, dataList);
        } catch (Exception e) {
            log.error("Fail to get usersOfGroup list, ", e);
            return new BaseResponse<>("获取用户组下所有用户列表失败");
        }
    }

    @ApiOperation(value = "用户所在用户组")
    @GetMapping(value = "groupsOfUser/list/{userId}")
    public BaseResponse<List<GroupRepresentation>> groupsOfUserList(@PathVariable(value = "userId") String userId) {
        try {
            log.info("groups of a certain user list query ...");
            GroupQuery groupQuery = identityService.createGroupQuery().groupMember(userId);
            List<Group> list = groupQuery.orderByGroupName().asc().list();
            List<GroupRepresentation> dataList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (Group group : list) {
                    dataList.add(new GroupRepresentation(group));
                }
            }
            return new BaseResponse<>(Boolean.TRUE, dataList);
        } catch (Exception e) {
            log.error("Fail to get groupsOfUser list, ", e);
            return new BaseResponse<>("获取用户所在用户组列表失败");
        }
    }

    @ApiOperation(value = "用户分页查询")
    @GetMapping(value = "user/page")
    public BaseResponse<PageResult<UserVO>> userPage(@RequestParam(value = "userId", required = false) String userId,
                                                     @RequestParam(value = "displayName", required = false) String displayName) {
        try {
            log.info("user page query ...");
            // 组装查询条件
            UserQuery userQuery = identityService.createUserQuery();
            if (StringUtils.isNotEmpty(userId)) {
                userQuery.userId(userId);
            }
            if (StringUtils.isNotEmpty(displayName)) {
                userQuery.userFirstNameLikeIgnoreCase("%" + displayName + "%");
            }
            long totalCount = userQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<User> list = userQuery.orderByUserId().asc().listPage(firstResult, getPagination().getPageSize());
            List<UserVO> dataList = new ArrayList<>();
            // 组装结果集
            if (CollectionUtils.isNotEmpty(list)) {
                for (User user : list) {
                    List<Group> userList = identityService.createGroupQuery().groupMember(user.getId()).list();
                    if (CollectionUtils.isNotEmpty(userList)) {
                        List<String> groupsIncludeUser = userList.stream().map(Group::getName).collect(Collectors.toList());
                        dataList.add(new UserVO(user, true, groupsIncludeUser));
                    } else {
                        dataList.add(new UserVO(user));
                    }
                }
            }
            PageResult<UserVO> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query user page, ", e);
            return new BaseResponse<>("用户分页查询失败");
        }
    }

    @ApiOperation(value = "用户列表查询")
    @GetMapping(value = "user/list")
    public BaseResponse<List<UserVO>> userList(@RequestParam(value = "groupId") String groupId) {
        try {
            log.info("user list query ...");
            UserQuery userQuery = identityService.createUserQuery();
            List<User> list = userQuery.orderByUserId().asc().list();
            List<UserVO> dataList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (User user : list) {
                    UserVO userRepresentationX = new UserVO(user);
                    List<Group> groupList = identityService.createGroupQuery().groupMember(user.getId()).list();
                    if (CollectionUtils.isNotEmpty(groupList)) {
                        List<String> collect = groupList.stream().map(Group::getId).collect(Collectors.toList());
                        if (collect.contains(groupId)) {
                            userRepresentationX = new UserVO(user, true, Lists.newArrayList());
                        }
                    }
                    dataList.add(userRepresentationX);
                }
            }
            return new BaseResponse<>(Boolean.TRUE, dataList);
        } catch (Exception e) {
            log.error("Fail to query user list, ", e);
            return new BaseResponse<>("用户列表查询失败");
        }
    }

    @ApiOperation(value = "创建用户")
    @PostMapping(value = "user/create")
    public BaseResponse<User> userCreate(BpmUserDTO bpmUserDTO) {
        try {
            log.info("user create ...");
            List<User> list = identityService.createUserQuery().userId(bpmUserDTO.getUserId()).list();
            if (CollectionUtils.isNotEmpty(list)) {
                return new BaseResponse<>("该工作流用户已存在");
            }
            User newUser = identityService.newUser(bpmUserDTO.getUserId());
            newUser.setFirstName(bpmUserDTO.getFirstName());
            newUser.setLastName(bpmUserDTO.getLastName());
            newUser.setDisplayName(bpmUserDTO.getDisplayName());
            newUser.setPassword(bpmUserDTO.getPassword());
            newUser.setEmail(bpmUserDTO.getEmail());
            identityService.saveUser(newUser);
            return new BaseResponse<>(Boolean.TRUE, newUser);
        } catch (Exception e) {
            log.error("Fail to create user, ", e);
            return new BaseResponse<>("用户创建失败");
        }
    }

    @ApiOperation(value = "批量创建用户")
    @PostMapping(value = "user/createBatch")
    public BaseResponse<Void> createBatch(@RequestBody List<String> staffCodes) {
        try {
            log.info("user batch create ...");
            List<com.yhl.scp.ips.rbac.entity.User> list = userService.selectByParams(ImmutableMap.of("staffCodes", staffCodes));
            if (CollectionUtils.isNotEmpty(list)) {
                for (com.yhl.scp.ips.rbac.entity.User user : list) {
                    saveUser(user);
                }
            }
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to batch create user, ", e);
            return new BaseResponse<>("用户批量创建失败");
        }
    }

    private void saveUser(com.yhl.scp.ips.rbac.entity.User user) {
        try {
            User newUser = identityService.newUser(user.getStaffCode());
            newUser.setFirstName(user.getFirstName());
            newUser.setLastName(user.getLastName());
            newUser.setDisplayName(user.getCnName());
            // TODO 密码需要解密或者设置默认密码
            newUser.setPassword(user.getPassword());
            newUser.setEmail(user.getEmail());
            identityService.saveUser(newUser);
        } catch (Exception e) {
            log.error("添加用户{}失败", user.getUserName());
        }
    }

    @ApiOperation(value = "更新用户")
    @PostMapping(value = "user/update")
    public BaseResponse<User> userUpdate(BpmUserDTO bpmUserDTO) {
        try {
            log.info("user update ...");
            String userId = bpmUserDTO.getUserId();
            if (StringUtils.isBlank(userId)) {
                return new BaseResponse<>("用户ID不能为空");
            }
            User updateUser = identityService.createUserQuery().userId(userId).singleResult();
            if (updateUser == null) {
                return new BaseResponse<>("用户不存在");
            }
            if (StringUtils.isNotBlank(bpmUserDTO.getFirstName())) {
                updateUser.setFirstName(bpmUserDTO.getFirstName());
            }
            if (StringUtils.isNotBlank(bpmUserDTO.getLastName())) {
                updateUser.setLastName(bpmUserDTO.getLastName());
            }
            if (StringUtils.isNotBlank(bpmUserDTO.getDisplayName())) {
                updateUser.setLastName(bpmUserDTO.getDisplayName());
            }
            if (StringUtils.isNotBlank(bpmUserDTO.getPassword())) {
                updateUser.setPassword(bpmUserDTO.getPassword());
            }
            if (StringUtils.isNotBlank(bpmUserDTO.getEmail())) {
                updateUser.setEmail(bpmUserDTO.getEmail());
            }
            identityService.saveUser(updateUser);
            return new BaseResponse<>(Boolean.TRUE, updateUser);
        } catch (Exception e) {
            log.error("Fail to update user, ", e);
            return new BaseResponse<>("用户修改失败");
        }
    }

    @ApiOperation(value = "删除用户")
    @PostMapping(value = "user/delete")
    public BaseResponse<Void> userDelete(@RequestParam(value = "userId") String userId) {
        try {
            log.info("user delete ...");
            identityService.deleteUser(userId);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to delete user, ", e);
            return new BaseResponse<>("用户删除失败");
        }
    }

    public static final String PAGE_FROM_USER = "USER";

    @ApiOperation(value = "绑定解绑用户用户组")
    @PostMapping(value = "membership")
    public BaseResponse<Void> membershipBind(@RequestParam(value = "userIds", required = false) String userIds,
                                             @RequestParam(value = "groupIds", required = false) String groupIds,
                                             @ApiParam(allowableValues = "USER, GROUP") @RequestParam(value = "pageFrom") String pageFrom) {
        try {
            log.info("user group membership establish ...");
            List<String> groupIdList = StringUtils.isBlank(groupIds) ? Lists.newArrayList() : Lists.newArrayList(groupIds.split(","));
            List<String> userIdList = StringUtils.isBlank(userIds) ? Lists.newArrayList() : Lists.newArrayList(userIds.split(","));
            if (PAGE_FROM_USER.equalsIgnoreCase(pageFrom)) {
                // 来源自工作流用户页面的绑定解绑请求
                String userId = userIdList.get(0);
                List<Group> list = identityService.createGroupQuery().groupMember(userId).list();
                List<String> oldGroupIdList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(list)) {
                    oldGroupIdList = list.stream().map(Group::getId).collect(Collectors.toList());
                }
                List<String> deleteList = (List<String>) CollectionUtils.getDiffSection(oldGroupIdList, groupIdList);
                List<String> createList = (List<String>) CollectionUtils.getDiffSection(groupIdList, oldGroupIdList);
                if (CollectionUtils.isNotEmpty(deleteList)) {
                    for (String groupId : deleteList) {
                        identityService.deleteMembership(userId, groupId);
                    }
                }
                if (CollectionUtils.isNotEmpty(groupIdList) && (CollectionUtils.isNotEmpty(createList))) {
                    for (String groupId : createList) {
                        identityService.createMembership(userId, groupId);
                    }
                }
            } else {
                // 来源自工作流用户组页面的绑定解绑请求
                String groupId = groupIdList.get(0);
                List<User> list = identityService.createUserQuery().memberOfGroup(groupId).list();
                List<String> oldUserIdList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(list)) {
                    oldUserIdList = list.stream().map(User::getId).collect(Collectors.toList());
                }
                List<String> deleteList = (List<String>) CollectionUtils.getDiffSection(oldUserIdList, userIdList);
                List<String> createList = (List<String>) CollectionUtils.getDiffSection(userIdList, oldUserIdList);
                if (CollectionUtils.isNotEmpty(deleteList)) {
                    for (String userId : deleteList) {
                        identityService.deleteMembership(userId, groupId);
                    }
                }
                if (CollectionUtils.isNotEmpty(userIdList) && (CollectionUtils.isNotEmpty(createList))) {
                    for (String userId : createList) {
                        identityService.createMembership(userId, groupId);
                    }
                }
            }
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to establish membership between user and group, ", e);
            return new BaseResponse<>("用户用户组关联失败");
        }
    }

    @ApiOperation("系统用户列表查询")
    @GetMapping(value = "systemUser/list")
    public BaseResponse<List<SystemUserVO>> systemUserList(@RequestParam(value = "userName", required = false) String userName,
                                                           @RequestParam(value = "cnName", required = false) String cnName,
                                                           @RequestParam(value = "staffCode", required = false) String staffCode) {
        List<SystemUserVO> data = new ArrayList<>();
        Map<String, Object> params = new HashMap<>(6);
        params.put("userName", userName);
        params.put("cnName", cnName);
        params.put("staffCode", staffCode);
        List<com.yhl.scp.ips.rbac.entity.User> users = userService.selectByParams(params);
        List<String> staffCodeList = identityService.createUserQuery().list().stream().map(User::getId).collect(Collectors.toList());
        users.forEach(source -> {
            SystemUserVO target = new SystemUserVO();
            BeanUtils.copyProperties(source, target);
            target.setImported(staffCodeList.contains(source.getStaffCode()));
            data.add(target);
        });
        return new BaseResponse<>(Boolean.TRUE, data);
    }

    @ApiOperation("用户下拉")
    @GetMapping("userSelect")
    public List<LabelValue<String>> userSelect() {
        List<User> list = identityService.createUserQuery().orderByUserId().asc().list();
        List<LabelValue<String>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(item.getFirstName() + (StringUtils.isNotBlank(item.getLastName()) ? item.getLastName() : ""));
                labelValue.setValue(item.getId());
                result.add(labelValue);
            });
        }
        return result;
    }

    @ApiOperation("用户组下拉")
    @GetMapping("groupSelect")
    public List<LabelValue<String>> groupSelect() {
        List<Group> list = identityService.createGroupQuery().orderByGroupId().asc().list();
        List<LabelValue<String>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(item.getName());
                labelValue.setValue(item.getId());
                result.add(labelValue);
            });
        }
        return result;
    }

}