package com.yhl.scp.ips.bpm.controller;

import com.yhl.platform.common.PlatformUser;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.scp.ips.bpm.constants.ProcessVariableConstants;
import com.yhl.scp.ips.bpm.enums.ProcessStatusEnum;
import com.yhl.scp.ips.bpm.vo.PageResult;
import com.yhl.scp.ips.bpm.vo.ProcessInstanceVO;
import com.yhl.scp.ips.bpm.vo.RunningProcessVO;
import com.yhl.scp.ips.common.SystemHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.*;
import org.flowable.engine.impl.ProcessInstanceQueryProperty;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceQuery;
import org.flowable.idm.api.User;
import org.flowable.image.ProcessDiagramGenerator;
import org.flowable.task.api.Task;
import org.flowable.ui.task.model.runtime.ProcessInstanceRepresentation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;

/**
 * <code>BpmRunningController</code>
 * <p>
 * BPM流程监控管理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021-11-22 14:36:00
 */
@Api(tags = "BPM流程监控管理")
@RestController
@RequestMapping(value = "bpmRunning")
@Slf4j
public class BpmRunningController extends BaseController {

    @Autowired
    private IdentityService identityService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Qualifier("processEngine")
    @Autowired
    private ProcessEngine processEngine;

    @ApiOperation(value = "流程监控分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageResult<ProcessInstanceVO>> page(@ApiParam(value = "流程名称,精准")
                                                            @RequestParam(value = "processDefinitionName", required = false) String processDefinitionName,
                                                            @ApiParam(value = "标题,模糊")
                                                            @RequestParam(value = "title", required = false) String title,
                                                            @ApiParam(value = "发起人,精准")
                                                            @RequestParam(value = "initiator", required = false) String initiator,
                                                            @ApiParam(value = "涉及人,精准")
                                                            @RequestParam(value = "involvedUser", required = false) String involvedUser,
                                                            @ApiParam(value = "涉及组,精准")
                                                            @RequestParam(value = "involvedGroup", required = false) String involvedGroup,
                                                            @ApiParam(value = "是否激活状态")
                                                            @RequestParam(value = "isActive", required = false) Boolean isActive,
                                                            @ApiParam(value = "抄送人")
                                                            @RequestParam(value = "carbonCopy", required = false) String carbonCopy) {
        try {
            log.info("group page query ...");
            // 查询参数组装
            ProcessInstanceQuery processInstanceQuery = assembleProcessInstanceQuery(processDefinitionName, title,
                    initiator, involvedUser, involvedGroup, isActive, carbonCopy);
            long totalCount = processInstanceQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<ProcessInstance> list = processInstanceQuery
                    .orderBy(new ProcessInstanceQueryProperty("RES.START_TIME_"))
                    .desc().listPage(firstResult, getPagination().getPageSize());
            List<ProcessInstanceVO> dataList = new ArrayList<>();
            // 结果集组装
            if (CollectionUtils.isNotEmpty(list)) {
                for (ProcessInstance processInstance : list) {
                    User startedBy = null;
                    if (processInstance.getStartUserId() != null) {
                        startedBy = identityService.createUserQuery().userId(processInstance.getStartUserId()).singleResult();
                    }
                    ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService
                            .getProcessDefinition(processInstance.getProcessDefinitionId());
                    Map<String, Object> variables = runtimeService.getVariables(processInstance.getId());
                    String titleName = variables.containsKey(ProcessVariableConstants.TITLE) ?
                            (String) variables.get(ProcessVariableConstants.TITLE) : "";
                    List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).list();
                    List<String> currentTasks = new ArrayList<>();
                    List<String> currentHandlers = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(tasks)) {
                        for (Task task : tasks) {
                            currentTasks.add(task.getName());
                            if (StringUtils.isBlank(task.getAssignee())) {
                                continue;
                            }
                            User user = identityService.createUserQuery().userId(task.getAssignee()).singleResult();
                            if (null != user) {
                                currentHandlers.add(user.getFirstName() + (StringUtils.isNotBlank(user.getLastName()) ? user.getLastName() : ""));
                            }
                        }
                    }
                    dataList.add(new ProcessInstanceVO(processInstance, processDefinitionEntity,
                            processDefinitionEntity.isGraphicalNotationDefined(), startedBy,
                            titleName.split("#")[0], String.join(",", currentTasks),
                            String.join(",", currentHandlers), processInstance.isSuspended(),
                            processInstance.getStartTime()));
                }
            }
            PageResult<ProcessInstanceVO> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query running process page, ", e);
            return new BaseResponse<>("流程监控分页查询失败");
        }
    }

    /**
     * 查询参数组装
     *
     * @param processDefinitionName 流程定义名
     * @param title                 标题
     * @param initiator             发起人
     * @param involvedUser          涉及人
     * @param involvedGroup         涉及组
     * @param isActive              是否激活
     * @param carbonCopy            抄送人
     * @return org.flowable.engine.runtime.ProcessInstanceQuery
     */
    private ProcessInstanceQuery assembleProcessInstanceQuery(String processDefinitionName, String title, String initiator,
                                                              String involvedUser, String involvedGroup, Boolean isActive,
                                                              String carbonCopy) {
        ProcessInstanceQuery processInstanceQuery = runtimeService.createProcessInstanceQuery();
        if (StringUtils.isNotBlank(processDefinitionName)) {
            processInstanceQuery.processDefinitionName(processDefinitionName);
        }
        if (StringUtils.isNotBlank(title)) {
            processInstanceQuery.variableValueLikeIgnoreCase(ProcessVariableConstants.TITLE, "%" + title + "%");
        }
        if (StringUtils.isNotBlank(initiator)) {
            processInstanceQuery.startedBy(initiator);
        }
        if (StringUtils.isNotBlank(involvedUser)) {
            processInstanceQuery.variableValueLike(ProcessVariableConstants.INVOLVED_USERS, "%" + involvedUser + "%");
        }
        if (StringUtils.isNotBlank(involvedGroup)) {
            processInstanceQuery.variableValueLike(ProcessVariableConstants.INVOLVED_GROUPS, "%" + involvedGroup + "%");
        }
        if (isActive != null) {
            if (isActive) {
                processInstanceQuery.active();
            } else {
                processInstanceQuery.suspended();
            }
        }
        if (StringUtils.isNotBlank(carbonCopy)) {
            processInstanceQuery.variableValueLike(ProcessVariableConstants.CARBON_COPY_USERS, "%" + carbonCopy + "%");
        }
        return processInstanceQuery;
    }

    @ApiOperation(value = "流程实例删除/提前结束")
    @PostMapping(value = "delete/{processInstanceId}")
    public BaseResponse<Void> deleteInstance(@PathVariable(value = "processInstanceId") String processInstanceId,
                                             @RequestParam(value = "deleteOaTaskFlag", required = false) boolean deleteOaTaskFlag,
                                             @RequestParam(value = "deleteReason") String deleteReason) {
        try {
            log.info("process instance delete ...");
            if (deleteOaTaskFlag) {
                // TODO 删除钉钉、企业微信、飞书待办

            }
            String initiator = runtimeService.getVariable(processInstanceId, ProcessVariableConstants.INITIATOR, String.class);
            String initiatorName = runtimeService.getVariable(processInstanceId, ProcessVariableConstants.INITIATOR_NAME, String.class);
            if (StringUtils.isNotEmpty(initiator)) {
                String nowUser = SystemHolder.getStaffCode();
                if (initiator.equals(nowUser)) {
                    runtimeService.setVariable(processInstanceId, ProcessVariableConstants.STATUS, ProcessStatusEnum.MANUAL_ABORT.getCode());
                    runtimeService.deleteProcessInstance(processInstanceId, deleteReason);
                    return new BaseResponse<>();
                } else {
                    return new BaseResponse<>(String.format("只有流程发起人可以删除该流程，请用户%s操作",
                            initiatorName + "[" + initiator + "]"));
                }
            } else {
                return new BaseResponse<>("流程实例删除失败");
            }
        } catch (Exception e) {
            log.error("Fail to delete process instance, ", e);
            return new BaseResponse<>("流程实例删除失败");
        }
    }

    @ApiOperation(value = "流程实例挂起")
    @PostMapping(value = "suspend/{processInstanceId}")
    public BaseResponse<Void> suspendProcessInstance(@PathVariable(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("process instance suspend ...");
            String initiator = runtimeService.getVariable(processInstanceId, ProcessVariableConstants.INITIATOR, String.class);
            String initiatorName = runtimeService.getVariable(processInstanceId, ProcessVariableConstants.INITIATOR_NAME, String.class);
            if (StringUtils.isNotEmpty(initiator)) {
                String nowUser = SystemHolder.getStaffCode();
                if (initiator.equals(nowUser)) {
                    runtimeService.suspendProcessInstanceById(processInstanceId);
                    return new BaseResponse<>();
                } else {
                    return new BaseResponse<>(String.format("只有流程发起人可以挂起该流程，请用户%s操作",
                            initiatorName + "[" + initiator + "]"));
                }
            } else {
                return new BaseResponse<>("流程实例挂起失败");
            }
        } catch (Exception e) {
            log.error("Fail to suspend process instance, ", e);
            return new BaseResponse<>("流程实例挂起失败");
        }
    }

    @ApiOperation(value = "流程实例激活")
    @PostMapping(value = "active/{processInstanceId}")
    public BaseResponse<Void> activeProcessInstance(@PathVariable(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("process instance active ...");
            String initiator = runtimeService.getVariable(processInstanceId, ProcessVariableConstants.INITIATOR, String.class);
            String initiatorName = runtimeService.getVariable(processInstanceId, ProcessVariableConstants.INITIATOR_NAME, String.class);
            if (StringUtils.isNotEmpty(initiator)) {
                String nowUser = SystemHolder.getStaffCode();
                if (initiator.equals(nowUser)) {
                    runtimeService.activateProcessInstanceById(processInstanceId);
                    return new BaseResponse<>();
                } else {
                    return new BaseResponse<>(String.format("只有流程发起人可以激活该流程，请用户%s操作",
                            initiatorName + "[" + initiator + "]"));
                }
            } else {
                return new BaseResponse<>("流程实例激活失败");
            }
        } catch (Exception e) {
            log.error("Fail to active process instance, ", e);
            return new BaseResponse<>("流程实例激活失败");
        }
    }

    @ApiOperation(value = "批注一览")
    @GetMapping(value = "overview/{processInstanceId}")
    public BaseResponse<RunningProcessVO> overview(@PathVariable(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("running process overview ...");
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId).singleResult();
            User startedBy = null;
            if (processInstance.getStartUserId() != null) {
                startedBy = identityService.createUserQuery().userId(processInstance.getStartUserId()).singleResult();
            }
            ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService
                    .getProcessDefinition(processInstance.getProcessDefinitionId());
            ProcessInstanceRepresentation processInstanceRepresentation = new ProcessInstanceRepresentation(processInstance,
                    processDefinitionEntity, processDefinitionEntity.isGraphicalNotationDefined(), startedBy);

            Map<String, Object> processVariables = runtimeService.getVariables(processInstanceRepresentation.getId());
            String titleName = processVariables.containsKey(ProcessVariableConstants.TITLE) ?
                    (String) processVariables.get(ProcessVariableConstants.TITLE) : "";
            ProcessInstanceVO processInstanceVO = new ProcessInstanceVO(processInstance, processDefinitionEntity,
                    processDefinitionEntity.isGraphicalNotationDefined(), startedBy,
                    titleName, null, null,
                    processInstance.isSuspended(), processInstance.getStartTime());
            RunningProcessVO build = RunningProcessVO.builder()
                    .processInstance(processInstanceVO)
                    .processVariables(processVariables).build();
            return new BaseResponse<>(Boolean.TRUE, build);
        } catch (Exception e) {
            log.error("Fail to overview running process, ", e);
            return new BaseResponse<>("流程监控批注一览失败");
        }
    }

    @ApiOperation(value = "添加流程变量")
    @PostMapping(value = "setVariables/{processInstanceId}")
    public BaseResponse<Void> setVariables(@PathVariable(value = "processInstanceId") String processInstanceId,
                                           @RequestParam(value = "variableKey", required = false) String variableKey,
                                           @RequestParam(value = "variableValue", required = false) Object variableValue,
                                           @RequestBody Map<String, Object> variableMap) {
        try {
            PlatformUser user = SystemHolder.getUser();
            if (MapUtils.isEmpty(variableMap)) {
                variableMap = new HashMap<>();
            }
            variableMap.put(ProcessVariableConstants.OPERATOR, user.getStaffCode());
            variableMap.put(ProcessVariableConstants.OPERATOR_NAME, user.getCnName());
            variableMap.put(ProcessVariableConstants.OPERATE_TIME, new Date());
            log.info("process variables add ...{}", JacksonUtils.toJson(variableMap));

            if (StringUtils.isNotBlank(variableKey) && Objects.nonNull(variableValue)) {
                runtimeService.setVariable(processInstanceId, variableKey, variableValue);
            } else if (MapUtils.isNotEmpty(variableMap)) {
                runtimeService.setVariables(processInstanceId, variableMap);
            }
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to add process variables, ", e);
            return new BaseResponse<>("添加流程变量失败");
        }
    }

    @ApiOperation(value = "获取流程变量")
    @GetMapping(value = "getVariables/{processInstanceId}")
    public BaseResponse<Object> getVariables(@PathVariable(value = "processInstanceId") String processInstanceId,
                                             @RequestParam(value = "variableKey", required = false) String variableKey) {
        try {
            log.info("process variable get ...");
            if (StringUtils.isNotBlank(variableKey)) {
                Object variable = runtimeService.getVariable(processInstanceId, variableKey);
                return new BaseResponse<>(Boolean.TRUE, variable);
            } else {
                Map<String, Object> variables = runtimeService.getVariables(processInstanceId);
                return new BaseResponse<>(Boolean.TRUE, variables);
            }
        } catch (Exception e) {
            log.error("Fail to get process variables, ", e);
            return new BaseResponse<>("获取流程变量失败");
        }
    }

    @ApiOperation("获取流程跟踪图")
    @GetMapping(value = "traceDiagram")
    public void traceDiagram(@RequestParam(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("trace process diagram ...");
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .singleResult();
            // 流程走完的不显示图
            if (processInstance == null) {
                return;
            }
            // 使用流程实例ID，查询正在执行的执行对象表，返回流程实例对象
            List<Execution> executions = runtimeService
                    .createExecutionQuery()
                    .processInstanceId(processInstanceId)
                    .list();

            // 得到正在执行的Activity的Id
            List<String> activityIds = new ArrayList<>();
            for (Execution execution : executions) {
                List<String> ids = runtimeService.getActiveActivityIds(execution.getId());
                activityIds.addAll(ids);
            }
            // 获取流程图并输出
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());
            ProcessEngineConfiguration engineConfiguration = processEngine.getProcessEngineConfiguration();
            ProcessDiagramGenerator diagramGenerator = engineConfiguration.getProcessDiagramGenerator();
            byte[] bytes = new byte[1024];
            int length;
            try (InputStream in = diagramGenerator.generateDiagram(bpmnModel, "png", activityIds, Collections.emptyList(),
                    engineConfiguration.getActivityFontName(), engineConfiguration.getLabelFontName(),
                    engineConfiguration.getAnnotationFontName(), null, 1.0, false);
                 OutputStream out = response.getOutputStream()) {
                while ((length = in.read(bytes)) != -1) {
                    out.write(bytes, 0, length);
                }
            }
        } catch (Exception e) {
            log.error("获取流程跟踪图失败, ", e);
        }
    }

    @ApiOperation(value = "Dashboard抄送任务分页查询")
    @GetMapping(value = "page4Dashboard")
    public BaseResponse<PageResult<ProcessInstanceVO>> page4Dashboard(@ApiParam(value = "抄送人")
                                                                      @RequestParam(value = "carbonCopy", required = false) String carbonCopy) {
        try {
            log.info("group page query for dashboard...");
            ProcessInstanceQuery processInstanceQuery = runtimeService.createProcessInstanceQuery();
            if (StringUtils.isNotBlank(carbonCopy)) {
                processInstanceQuery.variableValueLike(ProcessVariableConstants.CARBON_COPY_USERS, "%" + carbonCopy + "%");
            } else {
                String staffCode = SystemHolder.getStaffCode();
                processInstanceQuery.variableValueLike(ProcessVariableConstants.CARBON_COPY_USERS, "%" + staffCode + "%");
            }
            long totalCount = processInstanceQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<ProcessInstance> list = processInstanceQuery.orderByProcessInstanceId().desc()
                    .listPage(firstResult, getPagination().getPageSize());
            List<ProcessInstanceVO> dataList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (ProcessInstance processInstance : list) {
                    User startedBy = null;
                    if (processInstance.getStartUserId() != null) {
                        startedBy = identityService.createUserQuery().userId(processInstance.getStartUserId()).singleResult();
                    }
                    ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService
                            .getProcessDefinition(processInstance.getProcessDefinitionId());
                    Map<String, Object> variables = runtimeService.getVariables(processInstance.getId());
                    String titleName = variables.containsKey(ProcessVariableConstants.TITLE) ?
                            (String) variables.get(ProcessVariableConstants.TITLE) : "";
                    dataList.add(new ProcessInstanceVO(processInstance, processDefinitionEntity,
                            processDefinitionEntity.isGraphicalNotationDefined(), startedBy,
                            titleName, null, null,
                            processInstance.isSuspended(), processInstance.getStartTime()));
                }
            }
            PageResult<ProcessInstanceVO> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query running process page for dashboard, ", e);
            return new BaseResponse<>("Dashboard抄送任务分页查询失败");
        }
    }

}