package com.yhl.scp.ips.bpm.controller;

import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.bpm.constants.ProcessVariableConstants;
import com.yhl.scp.ips.bpm.service.BpmHistoryService;
import com.yhl.scp.ips.bpm.vo.HistoricProcessInstanceVO;
import com.yhl.scp.ips.bpm.vo.PageResult;
import com.yhl.scp.ips.bpm.vo.ProcessInstanceVO;
import com.yhl.scp.ips.bpm.vo.RunningProcessVO;
import com.yhl.scp.ips.common.SystemHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.IdentityService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.impl.persistence.entity.HistoricProcessInstanceEntity;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.flowable.idm.api.User;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>BpmHistoryController</code>
 * <p>
 * BPM流程历史管理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-05-16 18:05:17
 */
@Api(tags = "BPM流程历史管理")
@RestController
@RequestMapping(value = "bpmHistory")
@Slf4j
public class BpmHistoryController extends BaseController {

    @Autowired
    private IdentityService identityService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private BpmHistoryService bpmHistoryService;

    @ApiOperation("流程历史分页查询")
    @GetMapping("page")
    public BaseResponse<PageResult<HistoricProcessInstanceVO>> page(@ApiParam(value = "流程名称,精准")
                                                                    @RequestParam(value = "processDefinitionName", required = false) String processDefinitionName,
                                                                    @ApiParam(value = "标题,模糊")
                                                                    @RequestParam(value = "title", required = false) String title,
                                                                    @ApiParam(value = "发起人,精准")
                                                                    @RequestParam(value = "initiator", required = false) String initiator,
                                                                    @ApiParam(value = "涉及人,精准")
                                                                    @RequestParam(value = "involvedUser", required = false) String involvedUser,
                                                                    @ApiParam(value = "涉及组,精准")
                                                                    @RequestParam(value = "involvedGroup", required = false) String involvedGroup,
                                                                    @ApiParam(value = "抄送人")
                                                                    @RequestParam(value = "carbonCopy", required = false) String carbonCopy,
                                                                    @ApiParam(value = "是否结束")
                                                                    @RequestParam(value = "finished", required = false) Boolean finished,
                                                                    @ApiParam(value = "是否删除")
                                                                    @RequestParam(value = "deleted", required = false) Boolean deleted) {
        try {
            log.info("historic process page query ...");
            // 流程历史查询条件组装
            HistoricProcessInstanceQuery historicProcessInstanceQuery = assembleHistoricProcessInstanceQuery(
                    processDefinitionName, title, initiator, involvedUser, involvedGroup, carbonCopy);
            historicProcessInstanceQuery = assembleHistoricProcessInstanceQuery(historicProcessInstanceQuery, finished, deleted);
            long totalCount = historicProcessInstanceQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<HistoricProcessInstance> list = historicProcessInstanceQuery.orderByProcessInstanceStartTime()
                    .desc().listPage(firstResult, getPagination().getPageSize());
            // 流程历史数据组装
            List<HistoricProcessInstanceVO> dataList = assembleHistoricProcessInstanceVOList(list);
            for (HistoricProcessInstanceVO instanceVO : dataList) {
                String title1 = instanceVO.getTitle();
                if (StringUtils.isNotBlank(title1)) {
                    instanceVO.setTitle(title1.split("#")[0]);
                }
            }
            PageResult<HistoricProcessInstanceVO> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query historic process page, ", e);
            return new BaseResponse<>("流程历史分页查询失败");
        }
    }

    /**
     * 组装查询条件
     *
     * @param processDefinitionName 流程定义名
     * @param title                 标题
     * @param initiator             发起人
     * @param involvedUser          涉及人
     * @param involvedGroup         涉及组
     * @param carbonCopy            抄送人
     * @return org.flowable.engine.history.HistoricProcessInstanceQuery
     */
    private HistoricProcessInstanceQuery assembleHistoricProcessInstanceQuery(String processDefinitionName, String title,
                                                                              String initiator, String involvedUser,
                                                                              String involvedGroup, String carbonCopy) {
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService
                .createHistoricProcessInstanceQuery();
        if (StringUtils.isNotBlank(processDefinitionName)) {
            historicProcessInstanceQuery.processDefinitionName(processDefinitionName);
        }
        if (StringUtils.isNotBlank(title)) {
            historicProcessInstanceQuery.variableValueLikeIgnoreCase(ProcessVariableConstants.TITLE, "%" + title + "%");
        }
        if (StringUtils.isNotBlank(initiator)) {
            historicProcessInstanceQuery.startedBy(initiator);
        }
        if (StringUtils.isNotBlank(involvedUser)) {
            historicProcessInstanceQuery.variableValueLike(ProcessVariableConstants.INVOLVED_USERS, "%" + involvedUser + "%");
        }
        if (StringUtils.isNotBlank(involvedGroup)) {
            historicProcessInstanceQuery.variableValueLike(ProcessVariableConstants.INVOLVED_GROUPS, "%" + involvedGroup + "%");
        }
        if (StringUtils.isNotBlank(carbonCopy)) {
            historicProcessInstanceQuery.variableValueLike(ProcessVariableConstants.CARBON_COPY_USERS, "%" + carbonCopy + "%");
        }
        return historicProcessInstanceQuery;
    }

    /**
     * 组装查询条件
     *
     * @param historicProcessInstanceQuery 查询对象
     * @param finished                     是否结束
     * @param deleted                      是否删除
     * @return org.flowable.engine.history.HistoricProcessInstanceQuery
     */
    private HistoricProcessInstanceQuery assembleHistoricProcessInstanceQuery(HistoricProcessInstanceQuery historicProcessInstanceQuery,
                                                                              Boolean finished, Boolean deleted) {
        if (historicProcessInstanceQuery == null) {
            historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery();
        }
        if (finished != null) {
            if (finished) {
                historicProcessInstanceQuery.finished();
            } else {
                historicProcessInstanceQuery.unfinished();
            }
        }
        if (deleted != null) {
            if (deleted) {
                historicProcessInstanceQuery.deleted();
            } else {
                historicProcessInstanceQuery.notDeleted();
            }
        }
        return historicProcessInstanceQuery;
    }

    /**
     * 组装查询结果
     *
     * @param list 流程历史数据列表
     * @return java.util.List<com.yhl.network.bpm.vo.HistoricProcessInstanceVO>
     */
    private List<HistoricProcessInstanceVO> assembleHistoricProcessInstanceVOList(List<HistoricProcessInstance> list) {
        List<HistoricProcessInstanceVO> dataList = new ArrayList<>();
        // 流程历史数据组装
        for (HistoricProcessInstance historicProcessInstance : list) {
            HistoricProcessInstanceEntity entity = (HistoricProcessInstanceEntity) historicProcessInstance;
            HistoricProcessInstanceVO item = HistoricProcessInstanceVO.builder()
                    .id(entity.getId())
                    .name(entity.getName())
                    .processDefinitionName(entity.getProcessDefinitionName())
                    .deleteReason(entity.getDeleteReason())
                    .startUserId(entity.getStartUserId())
                    .startTime(entity.getStartTime())
                    .endTime(entity.getEndTime())
                    .build();
            String processInstanceId = entity.getProcessInstanceId();
            log.warn("@@@@@@@@@@@@@@@@@@@@ PROCESS_INSTANCE_ID = {}", processInstanceId);
            Map<String, Object> processVariables = new HashMap<>(16);
            List<HistoricVariableInstance> variableInstances = historyService
                    .createHistoricVariableInstanceQuery()
                    .processInstanceId(processInstanceId).executionId(processInstanceId)
                    .list();
            variableInstances.forEach(e -> processVariables.put(e.getVariableName(), e.getValue()));
            if (processVariables.containsKey(ProcessVariableConstants.TITLE)) {
                item.setTitle((String) processVariables.get(ProcessVariableConstants.TITLE));
            }
            if (processVariables.containsKey(ProcessVariableConstants.INITIATOR_NAME)) {
                item.setStartUserName((String) processVariables.get(ProcessVariableConstants.INITIATOR_NAME));
            }

            List<HistoricActivityInstance> historicActivityInstances = historyService
                    .createHistoricActivityInstanceQuery().processInstanceId(entity.getId())
                    .orderByHistoricActivityInstanceStartTime().desc().list();
            if (CollectionUtils.isNotEmpty(historicActivityInstances)) {
                HistoricActivityInstance historicActivityInstance = historicActivityInstances.get(0);
                item.setEndActivityId(historicActivityInstance.getActivityId());
                item.setEndActivityName(historicActivityInstance.getActivityName());
            }
            dataList.add(item);
        }
        return dataList;
    }

    @ApiOperation("流程历史删除")
    @PostMapping("delete/{processInstanceId}")
    public BaseResponse<Void> delete(@PathVariable(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("historic process delete ...");
            historyService.deleteHistoricProcessInstance(processInstanceId);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to delete historic process, ", e);
            return new BaseResponse<>("流程历史删除失败");
        }
    }

    @ApiOperation("历史任务实例查询")
    @GetMapping("historicTaskInstance/list")
    public BaseResponse<List<HistoricTaskInstance>> historicTaskInstanceList(@RequestParam(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("historic task instance list query ...");
            List<HistoricTaskInstance> list = historyService
                    .createHistoricTaskInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .orderByTaskCreateTime().asc()
                    .list();
            return new BaseResponse<>(Boolean.TRUE, list);
        } catch (Exception e) {
            log.error("Fail to query historic task instance list, ", e);
            return new BaseResponse<>("历史任务实例查询失败");
        }
    }

    @ApiOperation("流程历史变量查询")
    @GetMapping("getVariables")
    public BaseResponse<Map<String, Object>> getVariables(@RequestParam(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("historic process variables get ...");
            Map<String, Object> data = new HashMap<>(16);
            List<HistoricVariableInstance> list = historyService
                    .createHistoricVariableInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .list();
            list.forEach(e -> data.put(e.getVariableName(), e.getValue()));
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query historic task instance list, ", e);
            return new BaseResponse<>("流程历史变量查询失败");
        }
    }

    @ApiOperation("流程历史变量添加")
    @PostMapping("addVariables")
    public BaseResponse<Void> addVariables(@RequestParam(value = "processInstanceId") String processInstanceId,
                                           @RequestBody Map<String, String> variables) {
        try {
            log.error("historic process variables add ...");
            variables.forEach((k, v) -> bpmHistoryService.doInsertVariables(processInstanceId, k, v));
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to add historic variables, ", e);
            return new BaseResponse<>("流程历史变量添加失败");
        }
    }

    @ApiOperation(value = "批注一览")
    @GetMapping(value = "overview/{processInstanceId}")
    public BaseResponse<RunningProcessVO> overview(@PathVariable(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("historic process overview ...");
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId).singleResult();

            User startedBy = null;
            if (historicProcessInstance.getStartUserId() != null) {
                startedBy = identityService.createUserQuery().userId(historicProcessInstance.getStartUserId()).singleResult();
            }
            ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService
                    .getProcessDefinition(historicProcessInstance.getProcessDefinitionId());
            /*ProcessInstanceRepresentation processInstanceRepresentation = new ProcessInstanceRepresentation(
                    historicProcessInstance, processDefinitionEntity, processDefinitionEntity.isGraphicalNotationDefined(), startedBy);*/

            Map<String, Object> processVariables = new HashMap<>(16);
            List<HistoricVariableInstance> list = historyService
                    .createHistoricVariableInstanceQuery()
                    .processInstanceId(processInstanceId).executionId(processInstanceId)
                    .list();
            list.forEach(e -> processVariables.put(e.getVariableName(), e.getValue()));
            String titleName = processVariables.containsKey(ProcessVariableConstants.TITLE) ?
                    (String) processVariables.get(ProcessVariableConstants.TITLE) : "";
            ProcessInstanceVO processInstanceVO = new ProcessInstanceVO(historicProcessInstance, processDefinitionEntity,
                    processDefinitionEntity.isGraphicalNotationDefined(), startedBy,
                    titleName, null, null,
                    false, historicProcessInstance.getStartTime());
            RunningProcessVO build = RunningProcessVO.builder()
                    .processInstance(processInstanceVO)
                    .processVariables(processVariables).build();
            return new BaseResponse<>(Boolean.TRUE, build);
        } catch (Exception e) {
            log.error("Fail to overview historic process, ", e);
            return new BaseResponse<>("流程历史批注一览失败");
        }
    }

    @ApiOperation("历史活动列表查询")
    @GetMapping("historicActivity/list")
    public BaseResponse<List<HistoricActivityInstance>> historicActivityList(@RequestParam(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("historic activity list query ...");
            List<HistoricActivityInstance> list = historyService
                    .createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstanceId)
                    .orderByHistoricActivityInstanceStartTime().asc()
                    .list();
            return new BaseResponse<>(Boolean.TRUE, list);
        } catch (Exception e) {
            log.error("Fail to query historic activity list, ", e);
            return new BaseResponse<>("历史活动列表查询失败");
        }
    }

    @ApiOperation("流程历史发布到公告")
    @GetMapping("publishToBulletin")
    public BaseResponse<Void> publishToBulletin(@RequestParam(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("historic process publish to bulletin ...");
            // TODO

            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to publish historic process to bulletin, ", e);
            return new BaseResponse<>("流程历史发布到公告失败");
        }
    }

    @ApiOperation("流程历史从公告撤回")
    @GetMapping("revokeFromBulletin")
    public BaseResponse<Void> revokeFromBulletin(@RequestParam(value = "processInstanceId") String processInstanceId) {
        try {
            log.info("historic process revoke from bulletin ...");
            // TODO

            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to revoke historic process from bulletin, ", e);
            return new BaseResponse<>("流程历史从公告撤回失败");
        }
    }

    @ApiOperation("流程历史Dashboard分页查询")
    @GetMapping("page4Dashboard")
    public BaseResponse<PageResult<HistoricProcessInstanceVO>> page4Dashboard(@ApiParam(value = "抄送人")
                                                                              @RequestParam(value = "carbonCopy", required = false) String carbonCopy) {
        try {
            log.info("historic process page query for dashboard...");
            HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService
                    .createHistoricProcessInstanceQuery();
            if (StringUtils.isNotBlank(carbonCopy)) {
                historicProcessInstanceQuery.variableValueLike(ProcessVariableConstants.CARBON_COPY_USERS, "%" + carbonCopy + "%");
            } else {
                String staffCode = SystemHolder.getStaffCode();
                historicProcessInstanceQuery.variableValueLike(ProcessVariableConstants.CARBON_COPY_USERS, "%" + staffCode + "%");
            }
            long totalCount = historicProcessInstanceQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<HistoricProcessInstance> list = historicProcessInstanceQuery.orderByProcessInstanceStartTime().desc()
                    .listPage(firstResult, getPagination().getPageSize());
            List<HistoricProcessInstanceVO> dataList = new ArrayList<>();
            for (HistoricProcessInstance historicProcessInstance : list) {
                HistoricProcessInstanceEntity entity = (HistoricProcessInstanceEntity) historicProcessInstance;
                HistoricProcessInstanceVO item = HistoricProcessInstanceVO.builder()
                        .id(entity.getId())
                        .name(entity.getName())
                        .processDefinitionName(entity.getProcessDefinitionName())
                        .deleteReason(entity.getDeleteReason())
                        .startUserId(entity.getStartUserId())
                        .startTime(entity.getStartTime())
                        .endTime(entity.getEndTime())
                        .build();
                String processInstanceId = entity.getProcessInstanceId();
                Map<String, Object> processVariables = new HashMap<>(16);
                List<HistoricVariableInstance> variableInstances = historyService
                        .createHistoricVariableInstanceQuery()
                        .processInstanceId(processInstanceId).executionId(processInstanceId)
                        .list();
                variableInstances.forEach(e -> processVariables.put(e.getVariableName(), e.getValue()));
                if (processVariables.containsKey(ProcessVariableConstants.TITLE)) {
                    item.setTitle((String) processVariables.get(ProcessVariableConstants.TITLE));
                }
                if (processVariables.containsKey(ProcessVariableConstants.INITIATOR_NAME)) {
                    item.setStartUserName((String) processVariables.get(ProcessVariableConstants.INITIATOR_NAME));
                }
                dataList.add(item);
            }
            PageResult<HistoricProcessInstanceVO> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query historic process page for dashboard, ", e);
            return new BaseResponse<>("流程历史Dashboard分页查询失败");
        }
    }

}