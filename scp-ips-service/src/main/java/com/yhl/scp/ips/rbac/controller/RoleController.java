package com.yhl.scp.ips.rbac.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.ips.rbac.entity.Role;
import com.yhl.scp.ips.rbac.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <code>RoleController</code>
 * <p>
 * RoleController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
@Api(tags = "权限管理")
@RestController
@RequestMapping("role")
public class RoleController extends BaseController {

    @Autowired
    private RoleService roleService;

    @ApiOperation("创建角色")
    @RequestMapping(value = "/creat", method = RequestMethod.POST)
    public BaseResponse create(Role role) {
        return roleService.doCreate(role);
    }

    @ApiOperation("修改角色")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public BaseResponse update(Role role) {
        return roleService.doUpdate(role);
    }

    @ApiOperation("分页查询")
    @RequestMapping(value = "/page", method = RequestMethod.GET)
    public BaseResponse page() {
        List<Role> roles = roleService.selectPage(getPagination(), getSortParam(), getQueryCriteriaParam());
        PageInfo<Role> pageInfo = new PageInfo<>(roles);
        return new BaseResponse(true, pageInfo);
    }

    @ApiOperation("根据id查询")
    @RequestMapping(value = "/{roleId}", method = RequestMethod.GET)
    public BaseResponse getRole(@PathVariable("roleId") String roleId) {
        return new BaseResponse(Boolean.TRUE, roleService.getById(roleId));
    }

    @ApiOperation("根据id删除")
    @RequestMapping(value = "/{roleId}/delete", method = RequestMethod.POST)
    public BaseResponse delete(@PathVariable("roleId") String roleId) {
        roleService.doDelete(roleId);
        return BaseResponse.success();
    }

    @ApiOperation("是否可以打开工作台")
    @RequestMapping(value = "/whetherOpenWorkBench", method = RequestMethod.GET)
    public BaseResponse whetherOpenWorkBench() {
        boolean whetherOpenWorkBench = roleService.whetherOpenWorkBench();
        return BaseResponse.success(whetherOpenWorkBench);
    }

    @ApiOperation("角色列表")
    @RequestMapping(value = "/roles", method = RequestMethod.GET)
    public BaseResponse selectRoles() {
        return new BaseResponse(Boolean.TRUE, roleService.selectRoles());
    }

    @ApiOperation("角色分配菜单")
    @RequestMapping(value = "/role/resources", method = RequestMethod.POST)
    public BaseResponse createRoleResources(
            @RequestParam("roleId") String roleId,
            @RequestParam("resourceIds") String resourceIds) {
        List<String> resourceIdList = new ArrayList<String>();
        if (!StringUtils.isEmpty(resourceIds)) {
            resourceIdList = Arrays.asList(resourceIds.split(","));
        }
        roleService.doCreateRoleResources(roleId, resourceIdList);
        return new BaseResponse();
    }


}