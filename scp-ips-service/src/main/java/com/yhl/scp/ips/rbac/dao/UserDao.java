package com.yhl.scp.ips.rbac.dao;

import com.yhl.scp.ips.rbac.entity.User;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>UserDao</code>
 * <p>
 * UserDao
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-07-17 20:21:06
 */
public interface UserDao {

    int deleteByPrimaryKey(String id);

    int insert(User record);

    User selectByPrimaryKey(String id);

    int updateByPrimaryKey(User record);

    User selectByUserName(String userName);

    List<User> selectByPage(@Param("sortParam") String sortParam,
                            @Param("queryCriteriaParam") String queryCriteriaParam, @Param("tenantId") String tenantId);

    List<User> selectAll();

    List<User> selectByParams(@Param(value = "params") Map<String, Object> params);

    int insertBatchWithPrimaryKey(@Param("list") List<User> insertUserList);

    void deleteUserRelative(@Param(value = "userId") String userId);

	List<User> selectOrderPlannerDropDown();

}