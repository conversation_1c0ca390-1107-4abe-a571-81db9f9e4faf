package com.yhl.scp.ips.bpm.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.FileNameUtils;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.scp.ips.bpm.vo.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.editor.constants.ModelDataJsonConstants;
import org.flowable.editor.language.json.converter.BpmnJsonConverter;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.Model;
import org.flowable.engine.repository.ModelQuery;
import org.flowable.ui.modeler.serviceapi.ModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.zip.ZipInputStream;

/**
 * <code>BpmModelController</code>
 * <p>
 * BPM模型管理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021-11-21 19:35:00
 */
@Api(tags = "BPM流程模型管理")
@RestController
@RequestMapping(value = "bpmModel")
@Slf4j
public class BpmModelController extends BaseController {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    protected ModelService modelService;

    @ApiOperation(value = "模型分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageResult<Model>> page(@RequestParam(value = "modelName", required = false) String modelName) {
        try {
            log.info("model page query ...");
            // 查询条件组装
            ModelQuery modelQuery = repositoryService.createModelQuery();
            if (StringUtils.isNotEmpty(modelName)) {
                modelQuery = modelQuery.modelNameLike("%" + modelName + "%");
            }
            long totalCount = modelQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<Model> list = modelQuery.orderByLastUpdateTime().orderByCreateTime().desc()
                    .listPage(firstResult, getPagination().getPageSize());

            PageResult<Model> data = new PageResult<>(list, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query model page, ", e);
            return new BaseResponse<>("模型分页查询失败");
        }
    }

    @ApiOperation(value = "模型创建")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestParam(value = "modelKey") String modelKey,
                                     @RequestParam(value = "modelName") String modelName,
                                     @RequestParam(value = "modelDescription", required = false) String modelDescription) {
        try {
            log.info("model create ...");
            ObjectNode editorNode = JacksonUtils.createEmptyObjectNode();
            editorNode.put("id", "canvas");
            editorNode.put("resourceId", "canvas");
            ObjectNode stencilSetNode = JacksonUtils.createEmptyObjectNode();
            stencilSetNode.put("namespace", "http://b3mn.org/stencilset/bpmn2.0#");
            editorNode.set("stencilset", stencilSetNode);
            Model modelData = repositoryService.newModel();

            ObjectNode modelObjectNode = JacksonUtils.createEmptyObjectNode();
            modelObjectNode.put(ModelDataJsonConstants.MODEL_NAME, modelName);
            modelObjectNode.put(ModelDataJsonConstants.MODEL_REVISION, 1);
            modelObjectNode.put(ModelDataJsonConstants.MODEL_DESCRIPTION, StringUtils.defaultString(modelDescription));
            modelData.setMetaInfo(modelObjectNode.toString());
            modelData.setName(modelName);
            modelData.setKey(StringUtils.defaultString(modelKey));
            repositoryService.saveModel(modelData);
            repositoryService.addModelEditorSource(modelData.getId(), editorNode.toString().getBytes(StandardCharsets.UTF_8));
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to create model, ", e);
            return new BaseResponse<>("模型创建失败");
        }
    }

    @ApiOperation(value = "模型部署")
    @PostMapping(value = "deploy/{modelId}")
    public BaseResponse<Deployment> deploy(@PathVariable(value = "modelId") String modelId) {
        try {
            log.info("model deploy ...");
            org.flowable.ui.modeler.domain.Model model = this.modelService.getModel(modelId);
            byte[] bpmnBytes = modelService.getBpmnXML(model);
            String processName = model.getName() + BPMN_XML_MODEL_FILE;
            Deployment deployment = repositoryService.createDeployment().name(model.getName())
                    .addString(processName, new String(bpmnBytes, StandardCharsets.UTF_8)).deploy();
            return new BaseResponse<>(Boolean.TRUE, deployment);
        } catch (Exception e) {
            log.error("Fail to deploy model, ", e);
            return new BaseResponse<>("模型部署失败");
        }
    }

    public static final String BAR_MODEL_FILE = ".bar";

    public static final String ZIP_MODEL_FILE = ".zip";

    public static final String BPMN_MODEL_FILE = ".bpmn";

    public static final String BPMN_XML_MODEL_FILE = ".bpmn20.xml";

    @ApiOperation(value = "模型文件部署")
    @PostMapping(value = "deploy")
    public BaseResponse<Deployment> deployMultipartFile(@RequestPart(value = "file") MultipartFile file) {
        String fileName = file.getOriginalFilename();
        assert fileName != null;
        FileNameUtils.directoryTraversal(fileName);
        try {
            log.info("model deploy by file ...");
            InputStream fileInputStream = file.getInputStream();
            Deployment deployment;
            if (fileName.endsWith(ZIP_MODEL_FILE) || fileName.endsWith(BAR_MODEL_FILE)) {
                // 压缩文件部署
                ZipInputStream zip = new ZipInputStream(fileInputStream);
                deployment = repositoryService.createDeployment().addZipInputStream(zip).deploy();
            } else if (fileName.endsWith(BPMN_MODEL_FILE) || fileName.endsWith(BPMN_XML_MODEL_FILE)) {
                // 输入流部署
                deployment = repositoryService.createDeployment().addInputStream(fileName, fileInputStream).deploy();
            } else {
                return new BaseResponse<>("模型文件非.bpmn，.bpmn20.xml，.zip，.bar格式");
            }
            return new BaseResponse<>(Boolean.TRUE, deployment);
        } catch (Exception e) {
            log.error("Fail to deploy model by file, ", e);
            return new BaseResponse<>("模型文件部署失败");
        }
    }

    public static final String EXPORT_MODEL_FILE_TYPE_BPMN = "bpmn";

    public static final String EXPORT_MODEL_FILE_TYPE_JSON = "json";

    @ApiOperation(value = "模型导出")
    @GetMapping(value = "export/{modelId}/{type}")
    public void export(@PathVariable(value = "modelId") String modelId,
                       @ApiParam(allowableValues = "bpmn,json") @PathVariable(value = "type") String type) {
        try {
            log.info("model export ...");
            Model modelData = repositoryService.getModel(modelId);
            BpmnJsonConverter jsonConverter = new BpmnJsonConverter();
            byte[] modelEditorSource = repositoryService.getModelEditorSource(modelData.getId());

            JsonNode editorNode = JacksonUtils.toObj(Arrays.toString(modelEditorSource));
            BpmnModel bpmnModel = jsonConverter.convertToBpmnModel(editorNode);

            // 处理异常
            if (bpmnModel.getMainProcess() == null) {
                response.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value());
                response.getOutputStream().println("no main process, can't export for type: " + type);
                response.flushBuffer();
                return;
            }
            String filename = "";
            byte[] exportBytes = null;
            String mainProcessId = bpmnModel.getMainProcess().getId();
            if (EXPORT_MODEL_FILE_TYPE_BPMN.equals(type)) {
                BpmnXMLConverter xmlConverter = new BpmnXMLConverter();
                exportBytes = xmlConverter.convertToXML(bpmnModel);
                filename = mainProcessId + BPMN_XML_MODEL_FILE;
            } else if (EXPORT_MODEL_FILE_TYPE_JSON.equals(type)) {
                exportBytes = modelEditorSource;
                filename = mainProcessId + ".json";
            }

            assert exportBytes != null;
            ByteArrayInputStream in = new ByteArrayInputStream(exportBytes);
            IOUtils.copy(in, response.getOutputStream());

            response.setHeader("Content-Disposition", "attachment; filename=" + filename);
            response.flushBuffer();
        } catch (Exception e) {
            log.error("Fail to export model file", e);
        }
    }

    @ApiOperation(value = "模型删除")
    @PostMapping(value = "delete/{modelId}")
    public BaseResponse<Void> delete(@PathVariable(value = "modelId") String modelId) {
        try {
            log.info("model delete ...");
            repositoryService.deleteModel(modelId);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to delete model, ", e);
            return new BaseResponse<>("模型删除失败");
        }
    }

}