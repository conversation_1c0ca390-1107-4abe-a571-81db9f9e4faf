package com.yhl.scp.ips.bpm.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.bpm.constants.ProcessVariableConstants;
import com.yhl.scp.ips.enums.MessageTypeEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.untils.UserMessageUtils;
import com.yhl.scp.mps.plan.req.CoordinatedChangeReq;
import com.yhl.scp.ips.rbac.entity.User;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <code>ProductionCoordinateChangeListener</code>
 * <p>
 * 监听用户任务（UserTask）的创建、分配、完成等事件。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-11-06 14:47:23
 */
@Component
@Slf4j
public class CoordinatedChangeListener implements TaskListener {

    private static final long serialVersionUID = -793433528583631539L;

    @Autowired
    private DfpFeign dfpFeign;

    @Autowired
    private IpsNewFeign ipsNewFeign;

    public static final String DELIVERY_PLAN_DATA_LIST = "deliveryPlanDataList";

    @Autowired
    private UserMessageUtils userMessageUtils;

    @Override
    public void notify(DelegateTask delegateTask) {
        String processInstanceId = delegateTask.getProcessInstanceId();
        String taskId = delegateTask.getId();

        String productCode = (String) delegateTask.getVariable("productCode");
        String scenario = (String) delegateTask.getVariable("scenario");
        String assignee = delegateTask.getAssignee();
        String initiator = (String) delegateTask.getVariable(ProcessVariableConstants.INITIATOR);

        Map<String, Object> taskVariables = JacksonUtils.toObj(JacksonUtils.toJson(delegateTask.getVariable(taskId)),
                new TypeReference<LinkedHashMap<String, Object>>() {});
        if (MapUtils.isEmpty(taskVariables)) {
            return;
        }
        if (!taskVariables.containsKey(ProcessVariableConstants.OPINION)) {
            return;
        }
        String opinion = (String) taskVariables.get(ProcessVariableConstants.OPINION);
        if (StringUtils.isBlank(opinion)) {
            return;
        }
        Object object = delegateTask.getVariable(DELIVERY_PLAN_DATA_LIST);
        List<LinkedHashMap<String, Object>> deliveryPlanDataList = JacksonUtils.toObj(JacksonUtils.toJson(object),
                new TypeReference<List<LinkedHashMap<String, Object>>>() {});
        List<DeliveryPlanPublishedDTO> publishedList = new ArrayList<>();
        for (LinkedHashMap<String, Object> item : deliveryPlanDataList) {
            DeliveryPlanPublishedDTO publishedDTO = new DeliveryPlanPublishedDTO();
            publishedDTO.setId((String) item.get("id"));
            publishedDTO.setOemCode((String) item.get("oemCode"));
            publishedDTO.setDemandCategory((String) item.get("demandCategory"));
            publishedDTO.setProductCode((String) item.get("productCode"));
            publishedDTO.setCoordinationQuantity((int) item.get("coordinationQuantity"));
            publishedDTO.setDemandQuantity((int) item.get("demandQuantity"));
            publishedList.add(publishedDTO);
        }
        if (ProcessVariableConstants.AGREE.equals(opinion)) {
            // 产生订单计划变更的待办任务后，点击同意按钮将发货计划按照生产协调数量更新；操作后该待办不再展示，返回变更发起者一则变更完成的消息；
            String message = String.format("订单计划员【%s】，同意零件【%s】需求量变更", assignee, productCode);
            log.info(message);
            // 1.更新需求数量为生产协调量并发布
            BaseResponse<Void> response = dfpFeign.updateDemandQuantityAndPublish(scenario, assignee, publishedList);
            if (Boolean.FALSE.equals(response.getSuccess())) {
                throw new BusinessException(response.getMsg());
            }
            // 2.发送消息给流程发起者
            sendMessage(initiator, message, processInstanceId);
        } else {
            // 产生订单计划变更的待办任务后，点击不同意按钮将生产协调数量置空；操作后该待办不再展示；返回变更发起者一则变更未通过的消息；
            String message = String.format("订单计划员【%s】，不同意零件【%s】需求量变更", assignee, productCode);
            log.info(message);
            // 1.置空生产协调数量
            dfpFeign.updateCoordinationQuantity(scenario, true, publishedList);
            // 2.发送消息给流程发起者
            sendMessage(initiator, message, processInstanceId);
        }
    }

    /**
     * 发送消息
     *
     * @param initiator   发起人
     * @param message     消息体
     * @param messageLink 消息链接
     */
    public void sendMessage(String initiator, String message, String messageLink) {
        User user = ipsNewFeign.getUserByUserName(initiator);
        if (user == null) {
            return;
        }
        UserMessageDTO result = UserMessageDTO.builder()
                .id(UUIDUtil.getUUID())
                .userId(user.getId())
                .roleId(null)
                .messageSource("业务消息")
                .messageType(MessageTypeEnum.MY_MESSAGE.getCode())
                .messageTitle("生产反馈联动发货计划变更")
                .messageContent(message)
                .messageLink(messageLink)
                .messageEmergency("3")
                .readStatus(YesOrNoEnum.NO.getCode())
                .extraInfo(null)
                .build();
        userMessageUtils.sendMessage(Lists.newArrayList(result));
    }

}