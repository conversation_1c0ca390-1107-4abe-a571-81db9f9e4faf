package com.yhl.scp.ips.bpm.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.PlatformUser;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.scp.ips.bpm.constants.ProcessVariableConstants;
import com.yhl.scp.ips.bpm.vo.*;
import com.yhl.scp.ips.common.SystemHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.Gateway;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.engine.*;
import org.flowable.engine.form.StartFormData;
import org.flowable.engine.form.TaskFormData;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.idm.api.Group;
import org.flowable.idm.api.User;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.flowable.ui.task.model.runtime.TaskRepresentation;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>BpmTaskController</code>
 * <p>
 * BPM任务管理
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021-11-22 14:34:00
 */
@Api(tags = "BPM流程任务管理")
@RestController
@RequestMapping(value = "bpmTask")
@Slf4j
public class BpmTaskController extends BaseController {

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private IdentityService identityService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private FormService formService;

    public static final String TASK_ABSENT_MESSAGE = "任务已不存在，请刷新页面";

    @ApiOperation(value = "待办任务分页查询")
    @GetMapping(value = "todo/page")
    public BaseResponse<PageResult<TaskVO>> todoPage(@RequestParam(value = "staffCode", required = false) String staffCode) {
        try {
            log.info("todo task page query ...");
            if (StringUtils.isBlank(staffCode)) {
                staffCode = SystemHolder.getStaffCode();
                if (StringUtils.isBlank(staffCode)) {
                    return new BaseResponse<>(Boolean.TRUE, null);
                }
            }
            // 查询条件组装
            TaskQuery taskQuery = taskService.createTaskQuery().taskAssignee(staffCode);
            long totalCount = taskQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<Task> list = taskQuery.orderByTaskCreateTime().desc().listPage(firstResult,
                    getPagination().getPageSize());
            List<TaskVO> dataList = new ArrayList<>();
            // 结果集组装
            if (CollectionUtils.isNotEmpty(list)) {
                for (Task task : list) {
                    ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                            .processInstanceId(task.getProcessInstanceId()).singleResult();
                    Map<String, Object> variables = runtimeService.getVariables(processInstance.getProcessInstanceId());
                    boolean read = variables.containsKey(String.join("#", task.getTaskDefinitionKey(), staffCode));
                    ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                            .processDefinitionId(task.getProcessDefinitionId()).singleResult();

                    Map<String, Object> processVariables = runtimeService.getVariables(processInstance.getId());
                    dataList.add(new TaskVO(task, processDefinition, processInstance.getName(),
                            (String) variables.getOrDefault(ProcessVariableConstants.TITLE, null),
                            (String) variables.getOrDefault(ProcessVariableConstants.INITIATOR_NAME, null), read, processVariables));
                }
            }
            PageResult<TaskVO> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query todo task page, ", e);
            return new BaseResponse<>("待办任务分页查询失败，" + e.getMessage());
        }
    }

    @ApiOperation(value = "待领任务分页查询")
    @GetMapping(value = "unclaimed/page")
    public BaseResponse<PageResult<TaskVO>> unclaimedPage(@RequestParam(value = "staffCode", required = false) String staffCode) {
        try {
            log.info("not claimed task page query ...");
            if (StringUtils.isBlank(staffCode)) {
                staffCode = SystemHolder.getUserName();
            }
            TaskQuery taskQuery = taskService.createTaskQuery().taskCandidateUser(staffCode);
            long totalCount = taskQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<Task> list = taskQuery.orderByTaskCreateTime().asc().listPage(firstResult, getPagination().getPageSize());
            List<TaskVO> dataList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (Task task : list) {
                    ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                            .processInstanceId(task.getProcessInstanceId()).singleResult();
                    Map<String, Object> variables = runtimeService.getVariables(processInstance.getProcessInstanceId());
                    boolean read = variables.containsKey(String.join("#", task.getTaskDefinitionKey(), staffCode));
                    ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                            .processDefinitionId(task.getProcessDefinitionId()).singleResult();
                    Map<String, Object> processVariables = runtimeService.getVariables(processInstance.getId());
                    dataList.add(new TaskVO(task, processDefinition, processInstance.getName(),
                            (String) variables.getOrDefault(ProcessVariableConstants.TITLE, null),
                            (String) variables.getOrDefault(ProcessVariableConstants.INITIATOR_NAME, null),
                            read, processVariables));
                }
            }
            PageResult<TaskVO> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query not claimed task page, ", e);
            return new BaseResponse<>("待领任务分页查询失败，" + e.getMessage());
        }
    }

    @ApiOperation(value = "完成任务分页查询")
    @GetMapping(value = "finished/page")
    public BaseResponse<PageResult<TaskVO>> finishedPage(@RequestParam(value = "staffCode", required = false) String staffCode) {
        try {
            log.info("finished task page query ...");
            if (StringUtils.isBlank(staffCode)) {
                staffCode = SystemHolder.getUserName();
            }
            // 查询条件组装
            HistoricTaskInstanceQuery historicTaskInstanceQuery = historyService.createHistoricTaskInstanceQuery()
                    .finished().taskAssignee(staffCode);
            long totalCount = historicTaskInstanceQuery.count();
            int firstResult = PageResult.calcFirstResult(getPagination());
            List<HistoricTaskInstance> list = historicTaskInstanceQuery.orderByHistoricTaskInstanceEndTime().desc()
                    .listPage(firstResult, getPagination().getPageSize());
            List<TaskVO> dataList = new ArrayList<>();
            // 结果集组装
            if (CollectionUtils.isNotEmpty(list)) {
                String processInstanceId = list.get(0).getProcessInstanceId();
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                        .processInstanceId(processInstanceId).singleResult();
                Map<String, Object> variables = new HashMap<>(16);
                List<HistoricVariableInstance> variableInstances = historyService
                        .createHistoricVariableInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .list();
                variableInstances.forEach(e -> variables.put(e.getVariableName(), e.getValue()));
                for (HistoricTaskInstance task : list) {
                    dataList.add(new TaskVO(task, historicProcessInstance.getName(),
                            (String) variables.getOrDefault(ProcessVariableConstants.TITLE, null),
                            (String) variables.getOrDefault(ProcessVariableConstants.INITIATOR_NAME, null)));
                }
            }
            PageResult<TaskVO> data = new PageResult<>(dataList, getPagination().getPageNum(),
                    getPagination().getPageSize(), totalCount);
            return new BaseResponse<>(Boolean.TRUE, data);
        } catch (Exception e) {
            log.error("Fail to query finished task page, ", e);
            return new BaseResponse<>("完成任务分页查询失败，" + e.getMessage());
        }
    }

    @ApiOperation(value = "已读任务")
    @PostMapping(value = "read/{taskId}")
    public BaseResponse<Void> read(@PathVariable(value = "taskId") String taskId) {
        try {
            log.info("task read ...");
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                return new BaseResponse<>(TASK_ABSENT_MESSAGE);
            }
            String taskDefinitionKey = task.getTaskDefinitionKey();
            String staffCode = SystemHolder.getUserName();
            String variableKey = String.join("#", taskDefinitionKey, staffCode);
            taskService.setVariable(taskId, variableKey, true);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to complete task, ", e);
            return new BaseResponse<>("已读任务失败，" + e.getMessage());
        }
    }

    public static final String ASSIGN_TO_NEXT_KEY = "assign2Next";

    @ApiOperation(value = "保存任务")
    @PostMapping(value = "save/{taskId}")
    public BaseResponse<Void> saveTaskForm(@PathVariable(value = "taskId") String taskId,
                                           @RequestBody(required = false) Map<String, Object> variables) {
        try {
            log.info("task form data save ...");
            String assign2Next = null;
            if (variables != null && variables.containsKey(ASSIGN_TO_NEXT_KEY)) {
                assign2Next = (String) variables.get(ASSIGN_TO_NEXT_KEY);
                variables.remove(ASSIGN_TO_NEXT_KEY);
            }
            this.saveVariables(taskId, variables, assign2Next);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to save task, ", e);
            return new BaseResponse<>("保存任务失败，" + e.getMessage());
        }
    }

    /**
     * 保存变量
     *
     * @param taskId        任务ID
     * @param variables     变量对象
     * @param assignee2Next 指定下个处理人
     */
    public void saveVariables(String taskId, Map<String, Object> variables, String assignee2Next) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            throw new BusinessException(TASK_ABSENT_MESSAGE);
        }
        if (null == variables) {
            variables = new HashMap<>(16);
        }
        PlatformUser user = SystemHolder.getUser();
        variables.put(ProcessVariableConstants.OPERATOR, user.getUserName());
        variables.put(ProcessVariableConstants.OPERATOR_NAME, user.getCnName());
        variables.put(ProcessVariableConstants.OPERATE_TIME, new Date());
        variables.put(ProcessVariableConstants.TASK_NAME, task.getName());
        String taskDefinitionKey = task.getTaskDefinitionKey();
        variables.put("taskDefinitionKey", taskDefinitionKey);
        Object variable = taskService.getVariable(taskId, taskId);
        if (null != variable) {
            if (variable instanceof List) {
                List<Map<String, Object>> dataList = JacksonUtils.toObj(JacksonUtils.toJson(variable),
                        new TypeReference<List<Map<String, Object>>>() {
                        });
                dataList.add(variables);
                taskService.setVariable(taskId, taskId, dataList);
            } else {
                Map<String, Object> data = JacksonUtils.toObj(JacksonUtils.toJson(variable),
                        new TypeReference<Map<String, Object>>() {
                        });
                data.putAll(variables);
                taskService.setVariable(taskId, taskId, data);
            }
        } else {
            taskService.setVariable(taskId, taskId, variables);
        }
        if (StringUtils.isNotBlank(assignee2Next)) {
            Map<String, Object> variableMap = new HashMap<>(4);
            variableMap.put("assignee", assignee2Next);
            taskService.setVariable(taskId, taskDefinitionKey, variableMap);
        }
    }

    @ApiOperation(value = "完成任务")
    @PostMapping(value = "complete/{taskId}")
    public BaseResponse<Void> complete(@PathVariable(value = "taskId") String taskId,
                                       @RequestBody(required = false) Map<String, Object> variables) {
        try {
            String assign2Next = null;
            if (variables != null && variables.containsKey(ASSIGN_TO_NEXT_KEY)) {
                assign2Next = (String) variables.get(ASSIGN_TO_NEXT_KEY);
                variables.remove(ASSIGN_TO_NEXT_KEY);
            }
            this.saveVariables(taskId, variables, assign2Next);
            taskService.complete(taskId);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to complete task, ", e);
            return new BaseResponse<>("完成任务失败，" + e.getMessage());
        }
    }


    @ApiOperation(value = "申领任务")
    @PostMapping(value = "claim/{taskId}")
    public BaseResponse<Void> claim(@PathVariable(value = "taskId") String taskId,
                                    @RequestParam(value = "staffCode", required = false) String staffCode) {
        try {
            log.info("task claim ...");
            if (StringUtils.isBlank(staffCode)) {
                staffCode = SystemHolder.getUserName();
            }
            taskService.claim(taskId, staffCode);
            return new BaseResponse<>();
        } catch (Exception e) {
            log.error("Fail to claim task, ", e);
            return new BaseResponse<>("申领任务失败，" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取开始表单")
    @GetMapping(value = "startForm/{processDefinitionId}")
    public BaseResponse<StartFormVO> startForm(@PathVariable(value = "processDefinitionId") String processDefinitionId) {
        try {
            log.info("start form query ...");
            StartFormData startFormData = formService.getStartFormData(processDefinitionId);
            StartFormVO build = StartFormVO.builder()
                    .deploymentId(startFormData.getDeploymentId())
                    .formKey(startFormData.getFormKey())
                    .formProperties(startFormData.getFormProperties()).build();
            return new BaseResponse<>(Boolean.TRUE, build);
        } catch (Exception e) {
            log.error("Fail to query start form, ", e);
            return new BaseResponse<>("获取开始表单失败，" + e.getMessage());
        }
    }

    @ApiOperation(value = "获取任务表单")
    @GetMapping(value = "taskForm/{taskId}")
    public BaseResponse<TaskFormVO> taskForm(@PathVariable(value = "taskId") String taskId) {
        try {
            log.info("task form query ...");
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
            if (task == null) {
                return new BaseResponse<>(TASK_ABSENT_MESSAGE);
            }
            // 查询相关数据
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(task.getProcessInstanceId()).singleResult();
            User startedBy = null;
            if (processInstance.getStartUserId() != null) {
                startedBy = identityService.createUserQuery().userId(processInstance.getStartUserId()).singleResult();
            }
            ProcessDefinitionEntity processDefinitionEntity = (ProcessDefinitionEntity) repositoryService
                    .getProcessDefinition(processInstance.getProcessDefinitionId());
            Map<String, Object> processVariables = runtimeService.getVariables(processInstance.getId());
            String titleName = processVariables.containsKey(ProcessVariableConstants.TITLE) ?
                    (String) processVariables.get(ProcessVariableConstants.TITLE) : "";
            ProcessInstanceVO processInstanceVO = new ProcessInstanceVO(processInstance, processDefinitionEntity,
                    processDefinitionEntity.isGraphicalNotationDefined(), startedBy,
                    titleName, null, null,
                    processInstance.isSuspended(), processInstance.getStartTime());
            Map<String, Object> taskVariables = taskService.getVariables(taskId);
            TaskVO taskRepresentationVO = new TaskVO(task, processDefinitionEntity, processInstance.getName(),
                    (String) taskVariables.getOrDefault(ProcessVariableConstants.TITLE, null),
                    (String) taskVariables.getOrDefault(ProcessVariableConstants.INITIATOR_NAME, null), false, null);

            TaskFormData taskFormData = formService.getTaskFormData(taskId);
            // 结果数据组装
            TaskFormVO build = TaskFormVO.builder()
                    .processInstance(processInstanceVO)
                    .task(taskRepresentationVO)
                    .deploymentId(taskFormData.getDeploymentId())
                    .formKey(taskFormData.getFormKey())
                    .formProperties(taskFormData.getFormProperties())
                    .processVariables(processVariables)
                    .taskVariables(taskVariables).build();
            return new BaseResponse<>(Boolean.TRUE, build);
        } catch (Exception e) {
            log.error("Fail to query task form, ", e);
            return new BaseResponse<>("获取任务表单失败，" + e.getMessage());
        }
    }

    @ApiOperation(value = "待领任务1")
    @GetMapping(value = "unclaimedList")
    public BaseResponse<List<TaskRepresentation>> unclaimedList1(@RequestParam(value = "staffCode", required = false) String staffCode) {
        try {
            log.info("not claimed task list1 query ...");
            if (StringUtils.isBlank(staffCode)) {
                staffCode = SystemHolder.getUserName();
            }
            List<Group> groupList = identityService.createGroupQuery().groupMember(staffCode).list();
            List<String> groupListIds = groupList.stream().map(Group::getId).collect(Collectors.toList());
            List<Task> list = taskService.createTaskQuery().taskCandidateGroupIn(groupListIds).list();
            List<TaskRepresentation> dataList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (Task task : list) {
                    ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                            .processInstanceId(task.getProcessInstanceId()).singleResult();
                    ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                            .processDefinitionId(task.getProcessDefinitionId()).singleResult();
                    dataList.add(new TaskRepresentation(task, processDefinition, processInstance.getName()));
                }
            }
            return new BaseResponse<>(Boolean.TRUE, dataList);
        } catch (Exception e) {
            log.error("Fail to query not claimed task list1, ", e);
            return new BaseResponse<>("待领任务列表查询失败，" + e.getMessage());
        }
    }

    @ApiOperation(value = "nextTasks")
    @GetMapping(value = "nextTasks")
    public BaseResponse<List<LabelValue<String>>> nextTasks(@RequestParam(value = "taskId") String taskId) {
        Task currentTask = taskService.createTaskQuery().taskId(taskId).singleResult();
        String processDefinitionId = currentTask.getProcessDefinitionId();
        FlowNode flowNode = (FlowNode) repositoryService.getBpmnModel(processDefinitionId).getMainProcess().getFlowElement(currentTask.getTaskDefinitionKey());
        List<SequenceFlow> outgoingFlows = flowNode.getOutgoingFlows();
        List<SequenceFlow> taskList = outgoingFlows.stream().filter(item -> item.getTargetFlowElement() instanceof Task).collect(Collectors.toList());
        List<LabelValue<String>> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(taskList)) {
            taskList.forEach(item -> {
                String nextTaskId = item.getTargetRef();
                String nextTaskName = repositoryService.getBpmnModel(processDefinitionId).getMainProcess().getFlowElement(nextTaskId).getName();
                dataList.add(new LabelValue<>(nextTaskName, nextTaskId));
            });
        } else {
            List<SequenceFlow> gatewayList = outgoingFlows.stream().filter(item -> item.getTargetFlowElement() instanceof Gateway)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(gatewayList)) {
                return new BaseResponse<>(true, dataList);
            }
            nextTaskRecursion(processDefinitionId, dataList, outgoingFlows);
        }
        return new BaseResponse<>(true, dataList);
    }

    private void nextTaskRecursion(String processDefinitionId, List<LabelValue<String>> dataList, List<SequenceFlow> outgoingFlows) {
        for (SequenceFlow outgoingFlow : outgoingFlows) {
            String nextTaskId = outgoingFlow.getTargetRef();
            FlowElement targetFlowElement = outgoingFlow.getTargetFlowElement();
            if (targetFlowElement instanceof org.flowable.bpmn.model.Task) {
                String nextTaskName = repositoryService.getBpmnModel(processDefinitionId).getMainProcess().getFlowElement(nextTaskId).getName();
                dataList.add(new LabelValue<>(nextTaskName, nextTaskId));
            } else if (targetFlowElement instanceof Gateway) {
                List<SequenceFlow> newOutgoingFlows = ((Gateway) targetFlowElement).getOutgoingFlows();
                nextTaskRecursion(processDefinitionId, dataList, newOutgoingFlows);
            }
        }
    }

}