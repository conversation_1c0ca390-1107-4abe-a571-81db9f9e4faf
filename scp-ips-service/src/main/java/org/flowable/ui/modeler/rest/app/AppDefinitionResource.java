/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.flowable.ui.modeler.rest.app;

import com.yhl.platform.common.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.flowable.ui.common.service.exception.InternalServerErrorException;
import org.flowable.ui.modeler.model.AppDefinitionPublishRepresentation;
import org.flowable.ui.modeler.model.AppDefinitionRepresentation;
import org.flowable.ui.modeler.model.AppDefinitionSaveRepresentation;
import org.flowable.ui.modeler.model.AppDefinitionUpdateResultRepresentation;
import org.flowable.ui.modeler.service.AppDefinitionExportService;
import org.flowable.ui.modeler.service.AppDefinitionImportService;
import org.flowable.ui.modeler.serviceapi.AppDefinitionService;
import org.flowable.ui.modeler.serviceapi.ModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <code>AppDefinitionResource</code>
 * <p>
 * 重写AppDefinitionResource
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021-11-19 15:04:00
 */
@RestController("modelerAppDefinitionResource")
@RequestMapping("/app")
@Slf4j
public class AppDefinitionResource {

    @Autowired
    protected AppDefinitionService appDefinitionService;

    @Autowired
    protected AppDefinitionExportService appDefinitionExportService;

    @Autowired
    protected AppDefinitionImportService appDefinitionImportService;

    @Autowired
    protected ModelService modelService;

    @GetMapping(value = "/rest/app-definitions/{modelId}", produces = "application/json")
    public AppDefinitionRepresentation getAppDefinition(@PathVariable("modelId") String modelId) {
        return appDefinitionService.getAppDefinition(modelId);
    }

    @GetMapping(value = "/rest/app-definitions/{modelId}/history/{modelHistoryId}", produces = "application/json")
    public AppDefinitionRepresentation getAppDefinitionHistory(@PathVariable String modelId, @PathVariable String modelHistoryId) {
        return appDefinitionService.getAppDefinitionHistory(modelId, modelHistoryId);
    }

    @PutMapping(value = "/rest/app-definitions/{modelId}", produces = "application/json")
    public AppDefinitionUpdateResultRepresentation updateAppDefinition(@PathVariable("modelId") String modelId, @RequestBody AppDefinitionSaveRepresentation updatedModel) {
        AppDefinitionUpdateResultRepresentation resultRepresentation;
        try {
            resultRepresentation = appDefinitionService.updateAppDefinition(modelId, updatedModel);
        } catch (Exception ex) {
            resultRepresentation = new AppDefinitionUpdateResultRepresentation();
            resultRepresentation.setError(true);
            resultRepresentation.setErrorDescription(ex.getMessage());
        }
        return resultRepresentation;
    }

    @PostMapping(value = "/rest/app-definitions/{modelId}/publish", produces = "application/json")
    public AppDefinitionUpdateResultRepresentation publishAppDefinition(@PathVariable("modelId") String modelId, @RequestBody AppDefinitionPublishRepresentation publishModel) {
        AppDefinitionUpdateResultRepresentation resultRepresentation;
        try {
            resultRepresentation = appDefinitionImportService.publishAppDefinition(modelId, publishModel);
        } catch (Exception ex) {
            resultRepresentation = new AppDefinitionUpdateResultRepresentation();
            resultRepresentation.setError(true);
            resultRepresentation.setErrorDescription(ex.getMessage());
        }
        return resultRepresentation;
    }

    @GetMapping(value = "/rest/app-definitions/{modelId}/export")
    public void exportAppDefinition(HttpServletResponse response, @PathVariable String modelId) throws IOException {
        appDefinitionExportService.exportAppDefinition(response, modelId);
    }

    @GetMapping(value = "/rest/app-definitions/{modelId}/export-bar")
    public void exportDeployableAppDefinition(HttpServletResponse response, @PathVariable String modelId) throws IOException {
        appDefinitionExportService.exportDeployableAppDefinition(response, modelId);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "/rest/app-definitions/{modelId}/import", produces = "application/json")
    public AppDefinitionRepresentation importAppDefinition(HttpServletRequest request, @PathVariable String modelId, @RequestParam("file") MultipartFile file) {
        return appDefinitionImportService.importAppDefinitionNewVersion(request, file, modelId);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "/rest/app-definitions/{modelId}/text/import")
    public String importAppDefinitionText(HttpServletRequest request, @PathVariable String modelId, @RequestParam("file") MultipartFile file) {

        AppDefinitionRepresentation appDefinitionRepresentation = appDefinitionImportService.importAppDefinitionNewVersion(request, file, modelId);
        String appDefinitionRepresentationJson;
        try {
            appDefinitionRepresentationJson = JacksonUtils.toJson(appDefinitionRepresentation);
        } catch (Exception e) {
            log.error("Error while App Definition representation json", e);
            throw new InternalServerErrorException("App definition could not be saved");
        }

        return appDefinitionRepresentationJson;
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "/rest/app-definitions/import", produces = "application/json")
    public AppDefinitionRepresentation importAppDefinition(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        return appDefinitionImportService.importAppDefinition(request, file);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping(value = "/rest/app-definitions/text/import")
    public String importAppDefinitionText(HttpServletRequest request, @RequestParam("file") MultipartFile file) {
        AppDefinitionRepresentation appDefinitionRepresentation = appDefinitionImportService.importAppDefinition(request, file);
        String appDefinitionRepresentationJson;
        try {
            appDefinitionRepresentationJson = JacksonUtils.toJson(appDefinitionRepresentation);
        } catch (Exception e) {
            log.error("Error while App Definition representation json", e);
            throw new InternalServerErrorException("App definition could not be saved");
        }

        return appDefinitionRepresentationJson;
    }

}