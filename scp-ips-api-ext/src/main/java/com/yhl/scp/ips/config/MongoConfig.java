package com.yhl.scp.ips.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;

/**
 * <AUTHOR>
 */
@Configuration
public class MongoConfig {

    private final MongoTemplate mongoTemplate;

    public MongoConfig(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @Bean
    public void createIndexesForDataChangeRecords() {
        IndexOperations indexOps = mongoTemplate.indexOps("log_data_change_record");
        Index index = new Index()
                .on("dataTableName", Sort.Direction.ASC)
                .on("primaryId", Sort.Direction.ASC)
                .on("createTime", Sort.Direction.DESC)
                .named("log_data_change_record_index");
        indexOps.ensureIndex(index);
    }
}
