package com.yhl.scp.ips.requestLog.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value = "log_request_info查询请求参数")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestLogQueryDTO implements Serializable {
    /**
     * 用户
     */
    @ApiModelProperty(value = "用户")
    private String userId;
    /**
     * 请求uri
     */
    @ApiModelProperty(value = "请求uri")
    private String requestUri;

    /**
     * 浏览器（user-agent）
     */
    @ApiModelProperty(value = "浏览器（user-agent）")
    private String userAgent;
}
