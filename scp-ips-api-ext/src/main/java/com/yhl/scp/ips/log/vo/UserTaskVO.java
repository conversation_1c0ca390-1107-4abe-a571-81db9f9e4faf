package com.yhl.scp.ips.log.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>UserTaskVO</code>
 * <p>
 * 用户任务表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-11 09:48:02
 */
@ApiModel(value = "用户任务表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserTaskVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -66835343001342467L;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    @FieldInterpretation(value = "用户ID")
    private String userId;
    /**
     * 任务ID
     */
    @ApiModelProperty(value = "任务ID")
    @FieldInterpretation(value = "任务ID")
    private String taskId;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    @FieldInterpretation(value = "任务名称")
    private String taskName;
    /**
     * 任务类型
     */
    @ApiModelProperty(value = "任务类型")
    @FieldInterpretation(value = "任务类型")
    private String taskType;
    /**
     * 完成状态
     */
    @ApiModelProperty(value = "完成状态")
    @FieldInterpretation(value = "完成状态")
    private String completedStatus;
    /**
     * 任务默认状态
     */
    @ApiModelProperty(value = "任务默认状态")
    @FieldInterpretation(value = "任务默认状态")
    private String defaultStatus;

    @Override
    public void clean() {

    }

}
