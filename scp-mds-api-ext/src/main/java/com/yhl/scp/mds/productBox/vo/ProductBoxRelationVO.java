package com.yhl.scp.mds.productBox.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>ProductBoxRelationVO</code>
 * <p>
 * 产品与成品箱关系VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-20 21:03:24
 */
@ApiModel(value = "产品与成品箱关系VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductBoxRelationVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 142190585087273752L;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    @FieldInterpretation(value = "物料ID")
    private String productStockPointId;
    /**
     * 物品编码
     */
    @ApiModelProperty(value = "物品编码")
    @FieldInterpretation(value = "物品编码")
    private String productCode;
    /**
     * 物品名称
     */
    @ApiModelProperty(value = "物品名称")
    @FieldInterpretation(value = "物品名称")
    private String productName;
    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @FieldInterpretation(value = "库存点编码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 箱子类型
     */
    @ApiModelProperty(value = "箱子类型")
    @FieldInterpretation(value = "箱子类型")
    private String boxType;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @FieldInterpretation(value = "优先级")
    private Integer priority;
    /**
     * 标准装载量
     */
    @ApiModelProperty(value = "标准装载量")
    @FieldInterpretation(value = "标准装载量")
    private Integer standardLoad;
    /**
     * 箱子ID
     */
    @ApiModelProperty(value = "箱子ID")
    @FieldInterpretation(value = "箱子ID")
    private String boxId;
    /**
     * 箱子编码
     */
    @ApiModelProperty(value = "箱子编码")
    @FieldInterpretation(value = "箱子编码")
    private String boxCode;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    /**
     * kid
     */
    @ApiModelProperty(value = "kid")
    @FieldInterpretation(value = "kid")
    private String kid;
    /**
     * 最后修改时间
     */
    @ApiModelProperty(value = "最后修改时间")
    @FieldInterpretation(value = "最后修改时间")
    private Date lastUpdateTime;
    /**
     * 计划区域
     */
    @ApiModelProperty(value = "计划区域")
    @FieldInterpretation(value = "计划区域")
    private String planArea;
    @Override
    public void clean() {

    }

    private Integer piecePerBox;
    
    /**
     * 箱长
     */
    @ApiModelProperty(value = "箱长")
    @FieldInterpretation(value = "箱长")
    private BigDecimal boxLength;
    
    /**
     * 箱宽
     */
    @ApiModelProperty(value = "箱宽")
    @FieldInterpretation(value = "箱宽")
    private BigDecimal boxWidth;
    
    /**
     * 箱高
     */
    @ApiModelProperty(value = "箱高")
    @FieldInterpretation(value = "箱高")
    private BigDecimal boxHeight;
}
