package com.yhl.scp.mds.curingTime.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesFinishedProductDelivery;
import com.yhl.scp.mds.curingTime.dto.MdsFinishedProductDeliveryDTO;
import com.yhl.scp.mds.curingTime.vo.MdsFinishedProductDeliveryVO;

import java.util.List;

/**
 * <code>MdsFinishedProductDeliveryService</code>
 * <p>
 * mes系统成品发送属性接口同步中间表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-27 10:46:47
 */
public interface MdsFinishedProductDeliveryService extends BaseService<MdsFinishedProductDeliveryDTO, MdsFinishedProductDeliveryVO> {

    /**
     * 查询所有
     *
     * @return list {@link MdsFinishedProductDeliveryVO}
     */
    List<MdsFinishedProductDeliveryVO> selectAll();

    BaseResponse<Void> sync(List<MesFinishedProductDelivery> mesFinishedProductDeliveries);
    BaseResponse<Void> handleSyncFinishedProductDelivery(String tenantId);

}
