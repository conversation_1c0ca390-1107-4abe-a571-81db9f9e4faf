package com.yhl.scp.mds.routing.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpProductRouting;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesOpYield;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.routing.dto.ProductRoutingDTO;
import com.yhl.scp.mds.routing.vo.ProductRoutingVO;

import java.util.List;
import java.util.Map;

/**
 * <code>ProductRoutingService</code>
 * <p>
 * 物品工艺路径应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10 17:52:11
 */
public interface ProductRoutingService extends BaseService<ProductRoutingDTO, ProductRoutingVO> {

    /**
     * 查询所有
     *
     * @return list {@link ProductRoutingVO}
     */
    List<ProductRoutingVO> selectAll();
    List<ProductRoutingVO> selectByRoutingSequenceIds(List<String> routingSequenceIds);
    BaseResponse<Void> syncProductRoutings(String tenantId);
    List<ProductRoutingVO> selectByProductIds(List<String> productIds);
    void doCreateBatchWithPrimaryKey(List<ProductRoutingDTO> list);

    void doCreateBatchWithPartition(List<ProductRoutingDTO> list);

    void doUpdateBatchWithPartition(List<ProductRoutingDTO> list);

    BaseResponse<Void> doSync(List<ErpProductRouting> o, String scenario);

    BaseResponse<Void> syncOpYield(List<MesOpYield> o);

    BaseResponse<Void> handleSyncOpYield(String tenantId);

    void doLogicDeleteBatch(List<RemoveVersionDTO> deleteProductRoutingVersionList);
    
	List<ProductRoutingVO> selectVOByParams(Map<String, Object> params);
	
	void syncAllRouting();

    List<ProductRoutingVO> selectByRoutingSequenceIdNotNull();


}
