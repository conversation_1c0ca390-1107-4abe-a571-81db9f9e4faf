package com.yhl.scp.mds.routing.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>ProductCandidateResourceDTO</code>
 * <p>
 * 物品候选资源DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-18 11:43:23
 */
@ApiModel(value = "物品候选资源DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ProductCandidateResourceDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 700653516446105845L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
        
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
        
    /**
     * 物品类型
     */
    @ApiModelProperty(value = "物品类型")
    private String productType;
        
    /**
     * 物品系列ID
     */
    @ApiModelProperty(value = "物品系列ID")
    private String productSeriesId;
        
    /**
     * 物品ID
     */
    @ApiModelProperty(value = "物品ID")
    private String productId;
        
    /**
     * 标准工艺ID
     */
    @ApiModelProperty(value = "标准工艺ID")
    private String standardStepId;
        
    /**
     * 标准资源ID
     */
    @ApiModelProperty(value = "标准资源ID")
    private String standardResourceId;
        
    /**
     * 物理资源ID
     */
    @ApiModelProperty(value = "物理资源ID")
    private String physicalResourceId;
        
    /**
     * kid
     */
    @ApiModelProperty(value = "kid")
    private String kid;
        
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;
        
    /**
     * 设置时间
     */
    @ApiModelProperty(value = "设置时间")
    private Integer setupDuration;
        
    /**
     * 固定工时
     */
    @ApiModelProperty(value = "固定工时")
    private Integer fixedWorkHours;
        
    /**
     * 每小时产量
     */
    @ApiModelProperty(value = "每小时产量")
    private BigDecimal unitsPerHour;
        
    /**
     * 单件生产时间
     */
    @ApiModelProperty(value = "单件生产时间")
    private BigDecimal unitProductionTime;
        
    /**
     * 清洗时间
     */
    @ApiModelProperty(value = "清洗时间")
    private Integer cleanupDuration;
        
    /**
     * 替代工器具组号
     */
    @ApiModelProperty(value = "替代工器具组号")
    private String altToolCode;
        
    /**
     * 配套使用号
     */
    @ApiModelProperty(value = "配套使用号")
    private String matchCode;
        
    /**
     * 最大制造批量
     */
    @ApiModelProperty(value = "最大制造批量")
    private Integer maxLotSize;
        
    /**
     * 最小制造批量
     */
    @ApiModelProperty(value = "最小制造批量")
    private Integer minLotSize;
        
    /**
     * 设置必要资源量
     */
    @ApiModelProperty(value = "设置必要资源量")
    private BigDecimal setupUnitBatchSize;
        
    /**
     * 制造必要资源量
     */
    @ApiModelProperty(value = "制造必要资源量")
    private BigDecimal productionUnitBatchSize;
        
    /**
     * 清理必要资源量
     */
    @ApiModelProperty(value = "清理必要资源量")
    private BigDecimal cleanupUnitBatchSize;
        
    /**
     * 最大设置中断时间
     */
    @ApiModelProperty(value = "最大设置中断时间")
    private Integer maxSetupSuspendDuration;
        
    /**
     * 最大制造中断时间
     */
    @ApiModelProperty(value = "最大制造中断时间")
    private Integer maxProductionSuspendDuration;
        
    /**
     * 最大清理中断时间
     */
    @ApiModelProperty(value = "最大清理中断时间")
    private Integer maxCleanupSuspendDuration;
        
    /**
     * 生效时间
     */
    @ApiModelProperty(value = "生效时间")
    private Date effectiveTime;
        
    /**
     * 失效时间
     */
    @ApiModelProperty(value = "失效时间")
    private Date expiryTime;
        
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
        
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
