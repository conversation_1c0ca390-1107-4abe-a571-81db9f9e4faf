package com.yhl.scp.mds.productroutestepbase.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MdsProductStockPointBaseDTO</code>
 * <p>
 * 产品工艺基础数据DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 11:28:43
 */
@ApiModel(value = "产品工艺基础数据DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MdsProductStockPointBaseDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -32752518901140549L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    private String stockPointName;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 编码名称
     */
    @ApiModelProperty(value = "编码名称")
    private String productName;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    private BigDecimal productWidth;
    /**
     * 厚
     */
    @ApiModelProperty(value = "厚")
    private BigDecimal productThickness;
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    private String loadPosition;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    private String productColor;
    /**
     * 玻璃颜色
     */
    @ApiModelProperty(value = "玻璃颜色")
    private String glassColor;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String productType;
    /**
     * 难度等级
     */
    @ApiModelProperty(value = "难度等级")
    private String difficultyLevel;
    /**
     * 风栅类型
     */
    @ApiModelProperty(value = "风栅类型")
    private String gridType;
    /**
     * 生产模式
     */
    @ApiModelProperty(value = "生产模式")
    private String productionModel;
    /**
     * 钢化类型
     */
    @ApiModelProperty(value = "钢化类型")
    private String tougheningType;
    /**
     * 膜系
     */
    @ApiModelProperty(value = "膜系")
    private String membraneSystem;
    /**
     * HUD
     */
    @ApiModelProperty(value = "HUD")
    private String hud;
    /**
     * 夹丝类型
     */
    @ApiModelProperty(value = "夹丝类型")
    private String clampType;
    /**
     * 印边
     */
    @ApiModelProperty(value = "印边")
    private String sealEdge;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    private String productArea;
    /**
     * 曲率
     */
    @ApiModelProperty(value = "曲率")
    private String curvature;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 编码目录号
     */
    @ApiModelProperty(value = "编码目录号")
    private String dirNum;
    /**
     * 工艺类型
     */
    @ApiModelProperty(value = "工艺类型")
    private String itemType;
    /***
     * 除膜工艺
     */
    @ApiModelProperty(value = "除膜工艺")
    private String attr1;
    /***
     * 物料标识
     */
    @ApiModelProperty(value = "物料标识")
    private String itemFlag;
    /***
     * 生产线组
     */
    @ApiModelProperty(value = "生产线组")
    private String lineGroup;
    /***
     * 工装大类
     */
    @ApiModelProperty(value = "工装大类")
    private String standardResourceId;
    /***
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    private String partNum;
}
