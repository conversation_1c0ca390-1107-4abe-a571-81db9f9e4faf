package com.yhl.scp.mds.newproduct.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpProduct;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mdm.MdmProductionTime;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointDTO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointBaseVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.newproduct.vo.ProductMassProductionVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <code>MdsProductStockPointService</code>
 * <p>
 * 物品应用接口
 * </p>
 *
 * @version 1.0
 * @since 2024-07-30 15:54:39
 */
public interface NewProductStockPointService extends BaseService<NewProductStockPointDTO, NewProductStockPointVO> {

    /**
     * 根据主键批量查询数据
     *
     * @param ids
     * @return
     */
    List<NewProductStockPointVO> selectByPrimaryKeys(List<String> ids);

    /**
     * 查询所有
     *
     * @return list {@link NewProductStockPointVO}
     */
    List<NewProductStockPointVO> selectAll();

    /**
     * 根据车型编码获取本厂编码
     *
     * @param vehicleModelCodeList
     * @param productCode
     * @return
     */
    List<NewProductStockPointVO> selectProductCode(List<String> vehicleModelCodeList, String productCode);

    int deleteBatchVersion(List<RemoveVersionDTO> removeVersionDTOS);

    BaseResponse<List<LabelValue<String>>> dropDownAll();

    List<NewProductStockPointVO> selectByProductCode(List<String> codeList);

    /**
     * 根据车型编码获取库存数据
     *
     * @param vehicleModelCodeList
     * @return
     */
    List<NewProductStockPointVO> selectByVehicleModelCode(List<String> vehicleModelCodeList);

    List<NewProductStockPointVO> selectByStockPointCodes(List<String> stockPointCodes);

    BaseResponse<Void> syncProductStockPoints(String tenantId, String stockPoint);

    /**
     * 查询字段下拉
     *
     * @param field
     * @return
     */
    List<String> selectFields(String field);

    List<NewProductStockPointVO> selectProductCodeLike(String stockPointCode, String productCode,
                                                       List<String> productTypes);

    List<NewProductStockPointVO> selectProductCodeLikeByVehicleByStock(String productCode,
                                                                       List<String> vehicleModelCodeList,
                                                                       String stockPointCode);

    List<NewProductStockPointVO> selectByUserId(String userId, List<String> stockPointCodeList);

    List<NewProductStockPointVO> selectTwoData();

    List<NewProductStockPointVO> selectVOByParams(Map<String, Object> params);

    List<LabelValue<String>> getVehicleModelCode(String stockPointCode, String productCode);

    List<String> getLoadingPositionSub();

    BaseResponse<Void> syncProductionTime(String tenantId);

    /**
     * 获取主机厂信息通过物品编码
     *
     * @param productCode
     * @return
     */
    List<LabelValue<String>> getOemByProductCode(String productCode);

    List<NewProductStockPointVO> selectProductListByParamOnDynamicColumns(List<String> dynamicColumns, Map<String, Object> params);

    List<NewProductStockPointVO> selectProductTypeLike(String productType);

    List<LabelValue<String>> getOemByProductCodeAndVehicle(String productCode);

    /**
     * 供应类型（模糊搜索）
     *
     * @param supplyType 供应类型
     * @return 物品信息
     */
    List<NewProductStockPointVO> selectSupplyTypeLike(String supplyType);


    /**
     * 颜色（模糊搜索）
     *
     * @param productColor 颜色
     * @return 物品信息
     */
    List<NewProductStockPointVO> selectProductColorLike(String productColor);


    List<NewProductStockPointVO> selectByLoadPosition(List<String> accessPositionList);

    /**
     * 工作交接
     *
     * @param plannerType     计划员类型
     * @param originalPlanner 原始计划员
     * @param currentPlanner  当前计划员
     * @param productIds      当前计划员
     * @return java.util.List<com.yhl.platform.common.LabelValue < java.lang.String>>
     */
    void doHandover(String plannerType, String originalPlanner, String currentPlanner, List<String> productIds);

    List<NewProductStockPointVO> selectProduct4LoadingDemandSubmission();

    List<String> selectVehiclesByProductCode(List<String> codeList);

    BaseResponse<Void> sync(List<ErpProduct> o);

    int doLogicDeleteBatch(List<RemoveVersionDTO> deleteProductList);

    List<String> selectVehicleModelCode();

    List<NewProductStockPointVO> selectByStockCodeAndProductCode(Collection<ProductSubstitutionRelationshipVO> values);

    List<NewProductStockPointVO> selectSrmByStockCodeAndProductCode(Collection<SrmSupplierPurchase> values);

    /**
     * 通过库存点查物品基础数据信息（id,productCode,productName）
     *
     * @param stockPointCodes
     * @return
     */
    List<NewProductStockPointBaseVO> selectBaseInfoByStockPointCodes(List<String> stockPointCodes);

    List<String> selectProductCodeByParams(Map<String, Object> params);

    BaseResponse<Void> syncProductTime(List<MdmProductionTime> mdmProductionTimes);

    String checkTallyOrderMode(String productCode);

    List<String> selectYpProductCodes();

    List<NewProductStockPointVO> getYpFactoryCode();

    /**
     * 校验产品工艺路径
     *
     * @param productIds
     * @return
     */
    Map<String, String> checkPorductRouting(List<String> productIds);

    /**
     * 查询车型最大EOP最小SOP
     *
     * @param vehicleModelCodes
     * @return
     */
    Map<String, NewProductStockPointVO> selectMaxEopMinSop(Map<String, Object> params);

    /**
     * 通过主机厂编码获取物料下拉框
     *
     * @param vehicleModelCode
     * @return
     */
    List<LabelValue<String>> selectDropDownByVehicleModelCode(String vehicleModelCode);

	/**
	 * 获取物料属性信息（量产移交）
	 * @param productCodes
	 * @return
	 */
	List<ProductMassProductionVO> selectProductMassProduction(List<String> productCodes);

	/**
	 * 修改物料计划员及物料状态
	 * @param productCodes
	 * @param orderPlanner
	 * @return
	 */
	void updateOrderPlanner(List<String> productCodes, String orderPlanner);

    List<String> selectDistinctProductCodesByParams(Map<String, Object> params);
}
