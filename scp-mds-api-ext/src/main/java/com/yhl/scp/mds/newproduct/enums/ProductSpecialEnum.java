package com.yhl.scp.mds.newproduct.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <p>
 * 产品特性枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-05-16 18:05:17
 */
public enum ProductSpecialEnum implements CommonEnum {

    /**
     * 调光
     */
    TG("调光", "调光"),
    /**
     * 夹丝
     */
    JS("夹丝", "夹丝");

    private String code;

    private String desc;

    ProductSpecialEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}