package com.yhl.scp.mds.overdeadlineday.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MdsOverDeadlineDaysVO</code>
 * <p>
 * 超期界定天数VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-20 11:16:39
 */
@ApiModel(value = "超期界定天数VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MdsOverDeadlineDaysVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 915027864467132609L;
    
    /**
     * 组织（库存点)
     */
    @ApiModelProperty(value = "组织（库存点)")
    @FieldInterpretation(value = "组织（库存点)")
    private String stockPointCode;

    /**
     * 公司代码
     */
    @ApiModelProperty(value = "公司代码")
    @FieldInterpretation(value = "公司代码")
    private String companyCode;
    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    @FieldInterpretation(value = "物料类型")
    private String materialsType;
    /**
     * 物料分类大类
     */
    @ApiModelProperty(value = "物料分类大类")
    @FieldInterpretation(value = "物料分类大类")
    private String materialsMainClassification;
    /**
     * 物料分类小类
     */
    @ApiModelProperty(value = "物料分类小类")
    @FieldInterpretation(value = "物料分类小类")
    private String materialsSecondClassification;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    @FieldInterpretation(value = "产品类型")
    private String productType;
    /**
     * 颜色代码
     */
    @ApiModelProperty(value = "颜色代码")
    @FieldInterpretation(value = "颜色代码")
    private String colorCode;
    /**
     * 超期界定天数
     */
    @ApiModelProperty(value = "超期界定天数")
    @FieldInterpretation(value = "超期界定天数")
    private BigDecimal overDeadlineDay;

    @Override
    public void clean() {

    }

}
