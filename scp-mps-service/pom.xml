<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yhl.scp</groupId>
        <artifactId>bpim</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>scp-mps-service</artifactId>
    <name>scp-mps-service</name>
    <url>https://rzz.com</url>
    <description>生产计划模块</description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.yhl.platform</groupId>
            <artifactId>platform-gamma</artifactId>
            <version>${yhl.platform.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.annotation</groupId>
                    <artifactId>jsr250-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.platform</groupId>
            <artifactId>platform-component</artifactId>
            <version>${yhl.platform.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yhl.platform</groupId>
            <artifactId>platform-log</artifactId>
            <version>${yhl.platform.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yhl.algorithm</groupId>
            <artifactId>rzz-java-service</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/hikari-cp/hikari-cp -->
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>4.0.3</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/cn.smallbun.screw/screw-core -->
        <dependency>
            <groupId>cn.smallbun.screw</groupId>
            <artifactId>screw-core</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-mps-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-sds-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-mps-infra</artifactId>
            <version>${scp.extension.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-ips-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>flowable-engine</artifactId>
                    <groupId>org.flowable</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-mds-api-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-das-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-mps-api-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-dfp-api-ext</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-ips-api-ext</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>flowable-engine</artifactId>
                    <groupId>org.flowable</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-mrp-api-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yhl.scp</groupId>
            <artifactId>scp-das-api-ext</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>scp-mps-service</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok.mapstruct.binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>${maven.assembly.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven.jar.plugin.version}</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>com.yhl.scp.mps.MpsApplication</mainClass>
                            <addClasspath>true</addClasspath>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven.resources.plugin.version}</version>
                <configuration>
                    <delimiters>
                        <delimiter>@</delimiter>
                    </delimiters>
                    <useDefaultDelimiters>false</useDefaultDelimiters>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.maven.plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>