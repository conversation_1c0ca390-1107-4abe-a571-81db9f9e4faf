package com.yhl.scp.mps.domain.sync.support;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepInputDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepOutputDO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.domain.sync.model.SyncContext;
import com.yhl.scp.sds.extension.order.domain.entity.*;
import com.yhl.scp.sds.extension.order.dto.WorkOrderDTO;
import com.yhl.scp.sds.extension.order.infrastructure.po.*;
import com.yhl.scp.sds.extension.order.vo.OperationTaskVO;
import com.yhl.scp.sds.extension.pegging.domain.entity.DemandDO;
import com.yhl.scp.sds.extension.pegging.domain.entity.SupplyDO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.order.convertor.*;
import com.yhl.scp.sds.order.infrastructure.dao.*;
import com.yhl.scp.sds.order.service.OperationTaskService;
import com.yhl.scp.sds.pegging.convertor.DemandConvertor;
import com.yhl.scp.sds.pegging.convertor.SupplyConvertor;
import com.yhl.scp.sds.pegging.service.DemandService;
import com.yhl.scp.sds.pegging.service.SupplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yhl.platform.common.utils.DateUtils.COMMON_DATE_STR3;

/**
 * <code>SyncSupport</code>
 * <p>
 * SyncSupport提供流程通用的数据流程支持
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/10 11:14
 */
@Slf4j
public class SyncSupport {

    protected static final String COMPLETE_CHECK_KEY = "SYNC_CHECK_KEY";

    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    private OperationDao operationDao;
    @Resource
    private OperationInputDao operationInputDao;
    @Resource
    private OperationOutputDao operationOutputDao;
    @Resource
    private OperationResourceDao operationResourceDao;
    @Resource
    private OperationTaskService operationTaskService;
    @Resource
    private DemandService demandService;
    @Resource
    private SupplyService supplyService;
    @Resource
    protected IpsNewFeign ipsNewFeign;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private IpsFeign ipsFeign;

    @Resource
    protected RedisUtil redisUtil;
    protected ThreadPoolExecutor threadPoolExecutor;

    private static final List<String> LIMIT_RESOURCE_KEY = Arrays.asList("S1YZ", "S1SPYZ", "S1HW", "S1TC");

    @PostConstruct
    public void initThreadPool() {
        threadPoolExecutor = CustomThreadPoolFactory.instance();
    }

    /**
     * 同步数据初始化
     *
     * @param output
     * @return
     */
    protected SyncContext initContext(RzzMpsAlgorithmOutput output) {
        SyncContext syncContext = new SyncContext();
        List<WorkOrderPO> workOrderPOList;
        List<String> workOrderIds = output.getWorkOrderIds();
        if(CollectionUtils.isEmpty(workOrderIds)){
            throw new BusinessException("无订单要展开");
        }
        if (CollectionUtils.isNotEmpty(workOrderIds)) {
            workOrderPOList = workOrderDao.selectByParams(ImmutableMap.of("ids", output.getWorkOrderIds()));
        } else {
            workOrderPOList = workOrderDao.selectByParams(new HashMap<>());
        }
        //目前查询全部制造订单数据，后续考虑根据参数条件查询
        if (CollectionUtils.isEmpty(workOrderPOList)) {
            throw new BusinessException("制造订单数据为空，同步结束");
        }
        List<String> test = new ArrayList<>();
        //test.add("M2412008328");
        //workOrderPOList = workOrderPOList.stream().filter(t->test.contains(t.getOrderNo())).collect(Collectors.toList());

        List<String> syncWorkOrderIds = workOrderPOList.stream().map(WorkOrderPO::getId).collect(Collectors.toList());

        List<WorkOrderDO> workOrderDOS = this.getWorkOrderDOSByParams(ImmutableMap.of("ids", syncWorkOrderIds));
        syncContext.setSyncWorkOrderList(workOrderDOS);

        String mdsScenario = getMdsScenario();

        //查询库存点信息
        List<NewStockPointVO> newStockPointVOList = newMdsFeign.selectAllStockPoint(mdsScenario);
        Map<String, NewStockPointVO> stockPointVOMap =
                newStockPointVOList.stream().collect(Collectors.toMap(NewStockPointVO::getId, Function.identity(),
                        (v1, v2) -> v2));
        syncContext.setStockPointVOMap(stockPointVOMap);
        syncContext.setNewStockPointVOList(newStockPointVOList);

        //获取mps生成的demand,supply数据
        Map<String, Object> params = new HashMap<>();
        params.put("demandOrderIds", syncWorkOrderIds);
        List<DemandVO> demandVOList = demandService.selectByParams(params)
                        .stream().filter(p -> StrUtil.isEmpty(p.getCustomerId())).collect(Collectors.toList());
        syncContext.setMpsDemandVOList(demandVOList);
        params.put("supplyOrderIds", syncWorkOrderIds);
        List<SupplyVO> supplyVOList = supplyService.selectByParams(params);
        syncContext.setMpsSupplyVOList(supplyVOList);

        // 物料替代关系
        List<ProductSubstitutionRelationshipVO> productSubstitutionRelationship = newMdsFeign.getProductSubstitutionRelationship();
        Map<String, List<ProductSubstitutionRelationshipVO>> replaceMap = productSubstitutionRelationship.stream().collect(Collectors.groupingBy(ProductSubstitutionRelationshipVO::getProductCode, Collectors.toList()));
        syncContext.setProductSubstitutionRelationshipVOList(productSubstitutionRelationship);
        syncContext.setProductSubstitutionRelationshipVOMap(replaceMap);
        //摸具数量限制
        List<CollectionValueVO> mjLimitResourceCodeList = ipsFeign.getByCollectionCode("HW_LIMIT_STAND_RESOURCE_CODE");
        List<String> mjLimitResourceList = mjLimitResourceCodeList.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList());
        params = new HashMap<>();
        List<String> standardResourceCodes = new ArrayList<>();
        standardResourceCodes.addAll(mjLimitResourceList);
        standardResourceCodes.addAll(LIMIT_RESOURCE_KEY);
        params.put("standardResourceCodes", standardResourceCodes);
        List<StandardResourceVO> standardResourceVOList = newMdsFeign.selectStandardResourceVOSByParams(SystemHolder.getScenario(), params);
        List<String> mjLimitResourceIdList = standardResourceVOList.stream().filter(t->mjLimitResourceList.contains(t.getStandardResourceCode())).map(BaseVO::getId).collect(Collectors.toList());
        List<ProductCandidateResourceVO> productCandidateResourceVOS = newMdsFeign.selectMoldQuantityLimit();
        Map<String, BigDecimal> moldQuantityLimitMap =
                productCandidateResourceVOS.stream().collect(Collectors.toMap(t -> String.join("-",
                        t.getStockPointId(), t.getProductId(), t.getStandardStepCode()),
                        ProductCandidateResourceVO::getMoldQuantityLimit, (v1, v2) -> v2));
        syncContext.setMjLimitResourceList(mjLimitResourceIdList);
        syncContext.setMoldQuantityLimitMap(moldQuantityLimitMap);

        List<String> s1LimitResourceIdList = standardResourceVOList.stream().filter(t->LIMIT_RESOURCE_KEY.contains(t.getStandardResourceCode())).map(BaseVO::getId).collect(Collectors.toList());
        syncContext.setS1LimitResourceList(s1LimitResourceIdList);

        // List<RoutingStepVO> routingStepVOS = newMdsFeign.selectAllRoutingStep(null)
        //         .stream().filter(p -> StrUtil.isNotEmpty(p.getEnabled()) && p.getEnabled().equals(YesOrNoEnum.YES.getCode())).collect(Collectors.toList());
        //
        // List<RoutingStepDO> routingStepDOList = new ArrayList<>();
        // for (RoutingStepVO routingStepVO : routingStepVOS) {
        //     RoutingStepDO routingStepDO = new RoutingStepDO();
        //     BeanUtils.copyProperties(routingStepVO, routingStepDO);
        //     routingStepDOList.add(routingStepDO);
        // }
        // Map<String, List<RoutingStepDO>> routingIdToStepsMap = routingStepDOList.stream().collect(Collectors.groupingBy(RoutingStepDO::getRoutingId));
        // syncContext.setRoutingDOMap(routingIdToStepsMap);
        return syncContext;
    }

    /**
     * 获得当月第一天的字符串
     *
     * @param date
     * @return
     */
    private String getFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return DateUtils.dateToString(calendar.getTime(), COMMON_DATE_STR3);
    }

    /**
     * 组装workOrderDO对象
     *
     * @param params
     * @return
     */
    protected List<WorkOrderDO> getWorkOrderDOSByParams(Map<String, Object> params) {
        List<WorkOrderDO> workOrderDOList = new ArrayList<>();
        List<WorkOrderPO> workOrderPOS = workOrderDao.selectByParams(params);
        if (CollectionUtils.isEmpty(workOrderPOS)) {
            return workOrderDOList;
        }
        List<WorkOrderDO> workOrderDOS = WorkOrderConvertor.INSTANCE.po2Dos(workOrderPOS);
        workOrderDOList.addAll(workOrderDOS);
        List<String> workOrderIds = workOrderPOS.stream().map(WorkOrderPO::getId).collect(Collectors.toList());
        //组装operation相关数据
        List<OperationDO> operationDOS = this.getOperationDOByParams(ImmutableMap.of("orderIds", workOrderIds));
        Map<String, List<OperationDO>> workOrderId2OperationMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(operationDOS)) {
            workOrderId2OperationMap = operationDOS.stream().collect(Collectors.groupingBy(OperationDO::getOrderId));
        }
        for (WorkOrderDO workOrderDO : workOrderDOS) {
            String workOrderId = workOrderDO.getId();
            List<OperationDO> operationDOList = workOrderId2OperationMap.get(workOrderId);
            if (CollectionUtils.isNotEmpty(operationDOList)) {
                operationDOList.forEach(k -> k.setWorkOrderDO(workOrderDO));
                workOrderDO.setOperationDOList(workOrderId2OperationMap.get(workOrderId));
            }
        }
        return workOrderDOList;
    }

    /**
     * 获取物品相关信息
     *
     * @param syncContext
     */
    protected void getNewProductStockPoint(SyncContext syncContext) {
        String mdsScenario = getMdsScenario();
        List<WorkOrderDO> workOrderDOS = syncContext.getSyncWorkOrderList();
        //查询物品信息
        List<String> productStockPointIds = workOrderDOS.stream().map(WorkOrderDO::getProductId).collect(Collectors.toList());
        List<OperationDO> operationDOS = workOrderDOS.stream().filter(t -> CollectionUtils.isNotEmpty(t.getOperationDOList())).map(WorkOrderDO::getOperationDOList).flatMap(Collection::stream).collect(Collectors.toList());
        List<OperationInputDO> inputDOList = operationDOS.stream().filter(t -> CollectionUtils.isNotEmpty(t.getOperationInputDOList())).map(OperationDO::getOperationInputDOList).flatMap(Collection::stream).collect(Collectors.toList());
        List<OperationOutputDO> outputDOList = operationDOS.stream().filter(t -> CollectionUtils.isNotEmpty(t.getOperationOutputDOList())).map(OperationDO::getOperationOutputDOList).flatMap(Collection::stream).collect(Collectors.toList());
        List<RoutingDO> routingDOList = workOrderDOS.stream().map(WorkOrderDO::getRoutingDO).collect(Collectors.toList());
        List<RoutingStepDO> routingStepDOList = routingDOList.stream().filter(t -> CollectionUtils.isNotEmpty(t.getRoutingStepDOList())).map(RoutingDO::getRoutingStepDOList).flatMap(Collection::stream).collect(Collectors.toList());
        List<RoutingStepInputDO> routingStepInputDOList = routingStepDOList.stream().filter(t -> CollectionUtils.isNotEmpty(t.getRoutingStepInputDOList())).map(RoutingStepDO::getRoutingStepInputDOList).flatMap(Collection::stream).collect(Collectors.toList());
        List<RoutingStepOutputDO> routingStepOutputDOList = routingStepDOList.stream().filter(t -> CollectionUtils.isNotEmpty(t.getRoutingStepOutputDOList())).map(RoutingStepDO::getRoutingStepOutputDOList).flatMap(Collection::stream).collect(Collectors.toList());
        List<String> productCodes = syncContext.getProductSubstitutionRelationshipVOList().stream().map(ProductSubstitutionRelationshipVO::getSubstituteProductCode).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(inputDOList)) {
            productStockPointIds.addAll(inputDOList.stream().map(OperationInputDO::getProductStockPointId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(outputDOList)) {
            productStockPointIds.addAll(outputDOList.stream().map(OperationOutputDO::getProductStockPointId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(routingStepInputDOList)) {
            productStockPointIds.addAll(routingStepInputDOList.stream().map(RoutingStepInputDO::getInputProductId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(routingStepOutputDOList)) {
            productStockPointIds.addAll(routingStepOutputDOList.stream().map(RoutingStepOutputDO::getOutputProductId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(productCodes)) {
            // 替代料物品信息
            List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(null, productCodes);
            productStockPointIds.addAll(newProductStockPointVOS.stream().map(NewProductStockPointVO::getId).collect(Collectors.toList()));
        }
        //去重
        productStockPointIds = productStockPointIds.stream().distinct().collect(Collectors.toList());

        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByIds(mdsScenario, productStockPointIds);
        Map<String, NewProductStockPointVO> productStockPointVOMap =
                newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity(),(v1, v2) -> v2));
        Map<String, NewProductStockPointVO> productMapOnIdAndStockPointCodeMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(p -> StrUtil.join("#", p.getStockPointCode(), p.getProductCode()),
                        Function.identity(), (v1, v2) -> v2));
        Map<String, NewProductStockPointVO> productOnIdMap =
                newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId,
                        Function.identity(),(v1, v2) -> v2));
        syncContext.setProductStockPointVOMap(productStockPointVOMap);
        syncContext.setNewProductStockPointVOList(newProductStockPointVOS);
        syncContext.setProductMapOnIdAndStockPointCodeMap(productMapOnIdAndStockPointCodeMap);
        syncContext.setProductMapOnIdMap(productOnIdMap);
        //查询产品资源生产关系相关数据
        List<String> productCodeList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>(1);
        Date date = new Date();
        params.put("productCodeList", productCodeList);
        List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = newMdsFeign.selectProductCandidateResourceTimeByParams(mdsScenario, params);
        syncContext.setProductCandidateResourceTimeVOS(productCandidateResourceTimeVOS);
    }

    protected String getMdsScenario() {
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        return scenario.getData();
    }

    /**
     * 组装operationDO对象
     *
     * @param params
     * @return
     */
    protected List<OperationDO> getOperationDOByParams(Map<String, Object> params) {
        List<OperationPO> operationPOS = operationDao.selectByParams(params);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(operationPOS)) {
            return new ArrayList<>();
        }
        List<String> operationIds = operationPOS.stream().map(OperationPO::getId).collect(Collectors.toList());
        List<OperationDO> operationDOS = OperationConvertor.INSTANCE.po2Dos(operationPOS);
        List<OperationInputDO> operationInputDOS = this.getOperationInputDOByParams(ImmutableMap.of("operationIds", operationIds));
        Map<String, List<OperationInputDO>> operationId2InputDOSMap = operationInputDOS.stream().collect(Collectors.groupingBy(OperationInputDO::getOperationId));
        List<OperationOutputDO> operationOutputDOS = this.getOperationOutputDOByParams(ImmutableMap.of("operationIds", operationIds));
        Map<String, List<OperationOutputDO>> operationId2OutputDOSMap = operationOutputDOS.stream().collect(Collectors.groupingBy(OperationOutputDO::getOperationId));
        List<OperationResourceDO> operationResourceDOS = this.getOperationResourceDOByParams(ImmutableMap.of("operationIds", operationIds));
        Map<String, List<OperationResourceDO>> operationId2ResourceDOSMap = operationResourceDOS.stream().collect(Collectors.groupingBy(OperationResourceDO::getOperationId));
        List<OperationTaskVO> operationTaskVOS = operationTaskService.selectByParams(ImmutableMap.of("operationIds", operationIds));
        Map<String, List<OperationTaskVO>> operationId2TaskMaps = new HashMap<>();
        if (CollectionUtils.isNotEmpty(operationTaskVOS)) {
            operationId2TaskMaps = operationTaskVOS.stream().collect(Collectors.groupingBy(OperationTaskVO::getOperationId));
        }

        for (OperationDO operationDO : operationDOS) {
            List<OperationInputDO> operationInputDOList = operationId2InputDOSMap.get(operationDO.getId());
            if (CollectionUtils.isNotEmpty(operationInputDOList)) {
                operationInputDOList.forEach(k -> k.setOperationDO(operationDO));
                operationDO.setOperationInputDOList(operationInputDOList);
            }
            List<OperationOutputDO> operationOutputDOList = operationId2OutputDOSMap.get(operationDO.getId());
            if (CollectionUtils.isNotEmpty(operationOutputDOList)) {
                operationOutputDOList.forEach(k -> k.setOperationDO(operationDO));
                operationDO.setOperationOutputDOList(operationOutputDOList);
            }
            List<OperationResourceDO> operationResourceDOList = operationId2ResourceDOSMap.get(operationDO.getId());
            if (CollectionUtils.isNotEmpty(operationResourceDOList)) {
                operationResourceDOList.forEach(k -> k.setOperationDO(operationDO));
                operationDO.setOperationResourceDOList(operationResourceDOList);
            }
            List<OperationTaskVO> operationTaskVOList = operationId2TaskMaps.get(operationDO.getId());
            if (CollectionUtils.isNotEmpty(operationTaskVOList)) {
                List<OperationTaskDO> operationTaskDOList = OperationTaskConvertor.INSTANCE.vo2DOS(operationTaskVOList);
                operationDO.setOperationTaskDOList(operationTaskDOList);
            }
        }
        return operationDOS;
    }

    /**
     * 组装OperationInputDO对象
     *
     * @param params
     * @return
     */
    protected List<OperationInputDO> getOperationInputDOByParams(Map<String, Object> params) {
        List<OperationInputPO> operationInputPOS = operationInputDao.selectByParams(params);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(operationInputPOS)) {
            return new ArrayList<>();
        }
        List<OperationInputDO> operationInputDOS = OperationInputConvertor.INSTANCE.po2Dos(operationInputPOS);

        //查询对应需求
        List<String> ids = operationInputPOS.stream().map(OperationInputPO::getId).collect(Collectors.toList());
        List<DemandVO> demandVOS = demandService.selectByParams(ImmutableMap.of("operationInputIds", ids));
        List<DemandDO> demandDOS = DemandConvertor.INSTANCE.vo2Do(demandVOS);
        Map<String, DemandDO> operationInputId2DemandMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(demandDOS)) {
            operationInputId2DemandMap = demandDOS.stream().collect(Collectors.toMap(DemandDO::getOperationInputId,
                    Function.identity(), (v1, v2) -> v2));
        }
        for (OperationInputDO operationInputDO : operationInputDOS) {
            operationInputDO.setDemandDO(operationInputId2DemandMap.get(operationInputDO.getId()));
        }
        return operationInputDOS;
    }

    /**
     * 组装OperationOutputDO对象
     *
     * @param params
     * @return
     */
    protected List<OperationOutputDO> getOperationOutputDOByParams(Map<String, Object> params) {
        List<OperationOutputPO> operationOutputPOS = operationOutputDao.selectByParams(params);
        if (CollectionUtils.isEmpty(operationOutputPOS)) {
            return new ArrayList<>();
        }
        //查询对应的供应
        List<String> ids = operationOutputPOS.stream().map(OperationOutputPO::getId).collect(Collectors.toList());
        List<SupplyVO> supplyVOS = supplyService.selectByParams(ImmutableMap.of("operationOutputIds", ids));
        List<SupplyDO> SupplyDOS = SupplyConvertor.INSTANCE.vo2Dos(supplyVOS);
        Map<String, SupplyDO> operationOutputId2SupplyDOMap =
                SupplyDOS.stream().collect(Collectors.toMap(SupplyDO::getOperationOutputId, Function.identity(), (v1,
                                                                                                                  v2) -> v2));

        List<OperationOutputDO> operationOutputDOS = OperationOutputConvertor.INSTANCE.po2Dos(operationOutputPOS);
        for (OperationOutputDO operationOutputDO : operationOutputDOS) {
            operationOutputDO.setSupplyDO(operationOutputId2SupplyDOMap.get(operationOutputDO.getId()));
        }
        return operationOutputDOS;
    }

    /**
     * 组装OperationResourceDO对象
     *
     * @param params
     * @return
     */
    protected List<OperationResourceDO> getOperationResourceDOByParams(Map<String, Object> params) {
        List<OperationResourcePO> operationResourcePOS = operationResourceDao.selectByParams(params);
        List<OperationResourceDO> operationResourceDOS = OperationResourceConvertor.INSTANCE.po2Dos(operationResourcePOS);
        return operationResourceDOS;
    }

    /**
     * 批量更新制造订单状态
     *
     * @param workOrderDTOList
     */
    protected void doBatchUpdateForStatus(List<WorkOrderDTO> workOrderDTOList) {
        if (CollectionUtils.isEmpty(workOrderDTOList)) {
            return;
        }
        List<WorkOrderPO> workOrderPOS = WorkOrderConvertor.INSTANCE.dto2Pos(workOrderDTOList);
        BasePOUtils.updateBatchFiller(workOrderPOS);
        List<List<WorkOrderPO>> lists = CollectionUtils.splitList(workOrderPOS, 1000);
        StopWatch stopWatch = new StopWatch("AbstractWorkOrderSync.doBatchUpdateForStatus 批量更新制造订单状态");
        stopWatch.start("批量更新制造订单状态");
        for (List<WorkOrderPO> subList : lists) {
            workOrderDao.batchUpdateForStatus(subList);
        }
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }


    /**
     * 写入redis-hash表中，记录执行日志
     */
    protected void cacheLog(String msg) {
        log.info(msg);
        redisUtil.hset(COMPLETE_CHECK_KEY, msg, DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR1));
    }

}
