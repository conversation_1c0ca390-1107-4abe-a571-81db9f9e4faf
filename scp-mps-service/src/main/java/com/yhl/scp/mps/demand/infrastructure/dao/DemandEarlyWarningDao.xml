<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.demand.infrastructure.dao.DemandEarlyWarningDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.demand.infrastructure.po.DemandEarlyWarningPO">
        <!--@Table mps_demand_early_warning-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="demand_quantity" jdbcType="INTEGER" property="demandQuantity"/>
        <result column="early_warning_type" jdbcType="VARCHAR" property="earlyWarningType"/>
        <result column="early_warning_reason" jdbcType="VARCHAR" property="earlyWarningReason"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.demand.vo.DemandEarlyWarningVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,
        product_code,
        vehicle_model_code,
        delivery_time,
        demand_quantity,
        early_warning_type,
        early_warning_reason,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryTime != null">
                and delivery_time = #{params.deliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.earlyWarningType != null and params.earlyWarningType != ''">
                and early_warning_type = #{params.earlyWarningType,jdbcType=VARCHAR}
            </if>
            <if test="params.earlyWarningReason != null and params.earlyWarningReason != ''">
                and early_warning_reason = #{params.earlyWarningReason,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.unConcatKeys != null">
                and CONCAT(product_code, '_', DATE_FORMAT(delivery_time, '%Y-%m-%d')) in
                <foreach collection="params.unConcatKeys" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.deliverStartTime != null">
                and delivery_time &gt;= #{params.deliverStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.deliverEndTime != null">
                and delivery_time &lt;= #{params.deliverEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_demand_early_warning
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_demand_early_warning
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mps_demand_early_warning
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_demand_early_warning
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mps_demand_early_warning
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.demand.infrastructure.po.DemandEarlyWarningPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid())
            from dual
        </selectKey>
        insert into mps_demand_early_warning(id,
                                             product_code,
                                             vehicle_model_code,
                                             delivery_time,
                                             demand_quantity,
                                             early_warning_type,
                                             early_warning_reason,
                                             remark,
                                             enabled,
                                             creator,
                                             create_time,
                                             modifier,
                                             modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{deliveryTime,jdbcType=TIMESTAMP},
                #{demandQuantity,jdbcType=INTEGER},
                #{earlyWarningType,jdbcType=VARCHAR},
                #{earlyWarningReason,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.demand.infrastructure.po.DemandEarlyWarningPO">
        insert into mps_demand_early_warning(id,
                                             product_code,
                                             vehicle_model_code,
                                             delivery_time,
                                             demand_quantity,
                                             early_warning_type,
                                             early_warning_reason,
                                             remark,
                                             enabled,
                                             creator,
                                             create_time,
                                             modifier,
                                             modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{vehicleModelCode,jdbcType=VARCHAR},
                #{deliveryTime,jdbcType=TIMESTAMP},
                #{demandQuantity,jdbcType=INTEGER},
                #{earlyWarningType,jdbcType=VARCHAR},
                #{earlyWarningReason,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_demand_early_warning(id,
                                             product_code,
                                             vehicle_model_code,
                                             delivery_time,
                                             demand_quantity,
                                             early_warning_type,
                                             early_warning_reason,
                                             remark,
                                             enabled,
                                             creator,
                                             create_time,
                                             modifier,
                                             modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
             #{entity.productCode,jdbcType=VARCHAR},
             #{entity.vehicleModelCode,jdbcType=VARCHAR},
             #{entity.deliveryTime,jdbcType=TIMESTAMP},
             #{entity.demandQuantity,jdbcType=INTEGER},
             #{entity.earlyWarningType,jdbcType=VARCHAR},
             #{entity.earlyWarningReason,jdbcType=VARCHAR},
             #{entity.remark,jdbcType=VARCHAR},
             #{entity.enabled,jdbcType=VARCHAR},
             #{entity.creator,jdbcType=VARCHAR},
             #{entity.createTime,jdbcType=TIMESTAMP},
             #{entity.modifier,jdbcType=VARCHAR},
             #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_demand_early_warning(id,
                                             product_code,
                                             vehicle_model_code,
                                             delivery_time,
                                             demand_quantity,
                                             early_warning_type,
                                             early_warning_reason,
                                             remark,
                                             enabled,
                                             creator,
                                             create_time,
                                             modifier,
                                             modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
             #{entity.productCode,jdbcType=VARCHAR},
             #{entity.vehicleModelCode,jdbcType=VARCHAR},
             #{entity.deliveryTime,jdbcType=TIMESTAMP},
             #{entity.demandQuantity,jdbcType=INTEGER},
             #{entity.earlyWarningType,jdbcType=VARCHAR},
             #{entity.earlyWarningReason,jdbcType=VARCHAR},
             #{entity.remark,jdbcType=VARCHAR},
             #{entity.enabled,jdbcType=VARCHAR},
             #{entity.creator,jdbcType=VARCHAR},
             #{entity.createTime,jdbcType=TIMESTAMP},
             #{entity.modifier,jdbcType=VARCHAR},
             #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.demand.infrastructure.po.DemandEarlyWarningPO">
        update mps_demand_early_warning
        set product_code         = #{productCode,jdbcType=VARCHAR},
            vehicle_model_code   = #{vehicleModelCode,jdbcType=VARCHAR},
            delivery_time        = #{deliveryTime,jdbcType=TIMESTAMP},
            demand_quantity      = #{demandQuantity,jdbcType=INTEGER},
            early_warning_type   = #{earlyWarningType,jdbcType=VARCHAR},
            early_warning_reason = #{earlyWarningReason,jdbcType=VARCHAR},
            remark               = #{remark,jdbcType=VARCHAR},
            enabled              = #{enabled,jdbcType=VARCHAR},
            modifier             = #{modifier,jdbcType=VARCHAR},
            modify_time          = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.demand.infrastructure.po.DemandEarlyWarningPO">
        update mps_demand_early_warning
        <set>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryTime != null">
                delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.earlyWarningType != null and item.earlyWarningType != ''">
                early_warning_type = #{item.earlyWarningType,jdbcType=VARCHAR},
            </if>
            <if test="item.earlyWarningReason != null and item.earlyWarningReason != ''">
                early_warning_reason = #{item.earlyWarningReason,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_demand_early_warning
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="early_warning_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.earlyWarningType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="early_warning_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.earlyWarningReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mps_demand_early_warning
            <set>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                    vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.deliveryTime != null">
                    delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.earlyWarningType != null and item.earlyWarningType != ''">
                    early_warning_type = #{item.earlyWarningType,jdbcType=VARCHAR},
                </if>
                <if test="item.earlyWarningReason != null and item.earlyWarningReason != ''">
                    early_warning_reason = #{item.earlyWarningReason,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mps_demand_early_warning
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete
        from mps_demand_early_warning where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 条件查询 -->
    <select id="selectByParamsLimitOneThousand" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_demand_early_warning
        <include refid="Base_Where_Condition"/>
        order by delivery_time desc, id
        limit 1000
    </select>
</mapper>
