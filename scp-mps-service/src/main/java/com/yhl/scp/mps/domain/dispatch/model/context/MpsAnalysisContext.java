package com.yhl.scp.mps.domain.dispatch.model.context;

import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.ips.algorithm.dto.AlgorithmStepLogDTO;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mds.algorithmnew.input.pojo.PlanHorizonInputNewData;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.dispatch.output.RzzProductionIntervalOutput;
import com.yhl.scp.mps.dispatch.output.RzzProductionPlannedInIntervalOutput;
import com.yhl.scp.mps.dispatch.output.RzzProductionPlannedOutput;
import com.yhl.scp.mps.plan.infrastructure.po.ProductionIntervalPO;
import com.yhl.scp.mps.plan.vo.MasterPlanRelationVO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.DemandPO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.FulfillmentPO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.SupplyPO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <code>MpsAnalysisContext</code>
 * <p>
 * MpsAnalysisContext
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/24 10:14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MpsAnalysisContext {

    /**
     * 订单计划员id
     */
    private String creatorId;

    /**
     * 结果集
     */
    private RzzMpsAlgorithmOutput rzzMpsAlgorithmOutput;

    /**
     * log
     */
    private AlgorithmLog algorithmLog;

    /**
     * 制造订单
     */
    private List<WorkOrderPO> workOrderPOS;
    private List<WorkOrderPO> workOrderAll;
    private Map<String, WorkOrderPO> workOrderMap;

    /**
     * 计划单
     */
    private List<MasterPlanRelationVO> masterPlanRelationVOList;
    private Map<String, MasterPlanRelationVO> masterPlanRelationMap;

    /**
     * 生产批量
     */
    private List<RzzProductionIntervalOutput> productionIntervalOutputDataList;
    private Map<String, RzzProductionIntervalOutput> intervalOutputMap;


    /**
     * 批量关系
     */
    private List<RzzProductionPlannedInIntervalOutput> productionPlannedInIntervalOutputDataList;
    private Map<String, List<RzzProductionPlannedInIntervalOutput>> inIntervalMap;

    /**
     * 生产计划量
     */
    private List<RzzProductionPlannedOutput> productionPlannedOutputDataList;

    /**
     * 物品
     */
    private List<NewProductStockPointVO> productList;
    private Map<String, NewProductStockPointVO> productMap;

    /**
     * 物品基础数据
     */
    private Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap;

    /**
     * 库存点
     */
    private List<NewStockPointVO> newStockPointVOS;
    private Map<String, NewStockPointVO> stockMap;

    /**
     * 工艺路径
     */
    private List<RoutingVO> routingVOS;
    private Map<String, RoutingVO> routingVOMap;

    /**
     * 编码规则
     */
    private Map<String, RuleEncodingsVO> ruleEncodingsMap;

    /**
     * 发货计划
     */
    private List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS;
    private Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap;

    /**
     * 库存推移数据
     */
    private List<InventoryShiftVO> inventoryShiftVOList;
    private Map<String, List<InventoryShiftVO>> inventoryShiftMap;

    /**
     * 时段序列
     */
    private List<PlanHorizonInputNewData> planHorizonInputNewDataList;
    private Map<Integer, PlanHorizonInputNewData> planHorizonMap;

    /**
     * 计划批量关系
     */
    private List<ProductionIntervalPO> productionIntervalVOS;
    private Map<String, ProductionIntervalPO> productionIntervalMap;

    /**
     * 需求
     */
    private List<DemandPO> demandPOS;
    private Map<String, DemandPO> demandMap;

    /**
     * 供应
     */
    private List<SupplyPO> supplyPOS;
    private Map<String, SupplyPO> supplyPOMap;

    /**
     * 供需分配结果
     */
    private List<FulfillmentPO> fulfillmentPOS;
    private Map<String, List<FulfillmentPO>> fulfillmentPOMap;

    /**
     * 计划期间
     */
    private PlanningHorizonVO planningHorizonVO;

    /**
     * 工艺
     */
    private List<RoutingStepVO> routingStepVOS;
    private Map<String, RoutingStepVO> routingStepVOMap;
    private List<StandardStepVO> standardStepVOS;
    private Map<String, StandardStepVO> standardStepVOMap;

    /**
     * 解析结果集
     */
    private List<WorkOrderPO> createWorkOrderList;
    private List<WorkOrderPO> updateWorkOrderList;
    private List<DemandPO> createDemandsList;
    private List<DemandPO> updateDemandsList;
    private List<SupplyPO> createSupplyList;
    private List<SupplyPO> updateSupplyList;
    private List<ProductionIntervalPO> createProductionIntervalList;
    private List<ProductionIntervalPO> updateProductionIntervalList;
    private List<FulfillmentPO> createFulfillmentList;
    private List<FulfillmentPO> updateFulfillmentList;
    private List<String> workOrderCancelPlanIdList;

    /**
     * 指定待排工序
     */
    private List<OperationVO> useOperationList;
    private List<String> workOrderIds;

    /**
     * 解析步骤
     */
    private List<AlgorithmStepLogDTO> algorithmStepLogDTOList;

    /**
     * 记录算法包信息
     */
    private Map<String,Object> scheduleInfoMap;

    /**
     * 新增制造订单对应关键工序指定候选资源
     */
    private Map<String, String> physicalResourceCodeOnIdMap;
    private Map<String, String> workOrderOnResourceMap;

}
