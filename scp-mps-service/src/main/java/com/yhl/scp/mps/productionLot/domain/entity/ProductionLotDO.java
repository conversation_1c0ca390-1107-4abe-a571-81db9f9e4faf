package com.yhl.scp.mps.productionLot.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>ProductionLotDO</code>
 * <p>
 * 生产经济批量DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-14 15:24:00
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ProductionLotDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 105068139855979056L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 库存点名称
     */
    private String stockPointName;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 物品名称
     */
    private String productName;
    /**
     * 资源代码
     */
    private String resourceCode;
    /**
     * 资源名称
     */
    private String resourceName;
    /**
     * 工序代码
     */
    private String operationCode;
    /**
     * 工序名称
     */
    private String operationName;
    /**
     * 最大经济批量
     */
    private BigDecimal maxProduceQty;
    /**
     * 最小经济批量
     */
    private BigDecimal minProduceQty;


}
