package com.yhl.scp.mps.capacityBalance.service.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.capacityBalance.convertor.CapacityBalanceVersionConvertor;
import com.yhl.scp.mps.capacityBalance.domain.entity.CapacityBalanceVersionDO;
import com.yhl.scp.mps.capacityBalance.domain.service.CapacityBalanceVersionDomainService;
import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceVersionDTO;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacityBalanceVersionDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacityLoadDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityBalanceVersionPO;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceVersionService;
import com.yhl.scp.mps.capacityBalance.service.CapacityLoadService;
import com.yhl.scp.mps.capacityBalance.service.CapacitySupplyRelationshipService;
import com.yhl.scp.mps.capacityBalance.vo.*;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <code>CapacityBalanceVersionServiceImpl</code>
 * <p>
 * 产能平衡版本应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 14:16:50
 */
@Slf4j
@Service
public class CapacityBalanceVersionServiceImpl extends AbstractService implements CapacityBalanceVersionService {

    @Resource
    private CapacityBalanceVersionDao capacityBalanceVersionDao;
    @Resource
    private CapacityBalanceVersionDomainService capacityBalanceVersionDomainService;
    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private CapacityLoadService capacityLoadService;
    @Resource
    private CapacityLoadDao capacityLoadDao;
    @Resource
    private CapacitySupplyRelationshipService capacitySupplyRelationshipService;
    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(CapacityBalanceVersionDTO capacityBalanceVersionDTO) {
        // 0.数据转换
        CapacityBalanceVersionDO capacityBalanceVersionDO = CapacityBalanceVersionConvertor.INSTANCE.dto2Do(capacityBalanceVersionDTO);
        CapacityBalanceVersionPO capacityBalanceVersionPO = CapacityBalanceVersionConvertor.INSTANCE.dto2Po(capacityBalanceVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        capacityBalanceVersionDomainService.validation(capacityBalanceVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(capacityBalanceVersionPO);
        capacityBalanceVersionDao.insertWithPrimaryKey(capacityBalanceVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(CapacityBalanceVersionDTO capacityBalanceVersionDTO) {
        // 0.数据转换
        CapacityBalanceVersionDO capacityBalanceVersionDO = CapacityBalanceVersionConvertor.INSTANCE.dto2Do(capacityBalanceVersionDTO);
        CapacityBalanceVersionPO capacityBalanceVersionPO = CapacityBalanceVersionConvertor.INSTANCE.dto2Po(capacityBalanceVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        capacityBalanceVersionDomainService.validation(capacityBalanceVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(capacityBalanceVersionPO);
        capacityBalanceVersionDao.update(capacityBalanceVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<CapacityBalanceVersionDTO> list) {
        List<CapacityBalanceVersionPO> newList = CapacityBalanceVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        capacityBalanceVersionDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<CapacityBalanceVersionDTO> list) {
        List<CapacityBalanceVersionPO> newList = CapacityBalanceVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        capacityBalanceVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return capacityBalanceVersionDao.deleteBatch(idList);
        }
        return capacityBalanceVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public CapacityBalanceVersionVO selectByPrimaryKey(String id) {
        CapacityBalanceVersionPO po = capacityBalanceVersionDao.selectByPrimaryKey(id);
        return CapacityBalanceVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CAPACITY_BALANCE_VERSION")
    public List<CapacityBalanceVersionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CAPACITY_BALANCE_VERSION")
    public List<CapacityBalanceVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<CapacityBalanceVersionVO> dataList = capacityBalanceVersionDao.selectByCondition(sortParam, queryCriteriaParam);
        CapacityBalanceVersionServiceImpl target = springBeanUtils.getBean(CapacityBalanceVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<CapacityBalanceVersionVO> selectByParams(Map<String, Object> params) {
        List<CapacityBalanceVersionPO> list = capacityBalanceVersionDao.selectByParams(params);
        return CapacityBalanceVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<CapacityBalanceVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CAPACITY_BALANCE_VERSION.getCode();
    }

    @Override
    public List<CapacityBalanceVersionVO> invocation(List<CapacityBalanceVersionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    @BusinessMonitorLog(businessCode = "月度产能平衡发布", moduleCode = "MPS", businessFrequency = "MONTH")
    public BaseResponse<String> publishVersionLock() {
        Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(RedisKeyManageEnum.CAPACITY_BALANCE_PUBLISH.getKey(), RedisKeyManageEnum.CAPACITY_BALANCE_PUBLISH.getKey(), 30, TimeUnit.MINUTES);
        if (!Objects.nonNull(setIfAbsent) || Boolean.FALSE.equals(setIfAbsent)) {
            throw new BusinessException("产能平衡发布中, 请勿重复执行");
        }
        BaseResponse<String> baseResponse = BaseResponse.success();
        try {
            publishVersion();
        } catch (Exception e) {
            log.error("执行报错", e);
            baseResponse = BaseResponse.error("执行报错：" + e.getMessage());
        } finally {
            redisTemplate.delete(RedisKeyManageEnum.CAPACITY_BALANCE_PUBLISH.getKey());
        }
        return baseResponse;
    }

    @Override
    public void publishVersion() {
        CapacitySupplyRelationshipVO capacitySupplyRelationshipVO = capacitySupplyRelationshipDao.selectLastVersion();
        if (capacitySupplyRelationshipVO == null) { //没有要发布的数据
            log.warn("没有可发布的数据");
            return;
        }

        StopWatch stopWatch = new StopWatch("产能平衡数据发布");
        stopWatch.start("产能平衡数据开始发布");
        //对应的一致性业务预测计划周期
        String planPeriod = capacitySupplyRelationshipVO.getPlanPeriod();
        String versionCode = getVersionCode(planPeriod);

        CapacityBalanceVersionPO capacityBalanceVersionPO = new CapacityBalanceVersionPO();
        capacityBalanceVersionPO.setForecastMonth(planPeriod);
        capacityBalanceVersionPO.setForecastVersion(capacitySupplyRelationshipVO.getVersionCode());
        capacityBalanceVersionPO.setVersionCode(versionCode);
        BasePOUtils.insertFiller(capacityBalanceVersionPO);
        capacityBalanceVersionDao.insertWithPrimaryKey(capacityBalanceVersionPO);
        stopWatch.stop();
        stopWatch.start("删除过期版本数据（只保留3版结果）");
        // 始终保留每月第一版的数据
        List<String> needDeleteVersionIds = capacityBalanceVersionDao.selectNeedDeleteVersion();
        // String currentMonthFirstVersionCode = planPeriod + "V1";
        // needDeleteVersionIds.remove(currentMonthFirstVersionCode);
        needDeleteVersionIds = needDeleteVersionIds.stream().filter(t -> !t.contains("V1")).collect(Collectors.toList());
        capacitySupplyRelationshipService.doDeleteByVersionIds(needDeleteVersionIds);
        capacityLoadService.doDeleteByVersionIds(needDeleteVersionIds);
        if (CollectionUtils.isNotEmpty(needDeleteVersionIds)) {
            capacityBalanceVersionDao.deleteByVersionIds(needDeleteVersionIds);
        }
        stopWatch.stop();
        stopWatch.start("开始保存一版最新产能供应关系");
        log.info("开始保存一版最新产能供应关系");
        CapacityBalanceVersionVO capacityBalanceVersionVO = this.selectLatestVersionCode();
        log.info("$$$$$$$$$$$$ 最新版本产能平衡数据：{}", capacityBalanceVersionVO.getVersionCode());
        //不存id了，直接存版本号
        String versionId = capacityBalanceVersionPO.getVersionCode();
        capacitySupplyRelationshipService.doSaveCapacitySupplyRelationshipBasedOnVersion(versionId);
        stopWatch.stop();
        stopWatch.start("开始保存发布版本");
        log.info("开始保存发布版本");
        capacityLoadService.doSaveVersionCapacityLoad(versionId);
        //刷新产品资源生产关系
        stopWatch.stop();
        stopWatch.start("刷新产品资源生产关系");
        log.info("刷新产品资源生产关系");
        String scenario = SystemHolder.getScenario();
        CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            capacitySupplyRelationshipService.saveEquipmentProductionRelation();
            DynamicDataSourceContextHolder.clearDataSource();
        });

        //调用MRP材料推移
        stopWatch.stop();
        stopWatch.start("MRP材料推移");


//        CompletableFuture.runAsync(() -> {
//            try {
//                mrpFeign.noGlassRecalculateMrp(scenario);
//            } catch (Exception e) {
//                log.error("MRP材料推移失败", e);
//            }
//        });

//        CompletableFuture.runAsync(() -> {
//            try {
//                mrpFeign.glassRecalculateMrp(scenario, GlassRefreshMrpEnum.DEMAND_UPDATE.getCode());
//            } catch (Exception e) {
//                log.error("原片材料推移失败", e);
//            }
//        });

        // TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
        //     @Override
        //     public void afterCommit() {
        //         //需要提交之后执行的代码
        //         log.info("MRP材料推移");
        //     }
        // });
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
    }

    @Override
    public CapacityBalanceVersionVO selectLatestVersionCode() {
        return capacityBalanceVersionDao.selectLatestVersionCode(null);
    }

    @Override
    public List<CapacityLoadVO4> contrastCapacityLoad(List<String> versionIds, String plantCode, String operationCode, String operationName, String resourceGroupCode, String resourceCode) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("versionIds", versionIds);
        map.put("plantCode", plantCode);
        map.put("operationCode", operationCode);
        map.put("operationName", operationName);
        map.put("resourceGroupCode", resourceGroupCode);
        map.put("resourceCode", resourceCode);
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadService.selectByParams(map);
        List<Date> dateList = capacityLoadVOS.stream().sorted(Comparator.comparing(CapacityLoadVO::getForecastTime)).map(CapacityLoadVO::getForecastTime).distinct().collect(Collectors.toList());
        Map<Date, BigDecimal> capacityMap = new HashMap<>();
        dateList.forEach(t -> {
            int year = DateUtils.getYearByDate(t);
            int month = DateUtils.getMonthByDate(t) + 1;
            int days = DateUtils.getMonthDayCount(year, month) - 4;
            int i = days * 24 * 3600;
            capacityMap.put(t, BigDecimal.valueOf(i));
        });
        capacityLoadVOS.forEach(t -> {
            t.setAvailableCapacity(capacityMap.get(t.getForecastTime()));
            t.setCapacityUtilization(t.getProductionCapacity().divide(t.getAvailableCapacity(), 2, RoundingMode.HALF_UP));
        });
        // 根据工厂编码+设备组+设备分组整合数据并转换为需要的格式
        Map<String, List<CapacityLoadVO>> groupMap = capacityLoadVOS.stream().collect(Collectors.groupingBy(e -> e.getPlantCode() + e.getResourceGroupCode() + e.getResourceCode()));
        List<String> monthList = capacityLoadVOS.stream().map(t -> DateUtils.dateToString(t.getForecastTime(), "yyyyMM")).distinct().sorted().collect(Collectors.toList());
        List<String> versionCodeList = capacityLoadVOS.stream().map(CapacityLoadVO::getVersionId).distinct().collect(Collectors.toList());
        List<CapacityLoadVO4> vo4List = new ArrayList<>();
        for (Map.Entry<String, List<CapacityLoadVO>> entry : groupMap.entrySet()) {
            List<CapacityLoadVO> value = entry.getValue();
            CapacityLoadVO value1 = value.get(0);
            CapacityLoadVO4 capacityLoadVO4 = new CapacityLoadVO4();
            BeanUtils.copyProperties(value1, capacityLoadVO4);
            List<CapacityLoadVO3> versionList = new ArrayList<>();
            Map<String, List<CapacityLoadVO>> versionMap = value.stream().collect(Collectors.groupingBy(CapacityLoadVO::getVersionId));
            for (String versionCode : versionIds) {
                CapacityLoadVO3 capacityLoadVO3 = new CapacityLoadVO3();
                capacityLoadVO3.setVersionCode(versionCode);
                List<CapacityLoadVO2> dataList = new ArrayList<>();
                if (versionMap.containsKey(versionCode)) {
                    Map<String, List<CapacityLoadVO>> monthMap = versionMap.get(versionCode).stream().collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getForecastTime(), "yyyyMM")));
                    for (String month : monthList) {
                        if (monthMap.containsKey(month)) {
                            List<CapacityLoadVO> list = monthMap.get(month);
                            for (CapacityLoadVO capacityLoadVO : list) {
                                CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                                BeanUtils.copyProperties(capacityLoadVO, capacityLoadVO2);
                                dataList.add(capacityLoadVO2);
                            }
                        } else {
                            CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                            capacityLoadVO2.setForecastTime(DateUtils.stringToDate(month, "yyyyMM"));
                            capacityLoadVO2.setCapacityUtilization(BigDecimal.ZERO);
                            capacityLoadVO2.setDemandQuantity(BigDecimal.ZERO);
                            capacityLoadVO2.setProductionCapacity(BigDecimal.ZERO);
                            dataList.add(capacityLoadVO2);
                        }
                    }
                } else {
                    for (String month : monthList) {
                        CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                        capacityLoadVO2.setForecastTime(DateUtils.stringToDate(month, "yyyyMM"));
                        capacityLoadVO2.setCapacityUtilization(BigDecimal.ZERO);
                        capacityLoadVO2.setDemandQuantity(BigDecimal.ZERO);
                        capacityLoadVO2.setProductionCapacity(BigDecimal.ZERO);
                        dataList.add(capacityLoadVO2);
                    }
                }
                capacityLoadVO3.setDataList(dataList);
                versionList.add(capacityLoadVO3);
            }
            capacityLoadVO4.setVersionList(versionList);
            vo4List.add(capacityLoadVO4);
        }
        return vo4List;
    }

    @Override
    public List<CapacitySupplyRelationshipVO2> contrastCapacitySupplyRelationship(List<String> versionIds, String operationCode, String operationName, String supplyTimeStart, String supplyTimeEnd, String productCode) {
        if ((supplyTimeStart == null) != (supplyTimeEnd == null)) {
            throw new BusinessException("起始时间，结束时间必须同时存在");
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("versionIds", versionIds);
        map.put("operationCode", operationCode);
        map.put("operationName", operationName);
        map.put("supplyTimeStart", supplyTimeStart);
        map.put("supplyTimeEnd", supplyTimeEnd);
        map.put("productCode", productCode);
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS = capacitySupplyRelationshipService.selectByParams(map);
        // 根据工厂编码+设备组+设备分组整合数据并转换为需要的格式
        Map<String, List<CapacitySupplyRelationshipVO>> groupMap = capacitySupplyRelationshipVOS.stream().collect(Collectors.groupingBy(e -> e.getProductCode() + e.getOperationCode() + e.getResourceCode()));
        List<String> monthList = capacitySupplyRelationshipVOS.stream().map(t -> DateUtils.dateToString(t.getForecastTime(), "yyyyMM")).distinct().sorted().collect(Collectors.toList());
        List<String> versionCodeList = capacitySupplyRelationshipVOS.stream().map(CapacitySupplyRelationshipVO::getVersionId).distinct().sorted().collect(Collectors.toList());
        List<CapacitySupplyRelationshipVO2> vo2List = new ArrayList<>();
        for (Map.Entry<String, List<CapacitySupplyRelationshipVO>> entry : groupMap.entrySet()) {
            List<CapacitySupplyRelationshipVO> value = entry.getValue();
            CapacitySupplyRelationshipVO value1 = value.get(0);
            CapacitySupplyRelationshipVO2 vo2 = new CapacitySupplyRelationshipVO2();
            vo2.setProductCode(value1.getProductCode());
            vo2.setProductName(value1.getProductName());
            vo2.setOperationCode(value1.getOperationCode());
            vo2.setOperationName(value1.getOperationName());
            vo2.setResourceCode(value1.getResourceCode());
            vo2.setResourceName(value1.getResourceName());
            List<CapacitySupplyRelationshipVO3> dataList = new ArrayList<>();
            Map<String, List<CapacitySupplyRelationshipVO>> forecastTimeMap = value.stream().collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getForecastTime(), "yyyyMM")));
            for (String forecastTime : monthList) {

                CapacitySupplyRelationshipVO3 vo3 = new CapacitySupplyRelationshipVO3();
                vo3.setForecastTime(forecastTime);
                List<CapacitySupplyRelationshipVO4> versionList = new ArrayList<>();
                if (forecastTimeMap.containsKey(forecastTime)) {
                    Map<String, List<CapacitySupplyRelationshipVO>> versionMap = forecastTimeMap.get(forecastTime).stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getVersionId));
                    for (String versionCode : versionIds) {
                        CapacitySupplyRelationshipVO4 vo4 = new CapacitySupplyRelationshipVO4();
                        vo4.setVersionCode(versionCode);
                        if (versionMap.containsKey(versionCode)) {
                            vo4.setSupplyModel("本厂/委外");
        /*                    if (SupplyModelEnum.OUTSOURCED.getCode().equals(versionMap.get(versionCode).get(0).getSupplyModel())){
                                vo4.setSupplyQuantity(versionMap.get(versionCode).get(0).getDemandQuantity());
                            }else {
                                vo4.setSupplyQuantity(versionMap.get(versionCode).get(0).getSupplyQuantity());
                            }
                            vo4.setDemandQuantity(versionMap.get(versionCode).get(0).getDemandQuantity());*/
                            //todo 这里有点问题，这一个月的供应关系可能半个月是
                            BigDecimal demandQuantity = versionMap.get(versionCode).stream()
                                    .map(CapacitySupplyRelationshipVO::getDemandQuantity)
                                    .filter(quantity -> quantity.compareTo(BigDecimal.ZERO) >= 0)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            vo4.setDemandQuantity(demandQuantity);
                            BigDecimal outSourcedQty = versionMap.get(versionCode).stream()
                                    .filter(t -> SupplyModelEnum.OUTSOURCED.getCode().equals(t.getSupplyModel()))
                                    .map(CapacitySupplyRelationshipVO::getDemandQuantity)
                                    .filter(quantity -> quantity.compareTo(BigDecimal.ZERO) >= 0)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            BigDecimal localQty = versionMap.get(versionCode).stream()
                                    .filter(t -> SupplyModelEnum.LOCAL.getCode().equals(t.getSupplyModel()))
                                    .map(CapacitySupplyRelationshipVO::getDemandQuantity)
                                    .filter(quantity -> quantity.compareTo(BigDecimal.ZERO) >= 0)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                            String qty = localQty.intValue() + "/" + outSourcedQty.intValue();
                            vo4.setSupplyQuantity(qty);
                        }
                        versionList.add(vo4);
                    }
                } else {
                    for (String versionCode : versionIds) {
                        CapacitySupplyRelationshipVO4 vo4 = new CapacitySupplyRelationshipVO4();
                        vo4.setVersionCode(versionCode);
                        versionList.add(vo4);
                    }
                }
                vo3.setVersionList(versionList);
                dataList.add(vo3);
            }
            vo2.setDataList(dataList);
            vo2List.add(vo2);
        }
        List<CapacitySupplyRelationshipVO2> noResourceData = vo2List.stream().filter(t -> t.getResourceCode() == null).collect(Collectors.toList());
        noResourceData.forEach(t -> {
            t.setResourceCode(SupplyModelEnum.OUTSOURCED.getDesc());
            t.setResourceName(SupplyModelEnum.OUTSOURCED.getDesc());
        });
        return vo2List;
    }

    /**
     * 预测月份
     *
     * @param planPeriod 2024-08
     * @return
     */
    private String getVersionCode(String planPeriod) {
        String versionCode = planPeriod;
        // 查询最新版本号 格式：2024-08_V1
        CapacityBalanceVersionVO latestVersionCode = capacityBalanceVersionDao.selectLatestVersionCode(planPeriod);
        if (latestVersionCode == null || StringUtils.isBlank(latestVersionCode.getVersionCode())) {
            versionCode = versionCode + "V1";
        } else {
            String[] split = latestVersionCode.getVersionCode().split("V");
            try {
                int version = Integer.parseInt(split[1]);
                // 数字加1
                version++;
                versionCode = versionCode + "V" + version;
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid format for the last characters of the version code.", e);
            }
        }
        return versionCode;
    }

    @Override
    public String selectWeekMaxVersionTime() {
        Date date = capacityLoadDao.selectWeekMaxVersionTime();
        if (Objects.isNull(date)) {
            return "暂无";
        }
        return DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    public List<LabelValue<String>> versionDropdown() {
        List<CapacityBalanceVersionPO> capacityBalanceVersionPOS = capacityBalanceVersionDao.selectByParams(new HashMap<>());
        List<String> versionList = capacityBalanceVersionPOS.stream().map(CapacityBalanceVersionPO::getVersionCode).collect(Collectors.toList());
        return versionList.stream()
                .map(item -> new LabelValue<>(item, item))
                .collect(Collectors.toList());
    }

    @Override
    public CapacityBalanceVersionVO selectVersionInfoByVersionCode(String versionCode) {
        if (StringUtils.isEmpty(versionCode)) {
            throw new BusinessException("请选择一个版本号！");
        }
        List<CapacityBalanceVersionPO> capacityBalanceVersionPOS = capacityBalanceVersionDao.selectByParams(ImmutableMap.of("versionCode", versionCode));
        CapacityBalanceVersionVO vo = new CapacityBalanceVersionVO();
        if (CollectionUtils.isNotEmpty(capacityBalanceVersionPOS)) {
            vo = CapacityBalanceVersionConvertor.INSTANCE.po2Vo(capacityBalanceVersionPOS.get(0));
        }
        return vo;
    }

    @Override
    public Map<String, BigDecimal> getFirstVersionResourceQuantityByPlanPeriod(String planPeriod) {
        Date date = DateUtils.stringToDate(planPeriod, DateUtils.YEAR_MONTH);
        String beforeMonth = DateUtils.dateToString(DateUtils.moveMonth(date, -1), DateUtils.YEAR_MONTH);
        Map<String, BigDecimal> result = MapUtil.newHashMap();
        Map<String, Object> versionParam = MapUtil.newHashMap();
        versionParam.put("forecastMonth", beforeMonth);
        versionParam.put("enabled", YesOrNoEnum.YES.getCode());
        List<CapacityBalanceVersionVO> capacityBalanceVersions = this.selectByParams(versionParam);
        if (CollectionUtils.isEmpty(capacityBalanceVersions)) {
            return result;
        }
        CapacityBalanceVersionVO capacityBalanceVersionVO = capacityBalanceVersions.stream().min(Comparator.comparing(CapacityBalanceVersionVO::getVersionCode)).get();
        Map<String, Object> relationParam = MapUtil.newHashMap();
        Date yesterday = DateUtils.moveDay(new Date(), -1);
        String yesterdayOfMonth = DateUtils.dateToString(yesterday, DateUtils.YEAR_MONTH);
        relationParam.put("versionId", capacityBalanceVersionVO.getVersionCode());
        relationParam.put("forecastMonth", yesterdayOfMonth);
        relationParam.put("enabled", YesOrNoEnum.YES.getCode());
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationships = capacitySupplyRelationshipService.selectByParams(relationParam);
        if (CollectionUtils.isEmpty(capacitySupplyRelationships)) {
            return result;
        }
        capacitySupplyRelationships.stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getResourceCode))
                .forEach((k, v) -> {
                    BigDecimal quantity = v.stream().map(CapacitySupplyRelationshipVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    result.put(k, quantity);
                });
        return result;
    }
}
