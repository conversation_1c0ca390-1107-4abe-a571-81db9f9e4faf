package com.yhl.scp.mps.config;

import com.yhl.scp.biz.common.webSocket.WebSocketHandshakeInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

/**
 * <code>WebSocketConfig</code>
 * <p>
 * WebSocketConfig
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20 11:05:07
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {


    private final WorkReportWebSocketHandler workReportWebSocketHandler;

    public WebSocketConfig(WorkReportWebSocketHandler workReportWebSocketHandler) {
        this.workReportWebSocketHandler = workReportWebSocketHandler;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(workReportWebSocketHandler, "/ws/alert")
                .addInterceptors(new WebSocketHandshakeInterceptor())
                .setAllowedOrigins("*")
                .withSockJS();
    }


    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        container.setMaxTextMessageBufferSize(100 * 1024 * 1024);
        container.setMaxBinaryMessageBufferSize(100 * 1024 * 1024);
        container.setMaxSessionIdleTimeout(15 * 60000L);
        return container;
    }

}