package com.yhl.scp.mps.plan.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>DeliveryChangeRecordPO</code>
 * <p>
 * 发货变更记录表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-23 10:06:30
 */
@Data
public class DeliveryChangeRecordPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 184816382213195691L;

    /**
     * 车型
     */
    private String vehicleModelCode;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 发货日期
     */
    private Date demandTime;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 变化量
     */
    private Integer variation;
    /**
     * 发货版本id
     */
    private String deliveryVersionId;
    /**
     * 版本
     */
    private Integer versionValue;

}
