package com.yhl.scp.mps.model.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.mps.model.domain.entity.MoldChangeTimeDO;
import com.yhl.scp.mps.model.infrastructure.dao.MoldChangeTimeDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>MoldChangeTimeDomainService</code>
 * <p>
 * 换模换型时间领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-13 15:20:51
 */
@Service
public class MoldChangeTimeDomainService {

    @Resource
    private MoldChangeTimeDao moldChangeTimeDao;

    /**
     * 数据校验
     *
     * @param moldChangeTimeDO 领域对象
     */
    public void validation(MoldChangeTimeDO moldChangeTimeDO) {
        checkNotNull(moldChangeTimeDO);
        checkUniqueCode(moldChangeTimeDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param moldChangeTimeDO 领域对象
     */
    private void checkNotNull(MoldChangeTimeDO moldChangeTimeDO) {
        if (StringUtils.isBlank(moldChangeTimeDO.getOperationCode())) {
            throw new BusinessException("工序代码，不能为空");
        }
        if (StringUtils.isBlank(moldChangeTimeDO.getOperationName())) {
            throw new BusinessException("工序名称，不能为空");
        }
        if (moldChangeTimeDO.getDieChangeTime() == null){
            throw new BusinessException("换模时间，不能为空");
        }
    }

    /**
     * 唯一性校验
     *
     * @param moldChangeTimeDO 领域对象
     */
    private void checkUniqueCode(MoldChangeTimeDO moldChangeTimeDO) {
        // TODO
    }

}
