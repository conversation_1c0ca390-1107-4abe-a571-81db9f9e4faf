package com.yhl.scp.mps.job;

import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;

import lombok.extern.slf4j.Slf4j;

import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import com.yhl.scp.mps.job.dao.DailyBackupDao;

/**
 * <code>DailyBackupJob</code>
 * <p>
 * DailyBackupJob
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 11:42:24
 */
@Component
@Slf4j
public class DailyBackupJob {

    @Value("${dbu.enabled:false}")
    private boolean dbuEnabled;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private DailyBackupDao dailyBackupDao;


    @XxlJob("dbuJob")
    private ReturnT<String> dbuJob() {
        if (!dbuEnabled) {
            log.info("Database backup is disabled");
            return ReturnT.SUCCESS;
        }
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MPS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            log.info("租户下不存在MPS模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            String dataBaseName = scenario.getDataBaseName();
            try {
                log.info("开始备份 scenario：{}下的表", dataBaseName);
                DynamicDataSourceContextHolder.setDataSource(dataBaseName);
                dailyBackupDao.backupTables();
            } finally {
                DynamicDataSourceContextHolder.clearDataSource();
            }
            log.info("scenario：{}下的表备份完成", dataBaseName);
        }
        return ReturnT.SUCCESS;
    }

}
