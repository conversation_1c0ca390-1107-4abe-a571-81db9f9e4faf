package com.yhl.scp.mps.plan.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.plan.dto.MasterPlanDetailDTO;
import com.yhl.scp.mps.plan.service.MasterPlanDetailService;
import com.yhl.scp.mps.plan.vo.MasterPlanDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MasterPlanDetailController</code>
 * <p>
 * 主生产计划明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-02 11:39:53
 */
@Slf4j
@Api(tags = "主生产计划明细控制器")
@RestController
@RequestMapping("masterPlanDetail")
public class MasterPlanDetailController extends BaseController {

    @Resource
    private MasterPlanDetailService masterPlanDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MasterPlanDetailVO>> page() {
        List<MasterPlanDetailVO> masterPlanDetailList = masterPlanDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MasterPlanDetailVO> pageInfo = new PageInfo<>(masterPlanDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MasterPlanDetailDTO masterPlanDetailDTO) {
        return masterPlanDetailService.doCreate(masterPlanDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MasterPlanDetailDTO masterPlanDetailDTO) {
        return masterPlanDetailService.doUpdate(masterPlanDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        masterPlanDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MasterPlanDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanDetailService.selectByPrimaryKey(id));
    }

}
