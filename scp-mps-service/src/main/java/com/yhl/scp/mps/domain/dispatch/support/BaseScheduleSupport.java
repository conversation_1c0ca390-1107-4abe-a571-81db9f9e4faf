package com.yhl.scp.mps.domain.dispatch.support;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.exception.CustomRuntimeException;
import com.yhl.platform.common.utils.*;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.das.core.InputBase;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.enums.OemTradeTypeEnum;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.dps.algorithmnew.input.pojo.CustomerOrderInputNewData;
import com.yhl.scp.ips.algorithm.dto.AlgorithmStepLogDTO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.algo.pojo.AlgorithmPolymerization;
import com.yhl.scp.mds.algorithmnew.input.pojo.*;
import com.yhl.scp.mds.basic.product.enums.ProductTypeEnum;
import com.yhl.scp.mds.box.vo.BoxInfoVO;
import com.yhl.scp.mds.curingTime.vo.MdsCuringTimeVO;
import com.yhl.scp.mds.curingTime.vo.MdsFinishedProductDeliveryVO;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.calendar.domain.entity.ResourceCalendarDO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.productBox.vo.ProductBoxRelationVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.algorithm.enums.SupplyTypeEnum;
import com.yhl.scp.mps.algorithm.strategy.impl.CancelPlanCommand;
import com.yhl.scp.mps.demand.dto.DemandEarlyWarningDTO;
import com.yhl.scp.mps.demand.service.DemandEarlyWarningService;
import com.yhl.scp.mps.dispatch.mps.input.FinishedHalfProductMapping;
import com.yhl.scp.mps.dispatch.mps.input.MpsAlgoInput;
import com.yhl.scp.mps.dispatch.mps.input.ProductionIntervalInput;
import com.yhl.scp.mps.dispatch.mps.input.ProductionPlannedMergeMapping;
import com.yhl.scp.mps.dispatch.output.*;
import com.yhl.scp.mps.domain.dispatch.model.context.AmsAnalysisContext;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.ProductionIntervalDao;
import com.yhl.scp.mps.plan.infrastructure.po.ProductionIntervalPO;
import com.yhl.scp.mps.plan.service.MasterPlanRelationService;
import com.yhl.scp.mps.plan.service.MasterPlanVersionService;
import com.yhl.scp.mps.plan.vo.MasterPlanRelationVO;
import com.yhl.scp.mps.product.service.ProductAdvanceBatchRuleService;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.mps.productionLeadTime.enums.ProductionLeadTimeEnum;
import com.yhl.scp.mps.productionLeadTime.service.ProductionLeadTimeService;
import com.yhl.scp.mps.productionLeadTime.vo.ProductionLeadTimeVO;
import com.yhl.scp.mps.rule.infrastructure.dao.AlgorithmConstraintRuleDao;
import com.yhl.scp.mps.rule.vo.AlgorithmConstraintRuleVO;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.sds.algorithmnew.input.pojo.DemandInputNewData;
import com.yhl.scp.sds.algorithmnew.input.pojo.StockSupplyInputNewData;
import com.yhl.scp.sds.algorithmnew.input.pojo.SupplyInputNewData;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.pegging.enums.DemandTypeEnum;
import com.yhl.scp.sds.extension.feedback.infrastructure.po.FeedbackProductionPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationInputVO;
import com.yhl.scp.sds.extension.order.vo.OperationTaskVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.DemandPO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.FulfillmentPO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.SupplyPO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.feedback.infrastructure.dao.FeedbackProductionDao;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import com.yhl.scp.sds.order.service.*;
import com.yhl.scp.sds.pegging.infrastructure.dao.DemandDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.FulfillmentDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.SupplyDao;
import com.yhl.scp.sds.pegging.service.DemandService;
import com.yhl.scp.sds.pegging.service.SupplyService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 通用的数据支撑
 */
@Slf4j
public class BaseScheduleSupport {

    protected static final String STR_JOIN_VALUE = "&";
    protected static final String VIRTUAL = "virtual";
    protected static final String MPS_MODULE = ModuleCodeEnum.MPS.getCode();
    protected static final String AMS_MODULE = ModuleCodeEnum.AMS.getCode();
    @Resource
    protected IpsFeign ipsFeign;
    @Resource
    protected NewMdsFeign mdsFeign;
    @Resource
    protected MdsFeign otherFeign;
    @Resource
    protected IpsNewFeign ipsNewFeign;
    @Resource
    protected RedisUtil redisUtil;
    @Resource
    protected MasterPlanVersionService masterPlanVersionService;
    @Resource
    protected WorkOrderService workOrderService;
    @Resource
    protected DfpFeign dfpFeign;
    @Resource
    protected NewMdsFeign newMdsFeign;
    @Resource
    protected ProductionLeadTimeService productionLeadTimeService;
    @Resource
    protected WorkOrderDao workOrderDao;
    @Resource
    protected ProductionIntervalDao productionIntervalDao;
    @Resource
    protected FulfillmentDao fulfillmentDao;
    @Resource
    protected DemandDao demandDao;
    @Resource
    protected SupplyDao supplyDao;
    @Resource
    protected OperationService operationService;
    @Resource
    protected OperationTaskService operationTaskService;
    @Resource
    protected OperationInputService operationInputService;
    @Resource
    protected OperationOutputService operationOutputService;
    @Resource
    protected OperationResourceService operationResourceService;
    @Resource
    protected DemandService demandService;
    @Resource
    protected SupplyService supplyService;
    @Resource
    protected OperationExtendService operationExtendService;
    @Resource
    protected SubInventoryCargoLocationDao subInventoryCargoLocationDao;
    @Resource
    protected CancelPlanCommand cancelPlanCommand;
    @Resource
    protected AlgorithmConstraintRuleDao algorithmConstraintRuleDao;
    @Resource
    protected OperationDao operationDao;
    @Resource
    protected OperationTaskExtDao operationTaskExtDao;
    @Resource
    private ProductAdvanceBatchRuleService productAdvanceBatchRuleService;
    @Resource
    private MoldChangeTimeService moldChangeTimeService;
    @Resource
    private MasterPlanDao masterPlanDao;
    @Resource
    private MasterPlanRelationService masterPlanRelationService;
    @Resource
    private DemandEarlyWarningService demandEarlyWarningService;
    @Resource
    private FeedbackProductionDao feedbackProductionDao;

    public static boolean hasIntersection(ProductionIntervalInput interval1, ProductionIntervalInput interval2) {
        return !(interval1.getStart().after(interval2.getEnd()) || interval1.getEnd().before(interval2.getStart()));
    }

    public static void main(String[] args) {
        List<ProductionIntervalInput> productionIntervalInputs = ListUtil.of(
                new ProductionIntervalInput(DateUtils.stringToDate("2025-01-01"), DateUtils.stringToDate("2025-01-02"), "1"),
                new ProductionIntervalInput(DateUtils.stringToDate("2025-01-02"), DateUtils.stringToDate("2025-01-03"), "2"),
                new ProductionIntervalInput(DateUtils.stringToDate("2025-01-05"), DateUtils.stringToDate("2025-01-06"), "3"),
                new ProductionIntervalInput(DateUtils.stringToDate("2025-01-05"), DateUtils.stringToDate("2025-01-07"), "4"),
                new ProductionIntervalInput(DateUtils.stringToDate("2025-01-08"), DateUtils.stringToDate("2025-01-09"), "5"),
                new ProductionIntervalInput(DateUtils.stringToDate("2025-01-02"), DateUtils.stringToDate("2025-01-06"), "6")
        ).stream().sorted(Comparator.comparing(ProductionIntervalInput::getStart)).collect(Collectors.toList());

        List<ProductionIntervalInput> intersections = new ArrayList<>();
        for (ProductionIntervalInput interval1 : productionIntervalInputs) {
            if (CollectionUtils.isEmpty(intersections)) {
                intersections.add(interval1);
                continue;
            }
            ProductionIntervalInput temp = intersections.get(intersections.size() - 1);
            boolean hasIntersection = hasIntersection(interval1, temp);
            if (hasIntersection) {
                temp.setProductionIntervalId(temp.getProductionIntervalId() + "," + interval1.getProductionIntervalId());
            } else {
                intersections.add(interval1);
            }
        }
        System.out.println(intersections);

    }

    public static BigDecimal calculateLeadTime(String key, Map<String, List<ProductionLeadTimeVO>> lineGroup, String type,
                                               String productCode,
                                               Map<String, String> clampTypeBaseMap,
                                               Map<String, String> itemFlagBaseMap,
                                               Map<String, String> attr1BaseMap,
                                               Map<String, String> hudMap) {
        BigDecimal sumHour = BigDecimal.ZERO;
        List<ProductionLeadTimeVO> productionLeadTimeVOS = lineGroup.get(key);
        if (CollectionUtils.isEmpty(productionLeadTimeVOS)) {
            return sumHour;
        }
        if (StrUtil.isNotEmpty(type)) {
            sumHour = productionLeadTimeVOS.stream()
                    .filter(p -> StrUtil.isEmpty(p.getSpecialOperationSubclass()) && null != p.getLeadTime() && p.getLeadTimeType().equals(type))
                    .map(ProductionLeadTimeVO::getLeadTime)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            productionLeadTimeVOS = productionLeadTimeVOS.stream()
                    .filter(p -> StrUtil.isNotEmpty(p.getSpecialOperationSubclass()) && null != p.getLeadTime()
                            && p.getLeadTimeType().equals(type)).collect(Collectors.toList());
        } else {
            sumHour = productionLeadTimeVOS.stream()
                    .filter(p -> StrUtil.isEmpty(p.getSpecialOperationSubclass()) && null != p.getLeadTime())
                    .map(ProductionLeadTimeVO::getLeadTime)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            productionLeadTimeVOS = productionLeadTimeVOS.stream()
                    .filter(p -> StrUtil.isNotEmpty(p.getSpecialOperationSubclass()) && null != p.getLeadTime()).collect(Collectors.toList());
        }
        BigDecimal leadTimeSum = BigDecimal.ZERO;
        for (ProductionLeadTimeVO leadTimeVO : productionLeadTimeVOS) {
            String specialOperationSubclass = leadTimeVO.getSpecialOperationSubclass();
            BigDecimal leadTime = leadTimeVO.getLeadTime() == null ? BigDecimal.ZERO : leadTimeVO.getLeadTime();
            //物料对应夹丝类型
            if (clampTypeBaseMap.containsKey(productCode)) {
                String code = clampTypeBaseMap.get(productCode);
                if (code.equals(specialOperationSubclass)) {
                    leadTimeSum = BigDecimalUtils.add(leadTimeSum, leadTime);
                }
            }
            //物料对应调光类型
            if (itemFlagBaseMap.containsKey(productCode)) {
                String code = itemFlagBaseMap.get(productCode);
                if (code.equals(specialOperationSubclass)) {
                    leadTimeSum = BigDecimalUtils.add(leadTimeSum, leadTime);
                }
            }
            //物料对应除膜
            if (attr1BaseMap.containsKey(productCode)) {
                String code = attr1BaseMap.get(productCode);
                if (code.equals(specialOperationSubclass)) {
                    leadTimeSum = BigDecimalUtils.add(leadTimeSum, leadTime);
                }
            }
            // 物料对应HUD
            if (hudMap.containsKey(productCode)) {
                String code = hudMap.get(productCode);
                if (code.equals(specialOperationSubclass)) {
                    leadTimeSum = BigDecimalUtils.add(leadTimeSum, leadTime);
                }
            }
        }
        return sumHour.add(leadTimeSum);
    }

    protected InputBase initProcess(AlgorithmLog algorithmLog, String moduleCode) {
        log.info("算法日志id：{}，场景：{}，算法：{}", algorithmLog.getId(), algorithmLog.getScenario(), algorithmLog.getAlgorithmVersion());
        InputBase inputBase = new InputBase();
        inputBase.setAlgorithmLogId(algorithmLog.getId());
        inputBase.setExecutionNumber(algorithmLog.getExecutionNumber());
        inputBase.setModuleCode(moduleCode);
        inputBase.setScenario(algorithmLog.getScenario());
        inputBase.setAlgorithmVersion(algorithmLog.getAlgorithmVersion());
        return inputBase;
    }

    protected List<String> getPermissionProductIds(AlgorithmLog algorithmLog) {
        List<String> productIds;
        if (StrUtil.isNotEmpty(algorithmLog.getLineGroup())) {
            List<String> lineGroupList = Arrays.asList(algorithmLog.getLineGroup().split(","));
            productIds = newMdsFeign.getPlannerProductIdByLineGroup(lineGroupList);
            log.info("指定产线组获取物料id");
        } else {
            productIds = newMdsFeign.getPlannerProductIdList(algorithmLog.getCreator());
            log.info("不指定产线组使用人员获取物料id");
        }
        return productIds;
    }

    protected MpsAnalysisContext initResultContext(RzzMpsAlgorithmOutput mpsAlgorithmOutput, AlgorithmLog algorithmLog) {

        List<AlgorithmStepLogDTO> algorithmStepLogDTOList = new ArrayList<>();
        Date dateTime1 = new Date();
        mpsAlgorithmOutput.setWorkOrderIds(new ArrayList<>());
        String creator = algorithmLog.getCreator();
        String scenario = algorithmLog.getScenario();

        List<WorkOrderPO> workOrderAll = workOrderDao.selectByParams(new HashMap<>());
        List<String> productIds = getPermissionProductIds(algorithmLog);
        // 筛选当前计划员负责的物料
        List<WorkOrderPO> workOrderPOS = workOrderAll
                .stream().filter(p -> StrUtil.isEmpty(p.getParentId()) && productIds.contains(p.getProductId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderPOS)) {
            List<String> workOrderIds = StreamUtils.columnToList(workOrderPOS, WorkOrderPO::getId);
            // 父制造订单下所有的子订单
            List<WorkOrderPO> childWorkOrder = workOrderAll.stream().filter(p ->
                    StrUtil.isNotEmpty(p.getParentId()) && workOrderIds.contains(p.getParentId())).collect(Collectors.toList());
            workOrderPOS.addAll(childWorkOrder);
        }
        Map<String, WorkOrderPO> workOrderMap = workOrderPOS.stream().collect(Collectors.toMap(WorkOrderPO::getId, Function.identity()));
        log.info("制造订单数量：{}", workOrderPOS.size());

        List<MasterPlanRelationVO> masterPlanRelationVOS = masterPlanRelationService.selectAll();
        Map<String, MasterPlanRelationVO> masterPlanRelationVOMap = StreamUtils.mapByColumn(masterPlanRelationVOS, MasterPlanRelationVO::getOrderNo);
        log.info("计划单数量：{}", masterPlanRelationVOS.size());

        // 合批结果，将多个制造批量合成了一批
        List<RzzProductionIntervalOutput> productionIntervalOutputDataList = mpsAlgorithmOutput.getProductionIntervalOutputDataList();
        Map<String, RzzProductionIntervalOutput> intervalOutputMap = productionIntervalOutputDataList.stream()
                .collect(Collectors.toMap(RzzProductionIntervalOutput::getProductionIntervalId, v -> v));
        List<String> resourceCodes = StreamUtils.columnToList(productionIntervalOutputDataList, RzzProductionIntervalOutput::getStandardResourceId)
                .stream().distinct().collect(Collectors.toList());
        List<PhysicalResourceVO> physicalResourceVOS = mdsFeign.selectPhysicalResourceByParams(scenario, ImmutableMap.of("physicalResourceCodes", resourceCodes));
        Map<String, String> physicalResourceCodeOnId = physicalResourceVOS.stream().collect(Collectors
                .toMap(PhysicalResourceVO::getPhysicalResourceCode, PhysicalResourceVO::getId, (k1, k2) -> k1));
        log.info("生产批量输出数量：{}", productionIntervalOutputDataList.size());

        // 合批结果合批量关联关系
        List<RzzProductionPlannedInIntervalOutput> productionPlannedInIntervalOutputDataList = mpsAlgorithmOutput.getProductionPlannedInIntervalOutputDataList();
        Map<String, List<RzzProductionPlannedInIntervalOutput>> inIntervalMpa = productionPlannedInIntervalOutputDataList.stream()
                .collect(Collectors.groupingBy(RzzProductionPlannedInIntervalOutput::getProductionIntervalId));
        log.info("生产批量与生产计划量对应关系输出结果：{}", productionPlannedInIntervalOutputDataList.size());

        // 生产计划量
        List<RzzProductionPlannedOutput> productionPlannedOutputDataList = mpsAlgorithmOutput.getProductionPlannedOutputDataList();
        log.info("生产计划量数量：{}", productionPlannedOutputDataList.size());

        // 物品信息,是否只拿S2(非销售组织)下得物品信息
        List<String> outputProductId = mpsAlgorithmOutput.getProductionPlannedOutputOutputDataList().stream()
                .map(RzzProductionPlannedOutputOutput::getProductStockPointId).distinct().collect(Collectors.toList());
        List<String> inputProductId = mpsAlgorithmOutput.getProductionPlannedInputOutputDataList().stream()
                .map(RzzProductionPlannedInputOutput::getProductStockPointId).distinct().collect(Collectors.toList());
        List<String> productId = new ArrayList<>();
        // 获取输出输出的物品信息
        productId.addAll(getSplitMpsProductId(outputProductId));
        productId.addAll(getSplitMpsProductId(inputProductId));
        productId = productId.stream().distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productList = mdsFeign
                .selectProductStockPointByParams(null, ImmutableMap.of("ids", productId));
        Map<String, NewProductStockPointVO> productMap = productList.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        List<NewStockPointVO> newStockPointVOS = mdsFeign.selectAllStockPoint(null);
        Map<String, NewStockPointVO> stockMap = newStockPointVOS.stream()
                .collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity()));
        // 产品工艺基础数据
        List<String> productCodeList = StreamUtils.columnToList(productList, NewProductStockPointVO::getProductCode);
        List<MdsProductStockPointBaseVO> productBaseList = mdsFeign.selectProductStockPointBaseByParams(null, ImmutableMap.of("productCodeList", productCodeList));
        Map<String, MdsProductStockPointBaseVO> productStockPointBaseMap = StreamUtils.mapByColumn(productBaseList, MdsProductStockPointBaseVO::getProductCode);
        log.info("查询物品数量:{}，查询物品基础数据数量：{}", productList.size(), productBaseList.size());

        // 工艺路径
        List<RoutingVO> routingVOS = mdsFeign.selectRoutingByParams(null, new HashMap<>())
                .stream().filter(p -> YesOrNoEnum.YES.getCode().equals(p.getEnabled())).collect(Collectors.toList());
        Map<String, RoutingVO> routingVOMap = routingVOS.stream()
                .collect(Collectors.toMap(RoutingVO::getProductId, Function.identity()));
        List<RoutingStepVO> stepList = mdsFeign.selectAllRoutingStep(null);
        Map<String, RoutingStepVO> routingStepVOMap = stepList.stream().collect(Collectors.toMap(RoutingStepVO::getId, Function.identity()));
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(null);
        Map<String, StandardStepVO> standardStepVOMap = standardStepVOS.stream().collect(Collectors.toMap(StandardStepVO::getId, Function.identity()));
        List<RuleEncodingsVO> prsRulRuleEncodingsVOS = mdsFeign.getRuleEncoding();
        Map<String, RuleEncodingsVO> ruleEncodingsMap = prsRulRuleEncodingsVOS.stream()
                .collect(Collectors.toMap(RuleEncodingsVO::getRuleName, v -> v));

        // 发货计划&库存推移数据
        PlanningHorizonVO planningHorizonVO = mdsFeign.selectPlanningHorizon(null);
        // 过滤发货计划根据产线组
        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = filterProduct(creator, scenario, planningHorizonVO, algorithmLog);
        if (CollectionUtils.isEmpty(deliveryPlanPublishedVOS)) throw new BusinessException("解析失败，没有发货计划");
        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap = deliveryPlanPublishedVOS.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        String deliveryVersionId = deliveryPlanPublishedVOS.get(0).getDeliveryVersionId();
        List<InventoryShiftVO> inventoryShiftVOS = dfpFeign.selectInventoryShiftList(ImmutableMap.of("versionId", deliveryVersionId));
        Map<String, List<InventoryShiftVO>> inventoryShiftMap = inventoryShiftVOS.stream()
                .collect(Collectors.groupingBy(p -> StrUtil.join(STR_JOIN_VALUE, p.getOemCode(), p.getProductCode())));

        List<PlanHorizonInputNewData> planHorizonInputNewData = mdsFeign.selectTimeSequence();
        Map<Integer, PlanHorizonInputNewData> planHorizonInputNewDataMap = planHorizonInputNewData.stream()
                .collect(Collectors.toMap(PlanHorizonInputNewData::getPeriodSequence, Function.identity()));

        List<ProductionIntervalPO> productionIntervalVOS = productionIntervalDao.selectByParams(new HashMap<>());
        Map<String, ProductionIntervalPO> productionIntervalMap = productionIntervalVOS.stream()
                .collect(Collectors.toMap(ProductionIntervalPO::getOrderId, Function.identity()));

        List<FulfillmentPO> fulfillmentPOS = fulfillmentDao.selectByParams(new HashMap<>());
        Map<String, List<FulfillmentPO>> fulfillmentMap = fulfillmentPOS.stream()
                .collect(Collectors.groupingBy(FulfillmentPO::getSupplyOrderId));

        List<DemandPO> demandPOS = demandDao.selectByParams(new HashMap<>());
        Map<String, DemandPO> demandMap = demandPOS.stream()
                .collect(Collectors.toMap(DemandPO::getId, Function.identity()));

        List<SupplyPO> supplyPOS = supplyDao.selectByParams(new HashMap<>());
        Map<String, SupplyPO> supplyPOMap = supplyPOS.stream()
                .collect(Collectors.toMap(SupplyPO::getId, Function.identity()));

        algorithmStepLogDTOList.add(getStepLog("MPS结果解析数据支撑初始化完成", MPS_MODULE, algorithmLog.getId(), dateTime1, new Date()));
        return MpsAnalysisContext.builder()
                .physicalResourceCodeOnIdMap(physicalResourceCodeOnId)
                .workOrderOnResourceMap(new HashMap<>())
                .scheduleInfoMap(new HashMap<>())
                .workOrderIds(new ArrayList<>())
                .algorithmStepLogDTOList(algorithmStepLogDTOList)
                .planningHorizonVO(planningHorizonVO)
                .masterPlanRelationVOList(masterPlanRelationVOS)
                .masterPlanRelationMap(masterPlanRelationVOMap)
                .creatorId(creator)
                .inventoryShiftVOList(inventoryShiftVOS)
                .inventoryShiftMap(inventoryShiftMap)
                .routingStepVOS(stepList)
                .routingStepVOMap(routingStepVOMap)
                .standardStepVOS(standardStepVOS)
                .standardStepVOMap(standardStepVOMap)
                .supplyPOS(supplyPOS)
                .supplyPOMap(supplyPOMap)
                .demandPOS(demandPOS)
                .demandMap(demandMap)
                .fulfillmentPOS(fulfillmentPOS)
                .fulfillmentPOMap(fulfillmentMap)
                .productionIntervalVOS(productionIntervalVOS)
                .productionIntervalMap(productionIntervalMap)
                .planHorizonInputNewDataList(planHorizonInputNewData)
                .planHorizonMap(planHorizonInputNewDataMap)
                .rzzMpsAlgorithmOutput(mpsAlgorithmOutput)
                .workOrderPOS(workOrderPOS)
                .workOrderAll(workOrderAll)
                .workOrderMap(workOrderMap)
                .productMap(productMap)
                .productStockPointBaseMap(productStockPointBaseMap)
                .productionIntervalOutputDataList(productionIntervalOutputDataList)
                .intervalOutputMap(intervalOutputMap)
                .productionPlannedInIntervalOutputDataList(productionPlannedInIntervalOutputDataList)
                .inIntervalMap(inIntervalMpa)
                .productionPlannedOutputDataList(productionPlannedOutputDataList)
                .productList(productList)
                .newStockPointVOS(newStockPointVOS)
                .stockMap(stockMap)
                .routingVOS(routingVOS)
                .routingVOMap(routingVOMap)
                .ruleEncodingsMap(ruleEncodingsMap)
                .deliveryPlanPublishedVOS(deliveryPlanPublishedVOS)
                .createWorkOrderList(new ArrayList<>())
                .updateWorkOrderList(new ArrayList<>())
                .createDemandsList(new ArrayList<>())
                .updateDemandsList(new ArrayList<>())
                .createSupplyList(new ArrayList<>())
                .updateSupplyList(new ArrayList<>())
                .createProductionIntervalList(new ArrayList<>())
                .updateProductionIntervalList(new ArrayList<>())
                .createFulfillmentList(new ArrayList<>())
                .updateFulfillmentList(new ArrayList<>())
                .algorithmLog(algorithmLog)
                .deliveryPlanMap(deliveryPlanMap)
                .workOrderCancelPlanIdList(new ArrayList<>())
                .planningHorizonVO(planningHorizonVO)
                .build();
    }

    private List<String> getSplitMpsProductId(List<String> productIdList) {
        List<String> productIds = new ArrayList<>();
        for (String productId : productIdList) {
            if (productId.contains(STR_JOIN_VALUE)) {
                String[] split = productId.split(STR_JOIN_VALUE);
                productIds.add(split[2]);
            } else {
                productIds.add(productId);
            }
        }
        return productIds;
    }

    protected AlgorithmStepLogDTO getStepLog(String msg, String moduleCode, String logId, Date start, Date end) {
        AlgorithmStepLogDTO stepLogDTO = AlgorithmStepLogDTO.builder()
                .id(UUIDUtil.getUUID())
                .logId(logId)
                .stepMsg(msg)
                .moduleCode(moduleCode)
                .stepTime(start)
                .build();
        if (null != start && null != end) {
            long diffInSeconds = DateUtil.between(start, end, DateUnit.SECOND);
            long hours = diffInSeconds / 3600;
            long minutes = (diffInSeconds % 3600) / 60;
            long seconds = diffInSeconds % 60;
            String timeConsuming = String.format("%02d:%02d:%02d", hours, minutes, seconds);
            stepLogDTO.setTimeConsuming(timeConsuming);
        }
        return stepLogDTO;
    }

    protected void getInputData(AlgorithmLog algorithmLog, InputBase inputBase, List<AlgorithmStepLogDTO> algorithmStepLogDTOList) throws ExecutionException, InterruptedException {
        String creatorUserId = algorithmLog.getCreator();
        String algorithmLogId = algorithmLog.getId();
        String scenario = algorithmLog.getScenario();
        String mpsModule = ModuleCodeEnum.MPS.getCode();
        List<String> productLineList = Arrays.asList(algorithmLog.getProductLine().split(","));
        log.info("开始获取算法输入数据，logId：{}", creatorUserId);
        Date timeStep1 = new Date();
        PlanningHorizonVO planningHorizonVO = newMdsFeign.selectPlanningHorizon(null);
        // 筛选当前订单计划员的发货计划数据
        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = filterProduct(creatorUserId, scenario,
                planningHorizonVO, algorithmLog);
        algorithmStepLogDTOList.add(getStepLog("权限筛选发货计划完成", mpsModule, algorithmLogId, timeStep1, new Date()));
        Date timeStep2 = new Date();
        Map<String, List<DeliveryPlanPublishedVO>> deliverMap = StreamUtils.mapListByColumn(deliveryPlanPublishedVOS, DeliveryPlanPublishedVO::getProductCode);
        List<String> oemList = deliveryPlanPublishedVOS.stream().map(DeliveryPlanPublishedVO::getOemCode).collect(Collectors.toList());
        List<OemVO> oemCodes = dfpFeign.selectOemByParams(null, ImmutableMap.of("oemCodes", oemList));
        Map<String, OemVO> oemVOMap = oemCodes.stream().collect(Collectors.toMap(OemVO::getOemCode, Function.identity(), (k1, k2) -> k1));
        // thread
        CompletableFuture<List<InventoryBatchDetailVO>> stock =
                CompletableFuture.supplyAsync(() -> dfpFeign.selectInventoryDataByProductCodes(scenario,
                        new ArrayList<>(), StockPointTypeEnum.BC.getCode()));
        CompletableFuture<List<MoldChangeTimeVO>> changeTime = CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<MoldChangeTimeVO> moldChangeTimeVOS = moldChangeTimeService.selectAll();
            DynamicDataSourceContextHolder.clearDataSource();
            return moldChangeTimeVOS;
        });
        CompletableFuture<List<FeedbackProductionPO>> feedBackThread = CompletableFuture.supplyAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<FeedbackProductionPO> feedbackProductionPOS = feedbackProductionDao.selectByParams(new HashMap<>());
            DynamicDataSourceContextHolder.clearDataSource();
            return feedbackProductionPOS;
        });
        // mds组装基础输入数据
        AlgorithmPolymerization algorithmPolymerization = mdsFeign.getAlgorithmPolymerization();
        MdsAlgorithmInputNew mdsAlgorithmInput = algorithmPolymerization.getMdsAlgorithmInputNew();
        algorithmStepLogDTOList.add(getStepLog("MDS服务基础数据收集完成", mpsModule, algorithmLogId, timeStep2, new Date()));
        Date timeStep3 = new Date();
        // 物品信息
        List<NewProductStockPointVO> newProductStockPointVOS = algorithmPolymerization.getNewProductStockPointVOS();
        Map<String, NewProductStockPointVO> productStockPointVOMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(null);
        List<AlgorithmConstraintRuleVO> algorithmConstraintRuleVOS = algorithmConstraintRuleDao.selectVOByParams(ImmutableMap.of("ruleCode", "GENERAL#ORIGINAL#2"));
        List<ProductCandidateResourceTimeVO> productCandidateResourceVOS = newMdsFeign.selectProductCandidateResourceTimeByParams(null, new HashMap<>());
        Map<String, List<ProductCandidateResourceTimeVO>> productCandidateResourceMap = productCandidateResourceVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));
        log.info("基础输入查询完成");

        // join thread
        CompletableFuture.allOf(stock, changeTime, feedBackThread).join();
        List<InventoryBatchDetailVO> inventoryBatchDetailVOS = stock.get();
        List<MoldChangeTimeVO> moldChangeTimeVOS = changeTime.get();
        List<FeedbackProductionPO> feedbackProductionPOS = feedBackThread.get();

        // 获取发货计划关键工序输入
        List<String> routingIds = new ArrayList<>();
        List<String> routingStepIds = new ArrayList<>();
        List<String> semiProductIds = new ArrayList<>();
        // 待排需求code
        Set<String> demandCodeList = new TreeSet<>();
        // 待移除资源的集合id
        Set<String> removeResourceIdList = new TreeSet<>();
        Map<String, String> productDayMap = new HashMap<>();
        Map<String, String> originalTimeMap = new HashMap<>();
        List<StockSupplyInputNewData> stockSupplyInputNewData = new ArrayList<>();
        List<FinishedHalfProductMapping> finishedHalfProductMappingList = new ArrayList<>();
        List<CustomerOrderInputNewData> masterProductionPlanData = algorithmInputData(mdsAlgorithmInput.getWorkOrderRoutingInputDataList(),
                mdsAlgorithmInput.getWorkOrderRoutingStepInputDataList(),
                mdsAlgorithmInput.getWorkOrderRoutingStepInputInputDataList(),
                routingIds,
                deliveryPlanPublishedVOS,
                routingStepIds,
                newProductStockPointVOS,
                standardStepVOS,
                productDayMap,
                mdsAlgorithmInput,
                semiProductIds,
                originalTimeMap,
                stockSupplyInputNewData,
                algorithmPolymerization,
                inventoryBatchDetailVOS,
                productLineList,
                demandCodeList,
                removeResourceIdList,
                finishedHalfProductMappingList
        );
        algorithmStepLogDTOList.add(getStepLog("发货计划筛选需求计算完成", mpsModule, algorithmLogId, timeStep3, new Date()));
        Date timeStep4 = new Date();
        log.info("发货计划计算完成，待排需求物料code数量：{}", demandCodeList.size());
        inventoryStock(stockSupplyInputNewData, newProductStockPointVOS, algorithmPolymerization);
        algorithmStepLogDTOList.add(getStepLog("库存计算完成", mpsModule, algorithmLogId, timeStep4, new Date()));
        Date timeStep5 = new Date();
        // 根据发货计划明细创建demand
        List<DemandInputNewData> demandInputNewDataList = new ArrayList<>();
        // 滚动排程输入，区分锁定期内外输入数据
        List<ProductionPlannedMergeMapping> productionPlannedMergeMappings = new ArrayList<>();
        Set<String> productWorkOrder = new TreeSet<>();
        List<ProductionIntervalInput> workOrderRange = getWorkOrderRange(
                mdsAlgorithmInput,
                algorithmLog,
                routingStepIds,
                routingIds,
                algorithmPolymerization,
                productStockPointVOMap,
                productionPlannedMergeMappings,
                feedbackProductionPOS,
                productLineList,
                demandCodeList,
                productWorkOrder
        );
        // 构建需求订单，根据产线筛选
        masterProductionPlanData.removeIf(p -> !demandCodeList.contains(p.getResistanceDiff()));
        for (CustomerOrderInputNewData masterProductionPlanDatum : masterProductionPlanData) {
            String demandTime = originalTimeMap.get(masterProductionPlanDatum.getCustomerOrderId());
            DemandInputNewData demandInputNewData = getDemandInputNewData(masterProductionPlanDatum);
            demandInputNewData.setOriginalTime(demandTime);
            demandInputNewDataList.add(demandInputNewData);
        }
        // 补充的产品的候选资源，只保留原排产资源的候选资源
        afterHandleWorkOrderResource(productWorkOrder, mdsAlgorithmInput, algorithmPolymerization,
                standardStepVOS, productLineList, removeResourceIdList);
        algorithmStepLogDTOList.add(getStepLog("锁定期订单筛选完成", mpsModule, algorithmLogId, timeStep5, new Date()));
        Date timeStep6 = new Date();
        // 筛选需求物料
        getDemandProduct(mdsAlgorithmInput, routingStepIds, routingIds, semiProductIds, stockSupplyInputNewData);
        // 设置资源换模时间
        setResourceChangeTime(mdsAlgorithmInput, newProductStockPointVOS, standardStepVOS, productCandidateResourceMap, moldChangeTimeVOS);
        // 计算工艺路径批量，及设置虚拟物料关联成品信息
        getRoutingBatchQty(mdsAlgorithmInput, productDayMap, demandInputNewDataList, oemVOMap, productStockPointVOMap,
                deliverMap, algorithmConstraintRuleVOS);
        // 构建请求入参
        MpsAlgoInput mpsAlgoInput = setRequestParam(mdsAlgorithmInput, demandInputNewDataList,
                masterProductionPlanData, stockSupplyInputNewData, workOrderRange, productionPlannedMergeMappings,
                finishedHalfProductMappingList, algorithmPolymerization);
        // 构建齐套排产允差值
        tolerance(mpsAlgoInput, algorithmConstraintRuleVOS);
        log.info("结束获取算法输入数据");
        inputBase.setInput(mpsAlgoInput);
        algorithmStepLogDTOList.add(getStepLog("筛选需求及设置物料信息完成", mpsModule, algorithmLogId, timeStep6, new Date()));
    }

    private void afterHandleWorkOrderResource(Set<String> productWorkOrder,
                                              MdsAlgorithmInputNew mdsAlgorithmInput,
                                              AlgorithmPolymerization algorithmPolymerization,
                                              List<StandardStepVO> standardStepVOS,
                                              List<String> productLineList,
                                              Set<String> removeResourceIdList) {
        List<RoutingVO> routingVOS = algorithmPolymerization.getRoutingVOS();
        List<RoutingStepVO> routingStepVOS = algorithmPolymerization.getRoutingStepVOS();
        List<WorkOrderRoutingStepResourceInputNewData> workOrderRoutingStepResourceInputDataList = mdsAlgorithmInput.getWorkOrderRoutingStepResourceInputDataList();

        Map<String, RoutingVO> routingVOMap = StreamUtils.mapByColumn(routingVOS, RoutingVO::getProductCode);
        Map<String, List<RoutingStepVO>> routingStepMap = StreamUtils.mapListByColumn(routingStepVOS, RoutingStepVO::getRoutingId);
        Map<String, StandardStepVO> standardStepVOMap = StreamUtils.mapByColumn(standardStepVOS, StandardStepVO::getId);
        Map<String, List<WorkOrderRoutingStepResourceInputNewData>> routingResourceMap = StreamUtils.mapListByColumn(
                workOrderRoutingStepResourceInputDataList, WorkOrderRoutingStepResourceInputNewData::getRoutingStepId);
        // 排在指定产线订单的物品，处理候选资源筛选排在指定产线订单的候选资源
        for (String productCode : productWorkOrder) {
            RoutingVO routingVO = routingVOMap.get(productCode);
            String id = routingVO.getId();
            List<RoutingStepVO> routingSteps = routingStepMap.get(id);
            for (RoutingStepVO routingStep : routingSteps) {
                String stepId = routingStep.getId();
                String standardStepId = routingStep.getStandardStepId();
                StandardStepVO standardStepVO = standardStepVOMap.get(standardStepId);
                String standardStepType = standardStepVO.getStandardStepType();
                if (StandardStepEnum.FORMING_PROCESS.getCode().equals(standardStepType)) {
                    List<WorkOrderRoutingStepResourceInputNewData> waitingRemoveResource = routingResourceMap.get(stepId)
                            .stream().filter(p -> !productLineList.contains(p.getStandardResourceCode())).collect(Collectors.toList());
                    removeResourceIdList.addAll(waitingRemoveResource.stream().map(WorkOrderRoutingStepResourceInputNewData::getRoutingStepResourceId).collect(Collectors.toList()));
                }
            }
        }
        log.info("移出候选资源前行数：{}", mdsAlgorithmInput.getWorkOrderRoutingStepResourceInputDataList().size());
        mdsAlgorithmInput.getWorkOrderRoutingStepResourceInputDataList().removeIf(p -> removeResourceIdList.contains(p.getRoutingStepResourceId()));
        log.info("移出候选资源后行数：{}", mdsAlgorithmInput.getWorkOrderRoutingStepResourceInputDataList().size());
    }

    private void filterResource(MpsAlgoInput mpsAlgoInput, String creatorUserId) {
        log.info("过滤订单计划员路径候选资源前数量：{}", mpsAlgoInput.getWorkOrderRoutingStepResourceInputDataList().size());
        List<PhysicalResourceVO> plannerPhysicalResource = newMdsFeign.getPlannerPhysicalResource(creatorUserId);
        List<String> physicalResourceIds = plannerPhysicalResource.stream()
                .map(PhysicalResourceVO::getId).collect(Collectors.toList());
        mpsAlgoInput.getWorkOrderRoutingStepResourceInputDataList().removeIf(p -> !physicalResourceIds.contains(p.getStandardResourceId()));
        log.info("过滤订单计划员路径候选资源后数量：{}", mpsAlgoInput.getWorkOrderRoutingStepResourceInputDataList().size());
    }

    protected List<DeliveryPlanPublishedVO> filterProduct(String creator, String scenario,
                                                          PlanningHorizonVO planningHorizonVO, AlgorithmLog algorithmLog) {
        String lineGroup = algorithmLog.getLineGroup();
        List<String> lineGrouoList = Arrays.asList(lineGroup.split(","));
        // 根据计划员获取标准资源，得到产线组，根据产线组筛选base物品，得到当前计划员负责的物料code
        List<String> plannerProduct = newMdsFeign.selectPermissionsByLineGroupList(scenario, lineGrouoList);
        log.info("筛选订单计划员发货计划id：{}", creator);
        if (plannerProduct.isEmpty()) {
            throw new CustomRuntimeException("订单计划员无可运行的发货计划物料");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("productCodes", plannerProduct);
        params.put("startTimeStr", DateUtils.dateToString(planningHorizonVO.getPlanStartTime(), DateUtils.COMMON_DATE_STR3));
        params.put("endTimeStr", DateUtils.dateToString(planningHorizonVO.getPlanEndTime(), DateUtils.COMMON_DATE_STR3));
        List<DeliveryPlanPublishedVO> deliveryPlanPublished = dfpFeign.selectPublishDeliveryPlanByParams(params).stream()
                .filter(p -> !p.getProductCode().toUpperCase().startsWith("SZ"))
                .filter(p -> p.getDemandQuantity() != null && p.getDemandQuantity() > 0)
                .collect(Collectors.toList());
        if (deliveryPlanPublished.isEmpty()) {
            throw new CustomRuntimeException("订单计划员无可运行的发货计划");
        }
        log.info("订单计划员：{}，运行发货计划物料：{}", creator, deliveryPlanPublished.size());
        return deliveryPlanPublished;
    }

    private void tolerance(MpsAlgoInput mpsAlgoInput, List<AlgorithmConstraintRuleVO> algorithmConstraintRuleVOS) {
        if (CollectionUtils.isNotEmpty(algorithmConstraintRuleVOS)) {
            AlgorithmParameterInputNewData parameterInputData = AlgorithmParameterInputNewData.builder()
                    .paramName("tolerance")
                    .paramValue(algorithmConstraintRuleVOS.get(0).getRuleValue())
                    .build();
            mpsAlgoInput.getAlgorithmParameterInputDataList().add(parameterInputData);
        }
    }

    private Map<String, SubInventoryCargoLocationVO> getSubInventoryCargoLocation(List<InventoryBatchDetailVO> inventoryBatchDetailVOS) {
        List<String> freightSpaces = inventoryBatchDetailVOS.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .distinct().collect(Collectors.toList());
        // 库存货位
        return subInventoryCargoLocationDao
                .selectByBatchCodeAndStockType(freightSpaces, StockPointTypeEnum.BC.getCode())
                .stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (k1, k2) -> k1));
    }

    private List<String> getSaleOrganization(AlgorithmPolymerization algorithmPolymerization) {
        return algorithmPolymerization.getNewStockPointVOS()
                .stream().filter(p -> StrUtil.isNotEmpty(p.getOrganizeType()) &&
                        (p.getOrganizeType().equals(StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode())))
                .filter(p -> StrUtil.isNotEmpty(p.getStockPointType()) && p.getStockPointType().equals(StockPointTypeEnum.BC.getCode()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
    }

    private void inventoryStock(List<StockSupplyInputNewData> supplyList,
                                List<NewProductStockPointVO> newProductStockPointVOS,
                                AlgorithmPolymerization algorithmPolymerization) {
        // 成品库存：只取库存点为销售组织（组织类型为SALE_ORGANIZATION）、本厂（stock_point_type为BC），CPSJ子库存
        List<String> newStockPointVOS = getSaleOrganization(algorithmPolymerization);
        Map<String, NewProductStockPointVO> productStockPointVOMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(p -> StrUtil.join("&", p.getStockPointCode(), p.getProductCode()),
                        Function.identity(), (v1, v2) -> v1));
        // 筛选出生产组织和采购组织的库存点的库存批次
        List<InventoryBatchDetailVO> inventoryBatchDetailVOS = dfpFeign.selectInventoryBatchDetailVOByParams02(
                ImmutableMap.of("subinventory", getRangeData(), "stockPointCodeList", newStockPointVOS));
        log.info("inventoryStock库存数量：{}", inventoryBatchDetailVOS.size());
        Map<String, SubInventoryCargoLocationVO> cargoLocationVOMap = getSubInventoryCargoLocation(inventoryBatchDetailVOS);
        for (InventoryBatchDetailVO inventoryBatchDetailVO : inventoryBatchDetailVOS) {
            String freightSpace = inventoryBatchDetailVO.getFreightSpace();
            String productCode = inventoryBatchDetailVO.getProductCode();
            String stockPointCode = inventoryBatchDetailVO.getStockPointCode();
            String key = StrUtil.join("&", stockPointCode, productCode);
            if (!productStockPointVOMap.containsKey(key)) {
                continue;
            }
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(key);
            String productType = newProductStockPointVO.getProductType();
            String productClassify = newProductStockPointVO.getProductClassify();
            if (!(productType.equals(com.yhl.scp.mds.enums.ProductTypeEnum.MPBL.getCode()) || productClassify.startsWith("RA.A"))) {
                continue;
            }
            if (!cargoLocationVOMap.containsKey(freightSpace)) {
                continue;
            }
            SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationVOMap.get(freightSpace);
            String enabled = subInventoryCargoLocationVO.getEnabled();
            // 货位是否可用
            if (!enabled.equals(YesOrNoEnum.YES.getCode())) {
                continue;
            }
            String productId = newProductStockPointVO.getId();
            StockSupplyInputNewData supplyInputNewData = new StockSupplyInputNewData();
            supplyInputNewData.setStockId(inventoryBatchDetailVO.getId());
            supplyInputNewData.setStockCode(inventoryBatchDetailVO.getId());
            supplyInputNewData.setProductStockPointId(productId);
            supplyInputNewData.setSupplyTime(inventoryBatchDetailVO.getAssignedTime());
            supplyInputNewData.setQty(BigDecimalUtils.toBigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
            supplyInputNewData.setPriority(BigDecimal.ONE);
            supplyList.add(supplyInputNewData);
        }
    }

    private DemandInputNewData getDemandInputNewData(CustomerOrderInputNewData masterProductionPlanDatum) {
        DemandInputNewData demandInputNewData = new DemandInputNewData();
        demandInputNewData.setDemandId(masterProductionPlanDatum.getCustomerOrderId());
        demandInputNewData.setProductStockPointId(masterProductionPlanDatum.getProductStockPointId());
        demandInputNewData.setQty(masterProductionPlanDatum.getQty());
        demandInputNewData.setDemandTime(masterProductionPlanDatum.getDueDate());
        demandInputNewData.setUnfulfilledQty(masterProductionPlanDatum.getQty());
        demandInputNewData.setDemandType(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode());
        demandInputNewData.setDemandOrderId(masterProductionPlanDatum.getCustomerOrderId());
        return demandInputNewData;
    }

    private MpsAlgoInput setRequestParam(MdsAlgorithmInputNew mdsAlgorithmInput,
                                         List<DemandInputNewData> demandInputNewDataList,
                                         List<CustomerOrderInputNewData> masterProductionPlanData,
                                         List<StockSupplyInputNewData> stockSupplyInputNewData,
                                         List<ProductionIntervalInput> workOrderRange,
                                         List<ProductionPlannedMergeMapping> productionPlannedMergeMappings, List<FinishedHalfProductMapping> finishedHalfProductMappingList, AlgorithmPolymerization algorithmPolymerization) {

        List<SupplyInputNewData> supplyInputNewDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(stockSupplyInputNewData)) {
            for (StockSupplyInputNewData stockSupplyInputNewDatum : stockSupplyInputNewData) {
                SupplyInputNewData supplyInputNewData = new SupplyInputNewData();
                supplyInputNewData.setSupplyId(stockSupplyInputNewDatum.getStockId());
                supplyInputNewData.setSupplyOrderId(stockSupplyInputNewDatum.getStockId());
                supplyInputNewData.setProductStockPointId(stockSupplyInputNewDatum.getProductStockPointId());
                supplyInputNewData.setQty(stockSupplyInputNewDatum.getQty());
                supplyInputNewData.setUnfulfilledQty(stockSupplyInputNewDatum.getQty());
                supplyInputNewData.setSupplyType(SupplyTypeEnum.STOCK_SUPPLY.getCode());
                if (StrUtil.isNotEmpty(stockSupplyInputNewDatum.getSupplyTime())) {
                    supplyInputNewData.setSupplyTime(stockSupplyInputNewDatum.getSupplyTime());
                } else {
                    supplyInputNewData.setSupplyTime("1990-01-01 00:00:00");
                }
                supplyInputNewDataList.add(supplyInputNewData);
            }
        }
        log.info("运行需求:{}", JSON.toJSONString(algorithmPolymerization.getProcessRunMap()));
        return MpsAlgoInput.builder()
                .productionPlannedMergeMappingInputList(productionPlannedMergeMappings)
                .stockPointInputDataList(mdsAlgorithmInput.getStockPointInputDataList())
                .productInputDataList(mdsAlgorithmInput.getProductInputDataList())
                .productStockPointInputDataList(mdsAlgorithmInput.getProductStockPointInputDataList())
                .purchaseOrderRoutingInputDataList(mdsAlgorithmInput.getPurchaseOrderRoutingInputDataList())
                .alternativeProductInputDataList(mdsAlgorithmInput.getAlternativeProductInputDataList())
                .switchoverProductInputDataList(mdsAlgorithmInput.getSwitchoverProductInputDataList())
                .transportOrderRoutingInputDataList(mdsAlgorithmInput.getTransportOrderRoutingInputDataList())
                .transportSectionInputDataList(mdsAlgorithmInput.getTransportSectionInputDataList())
                .workOrderRoutingInputDataList(mdsAlgorithmInput.getWorkOrderRoutingInputDataList())
                .workOrderRoutingStepInputDataList(mdsAlgorithmInput.getWorkOrderRoutingStepInputDataList())
                .workOrderRoutingStepInputInputDataList(mdsAlgorithmInput.getWorkOrderRoutingStepInputInputDataList())
                .workOrderRoutingStepOutputInputDataList(mdsAlgorithmInput.getWorkOrderRoutingStepOutputInputDataList())
                .workOrderRoutingStepResourceInputDataList(mdsAlgorithmInput.getWorkOrderRoutingStepResourceInputDataList())
                .planHorizonInputDataList(mdsAlgorithmInput.getPlanHorizonInputDataList())
                .standardResourceInputDataList(mdsAlgorithmInput.getStandardResourceInputDataList())
                .physicalResourceInputDataList(mdsAlgorithmInput.getPhysicalResourceInputDataList())
                .calendarInputDataList(mdsAlgorithmInput.getCalendarInputDataList())
                .physicalResourceCalendarInputDataList(mdsAlgorithmInput.getPhysicalResourceCalendarInputDataList())
                .transportCalendarInputDataList(mdsAlgorithmInput.getTransportCalendarInputDataList())
                .purchaseCalendarInputDataList(mdsAlgorithmInput.getPurchaseCalendarInputDataList())
                .productionOrganizationInputDataList(mdsAlgorithmInput.getProductionOrganizationInputDataList())
                .supplierInputDataList(mdsAlgorithmInput.getSupplierInputDataList())
                .supplierCapacityInputDataList(mdsAlgorithmInput.getSupplierCapacityInputDataList())
                .supplierRatioInputDataList(mdsAlgorithmInput.getSupplierRatioInputDataList())
                .climbInputDataList(mdsAlgorithmInput.getClimbInputDataList())

                .customerOrderInputDataList(new ArrayList<>())
                .forecastDemandInputDataList(new ArrayList<>())

                .purchasePlannedInputDataList(new ArrayList<>())
                .productionPlannedInputDataList(new ArrayList<>())
                .transportPlannedInputDataList(new ArrayList<>())
                .purchaseForecastStockInputDataList(new ArrayList<>())
                .stockSupplyInputDataList(new ArrayList<>())
                .productionPlannedInputInputDataList(new ArrayList<>())
                .productionPlannedOutputInputDataList(new ArrayList<>())
                .productionPlannedResourceInputDataList(new ArrayList<>())
                .demandInputDataList(new ArrayList<>())
                .supplyInputDataList(new ArrayList<>())
                .optimizeConfigInputData(new ArrayList<>())
                .fulfillmentInputDataList(new ArrayList<>())
                .optimizeParameterInputDataList(new ArrayList<>())
                .algorithmParameterInputDataList(mdsAlgorithmInput.getAlgorithmParameterInputDataList())

                .customerOrderInputDataList(masterProductionPlanData)
                .demandInputDataList(demandInputNewDataList)
                .stockSupplyInputDataList(stockSupplyInputNewData)
                .supplyInputDataList(supplyInputNewDataList)
                .productionIntervalInputList(workOrderRange)
                .finishedHalfProductMappingList(finishedHalfProductMappingList)
//                .processRunMap(algorithmPolymerization.getProcessRunMap())
                .build();
    }

    private void setResourceChangeTime(MdsAlgorithmInputNew mdsAlgorithmInput, List<NewProductStockPointVO> newProductStockPointVOS,
                                       List<StandardStepVO> standardStepVOS, Map<String, List<ProductCandidateResourceTimeVO>> productCandidateResourceMap,
                                       List<MoldChangeTimeVO> moldChangeTimeVOS) {
        log.info("资源换模时间计算开始");
        List<WorkOrderRoutingStepInputNewData> workOrderRoutingStepInputDataList = mdsAlgorithmInput.getWorkOrderRoutingStepInputDataList();
        List<WorkOrderRoutingStepResourceInputNewData> workOrderRoutingStepResourceInputDataList = mdsAlgorithmInput.getWorkOrderRoutingStepResourceInputDataList();

        Map<String, List<WorkOrderRoutingStepInputNewData>> stepMap = workOrderRoutingStepInputDataList.stream()
                .collect(Collectors.groupingBy(WorkOrderRoutingStepInputNewData::getRoutingId));
        Map<String, List<WorkOrderRoutingStepResourceInputNewData>> resourceMap = workOrderRoutingStepResourceInputDataList.stream()
                .collect(Collectors.groupingBy(WorkOrderRoutingStepResourceInputNewData::getRoutingStepId));
        Map<String, NewProductStockPointVO> productStockPointVOMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        Map<String, StandardStepVO> standardStepVOMap = standardStepVOS.stream().collect(Collectors
                .toMap(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getStandardStepCode()), v -> v, (k1, k2) -> k1));
        Map<String, MoldChangeTimeVO> moldChangeTimeVOMap = moldChangeTimeVOS.stream()
                .collect(Collectors.toMap(p -> String.join("&", p.getOperationCode(), p.getResourceCode(), p.getProductCode()), Function.identity(), (k1, k2) -> k1));

        for (WorkOrderRoutingInputNewData workOrderRoutingInputNewData : mdsAlgorithmInput.getWorkOrderRoutingInputDataList()) {
            String productStockPointId = workOrderRoutingInputNewData.getProductStockPointId();
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(productStockPointId);
            String productCode = newProductStockPointVO.getProductCode();
            String stockPointCode = newProductStockPointVO.getStockPointCode();
            String routingId = workOrderRoutingInputNewData.getRoutingId();
            List<WorkOrderRoutingStepInputNewData> workOrderRoutingStepInputNewData = stepMap.get(routingId);
            for (WorkOrderRoutingStepInputNewData workOrderRoutingStepInputNewDatum : workOrderRoutingStepInputNewData) {
                // 判断是否是成型工序
                Integer sequenceNumber = workOrderRoutingStepInputNewDatum.getSequenceNumber();
                String joinKey = CharSequenceUtil.join("&", stockPointCode, sequenceNumber);
                // 判断当前工序是否为‘成型'工序
                StandardStepVO standardStepVO = standardStepVOMap.get(joinKey);
                if (null == standardStepVO) {
                    continue;
                }
                String standardStepType = standardStepVO.getStandardStepType() == null ? "" : standardStepVO.getStandardStepType();
                if (CharSequenceUtil.isNotEmpty(standardStepType) && "FORMING_PROCESS".equals(standardStepType)) {
                    String routingStepId = workOrderRoutingStepInputNewDatum.getRoutingStepId();
                    List<WorkOrderRoutingStepResourceInputNewData> workOrderRoutingStepResourceInputNewData = resourceMap.get(routingStepId);
                    if (CollectionUtils.isEmpty(workOrderRoutingStepResourceInputNewData)) {
                        log.info("routingId：{}，存在下挂路径步骤无候选资源stepId：{}", routingId, routingStepId);
                        continue;
                    }
                    workOrderRoutingStepResourceInputNewData.sort(Comparator.comparing(WorkOrderRoutingStepResourceInputNewData::getPriority));
                    String physicalResourceCode = workOrderRoutingStepResourceInputNewData.get(0).getStandardResourceCode();
                    String key = CharSequenceUtil.join(STR_JOIN_VALUE, sequenceNumber, physicalResourceCode, productCode);
                    String mainKey = CharSequenceUtil.join(STR_JOIN_VALUE, stockPointCode, productCode, sequenceNumber);
                    BigDecimal beat = BigDecimal.ONE;
                    if (productCandidateResourceMap.containsKey(mainKey)) {
                        ProductCandidateResourceTimeVO productCandidateResourceVO = productCandidateResourceMap.get(mainKey).get(0);
                        Double resourceBeat = productCandidateResourceVO.getBeat();
                        beat = resourceBeat == null ? BigDecimal.ZERO : BigDecimalUtils.toBigDecimal(resourceBeat);
                    }
                    // 最小换模量=（内换模时间+外换模时间）*60/关键工序节拍
                    workOrderRoutingInputNewData.setMinQuantity(BigDecimal.ONE);
                    if (moldChangeTimeVOMap.containsKey(key)) {
                        MoldChangeTimeVO moldChangeTimeVO = moldChangeTimeVOMap.get(key);
                        BigDecimal dieChangeTime = moldChangeTimeVO.getDieChangeTime() == null ?
                                BigDecimal.ZERO : BigDecimalUtils.toBigDecimal(moldChangeTimeVO.getDieChangeTime());
                        BigDecimal outsideDieChangeTime = moldChangeTimeVO.getOutsideDieChangeTime() == null ?
                                BigDecimal.ZERO : BigDecimalUtils.toBigDecimal(moldChangeTimeVO.getOutsideDieChangeTime());
                        BigDecimal sum = BigDecimalUtils.add(dieChangeTime, outsideDieChangeTime);
                        if (sum.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal changeTime = BigDecimalUtils.multiply(sum, BigDecimal.valueOf(60), 2);
                            BigDecimal minMoldChangeTime = BigDecimalUtils.divide(changeTime, beat, 1);
                            workOrderRoutingInputNewData.setMinQuantity(minMoldChangeTime);
                        }
                    }

                    for (WorkOrderRoutingStepResourceInputNewData workOrderRoutingStepResourceInputNewDatum : workOrderRoutingStepResourceInputNewData) {
                        String standardResourceCode = workOrderRoutingStepResourceInputNewDatum.getStandardResourceCode();
                        String joinKeyResource = CharSequenceUtil.join("&", sequenceNumber, standardResourceCode, productCode);
                        if (moldChangeTimeVOMap.containsKey(joinKeyResource)) {
                            MoldChangeTimeVO moldChangeTimeVO = moldChangeTimeVOMap.get(joinKeyResource);
                            if (null != moldChangeTimeVO.getDieChangeTime()) {
                                Long outsideDieChangeTime = moldChangeTimeVO.getDieChangeTime();
                                double outsideDieChangeTimeInHours = outsideDieChangeTime / 60.0;
                                BigDecimal outsideDieChangeTimeInHoursRounded = BigDecimal.valueOf(outsideDieChangeTimeInHours).setScale(2, BigDecimal.ROUND_HALF_UP);
                                workOrderRoutingStepResourceInputNewDatum.setChangeOverTime(outsideDieChangeTimeInHoursRounded);
                            }
                        } else {
                            workOrderRoutingStepResourceInputNewDatum.setChangeOverTime(BigDecimalUtils.toBigDecimal(1));
                        }
                    }
                }
            }
        }
        log.info("资源换模时间计算结束");
    }

    private <T> CompletableFuture<Void> filterListAsync(List<T> list, java.util.function.Predicate<T> predicate) {
        return CompletableFuture.runAsync(() -> list.removeIf(predicate.negate()));
    }

    private void getDemandProduct(MdsAlgorithmInputNew mdsAlgorithmInput, List<String> routingStepIds, List<String> routingIds,
                                  List<String> semiProductIds, List<StockSupplyInputNewData> stockSupplyInputNewData) {
        log.info("筛选需求物料开始");

        // 使用 HashSet 来存储 ids，以便快速查找
        Set<String> idsSet = new HashSet<>();

        // 过滤需求路径数据
        CompletableFuture<Void> future1 = filterListAsync(
                mdsAlgorithmInput.getWorkOrderRoutingInputDataList(),
                p -> routingIds.contains(p.getRoutingId())
        );
        CompletableFuture<Void> future2 = filterListAsync(
                mdsAlgorithmInput.getWorkOrderRoutingStepInputDataList(),
                p -> routingStepIds.contains(p.getRoutingStepId())
        );
        CompletableFuture<Void> future3 = filterListAsync(
                mdsAlgorithmInput.getWorkOrderRoutingStepInputInputDataList(),
                p -> routingStepIds.contains(p.getRoutingStepId())
        );
        CompletableFuture<Void> future4 = filterListAsync(
                mdsAlgorithmInput.getWorkOrderRoutingStepOutputInputDataList(),
                p -> routingStepIds.contains(p.getRoutingStepId())
        );
        CompletableFuture<Void> future5 = filterListAsync(
                mdsAlgorithmInput.getWorkOrderRoutingStepResourceInputDataList(),
                p -> routingStepIds.contains(p.getRoutingStepId())
        );
        CompletableFuture.allOf(future1, future2, future3, future4, future5).join();

        // 过滤物品数据
        mdsAlgorithmInput.getWorkOrderRoutingStepInputInputDataList().stream()
                .map(WorkOrderRoutingStepInputInputNewData::getProductStockPointId)
                .forEach(idsSet::add);
        mdsAlgorithmInput.getWorkOrderRoutingStepOutputInputDataList().stream()
                .map(WorkOrderRoutingStepOutputInputNewData::getProductStockPointId)
                .forEach(idsSet::add);
        stockSupplyInputNewData.stream()
                .map(StockSupplyInputNewData::getProductStockPointId)
                .forEach(idsSet::add);

        // 过滤 productStockPointInputDataList
        mdsAlgorithmInput.getProductStockPointInputDataList().removeIf(p -> !idsSet.contains(p.getProductStockPointId()));

        // 收集 productIds
        Set<String> productIdsSet = mdsAlgorithmInput.getProductStockPointInputDataList().stream()
                .map(ProductStockPointInputNewData::getProductId)
                .collect(Collectors.toSet());

        // 过滤 productInputDataList
        mdsAlgorithmInput.getProductInputDataList().removeIf(p -> !productIdsSet.contains(p.getProductId()));

        // 修改 S1 的首道工序且是 MBPL 的物料
        if (CollectionUtils.isNotEmpty(semiProductIds)) {
            log.info("修改S1的首道工序且是MBPL的物料：{}", JSON.toJSONString(semiProductIds));
            mdsAlgorithmInput.getProductStockPointInputDataList().forEach(p -> {
                if (semiProductIds.contains(p.getProductStockPointId())) {
                    p.setType(ProductTypeEnum.MATERIAL.getCode());
                    p.setKitComputation(YesOrNoEnum.NO.getCode());
                }
            });
        }
        log.info("筛选需求物料结束");
    }

    private void getRoutingBatchQty(MdsAlgorithmInputNew mdsAlgorithmInput, Map<String, String> productDayMap,
                                    List<DemandInputNewData> demandInputNewDataList, Map<String, OemVO> oemVOMap,
                                    Map<String, NewProductStockPointVO> productStockPointVOMap,
                                    Map<String, List<DeliveryPlanPublishedVO>> deliverMap,
                                    List<AlgorithmConstraintRuleVO> algorithmConstraintRuleVOS) {
        log.info("计算工艺路径批量，及设置虚拟物料关联成品信息开始");
        List<ProductStockPointInputNewData> productStockPointInputDataList = mdsAlgorithmInput.getProductStockPointInputDataList();
        List<String> productCodes = productStockPointInputDataList.stream()
                .map(ProductStockPointInputNewData::getProductCode).distinct().collect(Collectors.toList());
        List<WorkOrderRoutingInputNewData> workOrderRoutingInputDataList = mdsAlgorithmInput.getWorkOrderRoutingInputDataList();
        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS = productAdvanceBatchRuleService.selectAll();
        List<PartRiskLevelVO> partRiskLevelVOS = dfpFeign.selectMaterialRiskLeveByProductCodeList(null, productCodes);
        List<ProductBoxRelationVO> productBoxRelationVOS = mdsFeign.selectAllBox(null);
        List<BoxInfoVO> boxInfoVOS = mdsFeign.selectPiecePerBoxByBoxCode(null, MapUtil.newHashMap());

        Map<String, BoxInfoVO> boxInfoVOMap = boxInfoVOS.stream().collect(Collectors.toMap(BoxInfoVO::getId, Function.identity()));
        Map<String, ProductBoxRelationVO> productBoxRelationVOMap = productBoxRelationVOS.stream()
                .collect(Collectors.toMap(ProductBoxRelationVO::getProductStockPointId, v -> v, (k1, k2) -> k1));
        // 根据不同维度分组提前生产批次规则（物料编码，车型，风险等级）
        Map<String, ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getProductCode()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductCode, v -> v, (k1, k2) -> k1));
        Map<String, ProductAdvanceBatchRuleVO> productTypeAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getProductType()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductType, v -> v, (k1, k2) -> k1));
        Map<String, ProductAdvanceBatchRuleVO> riskLevelAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getRiskLevel()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getRiskLevel, v -> v, (k1, k2) -> k1));
        Map<String, PartRiskLevelVO> partRiskLevelVOMap = partRiskLevelVOS.stream()
                .collect(Collectors.toMap(PartRiskLevelVO::getProductCode, v -> v, (k1, k2) -> k1));

        String boxTypeStr = "铁箱";
        String lowStr = "低";
        for (ProductStockPointInputNewData productStockPointInputNewData : productStockPointInputDataList) {
            String productCode = productStockPointInputNewData.getProductCode();
            String vehicleModelCode = productStockPointInputNewData.getVehicleModelCode();
            if (productAdvanceBatchRuleVOMap.containsKey(productCode)) {
                // 根据物料代码获取
                productStockPointInputNewData.setMaxAheadProdTime(BigDecimalUtils
                        .toPlainString(productAdvanceBatchRuleVOMap.get(productCode).getMaximumNumberDays()));
                continue;
            }
            if (productTypeAdvanceBatchRuleVOMap.containsKey(vehicleModelCode)) {
                // 根据车型代码获取
                productStockPointInputNewData.setMaxAheadProdTime(BigDecimalUtils
                        .toPlainString(productTypeAdvanceBatchRuleVOMap.get(vehicleModelCode).getMaximumNumberDays()));
                continue;
            }
            if (partRiskLevelVOMap.containsKey(productCode)) {
                // 根据风险等级获取
                String materialRiskLevel = partRiskLevelVOMap.get(productCode).getMaterialRiskLevel();
                if (riskLevelAdvanceBatchRuleVOMap.containsKey(materialRiskLevel)) {
                    productStockPointInputNewData.setMaxAheadProdTime(BigDecimalUtils
                            .toPlainString(riskLevelAdvanceBatchRuleVOMap.get(materialRiskLevel).getMaximumNumberDays()));
                    continue;
                }
            }
            ProductAdvanceBatchRuleVO productAdvanceBatchRuleVO = riskLevelAdvanceBatchRuleVOMap.get(lowStr);
            if (null != productAdvanceBatchRuleVO) {
                // 虚拟物料也会被设置提前生产天数，二次处理重新设置对应成品生产天数
                productStockPointInputNewData.setMaxAheadProdTime(BigDecimalUtils
                        .toPlainString(riskLevelAdvanceBatchRuleVOMap.get(lowStr).getMaximumNumberDays()));
            }
        }

        String toleranceValue = CollectionUtils.isNotEmpty(algorithmConstraintRuleVOS) ? algorithmConstraintRuleVOS.get(0).getRuleValue() : "";
        for (DemandInputNewData demandInputNewData : demandInputNewDataList) {
            String productStockPointId = demandInputNewData.getProductStockPointId();
            NewProductStockPointVO productStockPointVO = productStockPointVOMap.get(productStockPointId);
            String productCode = productStockPointVO.getProductCode();
            String oemCode = deliverMap.get(productCode).get(0).getOemCode();
            if (!oemVOMap.containsKey(oemCode)) {
                demandInputNewData.setToleranceStatus(YesOrNoEnum.NO.getCode());
                continue;
            }
            OemVO oemVO = oemVOMap.get(oemCode);
            String marketType = oemVO.getMarketType();
            if (StrUtil.isEmpty(marketType)) {
                demandInputNewData.setToleranceStatus(YesOrNoEnum.NO.getCode());
                continue;
            }
            if (marketType.equals(OemTradeTypeEnum.OUT.getDesc())) {
                demandInputNewData.setToleranceStatus(YesOrNoEnum.YES.getCode());
                demandInputNewData.setToleranceValue(toleranceValue);
            }
            if (marketType.equals(OemTradeTypeEnum.IN.getDesc()) && partRiskLevelVOMap.containsKey(productCode)) {
                String materialRiskLevel = partRiskLevelVOMap.get(productCode).getMaterialRiskLevel();
                if (StrUtil.isNotEmpty(materialRiskLevel) && materialRiskLevel.equals(lowStr)) {
                    demandInputNewData.setToleranceStatus(YesOrNoEnum.YES.getCode());
                    demandInputNewData.setToleranceValue(toleranceValue);
                } else {
                    demandInputNewData.setToleranceStatus(YesOrNoEnum.NO.getCode());
                }
            } else {
                demandInputNewData.setToleranceStatus(YesOrNoEnum.NO.getCode());
            }
        }

        // 后处理设置虚拟物品最大提前生产天数
        processProductStockPointInputDataList(productStockPointInputDataList, mdsAlgorithmInput);

        for (WorkOrderRoutingInputNewData workOrderRoutingInputData : workOrderRoutingInputDataList) {
            BigDecimal maxQuantity = BigDecimal.ZERO;
            BigDecimal minQuantity = workOrderRoutingInputData.getMinQuantity() == null ? BigDecimal.ZERO : workOrderRoutingInputData.getMinQuantity();
            BigDecimal lotSize = BigDecimal.ONE;
            String productStockPointId = workOrderRoutingInputData.getProductStockPointId();
            // 计算最大批量
            ProductBoxRelationVO productBoxRelationVO = productBoxRelationVOMap.get(productStockPointId);
            if (null != productBoxRelationVO && StringUtils.equals(boxTypeStr, productBoxRelationVO.getBoxType())) {
                String boxId = productBoxRelationVO.getBoxId();
                maxQuantity = boxInfoVOMap.containsKey(boxId) ? BigDecimalUtils.toBigDecimal(boxInfoVOMap.get(boxId).getBoxQuantity()
                        * boxInfoVOMap.get(boxId).getPiecePerBox()) : BigDecimal.ZERO;
            }
            // 计算最小批量
            if (maxQuantity.compareTo(BigDecimal.ZERO) == 0 && minQuantity.compareTo(BigDecimal.ZERO) > 0) {
                maxQuantity = minQuantity;
            }
            workOrderRoutingInputData.setMaxQuantity(maxQuantity.compareTo(BigDecimal.ZERO) == 0 ?
                    BigDecimalUtils.toBigDecimal("99999") : maxQuantity);
            workOrderRoutingInputData.setMinQuantity(minQuantity);
            workOrderRoutingInputData.setLotSize(lotSize);
        }
        mdsAlgorithmInput.setWorkOrderRoutingInputDataList(workOrderRoutingInputDataList);
        log.info("计算工艺路径批量，及设置虚拟物料关联成品信息结束");
    }

    public void processProductStockPointInputDataList(List<ProductStockPointInputNewData> productStockPointInputDataList, MdsAlgorithmInputNew mdsAlgorithmInput) {
        // 筛选出包含 "virtual" 的产品并分组
        Map<String, List<ProductStockPointInputNewData>> virtualProductMap = productStockPointInputDataList.stream()
                .filter(p -> p.getProductId().contains("virtual"))
                .collect(Collectors.groupingBy(p -> p.getProductId().split("&")[2]));

        // 筛选出不包含 "virtual" 的产品
        List<ProductStockPointInputNewData> nonVirtualProductList = productStockPointInputDataList.stream()
                .filter(p -> !p.getProductId().contains("virtual"))
                .collect(Collectors.toList());

        // 处理不包含 "virtual" 的产品，更新 maxAheadProdTime 并合并到结果列表
        List<ProductStockPointInputNewData> interProductList = new ArrayList<>();
        for (ProductStockPointInputNewData productStockPointInputNewData : nonVirtualProductList) {
            String productId = productStockPointInputNewData.getProductId();
            if (!virtualProductMap.containsKey(productId)) {
                continue;
            }
            String maxAheadProdTime = productStockPointInputNewData.getMaxAheadProdTime();
            List<ProductStockPointInputNewData> productStockPointList = virtualProductMap.get(productId);
            for (ProductStockPointInputNewData stockPointInputNewData : productStockPointList) {
                stockPointInputNewData.setMaxAheadProdTime(maxAheadProdTime);
                interProductList.add(stockPointInputNewData);
            }
        }

        // 更新 productStockPointInputDataList
        productStockPointInputDataList.removeIf(p -> interProductList.stream().anyMatch(i -> i.getProductId().equals(p.getProductId())));
        productStockPointInputDataList.addAll(interProductList);

        // 设置到 mdsAlgorithmInput
        mdsAlgorithmInput.setProductStockPointInputDataList(productStockPointInputDataList);
    }

    private List<ProductionIntervalInput> getWorkOrderRange(MdsAlgorithmInputNew algorithmInput, AlgorithmLog algorithmLog,
                                                            List<String> routingStepIds, List<String> routingIds,
                                                            AlgorithmPolymerization algorithmPolymerization,
                                                            Map<String, NewProductStockPointVO> productStockPointVOMap,
                                                            List<ProductionPlannedMergeMapping> productionPlannedMergeMappings,
                                                            List<FeedbackProductionPO> feedbackProductionPOS,
                                                            List<String> productLineList,
                                                            Set<String> demandCodeList,
                                                            Set<String> productWorkOrder) {
        // 订单计划员id
        String creator = algorithmLog.getCreator();
        // 计划期间
        PlanningHorizonVO planningHorizonVO = algorithmPolymerization.getPlanningHorizonVO();
        // 历史展望开始时间
        Date historyRetrospectStartTime = planningHorizonVO.getHistoryRetrospectStartTime();
        // 计划开始时间
        Date planStartTime = planningHorizonVO.getPlanStartTime();
        // 计划冻结时间
        Date planLockEndTime = planningHorizonVO.getPlanLockEndTime();
        log.info("历史展望时间：{}，计划开始时间：{}，计划锁定时间：{}",
                DateUtils.dateToString(historyRetrospectStartTime, DateUtils.COMMON_DATE_STR1),
                DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR1),
                DateUtils.dateToString(planLockEndTime, DateUtils.COMMON_DATE_STR1));
        List<WorkOrderVO> workOrderVOAll = getSupplementsOrder(creator, algorithmLog);
        // 获取权限下的制造订单
        if (CollectionUtils.isEmpty(workOrderVOAll)) {
            log.info("订单计划员：{}，不存在已有制造订单", creator);
            return new ArrayList<>();
        }
        // 筛选指定排产产线的订单
        List<WorkOrderVO> workOrderVOS = getProductLineWorkOrder(workOrderVOAll, productLineList);
        Map<String, List<WorkOrderVO>> childWorkOrder = workOrderVOS.stream().filter(p -> StrUtil.isNotEmpty(p.getParentId())).collect(Collectors.toList())
                .stream().collect(Collectors.groupingBy(WorkOrderVO::getParentId));
        // 不存在需求且成型工序的时间不在锁定期范围内的待删除订单
        Map<String, List<WorkOrderRoutingStepInputNewData>> routingStepMap = algorithmInput
                .getWorkOrderRoutingStepInputDataList().stream().collect(Collectors.groupingBy(WorkOrderRoutingStepInputNewData::getRoutingId));
        List<StandardResourceInputNewData> standardResourceInputDataList = algorithmInput.getStandardResourceInputDataList();
        Map<String, StandardResourceInputNewData> standardResourceInputNewDataMap = standardResourceInputDataList.stream()
                .collect(Collectors.toMap(StandardResourceInputNewData::getStandardResourceId, Function.identity()));
        List<PlanHorizonInputNewData> planHorizonInputDataList = algorithmInput.getPlanHorizonInputDataList()
                .stream().sorted(Comparator.comparing(PlanHorizonInputNewData::getPeriodSequence)).collect(Collectors.toList());
        Map<String, WorkOrderVO> workOrderVOMap = workOrderVOS.stream()
                .collect(Collectors.toMap(WorkOrderVO::getId, Function.identity()));
        List<String> workOrderIds = StreamUtils.columnToList(workOrderVOS, WorkOrderVO::getId);
        // 过滤出关键工序在锁定期的已计划父工序，找到其对应的制造订单固定输入，其余设置不固定
        List<OperationVO> operationAll = operationService.selectVOByParams(ImmutableMap.of("orderIds", workOrderIds));
        List<String> operationIds = StreamUtils.columnToList(operationAll, OperationVO::getId);
        Map<String, List<OperationVO>> orderOperation = operationAll.stream()
                .collect(Collectors.groupingBy(OperationVO::getOrderId));
        List<OperationVO> childOperationList = operationAll.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getParentId())).collect(Collectors.toList());
        Map<String, List<OperationVO>> childMap = childOperationList.stream()
                .collect(Collectors.groupingBy(OperationVO::getParentId));
        List<OperationTaskVO> operationTaskVOS = operationTaskService.selectByParams(ImmutableMap.of("operationIds", operationIds));
        Map<String, List<OperationTaskVO>> taskMap = operationTaskVOS.stream()
                .collect(Collectors.groupingBy(OperationTaskVO::getOperationId));
        List<ProductionIntervalInput> productionIntervalInputList = new ArrayList<>();
        Map<String, List<FeedbackProductionPO>> feedBackResourceMap = StreamUtils.mapListByColumn(feedbackProductionPOS,
                FeedbackProductionPO::getPhysicalResourceId);
        // 物理资源
        Map<String, PhysicalResourceVO> physicalResourceVOMap = algorithmPolymerization.getPhysicalResourceVOMap();
        // 计划单信息
        List<MasterPlanRelationVO> masterPlanRelationVOS = masterPlanRelationService.selectAll();
        log.info("固定计划单数量：{}", masterPlanRelationVOS.size());
        Map<String, List<MasterPlanRelationVO>> planRelationMap = StreamUtils.mapListByColumn(masterPlanRelationVOS, MasterPlanRelationVO::getOrderNo);
        List<String> fixWorkOrderId = new ArrayList<>();
        List<String> unLockCancelPlanWorkOrderId = new ArrayList<>();
        List<String> lockOrderLastDateIds = new ArrayList<>();
        List<String> statusList = ListUtil.of(PlannedStatusEnum.STARTED.getCode());
        PlanHorizonInputNewData firstPlanHorizonInput = planHorizonInputDataList.get(0);
        PlanHorizonInputNewData lastPlanHorizonInput = planHorizonInputDataList.get(planHorizonInputDataList.size() - 1);
        // 制造订单计划单关联锁定
        lockPlanWorkOrder(workOrderVOS, statusList, fixWorkOrderId, planRelationMap, childWorkOrder);
        // 锁定期订单关联锁定资源占位
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            String id = workOrderVO.getId();
            boolean whetherFixed = productionIntervalInputList.stream().anyMatch(p -> p.getProductionIntervalId().equals(id));
            if (whetherFixed) {
                continue;
            }
            String fixed = Optional.ofNullable(workOrderVO.getFixed()).orElse(YesOrNoEnum.NO.getCode());
            List<OperationVO> operationVOS = orderOperation.get(id);
            if (CollectionUtils.isEmpty(operationVOS)) {
                log.warn("制造订单：{}，无工序信息", id);
                continue;
            }
            // 循环父工序：一个父工序对应多个子工序，子工序对应多个task工序任务，task工序任务对应多个subTask任务（设置，制造，清洗）
            List<OperationVO> operationParent = operationVOS.stream()
                    .filter(p -> StrUtil.isEmpty(p.getParentId()))
                    .sorted(Comparator.comparing(OperationVO::getRoutingStepSequenceNo)).collect(Collectors.toList());

            for (int i = 0; i < operationParent.size(); i++) {
                OperationVO operationVO = operationParent.get(i);
                String standardStepType = operationVO.getStandardStepType();
                if (StrUtil.isEmpty(standardStepType)) {
                    continue;
                }
                // 判断当前工序是否为关键成型工序
                if (!standardStepType.equals(StandardStepEnum.FORMING_PROCESS.getCode())) {
                    continue;
                }
                BigDecimal scheduleQuantity = workOrderVO.getQuantity();
                String operationId = operationVO.getId();
                Date startTime = operationVO.getStartTime();
                Date endTime = operationVO.getEndTime();
                if (null == startTime || null == endTime) {
                    log.warn("已计划关键工序制造开始结束时间存在空值，opid：{}", operationVO.getId());
                    continue;
                }
                // 判断排上的父工序的开始结束时间是否和锁定期开始时间和锁定期结束时间是否在锁定期内，存在则固定，不存在则删除制造订单
                boolean hasIntersection = hasIntersection(startTime, endTime, planStartTime, planLockEndTime);
                if (hasIntersection || YesOrNoEnum.YES.getCode().equals(fixed)) {
                    addWorkOrderAndParentIfNotEmpty(fixWorkOrderId, workOrderVO);
                    // 锁定期内报工判断
                    BigDecimal feedBackQty = feedBackTime(childMap, operationId, statusList, scheduleQuantity);
                    if (feedBackQty.compareTo(BigDecimal.ZERO) <= 0) {
                        log.warn("制造订单锁定数量：{}，完工计算后数量小于等于0不设置固定：{}", scheduleQuantity, workOrderVO.getOrderNo());
                        continue;
                    }
                    scheduleQuantity = feedBackQty;
                } else {
                    // 不在锁定期内，判断是否在历史展望期内，存在则不删除制造订单
                    boolean hasHistoryLock = hasIntersection(startTime, endTime, historyRetrospectStartTime, planStartTime);
                    if (hasHistoryLock) {
                        addWorkOrderAndParentIfNotEmpty(fixWorkOrderId, workOrderVO);
                        log.info("制造订单：{}，在计划历史展望期内，不删除", workOrderVO.getOrderNo());
                    }
                    continue;
                }
                boolean lastOperation = i == operationParent.size() - 1;
                // getFixedOperation校验并获取固定的制造订单
                Boolean fixedOperation = getFixedOperation(operationVO, workOrderVOMap, childMap,
                        taskMap, physicalResourceVOMap, standardResourceInputNewDataMap,
                        planHorizonInputDataList, workOrderVO, firstPlanHorizonInput, lastPlanHorizonInput,
                        planStartTime, routingIds, routingStepMap, routingStepIds, productionIntervalInputList,
                        lockOrderLastDateIds, scheduleQuantity, hasIntersection, productStockPointVOMap, lastOperation);
                // 判断是否是大小片，如果大小片情况下某一片固定，那么另一片也要固定，要判断是否已经固定过了
                String parentId = workOrderVO.getParentId();
                if (StrUtil.isNotEmpty(parentId) && fixedOperation) {
                    List<String> lockIds = StreamUtils.columnToList(productionIntervalInputList, ProductionIntervalInput::getProductionIntervalId);
                    List<WorkOrderVO> otherWorkOrder = childWorkOrder.get(parentId)
                            .stream().filter(p -> !p.getId().equals(id))
                            .filter(p -> !lockIds.contains(p.getId()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(otherWorkOrder)) {
                        continue;
                    }
                    getOtherFixedOperation(otherWorkOrder, workOrderVOMap, childMap,
                            taskMap, physicalResourceVOMap, standardResourceInputNewDataMap,
                            planHorizonInputDataList, firstPlanHorizonInput, lastPlanHorizonInput,
                            planStartTime, routingIds, routingStepMap, routingStepIds, productionIntervalInputList,
                            lockOrderLastDateIds, productStockPointVOMap, orderOperation, fixWorkOrderId, statusList, planLockEndTime);
                }
            }
        }
        // 补充固定的制造订单物料需求
        supplyLockOrderDemand(productionIntervalInputList, workOrderVOAll,
                demandCodeList, productStockPointVOMap, productWorkOrder, algorithmPolymerization);
        List<String> waitingDeleteOrder = workOrderVOS.stream().filter(p -> !fixWorkOrderId.contains(p.getId())).collect(Collectors.toList())
                .stream().map(WorkOrderVO::getId).collect(Collectors.toList());
        // redis记录固定不删除制造订单
        setLockWorkOrder(fixWorkOrderId, creator);
        // redis记录待删除订单
        setDeleteWorkOrder(waitingDeleteOrder, creator);
        // redis记录关联计划单待取消制造订单
        setCancelPlanWorkOrder(unLockCancelPlanWorkOrderId, creator);
        // redis记录锁定期订单不更新最早开始时间订单
        setLockWorkOrderLastDate(lockOrderLastDateIds, creator);
        Map<String, List<ResourceCalendarDO>> resourceCalendarMap = algorithmPolymerization.getResourceCalendarMap();
        resourceCalendarCalculate2(feedBackResourceMap, algorithmInput, resourceCalendarMap);
        // 合并存在交集的生产批量数据行
        productionIntervalInputList = processIntervalInput(productionIntervalInputList, productionPlannedMergeMappings, creator);
        // 去除时段的时分秒
        algorithmInput.getPlanHorizonInputDataList().forEach(data -> {
            data.setPeriodStartTime(formatDateString(data.getPeriodStartTime()));
            data.setPeriodEndTime(formatDateString(data.getPeriodEndTime()));
        });
        return productionIntervalInputList;
    }

    private void supplyLockOrderDemand(List<ProductionIntervalInput> productionIntervalInputList,
                                       List<WorkOrderVO> workOrderVOS,
                                       Set<String> demandCodeList, Map<String, NewProductStockPointVO> productStockPointVOMap,
                                       Set<String> productWorkOrder, AlgorithmPolymerization algorithmPolymerization) {
        if (CollectionUtils.isEmpty(productionIntervalInputList)) {
            return;
        }
        Set<String> supplyProductCode = new HashSet<>();
        List<String> orderIds = StreamUtils.columnToList(productionIntervalInputList, ProductionIntervalInput::getProductionIntervalId);
        // 锁定的制造订单
        List<WorkOrderVO> productLineWorkOrder = workOrderVOS.stream()
                .filter(p -> orderIds.contains(p.getId())).collect(Collectors.toList());
        // 添加锁定订单进待排需求
        productLineWorkOrder.forEach(p -> demandCodeList.add(productStockPointVOMap.get(p.getProductId()).getProductCode()));
        // 添加锁定订单进处理候选资源，仅保留排在指定产线的订单候选资源
        productLineWorkOrder.forEach(p -> productWorkOrder.add(productStockPointVOMap.get(p.getProductId()).getProductCode()));
        productLineWorkOrder.forEach(p -> supplyProductCode.add(productStockPointVOMap.get(p.getProductId()).getProductCode()));
        // 成品订单
        List<String> parentWorkOrderId = productLineWorkOrder.stream()
                .map(WorkOrderVO::getParentId)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(parentWorkOrderId)) {
            List<WorkOrderVO> parentWorkOrder = workOrderVOS.stream().filter(p -> parentWorkOrderId.contains(p.getId())).collect(Collectors.toList());
            // 添加成品锁定订单进待排需求
            parentWorkOrder.forEach(p -> demandCodeList.add(productStockPointVOMap.get(p.getProductId()).getProductCode()));
            parentWorkOrder.forEach(p -> supplyProductCode.add(productStockPointVOMap.get(p.getProductId()).getProductCode()));
        }
        algorithmPolymerization.getProcessRunMap().put("supplyProductCode", supplyProductCode);
        log.info("补充排程物料需求：{}", JSON.toJSONString(supplyProductCode));
    }

    private void getOtherFixedOperation(List<WorkOrderVO> otherWorkOrder,
                                        Map<String, WorkOrderVO> workOrderVOMap,
                                        Map<String, List<OperationVO>> childMap,
                                        Map<String, List<OperationTaskVO>> taskMap,
                                        Map<String, PhysicalResourceVO> physicalResourceVOMap,
                                        Map<String, StandardResourceInputNewData> standardResourceInputNewDataMap,
                                        List<PlanHorizonInputNewData> planHorizonInputDataList,
                                        PlanHorizonInputNewData firstPlanHorizonInput,
                                        PlanHorizonInputNewData lastPlanHorizonInput,
                                        Date planStartTime, List<String> routingIds,
                                        Map<String, List<WorkOrderRoutingStepInputNewData>> routingStepMap,
                                        List<String> routingStepIds,
                                        List<ProductionIntervalInput> productionIntervalInputList,
                                        List<String> lockOrderLastDateIds,
                                        Map<String, NewProductStockPointVO> productStockPointVOMap,
                                        Map<String, List<OperationVO>> orderOperation, List<String> fixWorkOrderId,
                                        List<String> statusList, Date planLockEndTime) {

        for (WorkOrderVO workOrderVO : otherWorkOrder) {
            String id = workOrderVO.getId();
            List<OperationVO> operationVOS = orderOperation.get(id);
            if (CollectionUtils.isEmpty(operationVOS)) {
                log.warn("other制造订单：{}，无工序信息", id);
                continue;
            }
            // 循环父工序：一个父工序对应多个子工序，子工序对应多个task工序任务，task工序任务对应多个subTask任务（设置，制造，清洗）
            List<OperationVO> operationParent = operationVOS.stream()
                    .filter(p -> StrUtil.isEmpty(p.getParentId()))
                    .sorted(Comparator.comparing(OperationVO::getRoutingStepSequenceNo)).collect(Collectors.toList());

            for (int i = 0; i < operationParent.size(); i++) {
                OperationVO operationVO = operationParent.get(i);
                String standardStepType = operationVO.getStandardStepType();
                if (StrUtil.isEmpty(standardStepType)) {
                    continue;
                }
                // 判断当前工序是否为关键成型工序
                if (!standardStepType.equals(StandardStepEnum.FORMING_PROCESS.getCode())) {
                    continue;
                }
                BigDecimal scheduleQuantity = workOrderVO.getQuantity();
                String operationId = operationVO.getId();
                Date startTime = operationVO.getStartTime();
                Date endTime = operationVO.getEndTime();
                if (null == startTime || null == endTime) {
                    log.warn("other已计划关键工序制造开始结束时间存在空值，opid：{}", operationVO.getId());
                    continue;
                }
                addWorkOrderAndParentIfNotEmpty(fixWorkOrderId, workOrderVO);
                // 报工判断
                BigDecimal feedBackQty = feedBackTime(childMap, operationId, statusList, scheduleQuantity);
                if (feedBackQty.compareTo(BigDecimal.ZERO) <= 0) {
                    log.warn("other制造订单锁定数量：{}，完工计算后数量小于等于0不设置固定：{}", scheduleQuantity, workOrderVO.getOrderNo());
                    continue;
                }
                boolean hasIntersection = hasIntersection(startTime, endTime, planStartTime, planLockEndTime);
                scheduleQuantity = feedBackQty;
                boolean lastOperation = i == operationParent.size() - 1;
                // getFixedOperation校验并获取固定的制造订单
                getFixedOperation(operationVO, workOrderVOMap, childMap,
                        taskMap, physicalResourceVOMap, standardResourceInputNewDataMap,
                        planHorizonInputDataList, workOrderVO, firstPlanHorizonInput, lastPlanHorizonInput,
                        planStartTime, routingIds, routingStepMap, routingStepIds, productionIntervalInputList,
                        lockOrderLastDateIds, scheduleQuantity, hasIntersection, productStockPointVOMap, lastOperation);
            }
        }
    }

    private Boolean getFixedOperation(OperationVO operationVO, Map<String, WorkOrderVO> workOrderVOMap,
                                      Map<String, List<OperationVO>> childMap, Map<String, List<OperationTaskVO>> taskMap,
                                      Map<String, PhysicalResourceVO> physicalResourceVOMap,
                                      Map<String, StandardResourceInputNewData> standardResourceInputNewDataMap,
                                      List<PlanHorizonInputNewData> planHorizonInputDataList,
                                      WorkOrderVO workOrderVO,
                                      PlanHorizonInputNewData firstPlanHorizonInput, PlanHorizonInputNewData lastPlanHorizonInput,
                                      Date planStartTime,
                                      List<String> routingIds,
                                      Map<String, List<WorkOrderRoutingStepInputNewData>> routingStepMap,
                                      List<String> routingStepIds, List<ProductionIntervalInput> productionIntervalInputList,
                                      List<String> lockOrderLastDateIds, BigDecimal scheduleQuantity,
                                      boolean whetherLock, Map<String, NewProductStockPointVO> productStockPointVOMap,
                                      boolean lastOperation) {
        // 校验工序
        Boolean checkOperation = checkOperation(operationVO, workOrderVOMap, childMap,
                taskMap, physicalResourceVOMap);
        if (!checkOperation) {
            return false;
        }
        String planStatus = operationVO.getPlanStatus();
        if (planStatus.equals(PlannedStatusEnum.FINISHED.getCode())) {
            log.info("制造订单：{}，关键工序已完工，不进MPS", operationVO.getOrderId());
            return false;
        }
        String operationId = operationVO.getId();
        String physicalResourceId = taskMap.get(childMap.get(operationId).get(0).getId()).stream()
                .map(OperationTaskVO::getPhysicalResourceId)
                .filter(physicalResourceVOMap::containsKey)
                .findFirst()
                .orElse(null);
        // 给MPS的主资源，也就是物理资源，需要扣减对应的日历的可用工时
        StandardResourceInputNewData standardResourceInputNewData = standardResourceInputNewDataMap.get(physicalResourceId);
        if (null == standardResourceInputNewData) {
            return false;
        }
        Date startTime = operationVO.getStartTime();
        Date endTime = operationVO.getEndTime();
        Optional<PlanHorizonInputNewData> startPeriod = planHorizonInputDataList.stream()
                .filter(p -> DateUtils.stringToDate(p.getPeriodStartTime(), DateUtils.COMMON_DATE_STR1).getTime() <= startTime.getTime())
                .sorted(Comparator.comparing(PlanHorizonInputNewData::getPeriodStartTime).reversed())
                .findFirst();
        Optional<PlanHorizonInputNewData> endPeriod = planHorizonInputDataList.stream()
                .filter(p -> DateUtils.stringToDate(p.getPeriodStartTime(), DateUtils.COMMON_DATE_STR1).getTime() <= endTime.getTime())
                .sorted(Comparator.comparing(PlanHorizonInputNewData::getPeriodStartTime).reversed())
                .findFirst();

        if (!startPeriod.isPresent() && !endPeriod.isPresent()) {
            log.warn("制造订单：{}，没有匹配到对应的计划时段信息，无法固定", workOrderVO.getId());
            return false;
        }
        String startPeriodSequence = startPeriod.orElse(firstPlanHorizonInput).getPeriodSequence().toString();
        String endPeriodSequence = endPeriod.orElse(lastPlanHorizonInput).getPeriodSequence().toString();
        PhysicalResourceVO physicalResourceVO = physicalResourceVOMap.get(physicalResourceId);
        // 工序的开始时间 > (计划期间开始时间 + 该资源的资源锁定时间)，则更新固定时间为计划期间开始周期
        Integer noBufferActionDuration = physicalResourceVO.getNoBufferActionDuration();
        if (null != noBufferActionDuration) {
            Date lockResourceDate = DateUtil.offsetDay(planStartTime, noBufferActionDuration);
            if (startTime.getTime() > lockResourceDate.getTime()) {
                startPeriodSequence = firstPlanHorizonInput.getPeriodSequence().toString();
                endPeriodSequence = firstPlanHorizonInput.getPeriodSequence().toString();
                log.info("工序：{}，开始时间 > 计划期间+资源下发周期时间，重置时时段", operationVO.getId());
            }
        }
        // 锁定物品id
        String productId = workOrderVO.getProductId();
        String lockProductId = getLockProductId(lastOperation, productStockPointVOMap, productId, operationVO);
        if (StrUtil.isEmpty(lockProductId)) {
            return false;
        }
        ProductionIntervalInput productionIntervalInput = new ProductionIntervalInput();
        productionIntervalInput.setProductStockPointId(lockProductId);
        productionIntervalInput.setFixed(YesOrNoEnum.YES.getCode());
        productionIntervalInput.setProductionIntervalId(workOrderVO.getId());
        productionIntervalInput.setQty(scheduleQuantity);
        productionIntervalInput.setStandardResourceId(standardResourceInputNewData.getStandardResourceCode());
        productionIntervalInput.setStartPeriod(startPeriodSequence);
        productionIntervalInput.setEndPeriod(endPeriodSequence);
        productionIntervalInput.setStart(DateUtil.beginOfDay(startTime));
        productionIntervalInput.setEnd(endTime);
        productionIntervalInputList.add(productionIntervalInput);
        String routingId = workOrderVO.getRoutingId();
        routingIds.add(routingId);
        List<WorkOrderRoutingStepInputNewData> workOrderRoutingStepInputNewData = routingStepMap.get(routingId);
        if (CollectionUtils.isNotEmpty(workOrderRoutingStepInputNewData)) {
            List<String> ids = StreamUtils.columnToList(workOrderRoutingStepInputNewData, WorkOrderRoutingStepInputNewData::getRoutingStepId);
            routingStepIds.addAll(ids);
        }
        if (whetherLock) {
            // 锁定期订单不更新最早开始时间订单
            lockOrderLastDateIds.add(workOrderVO.getId());
        }
        return true;
    }

    private String formatDateString(String dateStr) {
        return DateUtils.dateToString(
                DateUtils.stringToDate(dateStr, DateUtils.COMMON_DATE_STR3),
                DateUtils.COMMON_DATE_STR3
        );
    }

    private List<WorkOrderVO> getProductLineWorkOrder(List<WorkOrderVO> workOrderVOS,
                                                      List<String> productLineList) {
        List<WorkOrderVO> workOrderAll = new ArrayList<>();
        List<String> allOrderIds = StreamUtils.columnToList(workOrderVOS, WorkOrderVO::getId);
        List<String> unPlanOrderIds = operationTaskExtDao.selectKeyStepUnPlanOrderIds(allOrderIds);
        // 未计划制造订单ids
        if (CollectionUtils.isNotEmpty(unPlanOrderIds)) {
            List<WorkOrderVO> unPlanWorkOrder = workOrderVOS.stream().filter(
                    p -> unPlanOrderIds.contains(p.getId())).collect(Collectors.toList());
            workOrderAll.addAll(unPlanWorkOrder);
            log.info("未计划制造订单：{}", unPlanOrderIds);
        }
        // 已计划制造订单
        List<String> plannedWorkOrderIds = workOrderVOS.stream()
                .filter(p -> !p.getPlanStatus().equals(PlannedStatusEnum.UNPLAN.getCode()))
                .map(WorkOrderVO::getId).collect(Collectors.toList());
        // 获取排在指定产线的制造订单集合（链式订单，半品制造订单）
        List<String> productLineWorkOrderIds = operationTaskExtDao.selectPlannedOrderResource(productLineList, plannedWorkOrderIds);
        if (CollectionUtils.isNotEmpty(productLineWorkOrderIds)) {
            List<WorkOrderVO> productLineWorkOrder = workOrderVOS.stream()
                    .filter(p -> productLineWorkOrderIds.contains(p.getId())).collect(Collectors.toList());
            // 成品订单
            List<String> parentWorkOrderId = productLineWorkOrder.stream()
                    .map(WorkOrderVO::getParentId)
                    .filter(StrUtil::isNotEmpty)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(parentWorkOrderId)) {
                List<WorkOrderVO> parentWorkOrder = workOrderVOS.stream().filter(p -> parentWorkOrderId.contains(p.getId())).collect(Collectors.toList());
                // 父制造订单
                workOrderAll.addAll(parentWorkOrder);
            }
            workOrderAll.addAll(productLineWorkOrder);
        }
        return workOrderAll;
    }

    private void setUnPlanScheduleOrder(List<String> lockUnPlanScheduleWorkOrderId, String creator) {
        lockUnPlanScheduleWorkOrderId = lockUnPlanScheduleWorkOrderId.stream().distinct().collect(Collectors.toList());
        log.info("锁定期内取消前后非关键工序重排订单：{}", JSON.toJSONString(lockUnPlanScheduleWorkOrderId));
        if (CollectionUtils.isEmpty(lockUnPlanScheduleWorkOrderId)) {
            return;
        }
        String key = RedisKeyManageEnum.MPS_UN_PLAN_SCHEDULE_WORK_ORDER.getKey().replace("{userId}", creator);
        redisUtil.delete(key);
        if (CollectionUtils.isNotEmpty(lockUnPlanScheduleWorkOrderId)) {
            redisUtil.lSet(key, lockUnPlanScheduleWorkOrderId);
        }
    }

    private void resourceCalendarCalculate2(Map<String, List<FeedbackProductionPO>> feedBackResourceMap,
                                            MdsAlgorithmInputNew algorithmInput, Map<String, List<ResourceCalendarDO>> resourceCalendarMap) {
        Map<String, FeedbackProductionPO> lastFeedBackResourceMap = new HashMap<>();
        // 资源可用工时对应的资源日历
        for (Map.Entry<String, List<FeedbackProductionPO>> entry : feedBackResourceMap.entrySet()) {
            String key = entry.getKey();
            List<FeedbackProductionPO> value = entry.getValue();
            value.sort(Comparator.comparing(FeedbackProductionPO::getReportingTime).reversed());
            FeedbackProductionPO lastFeedBackTime = value.get(0);
            lastFeedBackResourceMap.put(key, lastFeedBackTime);
        }
        Iterator<CalendarInputNewData> iterator = algorithmInput.getCalendarInputDataList().iterator();
        while (iterator.hasNext()) {
            CalendarInputNewData calendarInputNewData = iterator.next();
            String resourceId = calendarInputNewData.getStandardResourceId();
            if (!lastFeedBackResourceMap.containsKey(resourceId)) {
                continue;
            }
            FeedbackProductionPO feedbackProductionPO = lastFeedBackResourceMap.get(resourceId);
            Date reportingTime = feedbackProductionPO.getReportingTime();
            BigDecimal generalCapacity = calendarInputNewData.getGeneralCapacity();
            String key = StrUtil.join(STR_JOIN_VALUE, resourceId, calendarInputNewData.getStartTime());
            List<ResourceCalendarDO> resourceCalendarDOS = resourceCalendarMap.get(key);
            if (CollectionUtils.isEmpty(resourceCalendarDOS)) {
                log.warn("MPS计算工时，key：{}，没有找到资源日历", key);
                continue;
            }
            List<ResourceCalendarDO> calendarDOList = resourceCalendarDOS.stream()
                    .filter(p -> p.getEndTime().getTime() > reportingTime.getTime()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(calendarDOList)) {
                // 判断是否存在跨资源日历报工时间
                for (ResourceCalendarDO resourceCalendarDO : resourceCalendarDOS) {
                    Date startTime = resourceCalendarDO.getStartTime();
                    Date endTime = resourceCalendarDO.getEndTime();
                    if (startTime.getTime() > reportingTime.getTime()) {
                        continue;
                    }
                    // 报工时间之前的资源日历全部扣减，横跨的部分做切割
                    if (endTime.getTime() < reportingTime.getTime()) {
                        generalCapacity = BigDecimalUtils.subtract(generalCapacity, resourceCalendarDO.getWorkHours());
                    }
                    if (startTime.getTime() < reportingTime.getTime() && endTime.getTime() > reportingTime.getTime()) {
                        // 计算横跨的部分，开始时段-报工时间之间的工时，扣减总产能
                        BigDecimal hour = getHour(startTime, reportingTime);
                        generalCapacity = BigDecimalUtils.subtract(generalCapacity, hour, 4);
                    }
                }
                if (generalCapacity.compareTo(BigDecimal.ZERO) > 0) {
                    calendarInputNewData.setGeneralCapacity(generalCapacity);
                } else {
                    iterator.remove();
                }
            } else {
                iterator.remove();
            }
        }
    }

    private BigDecimal getHour(Date startTime, Date endTime) {
        long hoursBetween = DateUtil.between(startTime, endTime, DateUnit.MINUTE);
        return BigDecimalUtils.divide(BigDecimalUtils.toBigDecimal(hoursBetween), BigDecimalUtils.toBigDecimal(60), 2);
    }

    protected List<WorkOrderVO> getSupplementsOrder(String creator, AlgorithmLog algorithmLog) {
        // 全量的制造订单，筛选出锁定期内外的制造订单，根据订单计划员负责的物料筛选
        List<String> productIds = getPermissionProductIds(algorithmLog);
        // 全量的制造订单
        List<WorkOrderVO> workOrderAll = workOrderService.selectAll();
        // 根据订单计划员负责的物料筛选出订单，仅筛选父制造订单
        List<WorkOrderVO> workOrderVOS = workOrderAll.stream()
                .filter(p -> StrUtil.isEmpty(p.getParentId()) && productIds.contains(p.getProductId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderVOS)) {
            return new ArrayList<>();
        }
        // 成品制造订单ids集合
        List<String> workOrderIds = StreamUtils.columnToList(workOrderVOS, WorkOrderVO::getId);
        // 父制造订单下所有的子订单
        List<WorkOrderVO> childWorkOrder = workOrderAll.stream().filter(p ->
                StrUtil.isNotEmpty(p.getParentId()) && workOrderIds.contains(p.getParentId())).collect(Collectors.toList());
        workOrderVOS.addAll(childWorkOrder);
        return workOrderVOS;
    }

    private BigDecimal feedBackTime(Map<String, List<OperationVO>> childMap,
                                    String operationId,
                                    List<String> statusList,
                                    BigDecimal scheduleQuantity) {
        /*
         * 子工序报工
         *
         * 和锁定期有交集，进入MPS设置固定，子工序，存在多个（拆批，已完工，已开始，已计划）
         * 资源1：日期2月27日，
         * 工序1，数量100，已完工；
         * 工序2，数量150，生产中，已完工50；
         * 工序3：数量50，已计划
         * 则输入给优化算法的工序是：
         * 日期2月27日，
         * 工序2，数量100；
         * 工序3，数量50
         * 资源1,2月27日产能：24小时 ➖ 工序1的生产时间 ➖ 工序2已完工的50的生产时间
         */
        List<OperationVO> childOperation = childMap.get(operationId);
        for (OperationVO child : childOperation) {
            String childPlanStatus = child.getPlanStatus();
            if (!statusList.contains(childPlanStatus)) {
                continue;
            }
            BigDecimal quantity = child.getQuantity();
            // 锁定数量 = 锁定数量 - 已完工数量
            scheduleQuantity = scheduleQuantity.subtract(quantity);
        }
        return scheduleQuantity;
    }


    private String getLockProductId(boolean lastOperation,
                                    Map<String, NewProductStockPointVO> productStockPointVOMap,
                                    String productId, OperationVO operationVO) {
        if (lastOperation) {
            // 最后一道工序为关键工序则构建成品id = 成品代码 + 库存点code
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(productId);
            if (Objects.isNull(newProductStockPointVO)) {
                log.warn("固定制造订单找不到物品：{}", productId);
                return null;
            }
            return newProductStockPointVO.getProductCode() + "*" + newProductStockPointVO.getStockPointCode();
        } else {
            // 非最后一道工序构建虚拟物品id = virtual& + 顺序号+ &成品id
            // String productId = "virtual&" + routingStepVO.getSequenceNumber() + "&" + routingProductId;
            Integer routingStepSequenceNo = operationVO.getRoutingStepSequenceNo();
            return "virtual&" + routingStepSequenceNo + "&" + productId + "*intermediateDummy";
        }
    }

    private Boolean checkOperation(OperationVO operationVO,
                                   Map<String, WorkOrderVO> workOrderVOMap,
                                   Map<String, List<OperationVO>> childMap,
                                   Map<String, List<OperationTaskVO>> taskMap,
                                   Map<String, PhysicalResourceVO> physicalResourceVOMap) {
        String operationId = operationVO.getId();
        String orderId = operationVO.getOrderId();
        if (!workOrderVOMap.containsKey(orderId)) {
            return false;
        }
        if (!childMap.containsKey(operationId)) {
            // 工序未计划，未计划的工序制造订单
            return false;
        }
        // 关键工序得工序任务，会有多个资源（AMS排程存在虚拟物理资源）决策出成型资源
        List<OperationTaskVO> childTask = taskMap.get(childMap.get(operationId).get(0).getId());
        if (CollectionUtils.isEmpty(childTask)) {
            return false;
        }
        String physicalResourceId = childTask.stream()
                .map(OperationTaskVO::getPhysicalResourceId)
                .filter(physicalResourceVOMap::containsKey)
                .findFirst()
                .orElse(null);
        return null != physicalResourceId;
    }

    /**
     * 锁定不删除制造订单
     */
    private void lockPlanWorkOrder(List<WorkOrderVO> workOrderVOS,
                                   List<String> statusList,
                                   List<String> fixWorkOrderId,
                                   Map<String, List<MasterPlanRelationVO>> planRelationMap,
                                   Map<String, List<WorkOrderVO>> childWorkOrder) {
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            String id = workOrderVO.getId();
            String planStatus = workOrderVO.getPlanStatus();
            String fixed = Optional.ofNullable(workOrderVO.getFixed()).orElse(YesOrNoEnum.NO.getCode());
            if (statusList.contains(planStatus) || YesOrNoEnum.YES.getCode().equals(fixed)) {
                // 已完工及已开始的订单不能删除
                fixWorkOrderId.add(workOrderVO.getId());
            }
            if (planRelationMap.containsKey(workOrderVO.getOrderNo()) || YesOrNoEnum.YES.getCode().equals(fixed)) {
                String parentId = workOrderVO.getId();
                // 计划单多级BOM情况下关联顶层制造订单，如果关联顶层制造订单则半品制造订单也需要固定不删除
                List<WorkOrderVO> semiWorkOrder = childWorkOrder.get(parentId);
                if (CollectionUtils.isNotEmpty(semiWorkOrder)) {
                    semiWorkOrder.forEach(t -> addWorkOrderAndParentIfNotEmpty(fixWorkOrderId, t));
                    log.info("成品制造订单关联计划单子订单：{}", id);
                }
                addWorkOrderAndParentIfNotEmpty(fixWorkOrderId, workOrderVO);
                log.info("制造订单：{}，存在关联计划单进入MPS资源占位，不删除", workOrderVO.getOrderNo());
            }
        }
    }

    private void setCancelPlanWorkOrder(List<String> unLockCancelPlanWorkOrderId, String creator) {
        unLockCancelPlanWorkOrderId = unLockCancelPlanWorkOrderId.stream().distinct().collect(Collectors.toList());
        log.info("计划单关联锁定期外取消订单：{}", JSON.toJSONString(unLockCancelPlanWorkOrderId));
        if (CollectionUtils.isEmpty(unLockCancelPlanWorkOrderId)) {
            return;
        }
        // 此处记录的订单算法解析可能会出现锁等待，by物料筛选的订单
        String key = RedisKeyManageEnum.MPS_ALGORITHM_UN_PLAN_WORK_ORDER.getKey().replace("{userId}", creator);
        redisUtil.delete(key);
        if (CollectionUtils.isNotEmpty(unLockCancelPlanWorkOrderId)) {
            redisUtil.lSet(key, unLockCancelPlanWorkOrderId);
        }
    }

    private void setLockWorkOrderLastDate(List<String> lockOrderLastDateIds, String creator) {
        lockOrderLastDateIds = lockOrderLastDateIds.stream().distinct().collect(Collectors.toList());
        log.info("锁定期订单不更新最早开始时间：{}", lockOrderLastDateIds.size());
        if (CollectionUtils.isEmpty(lockOrderLastDateIds)) {
            return;
        }
        String key = RedisKeyManageEnum.MPS_ALGORITHM_LOCK_WORK_ORDER.getKey().replace("{userId}", creator);
        redisUtil.delete(key);
        if (CollectionUtils.isNotEmpty(lockOrderLastDateIds)) {
            redisUtil.lSet(key, lockOrderLastDateIds);
        }
    }

    private void resourceCalendarCalculate(List<OperationVO> caculateTimeList, MdsAlgorithmInputNew algorithmInput) {
        if (CollectionUtils.isEmpty(caculateTimeList)) {
            return;
        }
        log.info("需要计算可用工时的工序：{}", JSON.toJSONString(caculateTimeList));
        List<CalendarInputNewData> calendarInputDataList = algorithmInput.getCalendarInputDataList();
        for (OperationVO operationVO : caculateTimeList) {
            String plannedResourceId = operationVO.getPlannedResourceId();
            Date productionStartTime = operationVO.getStartTime();
            Date productionEndTime = operationVO.getEndTime();
            if (null == productionStartTime || null == productionEndTime) {
                log.error("工序id：{}，时间为空，开始：{}，结束：{}", operationVO.getId(), productionStartTime, productionEndTime);
                continue;
            }
            // 判断是否跨天
            boolean crossDay = isCrossDay(productionStartTime, productionEndTime);
            if (!crossDay) {
                BigDecimal timeHour = getTimeHour(productionStartTime, productionEndTime);
                subtractCapacity(calendarInputDataList, plannedResourceId, productionStartTime, timeHour);
            } else {
                // 拆分跨天的时间段
                List<DateRange> dateRanges = splitCrossDayRange(productionStartTime, productionEndTime);
                for (DateRange dateRange : dateRanges) {
                    BigDecimal timeHour = getTimeHour(dateRange.getStartDate(), dateRange.getEndDate());
                    subtractCapacity(calendarInputDataList, plannedResourceId, dateRange.getStartDate(), timeHour);
                }
            }
        }
    }

    private void subtractCapacity(List<CalendarInputNewData> calendarInputDataList, String plannedResourceId, Date startTime, BigDecimal timeHour) {
        if (timeHour.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        String timeStr = DateUtils.dateToString(startTime, DateUtils.COMMON_DATE_STR3);
        for (CalendarInputNewData calendarInputNewData : calendarInputDataList) {
            BigDecimal generalCapacity = calendarInputNewData.getGeneralCapacity();
            String standardResourceId = calendarInputNewData.getStandardResourceId();
            String calendarStartTime = calendarInputNewData.getStartTime();
            if (!plannedResourceId.equals(standardResourceId)) {
                continue;
            }
            if (!timeStr.equals(calendarStartTime)) {
                continue;
            }
            BigDecimal subtract = BigDecimalUtils.subtract(generalCapacity, timeHour);
            if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                calendarInputNewData.setGeneralCapacity(subtract);
            }
            break;
        }
    }

    private List<DateRange> splitCrossDayRange(Date startDate, Date endDate) {
        List<DateRange> dateRanges = new ArrayList<>();
        LocalDate currentStartDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate currentEndDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        boolean whetherFirstTime = true;
        while (currentStartDate.isBefore(currentEndDate)) {
            Date startOfDay;
            Date endOfDay;

            if (whetherFirstTime) {
                // 使用原始的 startDate 作为第一天的开始时间
                startOfDay = startDate;
                endOfDay = DateUtil.endOfDay(startDate);
                whetherFirstTime = false;
            } else {
                // 使用 beginOfDay 和 endOfDay 处理后续的天数
                startOfDay = DateUtil.beginOfDay(Date.from(currentStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
                endOfDay = DateUtil.endOfDay(Date.from(currentStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            }

            dateRanges.add(new DateRange(startOfDay, endOfDay));
            currentStartDate = currentStartDate.plusDays(1);
        }

        // 添加最后一天的时间段
        dateRanges.add(new DateRange(DateUtil.beginOfDay(endDate), endDate));
        return dateRanges;
    }

    private BigDecimal getTimeHour(Date productionStartTime, Date productionEndTime) {
        // 计算时间差（毫秒）
        long timeDifferenceMillis = productionEndTime.getTime() - productionStartTime.getTime();
        // 将毫秒转换为秒
        double timeDifferenceSeconds = timeDifferenceMillis / 1000.0;
        // 将秒转换为小时，并保留两位小数
        return BigDecimal.valueOf(timeDifferenceSeconds / 3600.0).setScale(3, RoundingMode.HALF_UP);
    }

    public boolean isCrossDay(Date productionStartTime, Date productionEndTime) {
        // 将 Date 转换为 LocalDate
        LocalDate startDate = productionStartTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDate = productionEndTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        // 比较两个 LocalDate 是否相等
        return !startDate.equals(endDate);
    }

    private void addWorkOrderAndParentIfNotEmpty(List<String> fixWorkOrderId, WorkOrderVO workOrderVO) {
        fixWorkOrderId.add(workOrderVO.getId());
        if (StrUtil.isNotEmpty(workOrderVO.getParentId())) {
            fixWorkOrderId.add(workOrderVO.getParentId());
        }
    }

    /**
     * 判断两个时间段是否存在交集
     *
     * @param operationStart 工序开始的开始时间
     * @param operationEnd   工序结束的结束时间
     * @param planStart      计划的开始时间
     * @param planLockEnd    计划冻结的结束时间
     * @return 如果存在交集返回true，否则返回false
     */
    public boolean hasIntersection(Date operationStart, Date operationEnd, Date planStart, Date planLockEnd) {
        // 如果一个时间段的开始时间在另一个时间段的结束时间之后，或者结束时间在另一个时间段的开始时间之前，则没有交集
        return !(operationStart.after(planLockEnd) || operationEnd.before(planStart));
    }

    private List<ProductionIntervalInput> processIntervalInput(List<ProductionIntervalInput> productionIntervalInputList,
                                                               List<ProductionPlannedMergeMapping> productionPlannedMergeMappings,
                                                               String creator) {
        if (CollectionUtils.isEmpty(productionIntervalInputList)) {
            return new ArrayList<>();
        }
        log.info("生产批量数据行：{}", productionIntervalInputList.size());
        List<ProductionIntervalInput> productionIntervals = new ArrayList<>();
        Map<String, List<ProductionIntervalInput>> productIntervalMap = StreamUtils.mapListByColumn(productionIntervalInputList, ProductionIntervalInput::getProductStockPointId);
        for (Map.Entry<String, List<ProductionIntervalInput>> entry : productIntervalMap.entrySet()) {
            List<ProductionIntervalInput> value = entry.getValue()
                    .stream().sorted(Comparator.comparing(ProductionIntervalInput::getStart)).collect(Collectors.toList());
            List<ProductionIntervalInput> intersections = new ArrayList<>();
            for (ProductionIntervalInput interval1 : value) {
                if (CollectionUtils.isEmpty(intersections)) {
                    intersections.add(interval1);
                    mergeInterval(interval1, interval1, productionPlannedMergeMappings);
                    continue;
                }
                ProductionIntervalInput temp = intersections.get(intersections.size() - 1);
                boolean hasIntersection = hasIntersection(interval1, temp);
                if (hasIntersection) {
                    BigDecimal temQty = temp.getQty();
                    BigDecimal qty = interval1.getQty();
                    BigDecimal sumQty = BigDecimalUtils.add(temQty, qty);
                    temp.setQty(sumQty);
                    mergeInterval(temp, interval1, productionPlannedMergeMappings);
                } else {
                    intersections.add(interval1);
                    mergeInterval(interval1, interval1, productionPlannedMergeMappings);
                }
            }
            // 处理合并后的数据
            productionIntervals.addAll(intersections);
        }
        log.info("汇总批量数据行：{}", productionIntervals.size());
        return productionIntervals;
    }

    private void mergeInterval(ProductionIntervalInput target,
                               ProductionIntervalInput source,
                               List<ProductionPlannedMergeMapping> productionPlannedMergeMappings) {
        ProductionPlannedMergeMapping productionPlannedMergeMapping = new ProductionPlannedMergeMapping();
        productionPlannedMergeMapping.setWorkOrderId(target.getProductionIntervalId());
        productionPlannedMergeMapping.setMergeWorkOrderId(source.getProductionIntervalId());
        productionPlannedMergeMapping.setMergeStartPeriod(source.getStartPeriod());
        productionPlannedMergeMapping.setMergeEndPeriod(source.getEndPeriod());
        productionPlannedMergeMapping.setMergeQty(source.getQty());
        productionPlannedMergeMappings.add(productionPlannedMergeMapping);
    }

    private void setLockWorkOrder(List<String> fixWorkOrderId, String creator) {
        if (CollectionUtils.isEmpty(fixWorkOrderId)) {
            log.warn("固定制造订单为空");
            return;
        }
        fixWorkOrderId = fixWorkOrderId.stream().distinct().collect(Collectors.toList());
        log.info("固定制造订单：{}", JSON.toJSONString(fixWorkOrderId));
        String key = RedisKeyManageEnum.MPS_ALGORITHM_FIX_WORK_ORDER.getKey().replace("{userId}", creator);
        redisUtil.delete(key);
        redisUtil.lSet(key, fixWorkOrderId);
    }

    private void setDeleteWorkOrder(List<String> waitingDeleteWorkOrder, String creator) {
        String key = RedisKeyManageEnum.MPS_ALGORITHM_DELETE_WORK_ORDER.getKey().replace("{userId}", creator);
        log.info("MPS运行待删除的制造订单：{}，key：{}", waitingDeleteWorkOrder.size(), key);
        redisUtil.delete(key);
        if (CollectionUtils.isNotEmpty(waitingDeleteWorkOrder)) {
            redisUtil.lSet(key, waitingDeleteWorkOrder);
        }
    }

    public List<CustomerOrderInputNewData> algorithmInputData(List<WorkOrderRoutingInputNewData> workOrderRoutingInputDataList,
                                                              List<WorkOrderRoutingStepInputNewData> workOrderRoutingStepInputDataList,
                                                              List<WorkOrderRoutingStepInputInputNewData> workOrderRoutingStepInputInputDataList,
                                                              List<String> routingIdList,
                                                              List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS,
                                                              List<String> routingStepIds,
                                                              List<NewProductStockPointVO> newProductStockPointVOS,
                                                              List<StandardStepVO> standardStepVOS, Map<String, String> productDayMap,
                                                              MdsAlgorithmInputNew mdsAlgorithmInput, List<String> semiProductIds,
                                                              Map<String, String> originalTimeMap,
                                                              List<StockSupplyInputNewData> stockSupplyInputNewData,
                                                              AlgorithmPolymerization algorithmPolymerization,
                                                              List<InventoryBatchDetailVO> inventoryBatchDetailVOS,
                                                              List<String> productLineList,
                                                              Set<String> demandCodeList,
                                                              Set<String> removeResourceIdList,
                                                              List<FinishedHalfProductMapping> finishedHalfProductMappingList) {
        // 基础数据
        List<String> productCodes = deliveryPlanPublishedVOS.stream().map(DeliveryPlanPublishedVO::getProductCode).distinct().collect(Collectors.toList());
        List<RoutingStepVO> routingStepVOS = algorithmPolymerization.getRoutingStepVOS();
        Map<String, List<RoutingStepVO>> stepListMap = StreamUtils.mapListByColumn(routingStepVOS, RoutingStepVO::getRoutingId);
        List<SafetyStockLevelVO> safetyStockLevelVOS = dfpFeign.selectSafetyStockLevelByProductCodeList(null, productCodes);
        List<RoutingVO> routingVOS = algorithmPolymerization.getRoutingVOS();
        List<WorkOrderRoutingStepOutputInputNewData> workOrderRoutingStepOutputInputDataList = mdsAlgorithmInput.getWorkOrderRoutingStepOutputInputDataList();
        List<WorkOrderRoutingStepResourceInputNewData> workOrderRoutingStepResourceInputDataList = mdsAlgorithmInput.getWorkOrderRoutingStepResourceInputDataList();
        // 半品库存：取工厂的库存（组织类型为PRODUCT_ORGANIZATION）、本厂（stock_point_type为BC），注意：包装工序不要算到成品库里
        List<NewStockPointVO> newStockPointVOS = algorithmPolymerization.getNewStockPointVOS();
        Map<String, String> stockPointOrgTypeMap =
                newStockPointVOS.stream().filter(x -> StringUtils.isNotEmpty(x.getOrganizeType())).collect(Collectors
                        .toMap(NewStockPointVO::getStockPointCode, NewStockPointVO::getOrganizeType, (t1, t2) -> t1));

        List<String> bcStockPointList = newStockPointVOS
                .stream().filter(p -> StrUtil.isNotEmpty(p.getOrganizeType()))
                .filter(p -> StrUtil.isNotEmpty(p.getStockPointType()) && p.getStockPointType().equals(StockPointTypeEnum.BC.getCode()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        List<MdsProductStockPointBaseVO> stockPointBaseVOS = newMdsFeign.selectProductStockPointBaseByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodeList", productCodes));
        // 查询库存，不包含失效时间
        if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS) && CollectionUtils.isNotEmpty(bcStockPointList)) {
            // 过滤出本厂库存
            inventoryBatchDetailVOS = inventoryBatchDetailVOS.stream().filter(p -> bcStockPointList.contains(p.getStockPointCode())).collect(Collectors.toList());
        }
        List<String> saleOrganizationStockCode = getSaleOrganization(algorithmPolymerization);
        // 销售组织物料
        Map<String, NewProductStockPointVO> saleOrganizationProductMap = algorithmPolymerization.getNewProductStockPointVOS()
                .stream().filter(p -> saleOrganizationStockCode.contains(p.getStockPointCode()))
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, v -> v, (k1, k2) -> k1));
        // 固化时间
        Map<String, MdsCuringTimeVO> curingTimeVOMap = algorithmPolymerization.getMdsCuringTimeVOMap();
        Map<String, MdsFinishedProductDeliveryVO> finishedProductDeliveryVOMap = algorithmPolymerization.getMdsFinishedProductDeliveryVOS()
                .stream().filter(p -> saleOrganizationStockCode.contains(p.getStockPointCode()))
                .collect(Collectors.toMap(MdsFinishedProductDeliveryVO::getProductCode, v -> v, (k1, k2) -> k1));
        // 分组数据
        Map<String, NewProductStockPointVO> productStockPointVOMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getProductCode()), v -> v, (k1, k2) -> k1));
        Map<String, NewProductStockPointVO> productStockPointIdVOMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getId, v -> v, (k1, k2) -> k1));
        Map<String, RoutingVO> routingVOMap = routingVOS.stream()
                .collect(Collectors.toMap(p -> StrUtil.join(STR_JOIN_VALUE, p.getStockPointId(), p.getProductCode()), v -> v, (k1, k2) -> k1));
        Map<String, WorkOrderRoutingInputNewData> workOrderRoutingInputNewDataMap = workOrderRoutingInputDataList.stream()
                .collect(Collectors.toMap(WorkOrderRoutingInputNewData::getProductStockPointId, v -> v, (k1, k2) -> k1));
        Map<String, List<WorkOrderRoutingStepInputNewData>> routingStepMap = workOrderRoutingStepInputDataList.stream()
                .collect(Collectors.groupingBy(WorkOrderRoutingStepInputNewData::getRoutingId));
        Map<String, List<WorkOrderRoutingStepInputInputNewData>> routingStepInputMap = workOrderRoutingStepInputInputDataList.stream()
                .collect(Collectors.groupingBy(WorkOrderRoutingStepInputInputNewData::getRoutingStepId));
        Map<String, RoutingStepVO> routingStepVOMap = routingStepVOS.stream()
                .collect(Collectors.toMap(RoutingStepVO::getId, v -> v, (k1, k2) -> k1));
        Map<String, List<SafetyStockLevelVO>> safetyStockLecelMap = safetyStockLevelVOS.stream()
                .filter(p -> saleOrganizationStockCode.contains(p.getStockCode()))
                .collect(Collectors.groupingBy(SafetyStockLevelVO::getProductCode));
        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap = deliveryPlanPublishedVOS.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        Map<String, StandardStepVO> standardStepVOMap = standardStepVOS.stream().collect(Collectors
                .toMap(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getStandardStepCode()), v -> v, (k1, k2) -> k1));
        Map<String, List<WorkOrderRoutingStepOutputInputNewData>> routingStepOutPutMap = workOrderRoutingStepOutputInputDataList.stream()
                .collect(Collectors.groupingBy(WorkOrderRoutingStepOutputInputNewData::getRoutingStepId));
        Map<String, SubInventoryCargoLocationVO> cargoLocationVOMap = getSubInventoryCargoLocation(inventoryBatchDetailVOS);
        Map<String, List<WorkOrderRoutingStepResourceInputNewData>> routingResourceMap = StreamUtils.mapListByColumn(
                workOrderRoutingStepResourceInputDataList, WorkOrderRoutingStepResourceInputNewData::getRoutingStepId);
        Map<String, StandardStepVO> standardStepVOMapOnId = StreamUtils.mapByColumn(standardStepVOS, StandardStepVO::getId);
        // 库存分组
        Predicate<InventoryBatchDetailVO> isEnabled = p -> {
            String freightSpace = p.getFreightSpace();
            SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationVOMap.get(freightSpace);
            return subInventoryCargoLocationVO != null && subInventoryCargoLocationVO.getEnabled().equals(YesOrNoEnum.YES.getCode());
        };
        String subInventory = getRangeData();
        Map<String, BigDecimal> semiStockMap = inventoryBatchDetailVOS.stream()
                .filter(isEnabled)
                .filter(p -> CharSequenceUtil.isNotEmpty(p.getOperationCode()))
                .filter(p -> stockPointOrgTypeMap.containsKey(p.getStockPointCode())
                        && stockPointOrgTypeMap.get(p.getStockPointCode())
                        .equals(StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode()))
                .collect(Collectors.groupingBy(
                        p -> CharSequenceUtil.join("&", p.getProductCode(), p.getOperationCode()),
                        Collectors.mapping(
                                p -> new BigDecimal(p.getCurrentQuantity()),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));
        // 成品库存：销售组织+子库存为CPSJ的
        Map<String, BigDecimal> finishStockMap = inventoryBatchDetailVOS.stream()
                .filter(isEnabled)
                .filter(p -> CharSequenceUtil.isEmpty(p.getOperationCode()))
                .filter(p -> stockPointOrgTypeMap.containsKey(p.getStockPointCode())
                        && stockPointOrgTypeMap.get(p.getStockPointCode())
                        .equals(StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode())
                        && subInventory.equals(p.getSubinventory()))
                .collect(Collectors.groupingBy(
                        InventoryBatchDetailVO::getProductCode,
                        Collectors.mapping(
                                p -> new BigDecimal(p.getCurrentQuantity()),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));
        // 成品库存：生产组织
        Map<String, BigDecimal> productionFinishStockMap = inventoryBatchDetailVOS.stream()
                .filter(isEnabled)
                .filter(p -> CharSequenceUtil.isEmpty(p.getOperationCode()))
                .filter(p -> stockPointOrgTypeMap.containsKey(p.getStockPointCode())
                        && stockPointOrgTypeMap.get(p.getStockPointCode())
                        .equals(StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode()))
                .collect(Collectors.groupingBy(
                        InventoryBatchDetailVO::getProductCode,
                        Collectors.mapping(
                                p -> new BigDecimal(p.getCurrentQuantity()),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));
        List<ProductionLeadTimeVO> productionLeadTime = productionLeadTimeService.selectByParams(new HashMap<>());
        Map<String, List<ProductionLeadTimeVO>> leadTimeMap = productionLeadTime
                .stream().collect(Collectors.groupingBy(p -> StrUtil.join(STR_JOIN_VALUE, p.getStockPointCode(), p.getOperationCode(), p.getLeadTimeType(), p.getMainLineGroup())));
        Map<String, List<ProductionLeadTimeVO>> lineGroup = productionLeadTime.stream().filter(p -> StrUtil.isNotEmpty(p.getMainLineGroup())).collect(Collectors.toList())
                .stream().collect(Collectors.groupingBy(p -> StrUtil.join(STR_JOIN_VALUE, p.getMainLineGroup(), p.getOperationCode())));

        //物料对应夹丝类型
        Map<String, String> clampTypeBaseMap = stockPointBaseVOS.stream()
                .filter(t -> t.getClampType() != null && !"/".equals(t.getClampType()) && !"无".equals(t.getClampType()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getClampType, (v1, v2) -> v1));

        //物料对应调光类型
        Map<String, String> itemFlagBaseMap = stockPointBaseVOS.stream()
                .filter(t -> t.getItemFlag() != null && !"/".equals(t.getItemFlag()) && !"无".equals(t.getItemFlag()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getItemFlag, (v1, v2) -> v1));
        //物料对应除膜
        Map<String, String> attr1BaseMap = stockPointBaseVOS.stream()
                .filter(t -> t.getAttr1() != null && !"/".equals(t.getAttr1()) && !"无".equals(t.getAttr1()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getAttr1, (v1, v2) -> v1));
        // 物料对应HUD
        Map<String, String> hudMap = stockPointBaseVOS.stream()
                .filter(t -> t.getAttr1() != null && !"/".equals(t.getHud()) && !"无".equals(t.getHud()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getHud, (v1, v2) -> v1));
        String mpblCode = ipsFeign.getByCollectionCode("MPBL").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElse(null);
        String specialStockPoint = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElse(null);
        List<CustomerOrderInputNewData> customerOrderInputNewDataList = new ArrayList<>();
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : deliveryPlanMap.entrySet()) {
            String productCode = entry.getKey();
            String productKey = getKeyStockProduct(productCode, saleOrganizationProductMap);
            if (StrUtil.isEmpty(productKey)) {
                log.warn("发货计划：{}，没有销售组织key", productCode);
                continue;
            }
            RoutingVO routingVO = routingVOMap.get(productKey);
            if (null == routingVO) {
                log.warn("发货计划：{}，没有工艺路径", productCode);
                continue;
            }
            String stockPointCode = routingVO.getStockPointId();
            String joinKey = CharSequenceUtil.join("&", stockPointCode, productCode);
            if (!productStockPointVOMap.containsKey(joinKey)) {
                log.warn("发货计划物品：{}，基础数据不存在", joinKey);
                continue;
            }
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(joinKey);
            if (!workOrderRoutingInputNewDataMap.containsKey(newProductStockPointVO.getId())) {
                log.warn("发货计划物品：{}，工艺路径不存在", productCode);
                continue;
            }
            List<DeliveryPlanPublishedVO> detailList = entry.getValue();
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }

            detailList.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));
            String safetyKey = CharSequenceUtil.join("&", newProductStockPointVO.getStockPointCode(), productCode);
            List<WorkOrderRoutingStepInputNewData> result = new ArrayList<>();
            // 根据采购类别查找半品工艺路径
            List<NewProductStockPointVO> semiRoutingList = new ArrayList<>();
            List<String> whetherSchedule = new ArrayList<>();

            getChain(newProductStockPointVO, productStockPointIdVOMap, workOrderRoutingInputNewDataMap, routingStepMap,
                    routingStepInputMap, routingStepVOMap, result, standardStepVOMap, routingIdList, routingStepIds,
                    productDayMap, routingStepOutPutMap, semiProductIds, semiRoutingList, routingResourceMap, productLineList,
                    removeResourceIdList, whetherSchedule, mpblCode, specialStockPoint);

            if (CollectionUtils.isNotEmpty(whetherSchedule)) {
                demandCodeList.add(productCode);
            }

            if (CollectionUtils.isNotEmpty(semiRoutingList)) {
                List<FinishedHalfProductMapping> semiFinishedHalfProductMapping = new ArrayList<>();
                for (NewProductStockPointVO semi : semiRoutingList) {
                    String join = StrUtil.join(STR_JOIN_VALUE, semi.getStockPointCode(), semi.getProductCode());
                    RoutingVO semiRoutingVO = routingVOMap.get(join);
                    if (null == semiRoutingVO) {
                        continue;
                    }
                    // 构建半品库存
                    BigDecimal semiStockQty = calculateStock(semiRoutingVO, routingStepMap, stockSupplyInputNewData, semiStockMap, finishStockMap,
                            productionFinishStockMap, routingStepOutPutMap, routingStepVOMap, standardStepVOMapOnId, true);
                    getFinishedHalfProductMapping(newProductStockPointVO, semi, semiFinishedHalfProductMapping, semiStockQty);
                }
                if (!semiFinishedHalfProductMapping.isEmpty()) {
                    // 大小片配对，根据库存少的批次作为主批次，配对生成另外半边
                    semiFinishedHalfProductMapping.sort(Comparator.comparing(FinishedHalfProductMapping::getStockQty));
                    FinishedHalfProductMapping mainMapping = semiFinishedHalfProductMapping.get(0);
                    mainMapping.setWhetherMainMaterial(YesOrNoEnum.YES.getCode());
                    if (semiFinishedHalfProductMapping.size() > 1) {
                        mainMapping.setStockQtyDifference(
                                BigDecimalUtils.subtract(semiFinishedHalfProductMapping.get(1).getStockQty(),
                                        mainMapping.getStockQty())
                        );
                    }
                    finishedHalfProductMappingList.addAll(semiFinishedHalfProductMapping);
                }
                // 构建成品库存
                calculateStock(routingVO, routingStepMap, stockSupplyInputNewData, semiStockMap, finishStockMap, productionFinishStockMap,
                        routingStepOutPutMap, routingStepVOMap, standardStepVOMapOnId, false);
            } else {
                // 构建成品库存
                calculateStock(routingVO, routingStepMap, stockSupplyInputNewData, semiStockMap, finishStockMap, productionFinishStockMap,
                        routingStepOutPutMap, routingStepVOMap, standardStepVOMapOnId, true);
            }


            String routingId = routingVO.getId();
            String stockPointId = routingVO.getStockPointId();
            for (DeliveryPlanPublishedVO detailVO : detailList) {
                // 发货时间
                Date demandTime = detailVO.getDemandTime();
                // 需求数量
                Integer demandQuantity = detailVO.getDemandQuantity();
                // 记录原始需求时间
                originalTimeMap.put(detailVO.getId(), DateUtils.dateToString(demandTime, DateUtils.COMMON_DATE_STR1));
                // 当前发货计划对应的全部工序
                List<RoutingStepVO> currentRouting = stepListMap.get(routingId)
                        .stream().sorted(Comparator.comparing(RoutingStepVO::getSequenceNo).reversed()).collect(Collectors.toList());
                // - 标准安全库存天数
                if (safetyStockLecelMap.containsKey(productCode)) {
                    SafetyStockLevelVO safetyStockLevelVO = safetyStockLecelMap.get(productCode).get(0);
                    int minStockDay = safetyStockLevelVO.getStandardStockDay().intValue();
                    demandTime = DateUtil.offsetDay(demandTime, -minStockDay);
                }
                // -固化时间
                if (curingTimeVOMap.containsKey(safetyKey)) {
                    MdsCuringTimeVO mdsCuringTimeVO = curingTimeVOMap.get(safetyKey);
                    String ghTime = mdsCuringTimeVO.getGhTime();
                    if (StrUtil.isNotEmpty(ghTime) && BigDecimalUtils.toBigDecimal(ghTime).compareTo(BigDecimal.ONE) >= 1) {
                        demandTime = DateUtil.offsetHour(demandTime, -BigDecimalUtils.toBigDecimal(ghTime).intValue());
                    }
                }
                if (finishedProductDeliveryVOMap.containsKey(productCode)) {
                    MdsFinishedProductDeliveryVO mdsFinishedProductDeliveryVO = finishedProductDeliveryVOMap.get(productCode);
                    String storageTime = mdsFinishedProductDeliveryVO.getStorageTime();
                    if (StrUtil.isNotEmpty(storageTime) && BigDecimalUtils.toBigDecimal(storageTime).compareTo(BigDecimal.ONE) >= 1) {
                        demandTime = DateUtil.offsetHour(demandTime, -BigDecimalUtils.toBigDecimal(storageTime).intValue());
                    }
                }
                for (RoutingStepVO stepVO : currentRouting) {
                    String stepId = stepVO.getId();
                    String standardStepId = stepVO.getStandardStepId();
                    StandardStepVO standardStepVO = standardStepVOMapOnId.get(standardStepId);
                    String standardStepType = standardStepVO.getStandardStepType();
                    Integer sequenceNoStep = stepVO.getSequenceNo();
                    // 路径步骤对应的候选资源
                    List<WorkOrderRoutingStepResourceInputNewData> workOrderRoutingStepResourceInputNewData = routingResourceMap.get(stepId);
                    if (CollectionUtils.isEmpty(workOrderRoutingStepResourceInputNewData)) {
                        log.warn("路径步骤：{}，无候选资源", stepId);
                        continue;
                    }
                    workOrderRoutingStepResourceInputNewData.sort(Comparator.comparing(WorkOrderRoutingStepResourceInputNewData::getPriority));
                    WorkOrderRoutingStepResourceInputNewData resource = workOrderRoutingStepResourceInputNewData.get(0);
                    if (StandardStepEnum.FORMING_PROCESS.getCode().equals(standardStepType)) {
                        if (CollectionUtils.isNotEmpty(workOrderRoutingStepResourceInputNewData)) {
                            String standardResourceCode = resource.getRoutingCode();
                            String key = StrUtil.join("&", standardResourceCode, sequenceNoStep);
                            BigDecimal leadTimeCount = calculateLeadTime(key, lineGroup, ProductionLeadTimeEnum.POST_PRODUCTION_PROCESSING_TIME.getCode(),
                                    productCode, clampTypeBaseMap, itemFlagBaseMap, attr1BaseMap, hudMap);
                            demandTime = DateUtil.offsetHour(demandTime, -leadTimeCount.intValue());
                        }
                        break;
                    }
                    if (CollectionUtils.isNotEmpty(workOrderRoutingStepResourceInputNewData)) {
                        String standardResourceCode = resource.getRoutingCode();
                        String key = StrUtil.join(STR_JOIN_VALUE, standardResourceCode, sequenceNoStep);
                        BigDecimal leadTimeCount = calculateLeadTime(key, lineGroup, null,
                                productCode, clampTypeBaseMap, itemFlagBaseMap, attr1BaseMap, hudMap);
                        demandTime = DateUtil.offsetHour(demandTime, -leadTimeCount.intValue());
                    }
                    // 扣减成型后工序得时间
                    String key = StrUtil.join(STR_JOIN_VALUE, stockPointId, sequenceNoStep, ProductionLeadTimeEnum.PRODUCTION_PROCESSING_TIME.getCode(), resource.getRoutingCode());
                    if (leadTimeMap.containsKey(key)) {
                        BigDecimal productionTime = resource.getUnitsPerHour();
                        BigDecimal beatQty = BigDecimalUtils.multiply(BigDecimalUtils.toBigDecimal(demandQuantity), productionTime, 0);
                        demandTime = DateUtil.offsetSecond(demandTime, -BigDecimalUtils.toBigDecimal(beatQty).intValue());
                    }
                }

                CustomerOrderInputNewData customerOrderInputNewData = CustomerOrderInputNewData.builder()
                        .customerOrderId(detailVO.getId())
                        .customerOrderCode(detailVO.getId())
                        .productStockPointId(newProductStockPointVO.getId())
                        .dueDate(DateUtils.dateToString(demandTime, DateUtils.COMMON_DATE_STR1))
                        .qty(BigDecimalUtils.toBigDecimal(demandQuantity))
                        .resistanceDiff(productCode)
                        .build();
                customerOrderInputNewDataList.add(customerOrderInputNewData);
            }
        }
        mdsAlgorithmInput.getWorkOrderRoutingStepResourceInputDataList().forEach(p -> p.setRoutingCode(null));
        algorithmPolymerization.getProcessRunMap().put("runProductCode", demandCodeList);
        return customerOrderInputNewDataList;
    }

    private void getFinishedHalfProductMapping(NewProductStockPointVO newProductStockPointVO,
                                               NewProductStockPointVO semi,
                                               List<FinishedHalfProductMapping> semiFinishedHalfProductMapping,
                                               BigDecimal semiStockQty) {
        FinishedHalfProductMapping finishedHalfProductMapping = FinishedHalfProductMapping.builder()
                .finishedProductId(newProductStockPointVO.getId())
                .finishedProductCode(newProductStockPointVO.getProductCode())
                .halfProductId(semi.getId())
                .halfProductCode(semi.getProductCode())
                .stockQty(semiStockQty)
                .stockQtyDifference(BigDecimal.ZERO)
                .whetherMainMaterial(YesOrNoEnum.NO.getCode())
                .build();
        semiFinishedHalfProductMapping.add(finishedHalfProductMapping);
    }

    private String getKeyStockProduct(String productCode, Map<String, NewProductStockPointVO> saleOrganizationProductMap) {
        if (!saleOrganizationProductMap.containsKey(productCode)) {
            log.warn("物品：{}，没有销售组织物料", productCode);
            return StrUtil.EMPTY;
        }
        String poCategory = saleOrganizationProductMap.get(productCode).getPoCategory();
        if (StrUtil.isEmpty(poCategory)) {
            log.warn("物品：{}，销售组织物料采购关系为空", productCode);
            return StrUtil.EMPTY;
        }
        // 00819TRW00005下挂C00819TRW00005半品，BOM下是S1，实际工艺路径在S2
        //需要通过采购类别识别关联关系,S1的C00819TRW00005的物料属性是制造，且采购类别是S1.S2，则继续关联找S2的工艺路径
        String[] split = poCategory.split("\\.");
        String trueStock = split[1];
        return CharSequenceUtil.join(STR_JOIN_VALUE, trueStock, productCode);
    }


    private void checkData(Map<String, RoutingVO> routingVOMap,
                           Map<String, NewProductStockPointVO> productStockPointVOMap,
                           Map<String, WorkOrderRoutingInputNewData> workOrderRoutingInputNewDataMap,
                           Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap,
                           Map<String, List<WorkOrderRoutingStepInputNewData>> routingStepMap,
                           Map<String, List<WorkOrderRoutingStepResourceInputNewData>> routingResourceMap,
                           PlanningHorizonVO planningHorizonVO, List<NewProductStockPointVO> newProductStockPointVOS) {
        List<NewProductStockPointVO> productStockPointVOS = newProductStockPointVOS.stream().filter(p -> StrUtil.isNotEmpty(p.getVehicleModelCode())).collect(Collectors.toList());
        Map<String, NewProductStockPointVO> vehicleModelCodeMap = StreamUtils.mapByColumn(productStockPointVOS, NewProductStockPointVO::getProductCode);
        List<DemandEarlyWarningDTO> demandEarlyWarningDTOS = new ArrayList<>();
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : deliveryPlanMap.entrySet()) {
            List<DeliveryPlanPublishedVO> value = entry.getValue();
            value.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));
            Date demandTime = value.get(0).getDemandTime();
            Integer qty = value.get(0).getDemandQuantity();
            String productCode = entry.getKey();
            RoutingVO routingVO = routingVOMap.get(productCode);
            if (null == routingVO) {
                getDemandEarlyWarningDTO("工艺路径不存在", productCode, null, demandEarlyWarningDTOS, demandTime, qty);
                continue;
            }
            String stockPointCode = routingVO.getStockPointId();
            String joinKey = CharSequenceUtil.join("&", stockPointCode, productCode);
            if (!productStockPointVOMap.containsKey(joinKey)) {
                getDemandEarlyWarningDTO("物料信息不存在", productCode, null, demandEarlyWarningDTOS, demandTime, qty);
                continue;
            }
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMap.get(joinKey);
            if (!workOrderRoutingInputNewDataMap.containsKey(newProductStockPointVO.getId())) {
                getDemandEarlyWarningDTO("工艺路径无效", productCode, newProductStockPointVO.getVehicleModelCode(), demandEarlyWarningDTOS, demandTime, qty);
                continue;
            }
            String id = routingVO.getId();
            List<WorkOrderRoutingStepInputNewData> workOrderRoutingStepInputNewData = routingStepMap.get(id);
            if (CollectionUtils.isEmpty(workOrderRoutingStepInputNewData)) {
                getDemandEarlyWarningDTO("工序为空", productCode, newProductStockPointVO.getVehicleModelCode(), demandEarlyWarningDTOS, demandTime, qty);
                continue;
            }
            for (WorkOrderRoutingStepInputNewData workOrderRoutingStepInputNewDatum : workOrderRoutingStepInputNewData) {
                String routingStepId = workOrderRoutingStepInputNewDatum.getRoutingStepId();
                Integer sequenceNumber = workOrderRoutingStepInputNewDatum.getSequenceNumber();
                List<WorkOrderRoutingStepResourceInputNewData> workOrderRoutingStepResourceInputNewData = routingResourceMap.get(routingStepId);
                if (CollectionUtils.isEmpty(workOrderRoutingStepResourceInputNewData)) {
                    getDemandEarlyWarningDTO("工序：" + sequenceNumber + "无可用资源", productCode, newProductStockPointVO.getVehicleModelCode(), demandEarlyWarningDTOS, demandTime, qty);
                }
            }
        }

        // 校验无产线组的物料
        Map<String, Object> params = new HashMap<>();
        params.put("startDemandTime", DateUtils.dateToString(planningHorizonVO.getPlanStartTime(), DateUtils.COMMON_DATE_STR3));
        params.put("endDemandTime", DateUtils.dateToString(planningHorizonVO.getPlanEndTime(), DateUtils.COMMON_DATE_STR3));
        List<DeliveryPlanPublishedVO> deliveryPlanPublished = dfpFeign.selectSumDeliveryPlanPublished(params);
        List<String> lineGroupProduct = operationTaskExtDao.selectAllLineGroupProduct();
        // 没有产线组的物料
        List<DeliveryPlanPublishedVO> planPublishedVOS = deliveryPlanPublished.stream().filter(p ->
                !lineGroupProduct.contains(p.getProductCode()) && p.getDemandQuantity() > 0).collect(Collectors.toList());
        log.info("无产线组物料行数：{}", planPublishedVOS.size());
        if (CollectionUtils.isNotEmpty(planPublishedVOS)) {
            for (DeliveryPlanPublishedVO planPublishedVO : planPublishedVOS) {
                getDemandEarlyWarningDTO("物料：" + planPublishedVO.getProductCode() + " 无产线组", planPublishedVO.getProductCode(),
                        vehicleModelCodeMap.getOrDefault(planPublishedVO.getProductCode(), new NewProductStockPointVO()).getVehicleModelCode(),
                        demandEarlyWarningDTOS, planningHorizonVO.getPlanStartTime(), planPublishedVO.getDemandQuantity());
            }
        }
        log.info("异常预警数量：{}", demandEarlyWarningDTOS.size());
        if (CollectionUtils.isNotEmpty(demandEarlyWarningDTOS)) {
            demandEarlyWarningService.doBatchAddList(demandEarlyWarningDTOS);
        }
    }

    private void getDemandEarlyWarningDTO(String errorRemark, String productCode, String vehicleModelCode, List<DemandEarlyWarningDTO> demandEarlyWarningDTOS, Date demandTime, Integer qty) {
        DemandEarlyWarningDTO demandEarlyWarningDTO = DemandEarlyWarningDTO.builder()
                .id(UUIDUtil.getUUID())
                .productCode(productCode)
                .deliveryTime(demandTime)
                .vehicleModelCode(vehicleModelCode)
                // .earlyWarningType(DemandEarlyWarningEnum.DATA_BASE_ERROR.getCode())
                .earlyWarningReason(errorRemark)
                .demandQuantity(qty)
                .build();
        demandEarlyWarningDTOS.add(demandEarlyWarningDTO);
    }

    private BigDecimal calculateStock(RoutingVO routingVO,
                                      Map<String, List<WorkOrderRoutingStepInputNewData>> routingStepMap,
                                      List<StockSupplyInputNewData> stockSupplyInputNewDataList,
                                      Map<String, BigDecimal> semiStockMap,
                                      Map<String, BigDecimal> finishStockMap,
                                      Map<String, BigDecimal> productionFinishStockMap,
                                      Map<String, List<WorkOrderRoutingStepOutputInputNewData>> workOrderRoutingStepOutputInputDataList,
                                      Map<String, RoutingStepVO> routingStepVOMap, Map<String, StandardStepVO> standardStepVOMapOnId,
                                      boolean whetherFinished) {
        String routingVOId = routingVO.getId();
        String productCode = routingVO.getProductCode();
        List<WorkOrderRoutingStepInputNewData> stepVOS = routingStepMap.get(routingVOId);
        stepVOS.sort(Comparator.comparing(WorkOrderRoutingStepInputNewData::getSequenceNumber));
        String virtualProductStockPointId = "virtualProduct&";
        List<String> standardStepTypeCode = ListUtil.of(StandardStepEnum.FORMING_PROCESS.getCode());
        boolean whetherPreOperation = Boolean.FALSE;
        BigDecimal stock = BigDecimal.ZERO;
        for (int i = 0; i < stepVOS.size(); i++) {
            WorkOrderRoutingStepInputNewData step = stepVOS.get(i);
            RoutingStepVO routingStepVO = routingStepVOMap.get(step.getRoutingStepId());
            Integer sequenceNumber = step.getSequenceNumber();
            String stockKey = String.join("&", productCode, sequenceNumber.toString());
            // 最后一道工序构建成品库存+可能存在的半品库存
            if (i == stepVOS.size() - 1) {
                // 成品库存：销售组织+子库存为CPSJ
                BigDecimal finishStock = finishStockMap.getOrDefault(productCode, BigDecimal.ZERO);
                // 成品工序在制
                BigDecimal semiStock = semiStockMap.getOrDefault(productCode, BigDecimal.ZERO);
                // 成品库存：生产组织
                BigDecimal productionFinishStock = productionFinishStockMap.getOrDefault(productCode, BigDecimal.ZERO);
                BigDecimal stockQty = BigDecimalUtils.add(semiStock, finishStock).add(productionFinishStock);
                if (stockQty.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                List<WorkOrderRoutingStepOutputInputNewData> finishedProduct = workOrderRoutingStepOutputInputDataList.get(step.getRoutingStepId());
                if (CollectionUtils.isEmpty(finishedProduct)) {
                    return stock;
                }
                WorkOrderRoutingStepOutputInputNewData workOrderRoutingStepOutputInputNewData = finishedProduct.get(0);
                StockSupplyInputNewData stockSupplyInputNewData = getStock(stockQty, workOrderRoutingStepOutputInputNewData);
                stockSupplyInputNewDataList.add(stockSupplyInputNewData);
                stock = stockSupplyInputNewData.getQty();
            } else {
                String standardStepId = routingStepVO.getStandardStepId();
                StandardStepVO standardStepVO = standardStepVOMapOnId.get(standardStepId);
                if (null == standardStepVO) {
                    continue;
                }
                String standardStepType = standardStepVO.getStandardStepType();
                if (standardStepTypeCode.contains(standardStepType)) {
                    whetherPreOperation = Boolean.TRUE;
                }
                // 关键工序的前工序不构建库存
                if (whetherFinished && !whetherPreOperation) {
                    continue;
                }
                // 前工序构建半成品库存
                if (!semiStockMap.containsKey(stockKey)) {
                    continue;
                }
                List<WorkOrderRoutingStepOutputInputNewData> virtualProductStock = workOrderRoutingStepOutputInputDataList.get(step.getRoutingStepId())
                        .stream().filter(p -> p.getProductStockPointId().contains(virtualProductStockPointId)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(virtualProductStock)) {
                    continue;
                }
                BigDecimal stockQty = semiStockMap.get(stockKey);
                // 虚拟物品，构建虚拟输入物品的工序在制量
                WorkOrderRoutingStepOutputInputNewData workOrderRoutingStepInputInputNewData = virtualProductStock.get(0);
                stockSupplyInputNewDataList.add(getStock(stockQty, workOrderRoutingStepInputInputNewData));
            }
        }
        return stock;
    }

    private StockSupplyInputNewData getStock(BigDecimal stockQty, WorkOrderRoutingStepOutputInputNewData workOrderRoutingStepInputInputNewData) {
        String productStockPointId = workOrderRoutingStepInputInputNewData.getProductStockPointId();
        String stockId = UUIDUtil.getUUID();
        StockSupplyInputNewData stockSupplyInputNewData = new StockSupplyInputNewData();
        stockSupplyInputNewData.setStockId(stockId);
        stockSupplyInputNewData.setStockCode(stockId);
        stockSupplyInputNewData.setProductStockPointId(productStockPointId);
        stockSupplyInputNewData.setQty(stockQty);
        stockSupplyInputNewData.setPriority(BigDecimal.ZERO);
        return stockSupplyInputNewData;
    }

    protected Date calculateTime(String key, Map<String, List<ProductionLeadTimeVO>> productionLeadTimeMap, Date demandTime) {
        if (productionLeadTimeMap.containsKey(key)) {
            List<ProductionLeadTimeVO> readyTime = productionLeadTimeMap.get(key);
            BigDecimal leadTime = readyTime.stream().map(ProductionLeadTimeVO::getLeadTime).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal divide = BigDecimalUtils.divide(leadTime, BigDecimalUtils.toBigDecimal(readyTime.size()), 0);
            demandTime = DateUtil.offsetHour(demandTime, -divide.intValue());
        }
        return demandTime;
    }

    private void getChain(NewProductStockPointVO newProductStockPointVO,
                          Map<String, NewProductStockPointVO> productStockPointVOMap,
                          Map<String, WorkOrderRoutingInputNewData> workOrderRoutingInputNewDataMap,
                          Map<String, List<WorkOrderRoutingStepInputNewData>> routingStepMap,
                          Map<String, List<WorkOrderRoutingStepInputInputNewData>> routingStepInputMap,
                          Map<String, RoutingStepVO> routingStepVOMap,
                          List<WorkOrderRoutingStepInputNewData> result, Map<String, StandardStepVO> standardStepVOMap,
                          List<String> routingIds, List<String> routingStepIds, Map<String, String> productDayMap,
                          Map<String, List<WorkOrderRoutingStepOutputInputNewData>> routingStepOutPutMap, List<String> semiProductIds,
                          List<NewProductStockPointVO> semiRouting,
                          Map<String, List<WorkOrderRoutingStepResourceInputNewData>> routingResourceMap,
                          List<String> productLineList,
                          Set<String> removeResourceIdList,
                          List<String> whetherSchedule, String mpblCode,
                          String specialStockPoint) {
        // 发货计划物品工艺路径信息
        WorkOrderRoutingInputNewData workOrderRoutingInputNewData = workOrderRoutingInputNewDataMap.get(newProductStockPointVO.getId());
        if (null == workOrderRoutingInputNewData) {
            return;
        }
        List<WorkOrderRoutingStepInputNewData> workOrderRoutingStepInputNewData = routingStepMap.get(workOrderRoutingInputNewData.getRoutingId());
        if (CollectionUtils.isEmpty(workOrderRoutingStepInputNewData)) {
            return;
        }
        routingIds.add(workOrderRoutingInputNewData.getRoutingId());
        List<String> stepIds = workOrderRoutingStepInputNewData.stream()
                .map(WorkOrderRoutingStepInputNewData::getRoutingStepId).collect(Collectors.toList());
        routingStepIds.addAll(stepIds);
        // 循环输入物品展开BON查找全部的关键工序
        workOrderRoutingStepInputNewData.sort(Comparator.comparing(WorkOrderRoutingStepInputNewData::getSequenceNumber));
        for (int i = 0; i < workOrderRoutingStepInputNewData.size(); i++) {
            WorkOrderRoutingStepInputNewData routingStepInput = workOrderRoutingStepInputNewData.get(i);
            RoutingStepVO routingStepVO = routingStepVOMap.get(routingStepInput.getRoutingStepId());
            Integer sequenceNo = routingStepVO.getSequenceNo();
            String joinKey = CharSequenceUtil.join(STR_JOIN_VALUE, newProductStockPointVO.getStockPointCode(), sequenceNo);
            // 判断当前工序是否为‘成型'工序
            StandardStepVO standardStepVO = standardStepVOMap.get(joinKey);
            if (null == standardStepVO) {
                log.warn("未找到标准工艺：{}", joinKey);
                continue;
            }
            String standardStepType = standardStepVO.getStandardStepType() == null ? "" : standardStepVO.getStandardStepType();
            if (CharSequenceUtil.isNotEmpty(standardStepType) && StandardStepEnum.FORMING_PROCESS.getCode().equals(standardStepType)) {
                // 当前成型工序的输出物品
                List<WorkOrderRoutingStepOutputInputNewData> virtualProduct = routingStepOutPutMap.get(routingStepInput.getRoutingStepId())
                        .stream().filter(p -> p.getProductStockPointId().contains("virtual")).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(virtualProduct)) {
                    // 映射虚拟物料的id和成品的id关系，修改虚拟物料的最大合批天数
                    String virtualProductId = virtualProduct.get(0).getProductStockPointId();
                    String productStockPointId = workOrderRoutingInputNewData.getProductStockPointId();
                    productDayMap.put(virtualProductId, productStockPointId);
                }
                result.add(routingStepInput);
                // 校验指定产线的优先级最高的候选资源信息
                List<WorkOrderRoutingStepResourceInputNewData> workOrderRoutingStepResourceInputNewData = routingResourceMap.get(routingStepVO.getId());
                if (CollectionUtils.isEmpty(workOrderRoutingStepResourceInputNewData)) {
                    log.warn("物料：{}，关键工序没有可用资源，stepId：{}", newProductStockPointVO.getProductCode(), routingStepVO.getId());
                    return;
                }
                boolean nullPriority = workOrderRoutingStepResourceInputNewData.stream().allMatch(p -> Objects.isNull(p.getPriority()));
                if (nullPriority) {
                    log.error("路径步骤：{}，存在候选资源优先级为空", routingStepVO.getId());
                }
                // 根据优先级+主键id同时升序排序，为空的放在最后
                workOrderRoutingStepResourceInputNewData.sort(
                        Comparator.comparing(WorkOrderRoutingStepResourceInputNewData::getPriority, Comparator.nullsLast(Comparator.naturalOrder()))
                                .thenComparing(WorkOrderRoutingStepResourceInputNewData::getStandardResourceId)
                );
                // 找出优先级最高的资源
                String physicalResourceCode = workOrderRoutingStepResourceInputNewData.get(0).getStandardResourceCode();
                // 判断优先级最高的资源是否属于指定运行产线
                if (productLineList.contains(physicalResourceCode)) {
                    // 仅保留优先级最高且属于指定运行产线的候选资源
                    List<String> removeResourceId = workOrderRoutingStepResourceInputNewData.stream()
                            .filter(resource -> !productLineList.contains(resource.getStandardResourceCode()))
                            .map(WorkOrderRoutingStepResourceInputNewData::getRoutingStepResourceId)
                            .collect(Collectors.toList());
                    removeResourceIdList.addAll(removeResourceId);
                    // 待排需求物料
                    whetherSchedule.add(newProductStockPointVO.getProductCode());
                } else {
                    log.info("物料：{}，最高优先级候选资源，不在指定排产产线中", newProductStockPointVO.getProductCode());
                }
            }
            List<WorkOrderRoutingStepInputInputNewData> workOrderRoutingStepInputInputNewData = routingStepInputMap.get(routingStepInput.getRoutingStepId());
            if (CollectionUtils.isEmpty(workOrderRoutingStepInputInputNewData)) {
                continue;
            }
            for (WorkOrderRoutingStepInputInputNewData workOrderRoutingStepInputInputNewDatum : workOrderRoutingStepInputInputNewData) {
                String productStockPointId = workOrderRoutingStepInputInputNewDatum.getProductStockPointId();
                if (!productStockPointVOMap.containsKey(productStockPointId)) continue;
                // 输入物品
                NewProductStockPointVO newProductStockPointVOInput = productStockPointVOMap.get(productStockPointId);
                if (null == newProductStockPointVOInput) continue;
                if (i == 0) {
                    // 如果头道工序库存点为S1且物品类型为‘MPBL’的物品修改其为‘P’类物品
                    String stockPointCode = newProductStockPointVOInput.getStockPointCode();
                    String productType = newProductStockPointVOInput.getProductType();
                    if (stockPointCode.equals(specialStockPoint) && productType.equals(mpblCode)) {
                        semiProductIds.add(newProductStockPointVOInput.getId());
                    }
                }
                // 如果当前输入物品为‘制造’则继续查找工艺路径
                if (CharSequenceUtil.isNotEmpty(newProductStockPointVOInput.getProductType()) && com.yhl.scp.mds.enums.ProductTypeEnum.SA.getCode().equals(newProductStockPointVOInput.getProductType())) {
                    semiRouting.add(newProductStockPointVOInput);
                    getChain(newProductStockPointVOInput, productStockPointVOMap, workOrderRoutingInputNewDataMap,
                            routingStepMap, routingStepInputMap, routingStepVOMap, result, standardStepVOMap, routingIds,
                            routingStepIds, productDayMap, routingStepOutPutMap, semiProductIds, semiRouting, routingResourceMap,
                            productLineList, removeResourceIdList, whetherSchedule, mpblCode, specialStockPoint);
                }
            }
        }
    }

    protected AmsAnalysisContext initAmsContext(String creatorId, AlgorithmLog algorithmLog) {
        // 筛选出当前计划员相关物料对应得制造订单
        List<WorkOrderVO> workOrderVOList = getSupplementsOrder(creatorId, algorithmLog);
        if (CollectionUtils.isNotEmpty(workOrderVOList)) {
            List<String> orderIds = StreamUtils.columnToList(workOrderVOList, WorkOrderVO::getId);
            masterPlanDao.deleteOldDemand(orderIds);
        }
        List<String> workOrderIds = StreamUtils.columnToList(workOrderVOList, WorkOrderVO::getId);
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(null);
        List<OperationVO> sourceOperations = operationDao.selectVOByParams(ImmutableMap.of("orderIds", workOrderIds));
        List<OperationTaskVO> operationTaskVOS = operationTaskService.selectAll();
        PlanningHorizonVO planningHorizonVO = mdsFeign.selectPlanningHorizon(null);
        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectProductStockPointByIds(null, StreamUtils.columnToList(workOrderVOList, WorkOrderVO::getProductId));
        List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = dfpFeign.selectAllPublishDeliveryPlan()
                .stream().filter(p -> p.getDemandTime().getTime() >= planningHorizonVO.getPlanStartTime().getTime() &&
                        p.getDemandTime().getTime() <= planningHorizonVO.getPlanEndTime().getTime()).collect(Collectors.toList())
                .stream().filter(p -> p.getDemandQuantity() > 0).collect(Collectors.toList());
        List<ProductCandidateResourceTimeVO> productCandidateResourceVOS = newMdsFeign.selectProductCandidateResourceTimeByParams(null, new HashMap<>());
        List<ProductionLeadTimeVO> productionLeadTimeVOS = productionLeadTimeService.selectAll();
        List<String> productCode = deliveryPlanPublishedVOS.stream().map(DeliveryPlanPublishedVO::getProductCode).distinct().collect(Collectors.toList());
        List<InventoryBatchDetailVO> stock = getStock(productCode);
        List<RoutingVO> routingVOS = mdsFeign.selectRoutingByParams(null, new HashMap<>());
        List<DemandVO> demandVOS = demandService.selectByDemandOrderIds(workOrderIds)
                .stream().filter(p -> StrUtil.isNotEmpty(p.getOperationInputId())).collect(Collectors.toList());
        List<NewStockPointVO> newStockPointVOS = mdsFeign.selectStockPointByParams(null, new HashMap<>());
        List<OperationInputVO> operationInputVOS = operationInputService.selectAll();
        List<RoutingStepInputVO> routingStepInputVOS = newMdsFeign.selectByParamsRoutingStepInput(null, new HashMap<>());

        // 库存分组
        Map<String, Integer> stockMapOnProductCode = stock.stream()
                .collect(Collectors.groupingBy(
                        InventoryBatchDetailVO::getProductCode,
                        Collectors.summingInt(inv -> {
                            BigDecimal quantity = new BigDecimal(inv.getCurrentQuantity());
                            return quantity.intValue();
                        })
                ));
        List<SafetyStockLevelVO> safetyStockLevelVOS = dfpFeign.selectSafetyStockLevelByProductCodeList(null, productCode);
        Map<String, List<SafetyStockLevelVO>> safetyStockLecelMap = safetyStockLevelVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockCode(), p.getProductCode())));

        Map<String, List<ProductCandidateResourceTimeVO>> productCandidateResourceMap = productCandidateResourceVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getProductCode(), p.getOperationCode())));
        Map<String, List<ProductionLeadTimeVO>> productionLeadTimeMap = productionLeadTimeVOS.stream()
                .collect(Collectors.groupingBy(p -> CharSequenceUtil.join("&", p.getStockPointCode(), p.getOperationCode(), p.getLeadTimeType())));
        Map<String, StandardStepVO> standardStepJoinMap = standardStepVOS.stream()
                .collect(Collectors.toMap(p -> StrUtil.join("&", p.getStockPointCode(), p.getStandardStepName()), Function.identity()));

        return AmsAnalysisContext.builder()
                .routingStepInputMapOnRoutingStepId(StreamUtils.mapListByColumn(routingStepInputVOS, RoutingStepInputVO::getRoutingStepId))
                .operationInputMapOnOperationId(StreamUtils.mapListByColumn(operationInputVOS, OperationInputVO::getOperationId))
                .stockPointMapOnCode(StreamUtils.mapByColumn(newStockPointVOS, NewStockPointVO::getStockPointCode))
                .demandVOS(demandVOS)
                .demandMapOnOrderId(StreamUtils.mapListByColumn(demandVOS, DemandVO::getDemandOrderId))
                .demandMapOnOperationInputId(StreamUtils.mapListByColumn(demandVOS, DemandVO::getOperationInputId))
                .routingVOS(routingVOS)
                .routingMapOnProductCode(StreamUtils.mapByColumn(routingVOS, RoutingVO::getProductCode))
                .stockMapOnProductCode(stockMapOnProductCode)
                .safetyStockLecelMap(safetyStockLecelMap)
                .productionLeadTimeVOS(productionLeadTimeVOS)
                .productionLeadTimeMap(productionLeadTimeMap)
                .productCandidateResourceMap(productCandidateResourceMap)
                .deliveryPlanPublishedVOS(deliveryPlanPublishedVOS)
                .deliveryPlanPublishedMapOnProductCode(StreamUtils.mapListByColumn(deliveryPlanPublishedVOS, DeliveryPlanPublishedVO::getProductCode))
                .productStockPointVOS(newProductStockPointVOS)
                .productStockPointMapOnId(StreamUtils.mapByColumn(newProductStockPointVOS, NewProductStockPointVO::getId))
                .planningHorizonVO(planningHorizonVO)
                .workOrderVOS(workOrderVOList)
                .workOrderMapOnId(StreamUtils.mapByColumn(workOrderVOList, WorkOrderVO::getId))
                .workOrderMapOnProductId(StreamUtils.mapListByColumn(workOrderVOList, WorkOrderVO::getProductId))
                .operationVOS(sourceOperations)
                .operationMapOnOrderId(StreamUtils.mapListByColumn(sourceOperations, OperationVO::getOrderId))
                .operationTaskVOS(operationTaskVOS)
                .operationTaskMap(StreamUtils.mapListByColumn(operationTaskVOS, OperationTaskVO::getOperationId))
                .standardStepMapOnId(StreamUtils.mapByColumn(standardStepVOS, StandardStepVO::getId))
                .standardStepJoinMap(standardStepJoinMap)
                .demandResultMap(new HashMap<>())
                .demandVOResult(new ArrayList<>())
                .waitingDeleteDemand(new ArrayList<>())
                .build();
    }

    private List<InventoryBatchDetailVO> getStock(List<String> productCodeList) {
        List<String> bcStockPointList = mdsFeign.selectAllStockPoint(null)
                .stream()
                .filter(p -> StrUtil.isNotEmpty(p.getOrganizeType()) && p.getOrganizeType().equals(StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode()))
                .filter(p -> StrUtil.isNotEmpty(p.getStockPointType()) && p.getStockPointType().equals(StockPointTypeEnum.BC.getCode()))
                .map(NewStockPointVO::getStockPointCode)
                .collect(Collectors.toList());

        List<InventoryBatchDetailVO> inventoryBatchDetailVOS = dfpFeign.selectInventoryDataByProductCodes(null,
                productCodeList, StockPointTypeEnum.BC.getCode());
        List<String> spaceList = inventoryBatchDetailVOS.stream().map(InventoryBatchDetailVO::getFreightSpace).distinct().collect(Collectors.toList());

        Map<String, SubInventoryCargoLocationVO> cargoLocationVOMap = CollectionUtils.isEmpty(spaceList) ?
                new HashMap<>() :
                subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode())
                        .stream()
                        .collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode, Function.identity(), (v1, v2) -> v1));

        return inventoryBatchDetailVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getSubinventory()) && getRangeData().equals(p.getSubinventory()))
                .filter(p -> StrUtil.isNotEmpty(p.getFreightSpace()))
                .filter(p -> StringUtils.isEmpty(p.getOperationCode()))
                .filter(p -> bcStockPointList.contains(p.getStockPointCode()))
                .filter(p -> {
                    SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationVOMap.get(p.getFreightSpace());
                    return subInventoryCargoLocationVO != null && subInventoryCargoLocationVO.getEnabled().equals(YesOrNoEnum.YES.getCode());
                })
                .collect(Collectors.toList());
    }

    @Getter
    private static class DateRange {
        private final Date startDate;
        private final Date endDate;

        public DateRange(Date startDate, Date endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }

    }

    private String getRangeData() {
        BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(mdsScenario.getData(), "SUB_INVENTORY", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        return rangeData;
    }
}