package com.yhl.scp.mps.productionLimit.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ProductionLimitPO</code>
 * <p>
 * 特殊工艺产能约束PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-28 15:45:41
 */
public class ProductionLimitPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -61762694258235505L;

    /**
     * 公司代码
     */
    private String companyCode;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 要素值
     */
    private String factorValue;
    /**
     * 天数
     */
    private Integer days;
    /**
     * 主工序产能上限
     */
    private Long mainOperationProductionCapacity;
    /**
     * 限制开始时间
     */
    private Date startTime;
    /**
     * 限制结束时间
     */
    private Date endTime;
    /**
     * 版本
     */
    private Integer versionValue;

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }


    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getFactorValue() {
        return factorValue;
    }

    public void setFactorValue(String factorValue) {
        this.factorValue = factorValue;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Long getMainOperationProductionCapacity() {
        return mainOperationProductionCapacity;
    }

    public void setMainOperationProductionCapacity(Long mainOperationProductionCapacity) {
        this.mainOperationProductionCapacity = mainOperationProductionCapacity;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getVersionValue() {
        return versionValue;
    }

    public void setVersionValue(Integer versionValue) {
        this.versionValue = versionValue;
    }

}
