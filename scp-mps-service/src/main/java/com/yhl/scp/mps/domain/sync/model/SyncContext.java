package com.yhl.scp.mps.domain.sync.model;

import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepDO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.sds.extension.order.domain.entity.OperationDO;
import com.yhl.scp.sds.extension.order.domain.entity.WorkOrderDO;
import com.yhl.scp.sds.extension.order.dto.OperationDTO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <code>SyncContext</code>
 * <p>
 * SyncContext
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/10 11:12
 */
@Slf4j
@Data
@ApiModel("制造订单同步上下文")
public class SyncContext implements Serializable {


    private static final long serialVersionUID = -2824435858059946924L;

    /**
     * 需要同步的制造订单
     */
    private List<WorkOrderDO> syncWorkOrderList;

    /**
     * 涉及的库存点物品相关信息
     */
    private Map<String, NewProductStockPointVO> productStockPointVOMap;

    private List<NewProductStockPointVO> newProductStockPointVOList;
    private Map<String, NewProductStockPointVO> productMapOnIdAndStockPointCodeMap;
    private Map<String, NewProductStockPointVO> productMapOnIdMap;

    /**
     * 库存点信息
     */
    private Map<String, NewStockPointVO> stockPointVOMap;

    private List<NewStockPointVO> newStockPointVOList;

    /**
     * 待拆分分制造订单数据(拆分使用)
     */
    private List<WorkOrderDO> preparationSpiltWorkOrderList;

    /**
     * 同步的工序集合
     */
    private List<OperationDO> operationDOS;

    /**
     * 产品资源生产关系
     */
    private List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS;

    /**
     * 需要更改齐套状态的工序(没有输入物品"完全齐套")
     */
    private List<OperationDTO> updateOperationKitStatusList;

    /**
     * MPS得到的需求，需要回填operationInputId,
     */
    private List<DemandVO> mpsDemandVOList;

    /**
     * MPS得到的供应，需要回填operationOutputId,
     */
    private List<SupplyVO> mpsSupplyVOList;

    /**
     * 物料替代关系
     */
    private List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOList;
    private Map<String, List<ProductSubstitutionRelationshipVO>> productSubstitutionRelationshipVOMap;

    /**
     * 模具数量限制
     */
    private List<String> mjLimitResourceList;
    private Map<String, BigDecimal> moldQuantityLimitMap;

    /**
     * S1夹层产品限制资源Ids
     */
    private List<String> s1LimitResourceList;

    private Map<String,List<RoutingStepDO>> routingDOMap;

}
