package com.yhl.scp.mps.plan.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>SdsOrdOperationHistoryLogPO</code>
 * <p>
 * 工序历史记录表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-14 17:38:30
 */
public class SdsOrdOperationHistoryLogPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -76191556029853924L;

    /**
     * 算法包ID
     */
    private String logId;

    /**
     * 工单ID
     */
    private String workOrderId;
    /**
     * 工序ID
     */
    private String operationId;
    /**
     * 父工序ID
     */
    private String parentOperationId;
    /**
     * 计划状态
     */
    private String planStatus;
    /**
     * 源计划资源ID
     */
    private String sourcePlannedResourceId;
    /**
     * 源开始时间
     */
    private Date sourceStartTime;
    /**
     * 源结束时间
     */
    private Date sourceEndTime;
    /**
     * 目标计划资源ID
     */
    private String targetPlannedResourceId;
    /**
     * 目标开始时间
     */
    private Date targetStartTime;
    /**
     * 目标结束时间
     */
    private Date targetEndTime;

    public String getLogId() {
        return logId;
    }

    public void setLogId(String logId) {
        this.logId = logId;
    }

    public String getWorkOrderId() {
        return workOrderId;
    }

    public void setWorkOrderId(String workOrderId) {
        this.workOrderId = workOrderId;
    }

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public String getParentOperationId() {
        return parentOperationId;
    }

    public void setParentOperationId(String parentOperationId) {
        this.parentOperationId = parentOperationId;
    }

    public String getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(String planStatus) {
        this.planStatus = planStatus;
    }

    public String getSourcePlannedResourceId() {
        return sourcePlannedResourceId;
    }

    public void setSourcePlannedResourceId(String sourcePlannedResourceId) {
        this.sourcePlannedResourceId = sourcePlannedResourceId;
    }

    public Date getSourceStartTime() {
        return sourceStartTime;
    }

    public void setSourceStartTime(Date sourceStartTime) {
        this.sourceStartTime = sourceStartTime;
    }

    public Date getSourceEndTime() {
        return sourceEndTime;
    }

    public void setSourceEndTime(Date sourceEndTime) {
        this.sourceEndTime = sourceEndTime;
    }

    public String getTargetPlannedResourceId() {
        return targetPlannedResourceId;
    }

    public void setTargetPlannedResourceId(String targetPlannedResourceId) {
        this.targetPlannedResourceId = targetPlannedResourceId;
    }

    public Date getTargetStartTime() {
        return targetStartTime;
    }

    public void setTargetStartTime(Date targetStartTime) {
        this.targetStartTime = targetStartTime;
    }

    public Date getTargetEndTime() {
        return targetEndTime;
    }

    public void setTargetEndTime(Date targetEndTime) {
        this.targetEndTime = targetEndTime;
    }

}
