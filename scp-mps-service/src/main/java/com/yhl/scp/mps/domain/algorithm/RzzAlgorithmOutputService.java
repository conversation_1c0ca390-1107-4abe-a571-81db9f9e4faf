package com.yhl.scp.mps.domain.algorithm;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.yhl.aps.api.capacity.TimeSlotWithEfficiency;
import com.yhl.aps.api.obj.*;
import com.yhl.aps.api.runner.APSOutput;
import com.yhl.aps.kernel.capacity.TimeBlock;
import com.yhl.aps.kernel.capacity.TimeBlock4FixedDur;
import com.yhl.aps.kernel.capacity.TimeBlockForPoint;
import com.yhl.aps.kernel.obj.OperationSchedule;
import com.yhl.aps.kernel.obj.OperationWrapper;
import com.yhl.aps.kernel.obj.ProcessSchedule;
import com.yhl.aps.kernel.obj.Solution;
import com.yhl.aps.kernel.resource.SingleResource;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.CommonDateUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.enums.SubTaskTypeEnum;
import com.yhl.scp.mds.basic.resource.enums.ResourceCategoryEnum;
import com.yhl.scp.mds.basic.rule.enums.RuleEncodingsEnum;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.product.vo.ProductStockPointVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.rule.util.RuleEncodingsUtils;
import com.yhl.scp.mps.algorithm.schedule.output.RzzOperationDTO;
import com.yhl.scp.mps.cache.service.CacheSetService;
import com.yhl.scp.mps.dispatch.ams.input.OccupyTime;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.SdsOrdOperationHistoryLogDao;
import com.yhl.scp.mps.plan.infrastructure.po.SdsOrdOperationHistoryLogPO;
import com.yhl.scp.mps.plan.service.OperationDeleteLogService;
import com.yhl.scp.sds.basic.enums.PartitionTypeEnum;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.extension.order.dto.OperationExtendDTO;
import com.yhl.scp.sds.extension.order.dto.OperationSubTaskDTO;
import com.yhl.scp.sds.extension.order.dto.OperationTaskDTO;
import com.yhl.scp.sds.extension.order.dto.WorkOrderDTO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationExtendPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.order.convertor.OperationExtendConvertor;
import com.yhl.scp.sds.order.convertor.WorkOrderConvertor;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName RzzAlgorithmOutputServiceImpl
 * @Description TODO
 * @Date 2024-07-18 10:42:41
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Service
@Slf4j
public class RzzAlgorithmOutputService extends AbstractService {

    @Resource
    private WorkOrderService workOrderService;

    @Resource
    private OperationService operationService;

    @Resource
    private OperationTaskService operationTaskService;

    @Resource
    private OperationSubTaskService operationSubTaskService;

    @Resource
    private OperationExtendService operationExtendService;

    @Resource
    private OperationDao operationDao;

    @Resource
    private MdsFeign mdsFeign;

    @Resource
    private OperationDeleteLogService operationDeleteLogService;

    @Resource
    private RzzAMSSupportService rzzAMSSupportService;

    @Resource
    private OperationTaskExtDao operationTaskExtDao;

    @Resource
    private CacheSetService cacheSetService;

    @Resource
    private SdsOrdOperationHistoryLogDao sdsOrdOperationHistoryLogDao;

    public static void main(String[] args) {
        OperationWrapper operation = new OperationWrapper();
        OperationSchedule operationSchedule = new OperationSchedule(operation);
        // 资源
        ProcessSchedule workSchedule = new ProcessSchedule();
        SingleResource singleResource = new SingleResource();
        singleResource.setId("123");
        singleResource.setResourceId("123");
        workSchedule.setResources(SetUtils.hashSet(singleResource));
        // 主资源
        Solution solution = new Solution(singleResource);
        CandidateResource candidateResource = new CandidateResource(solution);
        candidateResource.setResourceId("123");
        candidateResource.setId("123");
        operation.setCandidateResource(ListUtil.of(candidateResource));
        // 默认不构建工具资源
        CandidateResources candidateResources = new CandidateResources(candidateResource, new ArrayList<>());
        operationSchedule.setCandidateResources(candidateResources);
        workSchedule.setOperationSchedule(operationSchedule);
        // 时间
        TimeBlockForPoint timeBlockForPoint = new TimeBlockForPoint(LocalDateTime.now(), LocalDateTime.now());
        TimeBlock timeBlock = new TimeBlock(timeBlockForPoint);
        workSchedule.setTimeBlock(timeBlock);
        operationSchedule.setProcessSchedule(workSchedule);

        // 测试结果
        operation.setSchedule(operationSchedule);
        OperationSchedule childSchedule = operation.getSchedule();
        List<WorkSchedule> workSchedules = childSchedule.getWorkSchedules();
        // 根据任务类型分组
        Map<String, List<Schedule>> scheduleMap = new HashMap<>();
        for (Schedule schedule : workSchedules) {
            ScheduleType type = schedule.getType();
            List<Schedule> scheduleList = scheduleMap.get(type.toString());
            if (CollectionUtils.isNotEmpty(scheduleList)) {
                scheduleList.add(schedule);
            } else {
                scheduleMap.put(type.toString(), new ArrayList<>(Collections.singletonList(schedule)));
            }
        }

        for (Schedule scheduleO : workSchedules) {
            List<CandidateResource> assignedCandidateResources = scheduleO.getAssignedCandidateResources();
            System.out.println(assignedCandidateResources);
        }

        System.out.println("Asd");
    }

    public void doAnalysisAlgorithmOutputData(APSOutput apsOutput, String logId, Map<String, String> operationOnResourceIdMap) {
        StopWatch stopWatch = new StopWatch("算法输出数据解析开始");
        if (null == operationOnResourceIdMap) {
            operationOnResourceIdMap = new HashedMap<>();
        }
        stopWatch.start("解析工序");
        //查询计划期间
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();

        //制造订单
        List<WorkOrderVO> workOrderVOList = workOrderService.selectByParams(new HashMap<>());
        //原工序
        List<OperationVO> sourceOperations = operationDao.selectVOByParams(new HashMap<>());

        doAnalysisAlgorithmOutputDataCommon(apsOutput, workOrderVOList, sourceOperations, logId, planningHorizon, false, operationOnResourceIdMap);
    }

    public void doAnalysisAlgorithmOutputDataCommon(APSOutput apsOutput,
                                                    List<WorkOrderVO> workOrderVOList,
                                                    List<OperationVO> sourceOperations, String logId,
                                                    PlanningHorizonVO planningHorizon,
                                                    Boolean isAdjust,
                                                    Map<String, String> operationOnResourceIdMap) {
        log.info("算法结果解析开始时间{}", System.currentTimeMillis());

        List<Operation> operationVecsa = apsOutput.getOperations();

        List<PhysicalResourceVO> physicalResourceVOList = mdsFeign.getPhysicalResourceParams(new HashMap<>());
        RuleEncodingsVO ruleEncodings = mdsFeign.getRuleEncodingsByRuleName(RuleEncodingsEnum.SUB_OPERATION_CODE.getDesc());
        List<StandardStepVO> standardStepVOS = operationTaskExtDao.selectStandardStep();
        Map<String, StandardStepVO> standardStepVOMap = StreamUtils.mapByColumn(standardStepVOS, StandardStepVO::getId);

        log.info("workOrderVOList = {}", workOrderVOList.size());
        log.info("sourceOperations = {}", sourceOperations.size());
        log.info("physicalResourceVOList = {}", physicalResourceVOList.size());

        Map<String, WorkOrderVO> workOrderIdMaps = workOrderVOList.stream().collect(Collectors.toMap(WorkOrderVO::getId, Function.identity()));

        Map<String, OperationVO> operationVoMapOfId = sourceOperations.stream().collect(Collectors.toMap(OperationVO::getId, item -> item));


        Map<String, PhysicalResourceVO> physicalResourceVOMap = physicalResourceVOList.stream().collect(Collectors.toMap(PhysicalResourceVO::getId, item -> item));

        //规则排程结果只需更新demand 和supply的时间
        List<RzzOperationDTO> allOperations = new ArrayList<>();
        List<OperationTaskDTO> operationTasks = new ArrayList<>();
        List<OperationSubTaskDTO> allSubTasks = new ArrayList<>();
        List<WorkOrderDTO> workOrderDTOS = new ArrayList<>();
        List<WorkOrderDTO> updateWorkOrders = new ArrayList<>();
        List<String> operationIds = new ArrayList<>();
        List<String> workOrderIds = new ArrayList<>();
        List<String> needDeleteOperationTaskOperationIds = new ArrayList<>();

        handleOperation(
                logId,
                operationVecsa,
                workOrderIdMaps,
                workOrderIds,
                workOrderDTOS,
                operationVoMapOfId,
                allOperations,
                needDeleteOperationTaskOperationIds,
                ruleEncodings,
                physicalResourceVOMap,
                operationTasks,
                allSubTasks,
                operationIds,
                planningHorizon,
                operationOnResourceIdMap,
                standardStepVOMap
        );
        Map<String, List<RzzOperationDTO>> orderOperationMap = MapUtil.newHashMap();
        if (isAdjust) {
            if (CollectionUtils.isNotEmpty(workOrderDTOS)) {
                //手动编辑需要补全所有工序
                Set<String> adjustWorkOrderIds = workOrderDTOS.stream().map(WorkOrderDTO::getId).collect(Collectors.toSet());
                Map<String, Object> param = MapUtil.newHashMap();
                param.put("workOrderIds", adjustWorkOrderIds);
                List<RzzOperationDTO> adjustOperations = operationTaskExtDao.selectOperationInfoByWorkOrderIds(param);
                if (CollectionUtils.isNotEmpty(adjustOperations)) {
                    orderOperationMap.putAll(adjustOperations.stream().collect(Collectors.groupingBy(RzzOperationDTO::getOrderId)));
                }
            }
        }

        //获取父工序
        List<RzzOperationDTO> parentOperationList = allOperations.stream().filter(item -> StringUtils.isBlank(item.getParentId())).collect(Collectors.toList());
        //获取子工序
        List<RzzOperationDTO> childOperationList = allOperations.stream().filter(item -> StringUtils.isNotBlank(item.getParentId())).collect(Collectors.toList());
        Map<String, List<RzzOperationDTO>> childOperationsMapOfParentId = childOperationList.stream().collect(Collectors.groupingBy(RzzOperationDTO::getParentId));
        //根据子工序计算父工序的开始和结束时间
        //demand和supply的时间按照父工序计算(子工序不影响demand和supply时间)，获取每个父工序下面的所有子工序

        List<RzzOperationDTO> updateParentOperations = new ArrayList<>();
        for (RzzOperationDTO parentOperationDTO : parentOperationList) {
            List<RzzOperationDTO> subOperations = childOperationsMapOfParentId.get(parentOperationDTO.getId());
            //子工序为空
            if (CollectionUtils.isEmpty(subOperations)) {
                //子工序没了，直接重置父工序信息
                setParentOperationFields(parentOperationDTO, Lists.newArrayList());
                updateParentOperations.add(parentOperationDTO);
                parentOperationDTO.setPlannedResourceId(null);
                continue;
            }
            List<Date> childOperationDates = getOperationsDates(subOperations);
            if (CollectionUtils.isNotEmpty(childOperationDates)) {
                parentOperationDTO.setStartTime(CommonDateUtils.getEarliest(childOperationDates));
                parentOperationDTO.setEndTime(CommonDateUtils.getLatest(childOperationDates));
                //计算父工序属性：状态、计划资源、工器具资源、制造开始时间、制造结束时刻
                setParentOperationFields(parentOperationDTO, subOperations);
            }
            updateParentOperations.add(parentOperationDTO);
        }

        Map<String, List<RzzOperationDTO>> parentOperationMapOfOrderId = parentOperationList.stream().collect(Collectors.groupingBy(RzzOperationDTO::getOrderId));
        for (WorkOrderDTO workOrderDTO : workOrderDTOS) {
            List<RzzOperationDTO> originRzzOperationDTOS = orderOperationMap.getOrDefault(workOrderDTO.getId(), Lists.newArrayList());
            //获取制造订单关联的父订单
            List<RzzOperationDTO> operationDTOS = parentOperationMapOfOrderId.get(workOrderDTO.getId());
            Set<String> calcOperationIds = operationDTOS.stream().map(RzzOperationDTO::getId).collect(Collectors.toSet());
            originRzzOperationDTOS.removeIf(rzzOperationDTO -> calcOperationIds.contains(rzzOperationDTO.getId()));
            originRzzOperationDTOS.addAll(operationDTOS);
            //计算制造订单开始和结束时间
            List<Date> childWorkOrderDates = getWorkOrderDates(originRzzOperationDTOS);
            //计算最早开始时间、最晚结束时间计算值
            List<Date> workOrderCalcTimes = getWorkOrderCalcTimes(originRzzOperationDTOS);

            //赋值时间相关字段
            setWorkOrderDateFields(workOrderDTO, childWorkOrderDates, workOrderCalcTimes);

            //计算制造订单状态
            workOrderDTO.setPlanStatus(getWorkOrderStatus(originRzzOperationDTOS, workOrderDTO));
            //计算是否延期
            if (workOrderDTO.getDueDate() != null && workOrderDTO.getEndTime() != null) {
                workOrderDTO.setDelayStatus(workOrderDTO.getEndTime().compareTo(workOrderDTO.getDueDate()) > 0 ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            }
            updateWorkOrders.add(workOrderDTO);
        }
        //获取父订单
        Map<String, List<WorkOrderDTO>> collect = workOrderDTOS.stream().filter(item -> StringUtils.isNotBlank(item.getParentId())).collect(Collectors.groupingBy(WorkOrderDTO::getParentId));
        for (Map.Entry<String, List<WorkOrderDTO>> entry : collect.entrySet()) {
            //父订单ID
            String parentWorkOrderId = entry.getKey();
            WorkOrderVO workOrderVO = workOrderIdMaps.get(parentWorkOrderId);
            WorkOrderDTO workOrderDTO = WorkOrderConvertor.INSTANCE.vo2Dto(workOrderVO);
            if (Objects.isNull(workOrderVO)) {
                continue;
            }
            //关联的子制造订单集合
            List<WorkOrderDTO> childWorkOrderList = entry.getValue();
            //计算制造订单开始和结束时间
            List<Date> childWorkOrderDates = getParentWorkOrderDates(childWorkOrderList);
            //计算最早开始时间、最晚结束时间计算值
            List<Date> workOrderCalcTimes = getParentWorkOrderCalcTimes(childWorkOrderList);

            //赋值时间相关字段
            setWorkOrderDateFields(workOrderDTO, childWorkOrderDates, workOrderCalcTimes);

            //计算父制造订单状态
            workOrderDTO.setPlanStatus(getParentWorkOrderStatus(childWorkOrderList));
            //计算是否延期
            if (workOrderDTO.getDueDate() != null && workOrderDTO.getEndTime() != null) {
                workOrderDTO.setDelayStatus(workOrderDTO.getEndTime().compareTo(workOrderDTO.getDueDate()) > 0 ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            }
            updateWorkOrders.add(workOrderDTO);
        }
        //删除更新的工序task和subTask
        if (CollectionUtils.isNotEmpty(needDeleteOperationTaskOperationIds)) {
            //删除工序任务(删除工序任务和工序子任务)
            this.doDeleteTaskAndSubTaskByOperationIds(needDeleteOperationTaskOperationIds);
        }
        //非手动排程删除排程成功的operation下面的task，
        if (CollectionUtils.isNotEmpty(operationIds)) {
            operationIds = operationIds.stream().distinct().collect(Collectors.toList());
            List<String> deleteOperationIds = operationIds;
            List<OperationVO> deleteOperationList = sourceOperations.stream()
                    .filter(t -> deleteOperationIds.contains(t.getId())).collect(Collectors.toList());
            operationDeleteLogService.record(deleteOperationList, logId + "-二次排程未排上");
            //删除工序
            operationService.doDelete(operationIds);
            //删除工序拓展表数据信息
            operationExtendService.doDeleteByOperationIds(operationIds);

            //删除工序任务(删除工序任务和工序子任务)
            this.doDeleteTaskAndSubTaskByOperationIds(operationIds);
            log.info("手动排程删除任务成功");
        }
        //系统已有工序（子工序）
        List<RzzOperationDTO> updateOperations = allOperations.stream().filter(item -> !item.getAlgo() && StringUtils.isNotBlank(item.getParentId())).collect(Collectors.toList());
        //本次算法生成的子工序
        List<RzzOperationDTO> insertOperations = allOperations.stream().filter(item -> item.getAlgo() && StringUtils.isNotBlank(item.getParentId())).collect(Collectors.toList());

        List<RzzOperationDTO> allUpdateOperations = new ArrayList<>(updateParentOperations);
        if (!CollectionUtils.isEmpty(updateOperations)) {
            updateOperations.forEach(item ->
                    {
                        item.setAlgo(false);
                        item.setEnabled(YesOrNoEnum.YES.getCode());
                    }
            );
            allUpdateOperations.addAll(updateOperations);
        }
        long operationUpdateStartTime = System.currentTimeMillis();

        this.doOperationUpdateBatch(allUpdateOperations);

        log.info("工序更新时间 ：" + (System.currentTimeMillis() - operationUpdateStartTime));

        if (CollectionUtils.isNotEmpty(insertOperations)) {
            insertOperations.forEach(item -> item.setAlgo(false));
            long operationInsertStartTime = System.currentTimeMillis();
            this.doOperationCreateBatch(insertOperations);
            log.info("工序插入时间 ：" + (System.currentTimeMillis() - operationInsertStartTime));
        }
        //更新制造订单
        long s4 = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(updateWorkOrders)) {
            List<WorkOrderPO> workOrderPOList = WorkOrderConvertor.INSTANCE.dto2Pos(updateWorkOrders);
            BasePOUtils.updateBatchFiller(workOrderPOList);
            for (List<WorkOrderPO> workOrderPOS : CollectionUtils.splitList(workOrderPOList, 2000)) {
                operationTaskExtDao.updateBatchWorkOrderByCaseWhen(workOrderPOS);
            }
        }
        log.info("算法结果解析workOrderService-更新数据耗时 4：" + (System.currentTimeMillis() - s4));
        //demand、supply 时间更新
        List<String> parentOperationId = allOperations.stream().filter(item -> StringUtils.isBlank(item.getParentId())).collect(Collectors.toList()).stream().map(RzzOperationDTO::getId).collect(Collectors.toList());
        Map<String, RzzOperationDTO> operationDTOMapOfId = allOperations.stream().collect(Collectors.toMap(RzzOperationDTO::getId, item -> item));
        //计算demand和supply时间公用方法
        rzzAMSSupportService.calculateCommon(workOrderIdMaps, operationDTOMapOfId, parentOperationId, planningHorizon);
        long s6 = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(operationTasks)) {
            operationTaskService.doCreateBatch(Lists.newArrayList(operationTasks));
        }
        if (CollectionUtils.isNotEmpty(allSubTasks)) {
            operationSubTaskService.doCreateBatch(Lists.newArrayList(allSubTasks));
        }
        log.info("算法结果解析-更新数据耗时7：" + (System.currentTimeMillis() - s6));

        log.info("算法结果解析完成时间{}", System.currentTimeMillis());
    }

    /**
     * 获取工序变更信息
     *
     * @param logId
     * @param originOperation
     * @return
     */
    private SdsOrdOperationHistoryLogPO getSdsOrdOperationHistoryLogDTO(String logId, OperationVO originOperation) {
        SdsOrdOperationHistoryLogPO sdsOrdOperationHistoryLogPO = new SdsOrdOperationHistoryLogPO();
        sdsOrdOperationHistoryLogPO.setId(UUID.randomUUID().toString());
        sdsOrdOperationHistoryLogPO.setLogId(logId);
        sdsOrdOperationHistoryLogPO.setWorkOrderId(originOperation.getOrderId());
        sdsOrdOperationHistoryLogPO.setOperationId(originOperation.getId());
        sdsOrdOperationHistoryLogPO.setParentOperationId(originOperation.getParentId());
        sdsOrdOperationHistoryLogPO.setPlanStatus(originOperation.getPlanStatus());
        sdsOrdOperationHistoryLogPO.setSourcePlannedResourceId(originOperation.getPlannedResourceId());
        sdsOrdOperationHistoryLogPO.setSourceStartTime(originOperation.getProductionStartTime());
        sdsOrdOperationHistoryLogPO.setSourceEndTime(originOperation.getProductionEndTime());
        return sdsOrdOperationHistoryLogPO;
    }

    public void doDeleteTaskAndSubTaskByOperationIds(List<String> operationIds) {
        if (CollectionUtils.isEmpty(operationIds)) {
            return;
        }
        this.operationTaskExtDao.deleteTaskByOperationIds(operationIds);
        this.operationTaskExtDao.deleteSubTaskByOperationIds(operationIds);
    }

    public void handleOperation(String logId, List<Operation> operationVecsa, Map<String, WorkOrderVO> workOrderIdMaps, List<String> workOrderIds,
                                List<WorkOrderDTO> workOrderDTOS, Map<String, OperationVO> operationVoMapOfId,
                                List<RzzOperationDTO> allOperations, List<String> needDeleteOperationTaskOperationIds,
                                RuleEncodingsVO ruleEncodings, Map<String, PhysicalResourceVO> physicalResourceVOMap,
                                List<OperationTaskDTO> operationTasks, List<OperationSubTaskDTO> allSubTasks,
                                List<String> operationIds, PlanningHorizonVO planningHorizon,
                                Map<String, String> operationOnResourceIdMap,
                                Map<String, StandardStepVO> standardStepVOMap) {
        List<String> planStatusList = ListUtil.of(PlannedStatusEnum.STARTED.getCode(), PlannedStatusEnum.FINISHED.getCode());
        List<SdsOrdOperationHistoryLogPO> sdsOrdOperationHistoryLogPOS = Lists.newArrayList();
        //父工序
        for (Operation operationO : operationVecsa) {
            int index = 0;
            String parentId = operationO.getId();
            WorkOrderVO sourceWorkOrder = workOrderIdMaps.get(operationO.getWorkOrder().getId());
            if (Objects.isNull(sourceWorkOrder)) {
                log.error("结果解析父制造订单不存在，工单id：{}", operationO.getWorkOrder().getId());
                continue;
            }
            WorkOrderDTO workOrderDTO = WorkOrderConvertor.INSTANCE.vo2Dto(sourceWorkOrder);
            workOrderDTO.setDueDate(sourceWorkOrder.getDueDate());
            if (!workOrderIds.contains(sourceWorkOrder.getId())) {
                workOrderDTOS.add(workOrderDTO);
            }
            workOrderIds.add(sourceWorkOrder.getId());
            //遍历父工序,获取父工序当前流水号
            Map<String, Integer> parentOperationIdToSerialNumMaps = new HashMap<>();
            OperationVO parentOperationVO = operationVoMapOfId.get(operationO.getId());
            RzzOperationDTO parentOperationDTO = new RzzOperationDTO();
            BeanUtils.copyProperties(parentOperationVO, parentOperationDTO);
            SdsOrdOperationHistoryLogPO parentHistoryLogDTO = getSdsOrdOperationHistoryLogDTO(logId, parentOperationVO);
            //解析未排程原因
            parentOperationDTO.setUnscheduledReason(null);
            JsonElement failedReason = operationO.getFailedReason();
            if (Objects.nonNull(failedReason) && failedReason.isJsonArray()) {
                JsonArray asJsonArray = failedReason.getAsJsonArray();
                if (!asJsonArray.isEmpty()) {
                    JsonElement type = asJsonArray.get(0).getAsJsonObject().get("type");
                    parentOperationDTO.setUnscheduledReason(type.getAsString());
                    parentOperationDTO.setAlgo(Boolean.FALSE);
                }
            }
            try {
                LocalDateTime earliestStartO = operationO.getEarliestStart();
                LocalDateTime latestEndO = operationO.getLatestEnd();
                if (null != earliestStartO) {
                    // parentOperationDTO.setEarliestStartTime(Date.from(earliestStartO.atZone(ZoneId.systemDefault()).toInstant()));
                    parentOperationDTO.setCalcEarliestStartTime(Date.from(earliestStartO.atZone(ZoneId.systemDefault()).toInstant()));
                }
                if (null != latestEndO) {
                    // parentOperationDTO.setLatestEndTime(Date.from(latestEndO.atZone(ZoneId.systemDefault()).toInstant()));
                    parentOperationDTO.setCalcLatestEndTime(Date.from(latestEndO.atZone(ZoneId.systemDefault()).toInstant()));
                }
            } catch (Exception e) {
                log.error("结果解析父工序开始结束时间异常，工单id：{}", operationO.getWorkOrder().getId());
            }
            allOperations.add(parentOperationDTO);
            sdsOrdOperationHistoryLogPOS.add(parentHistoryLogDTO);
            //获取子工序
            List<Operation> splittedOperations = operationO.getSplittedOperations();
            boolean whetherHandle = false;
            if (CollectionUtils.isEmpty(splittedOperations)) {
                String standardStepId = parentOperationVO.getStandardStepId();
                StandardStepVO standardStepVO = standardStepVOMap.get(standardStepId);
                if (Objects.nonNull(standardStepVO) && standardStepVO.getStandardStepType().equals(StandardStepEnum.FORMING_PROCESS.getCode())) {
                    // 首次排程，直接就是没排上，如果原先就是未计划，即新生成的制造订单展开的工序，则存为优先级最高的资源
                    OperationWrapper operation = new OperationWrapper();
                    operation.setId(UUIDUtil.getUUID());
                    operation.setQuantity(parentOperationVO.getQuantity().doubleValue());
                    operation.setIndex(0);
                    String resourceId = operationOnResourceIdMap.get(parentOperationVO.getId());
                    if (StrUtil.isEmpty(resourceId)) {
                        log.warn("工序：{}，排程失败，未找到对应资源，默认设置排程信息失败", parentOperationVO.getId());
                        continue;
                    }
                    // 构建制造任务
                    OperationSchedule workSchedule = getWorkSchedule(operation, planningHorizon, resourceId);
                    operation.setSchedule(workSchedule);
                    splittedOperations = Lists.newArrayList(operation);
                    whetherHandle = true;
                } else {
                    // 排程非关键工序没排上，不处理
                    continue;
                }
            }

            //获取存在的子工序
            List<OperationVO> subOperationsOfParentOperations = new ArrayList<>();
            for (Operation splittedOperation : splittedOperations) {
                OperationVO operationVO = operationVoMapOfId.get(splittedOperation.getId());
                if (null == operationVO) {
                    continue;
                }
                // 已存在的子工序做更新操作，删除对应的task和subtask重新生成
                needDeleteOperationTaskOperationIds.add(operationVO.getId());
                subOperationsOfParentOperations.add(operationVO);
            }
            if (CollectionUtils.isNotEmpty(subOperationsOfParentOperations)) {
                //如果父工序在当前系统中已经存在子工序，获取子工序的最大流水号
                Optional<OperationVO> optional = subOperationsOfParentOperations.stream().sorted(Comparator.comparing(OperationVO::getOperationCode).reversed()).findFirst();
                String maxOperationCodeOfParentOperation = "";
                if (optional.isPresent()) {
                    maxOperationCodeOfParentOperation = optional.get().getOperationCode();
                }
                String[] split = maxOperationCodeOfParentOperation.split("-");
                //当前最大序号
                index = Integer.parseInt(split[split.length - 1]);
            }
            parentOperationIdToSerialNumMaps.put(parentId, index);
            parentOperationDTO.setPartitionType(PartitionTypeEnum.PARENT_OPERATION.getCode());
            String plannedResourceId = null;
            //子工序
            for (Operation splittedOperation : splittedOperations) {
                SdsOrdOperationHistoryLogPO subHistoryLogPO = new SdsOrdOperationHistoryLogPO();
                subHistoryLogPO.setId((UUIDUtil.getUUID()));
                subHistoryLogPO.setLogId(logId);
                subHistoryLogPO.setWorkOrderId(sourceWorkOrder.getId());
                subHistoryLogPO.setOperationId(splittedOperation.getId());
                subHistoryLogPO.setParentOperationId(parentId);
                sdsOrdOperationHistoryLogPOS.add(subHistoryLogPO);
                OperationVO operationVO = operationVoMapOfId.get(splittedOperation.getId());
                if (Objects.nonNull(operationVO)) {
                    subHistoryLogPO.setPlanStatus(operationVO.getPlanStatus());
                    subHistoryLogPO.setSourcePlannedResourceId(operationVO.getPlannedResourceId());
                    subHistoryLogPO.setSourceStartTime(operationVO.getProductionStartTime());
                    subHistoryLogPO.setSourceEndTime(operationVO.getProductionEndTime());
                }
                // 获取所有子任务信息
                OperationSchedule childSchedule = splittedOperation.getSchedule();
                if (null == childSchedule) {
                    if (Objects.nonNull(operationVO) && planStatusList.contains(operationVO.getPlanStatus())) {
                        // 工序已开始或已完工，排程失败，则保留原先的计划开始、结束时间，不处理当前工序
                        log.warn("已完工/已开始工序，排程失败，不处理工序：{}", splittedOperation.getId());
                        continue;
                    }
                    if (StringUtils.isNotBlank(splittedOperation.getId())) {
                        // 处理排程失败的子工序，强行排
                        Operation failOperation = handleScheduleFailOperation(splittedOperation, operationVO, planningHorizon);
                        if (Objects.isNull(failOperation)) {
                            //原有子工序，此次排程失败，并且重置排程信息也失败，则删除该子工序
                            operationIds.add(splittedOperation.getId());
                            continue;
                        }
                        // 重置未排上的子工序
                        splittedOperation = failOperation;
                        childSchedule = splittedOperation.getSchedule();
                        log.warn("子工序：{}，排程失败，重置计划时间及资源", splittedOperation.getId());
                        whetherHandle = true;
                    }
                }

                RzzOperationDTO operationDTO;
//                OperationVO operationVO = operationVoMapOfId.get(splittedOperation.getId());
                if (null == operationVO) {
                    //算法新生成的工序
                    Integer newSerialNum = parentOperationIdToSerialNumMaps.get(parentId) + 1;
                    String serialNumber = String.valueOf(newSerialNum);
                    parentOperationIdToSerialNumMaps.put(parentId, newSerialNum);
                    operationDTO = createOperation(sourceWorkOrder, operationVoMapOfId.get(parentId), null, serialNumber, ruleEncodings);
                } else {
                    operationDTO = new RzzOperationDTO();
                    BeanUtils.copyProperties(operationVO, operationDTO);
                    operationDTO.setAlgo(Boolean.FALSE);
                }
                LocalDateTime earliestStart = splittedOperation.getEarliestStart();
                LocalDateTime latestEnd = splittedOperation.getLatestEnd();
                if (null != earliestStart) {
                    //operationDTO.setEarliestStartTime(Date.from(earliestStart.atZone(ZoneId.systemDefault()).toInstant()));
                    operationDTO.setCalcEarliestStartTime(Date.from(earliestStart.atZone(ZoneId.systemDefault()).toInstant()));
                }
                if (null != latestEnd) {
                    //operationDTO.setLatestEndTime(Date.from(latestEnd.atZone(ZoneId.systemDefault()).toInstant()));
                    operationDTO.setCalcLatestEndTime(Date.from(latestEnd.atZone(ZoneId.systemDefault()).toInstant()));
                }
                operationDTO.setPartitionType(PartitionTypeEnum.SON_OPERATION.getCode());
                operationDTO.setQuantity(BigDecimal.valueOf(splittedOperation.getQuantity()));
                operationDTO.setOperationIndex((int) splittedOperation.getIndex());
                operationDTO.setParentId(parentId);

                List<WorkSchedule> workSchedules = childSchedule.getWorkSchedules();
                // 根据任务类型分组
                Map<String, List<Schedule>> scheduleMap = new HashMap<>();
                for (Schedule schedule : workSchedules) {
                    ScheduleType type = schedule.getType();
                    List<Schedule> scheduleList = scheduleMap.get(type.toString());
                    if (CollectionUtils.isNotEmpty(scheduleList)) {
                        scheduleList.add(schedule);
                    } else {
                        scheduleMap.put(type.toString(), new ArrayList<>(Collections.singletonList(schedule)));
                    }
                }
                //计算子工序的开始和结束时间
                List<Date> childOperationStartDateList = new ArrayList<>();
                List<Date> childOperationEndDateList = new ArrayList<>();
                //制造任务
                operationDTO.setProductionStartTime(getEarliestByScheduleType(scheduleMap.get("ProcessTask")));
                operationDTO.setProductionEndTime(getLatestByScheduleType(scheduleMap.get("ProcessTask")));
                operationDTO.setLastProductionStartTime(getEarliestByScheduleType(scheduleMap.get("ProcessTask")));
                operationDTO.setLastProductionEndTime(getLatestByScheduleType(scheduleMap.get("ProcessTask")));
                if (null != operationDTO.getProductionStartTime()) {
                    childOperationStartDateList.add(operationDTO.getProductionStartTime());
                }
                if (null != operationDTO.getProductionEndTime()) {
                    childOperationEndDateList.add(operationDTO.getProductionEndTime());
                }
                //设置任务
                operationDTO.setSetupStartTime(getEarliestByScheduleType(scheduleMap.get("SetupTask")));
                operationDTO.setSetupEndTime(getLatestByScheduleType(scheduleMap.get("SetupTask")));
                operationDTO.setLastSetupStartTime(getEarliestByScheduleType(scheduleMap.get("SetupTask")));
                operationDTO.setLastSetupEndTime(getLatestByScheduleType(scheduleMap.get("SetupTask")));
                if (null != operationDTO.getSetupStartTime()) {
                    childOperationStartDateList.add(operationDTO.getSetupStartTime());
                }
                if (null != operationDTO.getSetupEndTime()) {
                    childOperationEndDateList.add(operationDTO.getSetupEndTime());
                }
                //清洗任务
                operationDTO.setCleanupStartTime(getEarliestByScheduleType(scheduleMap.get("CleanupTask")));
                operationDTO.setCleanupEndTime(getLatestByScheduleType(scheduleMap.get("CleanupTask")));
                operationDTO.setLastCleanupEndTime(getEarliestByScheduleType(scheduleMap.get("CleanupTask")));
                operationDTO.setLastCleanupEndTime(getLatestByScheduleType(scheduleMap.get("CleanupTask")));
                if (null != operationDTO.getCleanupStartTime()) {
                    childOperationStartDateList.add(operationDTO.getCleanupStartTime());
                }
                if (null != operationDTO.getCleanupEndTime()) {
                    childOperationEndDateList.add(operationDTO.getCleanupEndTime());
                }
                operationDTO.setStartTime(CommonDateUtils.getEarliest(childOperationStartDateList));
                operationDTO.setEndTime(CommonDateUtils.getLatest(childOperationEndDateList));


                List<Date> operationDates = getOperationDates(operationDTO);
                //工序切割出来的子工序， 并且没有具体排程时间
                if (StringUtils.isEmpty(splittedOperation.getId()) && CollectionUtils.isEmpty(operationDates)) {
                    continue;
                }
                if (PlannedStatusEnum.UNPLAN.getCode().equals(operationDTO.getPlanStatus())) {
                    //算法结果返回，只有未计划的工序需要改变状态
                    operationDTO.setPlanStatus(PlannedStatusEnum.PLANNED.getCode());
                }
                if (Objects.isNull(parentHistoryLogDTO.getTargetStartTime())) {
                    parentHistoryLogDTO.setTargetStartTime(operationDTO.getProductionStartTime());
                } else {
                    if (operationDTO.getProductionStartTime().before(parentHistoryLogDTO.getTargetStartTime())) {
                        parentHistoryLogDTO.setTargetStartTime(operationDTO.getProductionStartTime());
                    }
                }
                if (Objects.isNull(parentHistoryLogDTO.getTargetEndTime())) {
                    parentHistoryLogDTO.setTargetEndTime(operationDTO.getProductionEndTime());
                } else {
                    if (operationDTO.getProductionEndTime().after(parentHistoryLogDTO.getTargetEndTime())) {
                        parentHistoryLogDTO.setTargetEndTime(operationDTO.getProductionEndTime());
                    }
                }
                Set<String> resourceIdSet = new HashSet<>();
                StringBuilder mainResourceIds = new StringBuilder();
                StringBuilder toolResourceIds = new StringBuilder();
                for (Schedule workSchedule : workSchedules) {
                    Set<com.yhl.aps.api.obj.Resource> assignedResources = workSchedule.getAssignedResources();
                    for (com.yhl.aps.api.obj.Resource assignedResource : assignedResources) {
                        if (resourceIdSet.contains(assignedResource.getId())) {
                            continue;
                        }
                        PhysicalResourceVO physicalResourceVO = physicalResourceVOMap.get(assignedResource.getId());
                        if (null != physicalResourceVO) {
                            if (StringUtils.equals(physicalResourceVO.getResourceCategory(), ResourceCategoryEnum.MAIN.getCode())) {
                                mainResourceIds.append(physicalResourceVO.getId()).append(",");
                            } else {
                                toolResourceIds.append(physicalResourceVO.getId()).append(",");
                            }
                        }
                        resourceIdSet.add(assignedResource.getId());
                    }
                }
                operationDTO.setPlannedMainResourceId(removeEnd(mainResourceIds.toString(), ","));
                operationDTO.setPlannedToolResourceId(removeEnd(toolResourceIds.toString(), ","));
                operationDTO.setLastMainResourceId(removeEnd(mainResourceIds.toString(), ","));
                operationDTO.setLastToolResourceId(removeEnd(toolResourceIds.toString(), ","));
                if (whetherHandle) {
                    operationDTO.setRemark("后处理未计划工序");
                }
                allOperations.add(operationDTO);
                List<OperationSubTaskDTO> subTasks = new ArrayList<>();
                List<OperationTaskDTO> operationTask = getOperationTasks(workSchedules, operationDTO, subTasks, physicalResourceVOMap, operationIds);
                if (operationIds.contains(operationDTO.getId())) {
                    allOperations.remove(operationDTO);
                }
                //todo 这里看一下standardResourceId什么时候赋值的
                operationTasks.addAll(operationTask);
                OperationTaskDTO operationTaskDTO = operationTask.stream().filter(t ->
                                physicalResourceVOMap.containsKey(t.getPhysicalResourceId())
                                        && ResourceCategoryEnum.MAIN.getCode().equals(physicalResourceVOMap.get(t.getPhysicalResourceId()).getResourceCategory()))
                        .findFirst().orElse(null);
                if (operationTaskDTO != null) {
                    operationDTO.setPlannedResourceId(operationTaskDTO.getPhysicalResourceId());
                    plannedResourceId = operationTaskDTO.getPhysicalResourceId();
                }

                subHistoryLogPO.setTargetPlannedResourceId(plannedResourceId);
                subHistoryLogPO.setTargetStartTime(operationDTO.getProductionStartTime());
                subHistoryLogPO.setTargetEndTime(operationDTO.getProductionEndTime());
                allSubTasks.addAll(subTasks);
            }
            if (plannedResourceId != null) {
                parentOperationDTO.setPlannedResourceId(plannedResourceId);
                parentHistoryLogDTO.setTargetPlannedResourceId(plannedResourceId);
            }
        }
        saveOperationHistoryLog(sdsOrdOperationHistoryLogPOS);
    }

    /**
     * 保存operation历史记录
     *
     * @param sdsOrdOperationHistoryLogPOS
     */
    private void saveOperationHistoryLog(List<SdsOrdOperationHistoryLogPO> sdsOrdOperationHistoryLogPOS) {
        if (CollectionUtils.isEmpty(sdsOrdOperationHistoryLogPOS)) {
            return;
        }
        List<SdsOrdOperationHistoryLogPO> historyLogPOS = sdsOrdOperationHistoryLogPOS.stream().filter(x -> StringUtils.isNotBlank(x.getLogId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(historyLogPOS)) {
            return;
        }
        String scenario = SystemHolder.getScenario();
        CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            log.info("开始记录operation历史信息");
            List<List<SdsOrdOperationHistoryLogPO>> partition = Lists.partition(historyLogPOS, 2000);
            try {
                for (List<SdsOrdOperationHistoryLogPO> ordOperationHistoryLogPOS : partition) {
                    BasePOUtils.insertBatchFiller(ordOperationHistoryLogPOS);
                    sdsOrdOperationHistoryLogDao.insertBatch(ordOperationHistoryLogPOS);
                }
            } catch (Exception ex) {
                log.error("生成operation历史记录失败：{}", ex.getMessage());
            }
            log.info("记录operation历史信息结束");
            DynamicDataSourceContextHolder.clearDataSource();
        });

    }

    private Operation handleScheduleFailOperation(Operation splittedOperation, OperationVO operationVO, PlanningHorizonVO planningHorizon) {
        // 自动排产、手工调整以后，结果解析时，未计划的工序，开始结束时间都更新为计划期间结束，状态还是已计划
        // 资源：如果原先是已计划，则保留原资源(子工序id不变)，如果原先就是未计划，即新生成的制造订单展开的工序，则存为优先级最高的资源
        if (Objects.isNull(operationVO)) {
            return null;
        }
        OperationWrapper operation = new OperationWrapper();
        operation.setId(splittedOperation.getId());
        operation.setQuantity(operationVO.getQuantity().doubleValue());
        operation.setIndex(splittedOperation.getIndex());
        // 构建制造任务
        OperationSchedule workSchedule = getWorkSchedule(operation, planningHorizon, operationVO.getPlannedResourceId());
        operation.setSchedule(workSchedule);
        return operation;
    }

    private OperationSchedule getWorkSchedule(OperationWrapper operation, PlanningHorizonVO planningHorizon, String resourceId) {
        OperationSchedule operationSchedule = new OperationSchedule(operation);
        // 资源
        ProcessSchedule workSchedule = new ProcessSchedule();
        SingleResource singleResource = new SingleResource();
        singleResource.setId(resourceId);
        singleResource.setResourceId(resourceId);
        workSchedule.setResources(SetUtils.hashSet(singleResource));
        // 主资源
        Solution solution = new Solution(singleResource);
        CandidateResource candidateResource = new CandidateResource(solution);
        candidateResource.setResourceId(resourceId);
        candidateResource.setId(resourceId);
        operation.setCandidateResource(ListUtil.of(candidateResource));
        // 默认不构建工具资源
        CandidateResources candidateResources = new CandidateResources(candidateResource, new ArrayList<>());
        operationSchedule.setCandidateResources(candidateResources);
        workSchedule.setOperationSchedule(operationSchedule);
        // 时间
        LocalDateTime start = planningHorizon.getPlanEndTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime end = planningHorizon.getPlanEndTime().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDateTime();
        TimeBlockForPoint timeBlockForPoint = new TimeBlockForPoint(start, end);
        TimeBlock timeBlock = new TimeBlock(timeBlockForPoint);
        workSchedule.setTimeBlock(timeBlock);
        operationSchedule.setProcessSchedule(workSchedule);
        return operationSchedule;
    }

    /**
     * 工序数据更新
     *
     * @param
     */
    private void doOperationUpdateBatch(List<RzzOperationDTO> operList) {
        if (CollectionUtils.isEmpty(operList)) {
            return;
        }
        List<OperationExtendDTO> operationExtendDTOList = new ArrayList<>();
        List<OperationPO> newList = new ArrayList<>();
        // 修改扩展信息
        for (RzzOperationDTO operationDTO : operList) {
            OperationExtendDTO operationExtendDTO = new OperationExtendDTO();
            operationDtoSplit(operationDTO, operationExtendDTO);
            operationExtendDTOList.add(operationExtendDTO);
            newList.add(operationDtoToPo(operationDTO));
        }

//        BasePOUtils.updateBatchFiller(newList);
//        List<List<OperationPO>> lists = com.yhl.platform.common.utils.CollectionUtils.splitList(newList, 2000);
//        for (List<OperationPO> operationPOS : lists) {
//            operationDao.updateBatch(operationPOS);
//        }
//        //修改扩展表信息
//        List<List<OperationExtendDTO>> operationExtendList = CollectionUtils.splitList(operationExtendDTOList, 2000);
//        for (List<OperationExtendDTO> operationExtendDTOS : operationExtendList) {
//            operationExtendService.doUpdateBatch(operationExtendDTOS);
//        }

        BasePOUtils.updateBatchFiller(newList);
        List<List<OperationPO>> lists = com.yhl.platform.common.utils.CollectionUtils.splitList(newList, 2000);
        for (List<OperationPO> operationPOS : lists) {
            operationTaskExtDao.updateBatchOperations(operationPOS);
        }
        //修改扩展表信息
        List<OperationExtendPO> operationExtendPOList = OperationExtendConvertor.INSTANCE.dto2Pos(operationExtendDTOList);
        BasePOUtils.updateBatchFiller(operationExtendPOList);
        List<List<OperationExtendPO>> operationExtendList = CollectionUtils.splitList(operationExtendPOList, 2000);
        for (List<OperationExtendPO> operationExtendPOS : operationExtendList) {
            operationTaskExtDao.updateBatchOperationExtend(operationExtendPOS);
        }
    }

    /**
     * 工序数据新增
     *
     * @param
     */
    private void doOperationCreateBatch(List<RzzOperationDTO> operList) {
        if (CollectionUtils.isEmpty(operList)) {
            return;
        }
        List<OperationExtendDTO> operationExtendDTOList = new ArrayList<>();
        List<OperationPO> newList = new ArrayList<>();
        // 修改扩展信息
        for (RzzOperationDTO operationDTO : operList) {
            OperationExtendDTO operationExtendDTO = new OperationExtendDTO();
            operationDtoSplit(operationDTO, operationExtendDTO);
            operationExtendDTO.setId(UUIDUtil.getUUID());
            operationExtendDTOList.add(operationExtendDTO);
            newList.add(operationDtoToPo(operationDTO));
        }
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, splitList -> operationDao.insertBatch(splitList), 2000);
        //修改扩展表信息
        BulkOperationUtils.bulkUpdateOrCreate(operationExtendDTOList, splitList -> operationExtendService.doCreateBatch(splitList), 2000);

    }

    private void operationDtoSplit(RzzOperationDTO operationDTO, OperationExtendDTO operationExtendDTO) {
        operationExtendDTO.setOperationId(operationDTO.getId());
        operationExtendDTO.setPreOperationIds(operationDTO.getPreOperationIds());
        operationExtendDTO.setResourceIds(operationDTO.getResourceIds());
        operationExtendDTO.setNextOperationIds(operationDTO.getNextOperationIds());
//        operationExtendDTO.setAppointProductionTime(operationDTO.getAppointProductionTime());
        operationExtendDTO.setAppointQuantity(operationDTO.getAppointQuantity());
        operationExtendDTO.setAppointMainResourceId(operationDTO.getAppointMainResourceId());
        operationExtendDTO.setAppointStartTime(operationDTO.getAppointStartTime());
        operationExtendDTO.setAppointEndTime(operationDTO.getAppointEndTime());
        operationExtendDTO.setPartitionRate(operationDTO.getPartitionRate());
        operationExtendDTO.setPartitionNum(operationDTO.getPartitionNum());
        operationExtendDTO.setPartitionBatch(operationDTO.getPartitionBatch());
        operationExtendDTO.setMaxPartitionBatch(operationDTO.getMaxPartitionBatch());
        operationExtendDTO.setMinPartitionBatch(operationDTO.getMinPartitionBatch());
        operationExtendDTO.setPartitionBatchUnit(operationDTO.getPartitionBatchUnit());
        operationExtendDTO.setPartitionMantissaDeal(operationDTO.getPartitionMantissaDeal());
        operationExtendDTO.setPlannedMainResourceId(operationDTO.getPlannedMainResourceId());
        operationExtendDTO.setLastMainResourceId(operationDTO.getLastMainResourceId());
        operationExtendDTO.setPlannedToolResourceId(operationDTO.getPlannedToolResourceId());
        operationExtendDTO.setLastToolResourceId(operationDTO.getLastToolResourceId());
        operationExtendDTO.setPlannedSkillId(operationDTO.getPlannedSkillId());
        operationExtendDTO.setLastSkillId(operationDTO.getLastSkillId());
        operationExtendDTO.setSetupStartTime(operationDTO.getSetupStartTime());
        operationExtendDTO.setLastSetupStartTime(operationDTO.getLastSetupStartTime());
        operationExtendDTO.setSetupEndTime(operationDTO.getSetupEndTime());
        operationExtendDTO.setLastSetupEndTime(operationDTO.getLastSetupEndTime() == null ? null : operationDTO.getLastSetupEndTime());
        operationExtendDTO.setSetupDuration(operationDTO.getSetupDuration());
        operationExtendDTO.setProductionStartTime(operationDTO.getProductionStartTime());
        operationExtendDTO.setLastProductionStartTime(operationDTO.getLastProductionStartTime());
        operationExtendDTO.setProductionEndTime(operationDTO.getProductionEndTime());
        operationExtendDTO.setLastProductionEndTime(operationDTO.getLastProductionEndTime());
//        operationExtendDTO.setProductionDuration(operationDTO.getProductionDuration());
        operationExtendDTO.setLockStartTime(operationDTO.getLockStartTime());
        operationExtendDTO.setLastLockStartTime(operationDTO.getLastLockStartTime());
        operationExtendDTO.setLockEndTime(operationDTO.getLockEndTime());
        operationExtendDTO.setLastLockEndTime(operationDTO.getLastLockEndTime());
        operationExtendDTO.setLockDuration(operationDTO.getLockDuration());
        operationExtendDTO.setCleanupStartTime(operationDTO.getCleanupStartTime());
        operationExtendDTO.setLastCleanupStartTime(operationDTO.getLastCleanupStartTime());
        operationExtendDTO.setCleanupEndTime(operationDTO.getCleanupEndTime());
        operationExtendDTO.setLastCleanupEndTime(operationDTO.getLastCleanupEndTime());
        operationExtendDTO.setCleanupDuration(operationDTO.getCleanupDuration());
        operationExtendDTO.setFeedFinishTime(operationDTO.getFeedFinishTime());
        operationExtendDTO.setUnscheduledReason(operationDTO.getUnscheduledReason());
        operationExtendDTO.setDelayReason(operationDTO.getDelayReason());
    }

    private OperationPO operationDtoToPo(RzzOperationDTO obj) {
        OperationPO operationPO = new OperationPO();
        operationPO.setId(obj.getId());
        operationPO.setRemark(obj.getRemark());
        operationPO.setEnabled(obj.getEnabled());
        operationPO.setVersionValue(obj.getVersionValue());
        operationPO.setOperationCode(obj.getOperationCode());
        operationPO.setOrderId(obj.getOrderId());
        operationPO.setPlanUnitId(obj.getPlanUnitId());
        operationPO.setRoutingStepId(obj.getRoutingStepId());
        operationPO.setStandardStepId(obj.getStandardStepId());
        operationPO.setRoutingStepSequenceNo(obj.getRoutingStepSequenceNo());
        operationPO.setPreRoutingStepSequenceNo(obj.getPreRoutingStepSequenceNo());
        operationPO.setNextRoutingStepSequenceNo(obj.getNextRoutingStepSequenceNo());
        operationPO.setProductStockPointId(obj.getProductStockPointId());
        operationPO.setProductId(obj.getProductId());
        operationPO.setStockPointId(obj.getStockPointId());
        operationPO.setQuantity(obj.getQuantity());
        operationPO.setPlannedResourceId(obj.getPlannedResourceId());
        operationPO.setFrozen(obj.getFrozen());
        operationPO.setPlanStatus(obj.getPlanStatus());
        operationPO.setOrderType(obj.getOrderType());
        operationPO.setKitStatus(obj.getKitStatus());
        operationPO.setProcessingTime(obj.getProcessingTime());
        operationPO.setStartTime(obj.getStartTime());
        operationPO.setEndTime(obj.getEndTime());
        operationPO.setEarliestStartTime(obj.getEarliestStartTime());
        operationPO.setLatestEndTime(obj.getLatestEndTime());
        operationPO.setCalcEarliestStartTime(obj.getCalcEarliestStartTime());
        operationPO.setCalcLatestEndTime(obj.getCalcLatestEndTime());
        operationPO.setConnectionTask(obj.getConnectionTask());
        operationPO.setConnectionType(obj.getConnectionType());
        operationPO.setMaxConnectionDuration(obj.getMaxConnectionDuration());
        operationPO.setMinConnectionDuration(obj.getMinConnectionDuration());
        operationPO.setOperationIndex(obj.getOperationIndex());
        operationPO.setParentId(obj.getParentId());
        operationPO.setUnscheduledReason(obj.getUnscheduledReason());
        return operationPO;
    }


    private void setWorkOrderDateFields(WorkOrderDTO workOrderDTO, List<Date> planDates, List<Date> calcTimes) {
        if (null == workOrderDTO) {
            log.warn("制造订单为空---------------------");
            return;
        }
        if (CollectionUtils.isNotEmpty(planDates)) {
            workOrderDTO.setStartTime(CommonDateUtils.getEarliest(planDates));
            workOrderDTO.setEndTime(CommonDateUtils.getLatest(planDates));
        } else {
            workOrderDTO.setStartTime(null);
            workOrderDTO.setEndTime(null);
        }
        //计算最早开始时间、最晚结束时间计算值
        calcTimes = calcTimes.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(calcTimes)) {
            workOrderDTO.setCalcEarliestStartTime(CommonDateUtils.getEarliest(calcTimes));
            workOrderDTO.setCalcLatestEndTime(CommonDateUtils.getLatest(calcTimes));
        } else {
            workOrderDTO.setCalcEarliestStartTime(null);
            workOrderDTO.setCalcLatestEndTime(null);
        }
    }

    /**
     * 计算父制造订单计算开始和结束时间
     *
     * @param childWorkOrderList 关联的子制造订单数据
     * @return 时间集合
     */
    private List<Date> getParentWorkOrderCalcTimes(List<WorkOrderDTO> childWorkOrderList) {
        List<Date> dates = new ArrayList<>();
        for (WorkOrderDTO workOrderDTO : childWorkOrderList) {
            if (workOrderDTO.getCalcEarliestStartTime() != null) {
                dates.add(workOrderDTO.getCalcEarliestStartTime());
            }
            if (workOrderDTO.getCalcLatestEndTime() != null) {
                dates.add(workOrderDTO.getCalcLatestEndTime());
            }
        }
        return dates;
    }

    /**
     * 计算未拆分或子订单的计算开始和结束时间
     *
     * @param operationDTOS 关联的父工序数据
     * @return 时间集合
     */
    private List<Date> getWorkOrderCalcTimes(List<RzzOperationDTO> operationDTOS) {
        List<Date> dates = new ArrayList<>();
        for (RzzOperationDTO operationDTO : operationDTOS) {
            if (operationDTO.getCalcEarliestStartTime() != null) {
                dates.add(operationDTO.getCalcEarliestStartTime());
            } else {
                dates.add(operationDTO.getEarliestStartTime());
            }
            if (operationDTO.getCalcLatestEndTime() != null) {
                dates.add(operationDTO.getCalcLatestEndTime());
            } else {
                dates.add(operationDTO.getLatestEndTime());
            }
        }
        return dates;
    }

    private List<Date> getParentWorkOrderDates(List<WorkOrderDTO> workOrderDTOList) {
        List<Date> dates = new ArrayList<>();
        for (WorkOrderDTO workOrderDTO : workOrderDTOList) {
            if (workOrderDTO.getStartTime() != null) {
                dates.add(workOrderDTO.getStartTime());
            }
            if (workOrderDTO.getEndTime() != null) {
                dates.add(workOrderDTO.getEndTime());
            }
        }
        return dates;
    }

    /**
     * 获取未拆分制造订单或子制造订单的开始结束时间
     *
     * @param parentOperationList 关联的工序数据
     * @return 开始结束时间集合
     */
    private List<Date> getWorkOrderDates(List<RzzOperationDTO> parentOperationList) {
        List<Date> dates = new ArrayList<>();
        for (RzzOperationDTO operationDTO : parentOperationList) {
            if (operationDTO.getStartTime() != null) {
                dates.add(operationDTO.getStartTime());
            }
            if (operationDTO.getEndTime() != null) {
                dates.add(operationDTO.getEndTime());
            }
        }
        return dates;
    }

    /**
     * 计算operation 一个类型任务的开始时间 取最早时间
     *
     * @param scheduleList
     * @return
     */
    private Date getEarliestByScheduleType(List<Schedule> scheduleList) {
        if (CollectionUtils.isEmpty(scheduleList)) {
            return null;
        }
        List<Date> dates = new ArrayList<>();
        for (Schedule schedule : scheduleList) {
            TimeBlock4FixedDur timeBlock = schedule.getTimeBlock();
            TimeBlock timeBlock1 = timeBlock.getTimeBlock();
            List<TimeSlotWithEfficiency> slots = timeBlock1.getSlots();
            for (TimeSlotWithEfficiency slot : slots) {
                LocalDateTime beginTime = slot.getBeginTime();
                dates.add(DateUtils.stringToDate(beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), DateUtils.COMMON_DATE_STR1));
            }
        }
        Collections.sort(dates, new Comparator<Date>() {
            @Override
            public int compare(Date o1, Date o2) {
                return o1.compareTo(o2);
            }

        });
        if (CollectionUtils.isEmpty(dates)) {
            return null;
        }
        return dates.get(0);
    }

    /**
     * 计算operation 一个类型任务的结束时间 取最晚时间
     *
     * @param scheduleList
     * @return
     */
    private Date getLatestByScheduleType(List<Schedule> scheduleList) {
        if (CollectionUtils.isEmpty(scheduleList)) {
            return null;
        }
        List<Date> dates = new ArrayList<>();
        for (Schedule schedule : scheduleList) {
            TimeBlock4FixedDur timeBlock = schedule.getTimeBlock();
            TimeBlock timeBlock1 = timeBlock.getTimeBlock();
            List<TimeSlotWithEfficiency> slots = timeBlock1.getSlots();
            for (TimeSlotWithEfficiency slot : slots) {
                LocalDateTime endTime = slot.getEndTime();
                dates.add(DateUtils.stringToDate(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), DateUtils.COMMON_DATE_STR1));
            }
        }
        Collections.sort(dates, new Comparator<Date>() {
            @Override
            public int compare(Date o1, Date o2) {
                return o2.compareTo(o1);
            }

        });
        return dates.get(0);
    }

    private RzzOperationDTO createOperation(WorkOrderVO workOrderVO,
                                            OperationVO parentOperation, ProductStockPointVO productStockPoint,
                                            String serialNumber, RuleEncodingsVO ruleEncodings) {
        RzzOperationDTO operationDTO = new RzzOperationDTO();
        String operationOwnCode = workOrderVO.getOrderNo() + "-" + parentOperation.getRoutingStepSequenceNo();
        String subOperationCode = RuleEncodingsUtils.getCode(ruleEncodings, operationOwnCode, serialNumber);

        operationDTO.setId(UUIDUtil.getUUID());
        operationDTO.setOperationCode(subOperationCode);
        operationDTO.setProductStockPointId(parentOperation.getProductStockPointId());
        operationDTO.setStockPointId(parentOperation.getStockPointId());
        operationDTO.setProductId(parentOperation.getProductId());
        operationDTO.setConnectionTask(parentOperation.getConnectionTask());
        operationDTO.setOrderId(workOrderVO.getId());
        operationDTO.setRoutingStepId(parentOperation.getRoutingStepId());
        operationDTO.setStandardStepId(parentOperation.getStandardStepId());
        operationDTO.setRoutingStepSequenceNo(parentOperation.getRoutingStepSequenceNo());
        operationDTO.setPreRoutingStepSequenceNo(parentOperation.getPreRoutingStepSequenceNo());
        operationDTO.setNextRoutingStepSequenceNo(parentOperation.getNextRoutingStepSequenceNo());
        operationDTO.setOrderType(parentOperation.getOrderType());
        operationDTO.setKitStatus(parentOperation.getKitStatus());
        operationDTO.setFrozen(parentOperation.getFrozen());
        operationDTO.setPlanStatus(PlannedStatusEnum.UNPLAN.getCode());
        operationDTO.setAlgo(Boolean.TRUE);
        return operationDTO;
    }


    //保存task
    private List<OperationTaskDTO> getOperationTasks(List<WorkSchedule> operationScheduleOs,
                                                     RzzOperationDTO childOperation,
                                                     List<OperationSubTaskDTO> subTasks,
                                                     Map<String, PhysicalResourceVO> resourceMap,
                                                     List<String> operationIds) {
        childOperation.setProductionDuration(null);
        childOperation.setSetupDuration(null);
        childOperation.setCleanupDuration(null);
        List<OperationTaskDTO> operationTasks = new ArrayList<>();
        //用于计算最早时间和最晚时间
        List<OccupyTime> occupyTimes = new ArrayList<>();
        //<resourceId,List<ScheduleO>>
        Map<String, String> resourceIdMap = new HashedMap<>();
        Map<String, List<Schedule>> resourceSchMap = new HashMap<>();
        for (Schedule scheduleO : operationScheduleOs) {
            List<TimeSlotWithEfficiency> slots = scheduleO.getTimeSlots();
            for (TimeSlotWithEfficiency slot : slots) {
                OccupyTime occupyTime = new OccupyTime();
                LocalDateTime beginTime = slot.getBeginTime();
                LocalDateTime endTime = slot.getEndTime();
                occupyTime.setTimeBegin(beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                occupyTime.setTimeEnd(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                occupyTimes.add(occupyTime);
            }


            if (CollectionUtils.isEmpty(scheduleO.getAssignedResources())) {
                log.info("scheduleO, resourceIds is null : " + childOperation.getId());
            }
            List<String> resourceIds = new ArrayList<>();
            Set<com.yhl.aps.api.obj.Resource> resourceIdSets = scheduleO.getAssignedResources();
            for (com.yhl.aps.api.obj.Resource resourceIdSet : resourceIdSets) {
                resourceIds.add(resourceIdSet.getId());
            }

            List<String> operationResourceIds = new ArrayList<>();
            List<CandidateResource> assignedCandidateResources = scheduleO.getAssignedCandidateResources();
            for (CandidateResource candidateResource : assignedCandidateResources) {
                operationResourceIds.add(candidateResource.getId());
            }
            if (CollectionUtils.isEmpty(operationResourceIds)) {
                log.warn("operationResourceIds is null, resourceIds is : {}, operationId is {} ", resourceIds, childOperation.getId());
                operationIds.add(childOperation.getId());
                continue;
            }

            for (int i = 0; i < resourceIds.size(); i++) {
                List<Schedule> scheduleOs = resourceSchMap.get(resourceIds.get(i));
                if (CollectionUtils.isEmpty(scheduleOs)) {
                    scheduleOs = new ArrayList<>();
                }
                scheduleOs.add(scheduleO);
                resourceSchMap.put(resourceIds.get(i), scheduleOs);
                if (resourceIds.size() == operationResourceIds.size()) {
                    resourceIdMap.put(resourceIds.get(i), operationResourceIds.get(i));
                }
            }
        }

        for (Map.Entry<String, List<Schedule>> entry : resourceSchMap.entrySet()) {
            OperationTaskDTO operationTaskDTO = new OperationTaskDTO();
            operationTaskDTO.setId(UUIDUtil.getUUID());
            operationTaskDTO.setPhysicalResourceId(entry.getKey());
            //get不到说明它是构建的工具资源
            String standardResourceId = resourceMap.containsKey(entry.getKey()) ? resourceMap.get(entry.getKey()).getStandardResourceId() : entry.getKey();
            operationTaskDTO.setStandardResourceId(standardResourceId);

            operationTaskDTO.setOperationId(childOperation.getId());
            List<Schedule> value = entry.getValue();
            Map<String, List<Schedule>> scheduleMap = new HashMap<>();
            for (Schedule schedule : value) {
                ScheduleType type = schedule.getType();
                List<Schedule> scheduleList = scheduleMap.get(type.toString());
                if (CollectionUtils.isNotEmpty(scheduleList)) {
                    scheduleList.add(schedule);
                } else {
                    scheduleMap.put(type.toString(), new ArrayList<>(Collections.singletonList(schedule)));
                }
            }

            operationTaskDTO.setSetupStartTime(getEarliestByScheduleType(scheduleMap.get("SetupTask")));
            operationTaskDTO.setSetupEndTime(getLatestByScheduleType(scheduleMap.get("SetupTask")));
            operationTaskDTO.setProductionStartTime(getEarliestByScheduleType(scheduleMap.get("ProcessTask")));
            operationTaskDTO.setProductionEndTime(getLatestByScheduleType(scheduleMap.get("ProcessTask")));
            operationTaskDTO.setCleanupStartTime(getEarliestByScheduleType(scheduleMap.get("CleanupTask")));
            operationTaskDTO.setCleanupEndTime(getLatestByScheduleType(scheduleMap.get("CleanupTask")));
            operationTaskDTO.setStartTime(CommonDateUtils.getEarliestForStringDate(occupyTimes.stream().map(OccupyTime::getTimeBegin).collect(Collectors.toList())));
            operationTaskDTO.setEndTime(CommonDateUtils.getLatestForStringDate(occupyTimes.stream().map(OccupyTime::getTimeEnd).collect(Collectors.toList())));
//            operationTaskDTO.setNoBufferActionStart(getLatestByScheduleType(scheduleMap.get(ResourceData.SUB_TASK_MAP.get(SubTaskTypeEnum.LOCK.getCode()))));
//            operationTaskDTO.setNoBufferActionEnd(getLatestByScheduleType(scheduleMap.get(ResourceData.SUB_TASK_MAP.get(SubTaskTypeEnum.LOCK.getCode()))));
            List<OperationSubTaskDTO> operationSubTasks = getOperationSubTasks(childOperation, operationTaskDTO, entry.getValue());
            subTasks.addAll(operationSubTasks);
            operationTasks.add(operationTaskDTO);
        }
        return operationTasks;
    }

    private List<OperationSubTaskDTO> getOperationSubTasks(RzzOperationDTO childOperation, OperationTaskDTO operationTask, List<Schedule> scheduleOs) {
        List<OperationSubTaskDTO> subTasks = new ArrayList<>();
        int productionDuration = 0;
        int cleanupDuration = 0;
        int setupDuration = 0;
        for (Schedule scheduleO : scheduleOs) {
            ScheduleType type = scheduleO.getType();
            TimeBlock4FixedDur timeBlock = scheduleO.getTimeBlock();
            TimeBlock timeBlock1 = timeBlock.getTimeBlock();
            List<TimeSlotWithEfficiency> slots = timeBlock1.getSlots();
            for (TimeSlotWithEfficiency slot : slots) {
                long seconds = slot.getDur().getSeconds();
                if (StringUtils.equals(type.toString(), "ProcessTask")) {
                    productionDuration += (int) seconds;
                }
                if (StringUtils.equals(type.toString(), "SetupTask")) {
                    setupDuration += (int) seconds;
                }
                if (StringUtils.equals(type.toString(), "CleanupTask")) {
                    cleanupDuration += (int) seconds;
                }
                LocalDateTime beginTime = slot.getBeginTime();
                LocalDateTime endTime = slot.getEndTime();
                OperationSubTaskDTO operationSubTaskDTO = new OperationSubTaskDTO();
                operationSubTaskDTO.setTaskId(operationTask.getId());
                operationSubTaskDTO.setOperationId(operationTask.getOperationId());
                operationSubTaskDTO.setPhysicalResourceId(operationTask.getPhysicalResourceId());
                operationSubTaskDTO.setTaskType(scheduleTypeSwitch(type.toString()));
                operationSubTaskDTO.setStartTime(DateUtils.stringToDate(beginTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), DateUtils.COMMON_DATE_STR1));
                operationSubTaskDTO.setEndTime(DateUtils.stringToDate(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), DateUtils.COMMON_DATE_STR1));
                operationSubTaskDTO.setId(UUIDUtil.getUUID());
                subTasks.add(operationSubTaskDTO);
            }
        }
        childOperation.setProductionDuration(productionDuration);
        childOperation.setCleanupDuration(cleanupDuration);
        childOperation.setSetupDuration(setupDuration);
        return subTasks;
    }

    private String scheduleTypeSwitch(String althgrimScheduleType) {
        switch (althgrimScheduleType) {
            case "SetupTask":
                return SubTaskTypeEnum.SET.getCode();
            case "ProcessTask":
                return SubTaskTypeEnum.WORK.getCode();
            case "CleanupTask":
                return SubTaskTypeEnum.CLEAN.getCode();
            case "lockSchedule":
                return SubTaskTypeEnum.LOCK.getCode();
            default:
                return null;
        }
    }

    private List<Date> getOperationDates(RzzOperationDTO operationDTO) {
        List<Date> dates = new ArrayList<>();
        if (null == operationDTO) {
            return dates;
        }
        if (operationDTO.getCleanupEndTime() != null) {
            dates.add(operationDTO.getCleanupEndTime());
        }

        if (operationDTO.getProductionEndTime() != null) {
            dates.add(operationDTO.getProductionEndTime());
        }

        if (operationDTO.getSetupEndTime() != null) {
            dates.add(operationDTO.getSetupEndTime());
        }

        if (operationDTO.getCleanupStartTime() != null) {
            dates.add(operationDTO.getCleanupStartTime());
        }

        if (operationDTO.getProductionStartTime() != null) {
            dates.add(operationDTO.getProductionStartTime());
        }

        if (operationDTO.getSetupStartTime() != null) {
            dates.add(operationDTO.getSetupStartTime());
        }
        return dates;
    }

    private void setParentOperationFields(RzzOperationDTO parentOperation, List<RzzOperationDTO> subOperations) {
        Set<String> mainResourceIds = new HashSet<>();
        Set<String> toolResourceIds = new HashSet<>();
        List<Date> dates = new ArrayList<>();
        List<Date> productionDates = new ArrayList<>();
        List<Date> setupDates = new ArrayList<>();
        List<Date> cleanupDates = new ArrayList<>();
        int productionDuration = 0;
        int setupDuration = 0;
        int cleanupDuration = 0;
        for (RzzOperationDTO operationDTO : subOperations) {
            if (null != operationDTO) {
                //解析时间
                if (operationDTO.getProductionStartTime() != null) {
                    productionDates.add(operationDTO.getProductionStartTime());
                    dates.add(operationDTO.getProductionStartTime());
                }
                if (operationDTO.getProductionEndTime() != null) {
                    productionDates.add(operationDTO.getProductionEndTime());
                    dates.add(operationDTO.getProductionEndTime());
                }
                if (operationDTO.getSetupStartTime() != null) {
                    setupDates.add(operationDTO.getSetupStartTime());
                    dates.add(operationDTO.getSetupStartTime());
                }
                if (operationDTO.getSetupEndTime() != null) {
                    setupDates.add(operationDTO.getSetupEndTime());
                    dates.add(operationDTO.getSetupEndTime());
                }
                if (operationDTO.getCleanupStartTime() != null) {
                    cleanupDates.add(operationDTO.getCleanupStartTime());
                    dates.add(operationDTO.getCleanupStartTime());
                }
                if (operationDTO.getCleanupEndTime() != null) {
                    cleanupDates.add(operationDTO.getCleanupEndTime());
                    dates.add(operationDTO.getCleanupEndTime());
                }
                //解析时长
                if (operationDTO.getProductionDuration() != null) {
                    productionDuration += operationDTO.getProductionDuration();
                }
                if (operationDTO.getSetupDuration() != null) {
                    setupDuration += operationDTO.getSetupDuration();
                }
                if (operationDTO.getCleanupDuration() != null) {
                    cleanupDuration += operationDTO.getCleanupDuration();
                }
                //解析资源
                if (StringUtils.isNotBlank(operationDTO.getPlannedMainResourceId())) {
                    String[] mailResourceIds = operationDTO.getPlannedMainResourceId().split(",");
                    mainResourceIds.addAll(Arrays.asList(mailResourceIds));
                }
                if (StringUtils.isNotBlank(operationDTO.getPlannedToolResourceId())) {
                    String[] mailResourceIds = operationDTO.getPlannedToolResourceId().split(",");
                    toolResourceIds.addAll(Arrays.asList(mailResourceIds));
                }
            }
        }
        parentOperation.setStartTime(CommonDateUtils.getEarliest(dates));
        parentOperation.setEndTime(CommonDateUtils.getLatest(dates));
        parentOperation.setProductionStartTime(CommonDateUtils.getEarliest(productionDates));
        parentOperation.setProductionEndTime(CommonDateUtils.getLatest(productionDates));
        parentOperation.setSetupStartTime(CommonDateUtils.getEarliest(setupDates));
        parentOperation.setSetupEndTime(CommonDateUtils.getLatest(setupDates));
        parentOperation.setCleanupStartTime(CommonDateUtils.getEarliest(cleanupDates));
        parentOperation.setCleanupEndTime(CommonDateUtils.getLatest(cleanupDates));

        parentOperation.setLastProductionStartTime(parentOperation.getProductionStartTime());
        parentOperation.setLastProductionEndTime(parentOperation.getProductionEndTime());
        parentOperation.setLastSetupStartTime(parentOperation.getSetupStartTime());
        parentOperation.setLastSetupEndTime(parentOperation.getSetupEndTime());
        parentOperation.setLastCleanupStartTime(parentOperation.getCleanupStartTime());
        parentOperation.setLastCleanupEndTime(parentOperation.getCleanupEndTime());

        //时长
        parentOperation.setProductionDuration(productionDuration);
        parentOperation.setSetupDuration(setupDuration);
        parentOperation.setCleanupDuration(cleanupDuration);

        //计划资源
        parentOperation.setPlannedMainResourceId(String.join(",", mainResourceIds));
        parentOperation.setPlannedToolResourceId(String.join(",", toolResourceIds));
        parentOperation.setLastMainResourceId(parentOperation.getPlannedMainResourceId());
        parentOperation.setLastToolResourceId(parentOperation.getPlannedToolResourceId());

        //计算父工序状态
        String parentOperationStatus = this.getParentOperationStatus(parentOperation.getQuantity(),
                subOperations);
        parentOperation.setPlanStatus(parentOperationStatus);
        if (PlannedStatusEnum.STARTED.getCode().equals(parentOperationStatus) ||
                PlannedStatusEnum.FINISHED.getCode().equals(parentOperationStatus)) {
            parentOperation.setEarliestStartTime(null);
            parentOperation.setLatestEndTime(null);
            if (CollectionUtils.isNotEmpty(subOperations)) {
                subOperations.forEach(x -> {
                    x.setEarliestStartTime(null);
                    x.setLatestEndTime(null);
                });
            }
        }
    }

    public String getParentOperationStatus(BigDecimal parentOperationQuantity, List<RzzOperationDTO> operations) {
        if (CollectionUtils.isEmpty(operations)) {
            return PlannedStatusEnum.UNPLAN.getCode();
        }
        for (RzzOperationDTO operation : operations) {
            if (operation.getQuantity() == null) {
                log.info("getParentOperationStatus，exist children qty is null, operationId is {}", operation.getParentId());
            }
        }
        Boolean leftQtyFlag = Boolean.FALSE;
        //父工序有剩余量则reserve置为TRUE，表示待拆
        BigDecimal subOperationQty = operations.stream().map(RzzOperationDTO::getQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        if ((parentOperationQuantity.subtract(subOperationQty)).compareTo(new BigDecimal(0)) > 0) {
            leftQtyFlag = Boolean.TRUE;
        }
        long plannedSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.PLANNED.getCode())).count();
        long issuedPlanSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.ISSUED.getCode())).count();
        long identifiedPlanSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.PLAN_IDENTIFIED.getCode())).count();
        long startedPlanSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.STARTED.getCode())).count();
        long finishedPlanSize = operations.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.FINISHED.getCode())).count();
        if (CollectionUtils.isEmpty(operations)) {
            return PlannedStatusEnum.UNPLAN.getCode();
        }
        if (!leftQtyFlag && plannedSize == operations.size()) {
            return PlannedStatusEnum.PLANNED.getCode();
        }
        if (leftQtyFlag) {
            return PlannedStatusEnum.SOME_PLANNED.getCode();
        }
        if (issuedPlanSize == operations.size()) {
            return PlannedStatusEnum.ISSUED.getCode();
        }
        if (issuedPlanSize > 0 && identifiedPlanSize == 0 && startedPlanSize == 0 && finishedPlanSize == 0) {
            return PlannedStatusEnum.ISSUED.getCode();
        }
        if (identifiedPlanSize == operations.size()) {
            return PlannedStatusEnum.PLAN_IDENTIFIED.getCode();
        }
        if (identifiedPlanSize > 0 && startedPlanSize == 0 && finishedPlanSize == 0) {
            return PlannedStatusEnum.PLAN_IDENTIFIED.getCode();
        }
        if (finishedPlanSize == operations.size()) {
            return PlannedStatusEnum.FINISHED.getCode();
        }
        if (finishedPlanSize > 0 && identifiedPlanSize > 0 && (finishedPlanSize + identifiedPlanSize) >= operations.size()) {
            return PlannedStatusEnum.FINISHED.getCode();
        }
        return PlannedStatusEnum.STARTED.getCode();
    }

    private List<Date> getOperationsDates(List<RzzOperationDTO> operations) {
        //只获取子工序
        List<RzzOperationDTO> subOperations = operations.stream().filter(k -> StringUtils.isNotBlank(k.getParentId())).collect(Collectors.toList());
        List<Date> dates = new ArrayList<>();
        for (RzzOperationDTO operationDTO : subOperations) {
            dates.addAll(getOperationDates(operationDTO));
        }
        return dates;
    }

    private String removeEnd(String str, String character) {
        if (!StringUtils.isEmpty(str)) {
            return org.apache.commons.lang3.StringUtils.removeEnd(str, character);
        }
        return null;
    }

    @Override
    public String getObjectType() {
        return "";
    }

    public String getWorkOrderStatus(List<RzzOperationDTO> operationDtoList, WorkOrderDTO workOrderDTO) {
        String planStatus = workOrderDTO.getPlanStatus();
        if (planStatus.equals(PlannedStatusEnum.ISSUED.getCode())) {
            // 已经下发得制造订单不改变原始状态仍为已下发
            return planStatus;
        }
        long unPlanSize = operationDtoList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.UNPLAN.getCode())).count();
        long someDealSize = operationDtoList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.SOME_PLANNED.getCode())).count();
        long plannedSize = operationDtoList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.PLANNED.getCode())).count();
        long issuedSize = operationDtoList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.ISSUED.getCode())).count();
        long planIdentified = operationDtoList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.PLAN_IDENTIFIED.getCode())).count();
        long startedSize = operationDtoList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.STARTED.getCode())).count();
        long finishedSize = operationDtoList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.FINISHED.getCode())).count();
        if (unPlanSize == operationDtoList.size()) {
            return PlannedStatusEnum.UNPLAN.getCode();
        }
        if (startedSize == operationDtoList.size()) {
            return PlannedStatusEnum.SOME_PLANNED.getCode();
        }
        if (someDealSize > 0 && issuedSize == 0 && startedSize == 0 && finishedSize == 0 && planIdentified == 0) {
            return PlannedStatusEnum.SOME_PLANNED.getCode();
        }
        if (unPlanSize > 0 && plannedSize > 0 && (unPlanSize + plannedSize) >= operationDtoList.size()) {
            return PlannedStatusEnum.SOME_PLANNED.getCode();
        }
        if (plannedSize == operationDtoList.size()) {
            return PlannedStatusEnum.PLANNED.getCode();
        }
        if (issuedSize == operationDtoList.size()) {
            return PlannedStatusEnum.ISSUED.getCode();
        }
        if (issuedSize > 0 && startedSize == 0 && finishedSize == 0 && planIdentified == 0) {
            return PlannedStatusEnum.ISSUED.getCode();
        }
        if (planIdentified == operationDtoList.size()) {
            return PlannedStatusEnum.PLAN_IDENTIFIED.getCode();
        }
        if (planIdentified > 0 && someDealSize == 0 && startedSize == 0 && finishedSize == 0) {
            return PlannedStatusEnum.PLAN_IDENTIFIED.getCode();
        }
        if (finishedSize == operationDtoList.size()) {
            return PlannedStatusEnum.FINISHED.getCode();
        }
        if (finishedSize > 0 && planIdentified > 0 && (finishedSize + planIdentified) >= operationDtoList.size()) {
            return PlannedStatusEnum.FINISHED.getCode();
        }
        return PlannedStatusEnum.STARTED.getCode();
    }

    public String getParentWorkOrderStatus(List<WorkOrderDTO> workOrderDTOList) {
        long unPlanSize = workOrderDTOList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.UNPLAN.getCode())).count();
        long someDealSize = workOrderDTOList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.SOME_PLANNED.getCode())).count();
        long plannedSize = workOrderDTOList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.PLANNED.getCode())).count();
        long issuedSize = workOrderDTOList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.ISSUED.getCode())).count();
        long planIdentified = workOrderDTOList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.PLAN_IDENTIFIED.getCode())).count();
        long startedSize = workOrderDTOList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.STARTED.getCode())).count();
        long finishedSize = workOrderDTOList.stream().filter(k -> k.getPlanStatus().equals(PlannedStatusEnum.FINISHED.getCode())).count();
        if (unPlanSize == workOrderDTOList.size()) {
            return PlannedStatusEnum.UNPLAN.getCode();
        }
        if (someDealSize == workOrderDTOList.size()) {
            return PlannedStatusEnum.SOME_PLANNED.getCode();
        }
        if (unPlanSize > 0 && plannedSize > 0 && (unPlanSize + plannedSize) >= workOrderDTOList.size()) {
            return PlannedStatusEnum.SOME_PLANNED.getCode();
        }
        if (someDealSize > 0 && issuedSize == 0 && planIdentified == 0 && startedSize == 0 && finishedSize == 0) {
            return PlannedStatusEnum.SOME_PLANNED.getCode();
        }
        if (plannedSize == workOrderDTOList.size()) {
            return PlannedStatusEnum.PLANNED.getCode();
        }
        if (issuedSize == workOrderDTOList.size()) {
            return PlannedStatusEnum.ISSUED.getCode();
        }
        if (issuedSize > 0 && planIdentified == 0 && startedSize == 0 && finishedSize == 0) {
            return PlannedStatusEnum.SOME_PLANNED.getCode();
        }
        if (planIdentified == workOrderDTOList.size()) {
            return PlannedStatusEnum.PLAN_IDENTIFIED.getCode();
        }
        if (planIdentified > 0 && someDealSize == 0 && startedSize == 0 && finishedSize == 0) {
            return PlannedStatusEnum.PLAN_IDENTIFIED.getCode();
        }
        if (finishedSize == workOrderDTOList.size()) {
            return PlannedStatusEnum.FINISHED.getCode();
        }
        if (finishedSize > 0 && planIdentified > 0 && (finishedSize + planIdentified) >= workOrderDTOList.size()) {
            return PlannedStatusEnum.FINISHED.getCode();
        }
        return PlannedStatusEnum.STARTED.getCode();
    }
}
