<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO">
        <!--@Table mps_capacity_supply_relationship-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="forecast_time" jdbcType="TIMESTAMP" property="forecastTime"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="supply_quantity" jdbcType="VARCHAR" property="supplyQuantity"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="operation_name" jdbcType="VARCHAR" property="operationName"/>
        <result column="supply_model" jdbcType="VARCHAR" property="supplyModel"/>
        <result column="supply_time" jdbcType="TIMESTAMP" property="supplyTime"/>
        <result column="forecast_month" jdbcType="VARCHAR" property="forecastMonth"/>
        <result column="supply_month" jdbcType="TIMESTAMP" property="supplyMonth"/>
        <result column="resource_code" jdbcType="VARCHAR" property="resourceCode"/>
        <result column="resource_name" jdbcType="VARCHAR" property="resourceName"/>
        <result column="beat" jdbcType="VARCHAR" property="beat"/>
        <result column="lock_status" jdbcType="VARCHAR" property="lockStatus"/>
        <result column="rule" jdbcType="VARCHAR" property="rule"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="consistence_demand_forecast_version_id" jdbcType="VARCHAR" property="consistenceDemandForecastVersionId"/>
        <result column="plan_period" jdbcType="VARCHAR" property="planPeriod"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="import_flag" jdbcType="VARCHAR" property="importFlag"/>
        <result column="source_product_code" jdbcType="VARCHAR" property="sourceProductCode"/>
        <result column="routing_step_id" jdbcType="VARCHAR" property="routingStepId"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO">
    </resultMap>
    <sql id="Base_Column_List">
id,version_id,vehicle_model_code,product_code,product_name,forecast_time,demand_quantity,supply_quantity,operation_code,operation_name,supply_model,supply_time,resource_code,resource_name,beat,lock_status,rule,remark,enabled,creator,create_time,modifier,modify_time,version_value
,plan_period,version_code,consistence_demand_forecast_version_id,import_flag,source_product_code,routing_step_id,forecast_month,supply_month
</sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List" />
        ,resource_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.sourceProductCode != null and params.sourceProductCode != ''">
                and source_product_code = #{params.sourceProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastTime != null">
                and forecast_time = #{params.forecastTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyQuantity != null">
                and supply_quantity = #{params.supplyQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.operationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.operationName != null and params.operationName != ''">
                and operation_name = #{params.operationName,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyModel != null and params.supplyModel != ''">
                and supply_model = #{params.supplyModel,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyTime != null">
                and supply_time = #{params.supplyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.resourceCode != null and params.resourceCode != ''">
                and resource_code = #{params.resourceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceName != null and params.resourceName != ''">
                and resource_name = #{params.resourceName,jdbcType=VARCHAR}
            </if>
            <if test="params.forecastMonth != null and params.forecastMonth != ''">
                and forecast_month = #{params.forecastMonth,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyMonth != null and params.supplyMonth != ''">
                and supply_month = #{params.supplyMonth,jdbcType=VARCHAR}
            </if>
            <if test="params.beat != null and params.beat != ''">
                and beat = #{params.beat,jdbcType=VARCHAR}
            </if>
            <if test="params.lockStatus != null and params.lockStatus != ''">
                and lock_status = #{params.lockStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.rule != null and params.rule != ''">
                and rule = #{params.rule,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.supplyTimeStart != null and params.supplyTimeEnd!=null">
                and forecast_time between #{params.supplyTimeStart,jdbcType=TIMESTAMP} and #{params.supplyTimeEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="params.planPeriod != null and params.planPeriod != ''">
                and plan_period = #{params.planPeriod,jdbcType=VARCHAR}
            </if>
            <if test="params.versionCode != null and params.versionCode != ''">
                and version_code = #{params.versionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.consistenceDemandForecastVersionId != null and params.consistenceDemandForecastVersionId != ''">
                and consistence_demand_forecast_version_id = #{params.consistenceDemandForecastVersionId,jdbcType=VARCHAR}
            </if>
            <if test="params.latestFlag != null and params.latestFlag == true">
                and version_id = 'NEW'
            </if>
            <if test="params.importFlag != null and params.importFlag == true">
                and import_flag  = #{params.importFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.routingStepId != null and params.routingStepId != ''">
                and routing_step_id = #{params.routingStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.versionIds != null and params.versionIds.size()>0">
                and version_id in
                <foreach collection="params.versionIds" index="index" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="params.supplyMonthList != null and params.supplyMonthList.size() > 0">
                and supply_month in
                <foreach collection="params.supplyMonthList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.resourceCodeList != null and params.resourceCodeList.size() > 0">
                and resource_code in
                <foreach collection="params.resourceCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_supply_relationship
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_supply_relationship
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mps_capacity_supply_relationship
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from v_mps_capacity_supply_relationship
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectCapacitySupplyRelationshipCollect" resultMap="VOResultMap">
        select
        id,
        routing_step_id,
        vehicle_model_code,
        product_code,
        product_name,
        operation_code,
        operation_name,
        forecast_month,
        demand_quantity,
        supply_month,
        supply_quantity,
        supply_model,
        resource_code,
        resource_name,
        lock_status,
        beat,
        rule
        from mps_capacity_supply_relationship_read_only
        <include refid="Base_Where_Condition" />
    </select>

    <select id="selectCapacitySupplyRelationshipCollectNew" resultMap="VOResultMap">
        select
        id,
        routing_step_id,
        vehicle_model_code,
        product_code,
        product_name,
        operation_code,
        operation_name,
        forecast_month,
        demand_quantity,
        supply_month,
        supply_quantity,
        supply_model,
        resource_code,
        resource_name,
        lock_status,
        beat,
        rule
        from mps_capacity_supply_relationship_read_only
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectLatestData" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_supply_relationship
        where version_id = 'NEW'
        <if test="lockStatus != null and lockStatus != ''">
            and lock_status = #{lockStatus,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectLockOrOutData" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_supply_relationship
        where version_id = 'NEW'
#         and (lock_status = '1' or supply_model = 'OUTSOURCED')
        and lock_status = '1'
    </select>
    <select id="selectLatestCode" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_supply_relationship
        where version_id = 'NEW'
    </select>
    <select id="selectLastVersion" resultMap="VOResultMap">
        select
            plan_period,version_code
        from mps_capacity_supply_relationship
        where version_id = 'NEW'
        group by plan_period,version_code
    </select>

    <select id="selectForecastTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_capacity_supply_relationship
        where product_code = #{productCode,jdbcType=VARCHAR}
        <if test="operationCode != null and operationCode != ''">
            and operation_code = #{operationCode,jdbcType=VARCHAR}
        </if>
        and forecast_time between #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and version_id = 'NEW'
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mps_capacity_supply_relationship(
        id,
        version_id,
        vehicle_model_code,
        source_product_code,
        product_code,
        product_name,
        forecast_time,
        demand_quantity,
        supply_quantity,
        operation_code,
        operation_name,
        supply_model,
        supply_time,
        resource_code,
        resource_name,
        forecast_month,
        supply_month,
        beat,
        lock_status,
        rule,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        consistence_demand_forecast_version_id,
        plan_period,
        version_code,
        import_flag,
        routing_step_id)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{sourceProductCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{forecastTime,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=VARCHAR},
        #{supplyQuantity,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{operationName,jdbcType=VARCHAR},
        #{supplyModel,jdbcType=VARCHAR},
        #{supplyTime,jdbcType=TIMESTAMP},
        #{resourceCode,jdbcType=VARCHAR},
        #{resourceName,jdbcType=VARCHAR},
        #{forecastMonth,jdbcType=VARCHAR},
        #{supplyMonth,jdbcType=VARCHAR},
        #{beat,jdbcType=VARCHAR},
        #{lockStatus,jdbcType=VARCHAR},
        #{rule,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{consistenceDemandForecastVersionId,jdbcType=VARCHAR},
        #{planPeriod,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},
        #{importFlag,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO">
        insert into mps_capacity_supply_relationship(
        id,
        version_id,
        vehicle_model_code,
        product_code,
        source_product_code,
        product_name,
        forecast_time,
        demand_quantity,
        supply_quantity,
        operation_code,
        operation_name,
        supply_model,
        supply_time,
        resource_code,
        resource_name,
        forecast_month,
        supply_month,
        beat,
        lock_status,
        rule,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        consistence_demand_forecast_version_id,
        plan_period,
        version_code,
        import_flag,
        routing_step_id)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{vehicleModelCode,jdbcType=VARCHAR},
        #{sourceProductCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{forecastTime,jdbcType=TIMESTAMP},
        #{demandQuantity,jdbcType=VARCHAR},
        #{supplyQuantity,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{operationName,jdbcType=VARCHAR},
        #{supplyModel,jdbcType=VARCHAR},
        #{supplyTime,jdbcType=TIMESTAMP},
        #{resourceCode,jdbcType=VARCHAR},
        #{resourceName,jdbcType=VARCHAR},
        #{forecastMonth,jdbcType=VARCHAR},
        #{supplyMonth,jdbcType=VARCHAR},
        #{beat,jdbcType=VARCHAR},
        #{lockStatus,jdbcType=VARCHAR},
        #{rule,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{consistenceDemandForecastVersionId,jdbcType=VARCHAR},
        #{planPeriod,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},
        #{importFlag,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_capacity_supply_relationship(
        id,
        version_id,
        vehicle_model_code,
        source_product_code,
        product_code,
        product_name,
        forecast_time,
        demand_quantity,
        supply_quantity,
        operation_code,
        operation_name,
        supply_model,
        supply_time,
        resource_code,
        resource_name,
        forecast_month,
        supply_month,
        beat,
        lock_status,
        rule,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        consistence_demand_forecast_version_id,
        plan_period,
        version_code,
        import_flag,
        routing_step_id)
        values
        <foreach collection="list" item="entity" separator=",">
        (#{entity.id,jdbcType=VARCHAR},
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.vehicleModelCode,jdbcType=VARCHAR},
        #{entity.sourceProductCode,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.productName,jdbcType=VARCHAR},
        #{entity.forecastTime,jdbcType=TIMESTAMP},
        #{entity.demandQuantity,jdbcType=VARCHAR},
        #{entity.supplyQuantity,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.operationName,jdbcType=VARCHAR},
        #{entity.supplyModel,jdbcType=VARCHAR},
        #{entity.supplyTime,jdbcType=TIMESTAMP},
        #{entity.resourceCode,jdbcType=VARCHAR},
        #{entity.resourceName,jdbcType=VARCHAR},
        #{entity.forecastMonth,jdbcType=VARCHAR},
        #{entity.supplyMonth,jdbcType=VARCHAR},
        #{entity.beat,jdbcType=VARCHAR},
        #{entity.lockStatus,jdbcType=VARCHAR},
        #{entity.rule,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.consistenceDemandForecastVersionId,jdbcType=VARCHAR},
        #{entity.planPeriod,jdbcType=VARCHAR},
        #{entity.versionCode,jdbcType=VARCHAR},
        #{entity.importFlag,jdbcType=VARCHAR},
            #{entity.routingStepId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_capacity_supply_relationship(
        id,
        version_id,
        vehicle_model_code,
        source_product_code,
        product_code,
        product_name,
        forecast_time,
        demand_quantity,
        supply_quantity,
        operation_code,
        operation_name,
        supply_model,
        supply_time,
        resource_code,
        resource_Name,
        forecast_month,
        supply_month,
        beat,
        lock_status,
        rule,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        consistence_demand_forecast_version_id,
        plan_period,
        version_code,
        import_flag,
                                                     routing_step_id)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.versionId,jdbcType=VARCHAR},
        #{entity.vehicleModelCode,jdbcType=VARCHAR},
        #{entity.sourceProductCode,jdbcType=VARCHAR},
        #{entity.productCode,jdbcType=VARCHAR},
        #{entity.productName,jdbcType=VARCHAR},
        #{entity.forecastTime,jdbcType=TIMESTAMP},
        #{entity.demandQuantity,jdbcType=VARCHAR},
        #{entity.supplyQuantity,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.operationName,jdbcType=VARCHAR},
        #{entity.supplyModel,jdbcType=VARCHAR},
        #{entity.supplyTime,jdbcType=TIMESTAMP},
        #{entity.resourceCode,jdbcType=VARCHAR},
        #{entity.resourceName,jdbcType=VARCHAR},
        #{entity.forecastMonth,jdbcType=VARCHAR},
        #{entity.supplyMonth,jdbcType=VARCHAR},
        #{entity.beat,jdbcType=VARCHAR},
        #{entity.lockStatus,jdbcType=VARCHAR},
        #{entity.rule,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.consistenceDemandForecastVersionId,jdbcType=VARCHAR},
        #{entity.planPeriod,jdbcType=VARCHAR},
        #{entity.versionCode,jdbcType=VARCHAR},
        #{entity.importFlag,jdbcType=VARCHAR},
            #{entity.routingStepId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO">
        update mps_capacity_supply_relationship set
        version_id = #{versionId,jdbcType=VARCHAR},
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
        product_code = #{productCode,jdbcType=VARCHAR},
        source_product_code = #{productCode,jdbcType=VARCHAR},
        product_name = #{productName,jdbcType=VARCHAR},
        forecast_time = #{forecastTime,jdbcType=TIMESTAMP},
        demand_quantity = #{demandQuantity,jdbcType=VARCHAR},
        supply_quantity = #{supplyQuantity,jdbcType=VARCHAR},
        operation_code = #{operationCode,jdbcType=VARCHAR},
        operation_name = #{operationName,jdbcType=VARCHAR},
        supply_model = #{supplyModel,jdbcType=VARCHAR},
        supply_time = #{supplyTime,jdbcType=TIMESTAMP},
        resource_code = #{resourceCode,jdbcType=VARCHAR},
        resource_name = #{resourceName,jdbcType=VARCHAR},
        forecast_month = #{forecastMonth,jdbcType=VARCHAR},
        supply_month = #{supplyMonth,jdbcType=VARCHAR},
        beat = #{beat,jdbcType=VARCHAR},
        lock_status = #{lockStatus,jdbcType=VARCHAR},
        rule = #{rule,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        consistence_demand_forecast_version_id  = #{consistenceDemandForecastVersionId,jdbcType=VARCHAR},
        plan_period  = #{planPeriod,jdbcType=VARCHAR},
        version_code = #{versionCode,jdbcType=VARCHAR},
        import_flag = #{importFlag,jdbcType=VARCHAR},
        routing_step_id = #{routingStepId,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR};
        update mps_capacity_supply_relationship set
            version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO">
        update mps_capacity_supply_relationship
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceProductCode != null and item.sourceProductCode != ''">
                source_product_code = #{item.sourceProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastTime != null">
                forecast_time = #{item.forecastTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyQuantity != null">
                supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.operationName != null and item.operationName != ''">
                operation_name = #{item.operationName,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyModel != null and item.supplyModel != ''">
                supply_model = #{item.supplyModel,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyTime != null">
                supply_time = #{item.supplyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.resourceCode != null and item.resourceCode != ''">
                resource_code = #{item.resourceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceName != null and item.resourceName != ''">
                resource_name = #{item.resourceName,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastMonth != null and item.forecastMonth != ''">
                forecast_month = #{item.forecastMonth,jdbcType=VARCHAR}
            </if>
            <if test="item.supplyMonth != null and item.supplyMonth != ''">
                supply_month = #{item.supplyMonth,jdbcType=VARCHAR}
            </if>
            <if test="item.beat != null and item.beat != ''">
                beat = #{item.beat,jdbcType=VARCHAR},
            </if>
            <if test="item.lockStatus != null and item.lockStatus != ''">
                lock_status = #{item.lockStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.rule != null and item.rule != ''">
                rule = #{item.rule,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.consistenceDemandForecastVersionId != null and item.consistenceDemandForecastVersionId != ''">
                consistence_demand_forecast_version_id = #{item.consistenceDemandForecastVersionId,jdbcType=VARCHAR}
            </if>
            <if test="item.planPeriod != null and item.planPeriod != ''">
                plan_period = #{item.planPeriod,jdbcType=VARCHAR},
            </if>
            <if test="item.versionCode != null and item.versionCode != ''">
                version_code = #{item.versionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.importFlag != null and item.importFlag != ''">
                import_flag = #{item.importFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR};
        update mps_capacity_supply_relationship set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_capacity_supply_relationship
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="vehicle_model_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.vehicleModelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sourceProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="forecast_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_model = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyModel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="resource_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="forecast_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.forecastMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_month = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyMonth,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="beat = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.beat,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lock_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lockStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rule = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rule,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="consistence_demand_forecast_version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.consistenceDemandForecastVersionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_period = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planPeriod,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="import_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.importFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.routingStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update mps_capacity_supply_relationship set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mps_capacity_supply_relationship
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.vehicleModelCode != null and item.vehicleModelCode != ''">
                vehicle_model_code = #{item.vehicleModelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.sourceProductCode != null and item.sourceProductCode != ''">
                source_product_code = #{item.sourceProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastTime != null">
                forecast_time = #{item.forecastTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyQuantity != null">
                supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.operationName != null and item.operationName != ''">
                operation_name = #{item.operationName,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyModel != null and item.supplyModel != ''">
                supply_model = #{item.supplyModel,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyTime != null">
                supply_time = #{item.supplyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.resourceCode != null and item.resourceCode != ''">
                resource_code = #{item.resourceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceName != null and item.resourceName != ''">
                resource_name = #{item.resourceName,jdbcType=VARCHAR},
            </if>
            <if test="item.forecastMonth != null and item.forecastMonth != ''">
                forecast_month = #{item.forecastMonth,jdbcType=VARCHAR}
            </if>
            <if test="item.supplyMonth != null and item.supplyMonth != ''">
                supply_month = #{item.supplyMonth,jdbcType=VARCHAR}
            </if>
            <if test="item.beat != null and item.beat != ''">
                beat = #{item.beat,jdbcType=VARCHAR},
            </if>
            <if test="item.lockStatus != null and item.lockStatus != ''">
                lock_status = #{item.lockStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.rule != null and item.rule != ''">
                rule = #{item.rule,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.consistenceDemandForecastVersionId != null and item.consistenceDemandForecastVersionId != ''">
                consistence_demand_forecast_version_id = #{item.consistenceDemandForecastVersionId,jdbcType=VARCHAR}
            </if>
            <if test="item.planPeriod != null and item.planPeriod != ''">
                plan_period = #{item.planPeriod,jdbcType=VARCHAR},
            </if>
            <if test="item.versionCode != null and item.versionCode != ''">
                version_code = #{item.versionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.importFlag != null and item.importFlag != ''">
                import_flag = #{item.importFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER};
        update mps_capacity_supply_relationship set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mps_capacity_supply_relationship where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mps_capacity_supply_relationship where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteUnpublishedBatch">
        delete from mps_capacity_supply_relationship where version_id = 'NEW'
    </delete>
    <select id="selectNeedDeleteIds" resultType="String">
        select id
        from mps_capacity_supply_relationship
        <where>
            <choose>
                <when test="versionIds!=null and versionIds.size() > 0">
                    version_id in
                    <foreach collection="versionIds" item="item" open="(" separator="," close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </when>
                <when test="versionIds==null">
                    version_id is null
                </when>
                <otherwise>
                    1 = -1
                </otherwise>
            </choose>
        </where>
    </select>
    <delete id="deleteWeekData">
        delete from mps_capacity_supply_relationship where version_id  = #{type,jdbcType=VARCHAR}
    </delete>

     <select id="selectForSupplyCalculate" resultMap="BaseResultMap">
		 SELECT
			id,
			product_code,
			product_name,
			supply_model,
			supply_time,
			supply_quantity,
			operation_code
		FROM
			v_mps_capacity_supply_relationship
		WHERE
         version_id = 'NEW'
			and supply_model = 'OUTSOURCED'
		 	<if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyStartDate != null">
                and supply_time <![CDATA[ >= ]]>  #{params.supplyStartDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.supplyEndDate != null">
                and supply_time <![CDATA[ <= ]]>  #{params.supplyEndDate,jdbcType=TIMESTAMP}
            </if>
    </select>
    <select id="selectBeatAverage" resultType="com.yhl.scp.mps.capacityBalance.vo.BeatAverageVO">
        select
            resource_code as resourceCode,
            date_format(supply_time,'%Y-%m') as yearMonth,
            sum(demand_quantity * cast(beat as decimal(10,0)))/sum(demand_quantity) as averageBeat
        from mps_capacity_supply_relationship
        where supply_model != 'OUTSOURCED'
            and version_id = 'NEW'
        group by resource_code, date_format(supply_time,'%Y-%m')
    </select>
    <update id="updateVersionIds" parameterType="java.util.List">
        update mps_capacity_supply_relationship t set t.version_id = #{versionId,jdbcType=VARCHAR}
        where version_id = 'NEW'
    </update>
    
    <select id="selectLoad" resultType="com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO">
        SELECT
            t.supply_month as forecastMonth,
            t.operation_code as operationCode,
            t.operation_name as operationName,
            t.resource_code as resourceCode,
            t.resource_name as resourceName,
            sum( t.supply_quantity ) as demandQuantity,
            sum( t.supply_quantity * t.beat ) as productionCapacity
        FROM
            `mps_capacity_supply_relationship` t
        WHERE
            t.version_id = 'NEW'
          AND t.supply_model = 'LOCAL'
          AND t.resource_code != 'VIRTUAL_RESOURCE'
        GROUP BY
            t.supply_month,
            t.operation_code,
            t.operation_name,
            t.resource_code,
            t.resource_name
    </select>

    <select id = "selectLatestPlanPeriod" resultType="String">
        select t.plan_period
        from mps_capacity_supply_relationship t
        where t.version_id = 'NEW'
        group by t.plan_period
    </select>
    
    <select id="selectCollectByParams" resultMap="VOResultMap">
        WITH t AS (
        SELECT
        t.vehicle_model_code AS vehicle_model_code,
        t.product_code AS product_code,
        t.product_name AS product_name,
        t.operation_code AS operation_code,
        t.operation_name AS operation_name,
        t.demand_quantity AS demand_quantity,
        t.routing_step_id AS routing_step_id,
        t.forecast_time AS forecast_time,
        t.supply_time AS supply_time,
        t.forecast_month AS forecast_month,
        t.supply_month AS supply_month,
        t.supply_quantity AS supply_quantity,
        t.supply_model AS supply_model,
        t.resource_code AS resource_code,
        t.resource_name AS resource_name,
        t.lock_status AS lock_status,
        t.beat AS beat,
        t.rule AS rule
        FROM
        mps_capacity_supply_relationship t
        WHERE
        t.resource_code != 'VIRTUAL_RESOURCE'
        <if test="params.versionId != null and params.versionId != ''">
            and version_id = #{params.versionId,jdbcType=VARCHAR}
        </if>
        ),
        b AS (
        SELECT
        t.vehicle_model_code AS vehicle_model_code,
        t.product_code AS product_code,
        t.product_name AS product_name,
        t.operation_code AS operation_code,
        t.operation_name AS operation_name,
        t.supply_model AS supply_model,
        t.forecast_time AS forecast_time,
        t.forecast_month AS forecast_month,
        t.demand_quantity AS demand_quantity,
        t.routing_step_id AS routing_step_id
        FROM
        t
        GROUP BY
        t.vehicle_model_code,
        t.product_code,
        t.product_name,
        t.operation_code,
        t.operation_name,
        t.supply_model,
        t.forecast_time,
        t.forecast_month,
        t.demand_quantity,
        t.routing_step_id
        ),
        c AS (
        SELECT
        b.vehicle_model_code AS vehicle_model_code,
        b.product_code AS product_code,
        b.product_name AS product_name,
        b.forecast_month AS forecast_month,
        SUM(b.demand_quantity) AS demand_quantity,
        b.operation_code AS operation_code,
        b.operation_name AS operation_name,
        b.supply_model AS supply_model,
        b.routing_step_id AS routing_step_id
        FROM
        b
        GROUP BY
        b.vehicle_model_code,
        b.product_code,
        b.product_name,
        b.forecast_month,
        b.operation_code,
        b.operation_name,
        b.supply_model,
        b.routing_step_id
        ),
        a AS (
        SELECT
        t.vehicle_model_code AS vehicle_model_code,
        t.product_code AS product_code,
        t.product_name AS product_name,
        t.operation_code AS operation_code,
        t.operation_name AS operation_name,
        t.forecast_month AS forecast_month,
        t.supply_month AS supply_month,
        SUM(t.supply_quantity) AS supply_quantity,
        t.supply_model AS supply_model,
        t.resource_code AS resource_code,
        t.resource_name AS resource_name,
        t.lock_status AS lock_status,
        t.beat AS beat,
        t.rule AS rule
        FROM
        t
        GROUP BY
        t.vehicle_model_code,
        t.product_code,
        t.product_name,
        t.operation_code,
        t.operation_name,
        t.forecast_month,
        t.supply_month,
        t.supply_model,
        t.resource_code,
        t.resource_name,
        t.lock_status,
        t.beat,
        t.rule
        )
        SELECT
        UUID() AS id,
        c.routing_step_id AS routing_step_id,
        c.vehicle_model_code AS vehicle_model_code,
        c.product_code AS product_code,
        c.product_name AS product_name,
        c.operation_code AS operation_code,
        c.operation_name AS operation_name,
        c.forecast_month AS forecast_month,
        c.demand_quantity AS demand_quantity,
        a.supply_month AS supply_month,
        a.supply_quantity AS supply_quantity,
        a.supply_model AS supply_model,
        a.resource_code AS resource_code,
        a.resource_name AS resource_name,
        a.lock_status AS lock_status,
        a.beat AS beat,
        a.rule AS rule
        FROM
        a
        JOIN
        c
        ON
        a.product_code = c.product_code
        AND a.forecast_month = c.forecast_month
        AND a.operation_code = c.operation_code
        AND a.operation_name = c.operation_name
        AND a.supply_model = c.supply_model
        <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
            and a.vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
        </if>
        <if test="params.productCode != null and params.productCode != ''">
            and a.product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.productName != null and params.productName != ''">
            and a.product_name = #{params.productName,jdbcType=VARCHAR}
        </if>
        <if test="params.operationCode != null and params.operationCode != ''">
            and a.operation_code = #{params.operationCode,jdbcType=VARCHAR}
        </if>
        <if test="params.operationName != null and params.operationName != ''">
            and a.operation_name = #{params.operationName,jdbcType=VARCHAR}
        </if>
        <if test="params.resourceCode != null and params.resourceCode != ''">
            and a.resource_code = #{params.resourceCode,jdbcType=VARCHAR}
        </if>
        <if test="params.resourceName != null and params.resourceName != ''">
            and a.resource_name = #{params.resourceName,jdbcType=VARCHAR}
        </if>
        <if test="params.forecastMonth != null and params.forecastMonth != ''">
            and a.forecast_month = #{params.forecastMonth,jdbcType=VARCHAR}
        </if>
        <if test="params.supplyMonth != null and params.supplyMonth != ''">
            and a.supply_month = #{params.supplyMonth,jdbcType=VARCHAR}
        </if>
        <if test="params.lockStatus != null and params.lockStatus != ''">
            and a.lock_status = #{params.lockStatus,jdbcType=VARCHAR}
        </if>
        <if test="params.rule != null and params.rule != ''">
            and a.rule = #{params.rule,jdbcType=VARCHAR}
        </if>
        <if test="params.supplyModel != null and params.supplyModel != ''">
            and a.supply_model = #{params.supplyModel,jdbcType=VARCHAR}
        </if>
        ORDER BY
        c.forecast_month;
    </select>

    <select id="selectCollectByParamsNew" resultMap="VOResultMap">
        SELECT
        `a`.`id` AS `id`,
        `d`.`routing_step_id` AS `routing_step_id`,
        `d`.`vehicle_model_code` AS `vehicle_model_code`,
        `c`.`product_code` AS `product_code`,
        `d`.`product_name` AS `product_name`,
        `c`.`operation_code` AS `operation_code`,
        `d`.`operation_name` AS `operation_name`,
        `c`.`forecast_month` AS `forecast_month`,
        `c`.`demand_quantity` AS `demand_quantity`,
        `a`.`supply_month` AS `supply_month`,
        `a`.`supply_quantity` AS `supply_quantity`,
        `a`.`supply_model` AS `supply_model`,
        `a`.`resource_code` AS `resource_code`,
        `d`.`resource_name` AS `resource_name`,
        `a`.`lock_status` AS `lock_status`,
        `d`.`beat` AS `beat`,
        `a`.`rule` AS `rule`
        FROM
        (
        SELECT
        min(`t`.`id`) AS `id`,
        `t`.`product_code` AS `product_code`,
        `t`.`operation_code` AS `operation_code`,
        `t`.`forecast_month` AS `forecast_month`,
        `t`.`supply_month` AS `supply_month`,
        sum(`t`.`supply_quantity`) AS `supply_quantity`,
        `t`.`supply_model` AS `supply_model`,
        `t`.`resource_code` AS `resource_code`,
        `t`.`lock_status` AS `lock_status`,
        `t`.`rule` AS `rule`
        FROM
        `mps_capacity_supply_relationship` `t`
        where
        `t`.`version_id` = 'NEW'
        GROUP BY
        `t`.`product_code`,
        `t`.`operation_code`,
        `t`.`forecast_month`,
        `t`.`supply_month`,
        `t`.`supply_model`,
        `t`.`resource_code`,
        `t`.`lock_status`,
        `t`.`rule`
        ) a
        left join (
        SELECT
        `b`.`product_code` AS `product_code`,
        `b`.`forecast_month` AS `forecast_month`,
        sum(`b`.`demand_quantity`) AS `demand_quantity`,
        `b`.`operation_code` AS `operation_code`,
        `b`.`supply_model` AS `supply_model`
        FROM
        `mps_capacity_supply_relationship` `b`
        where
        `b`.`version_id` = 'NEW'
        GROUP BY
        `b`.`product_code`,
        `b`.`forecast_month`,
        `b`.`operation_code`,
        `b`.`supply_model`
        ) c on `c`.`product_code` = `a`.`product_code`
        AND `c`.`forecast_month` = `a`.`forecast_month`
        AND `c`.`operation_code` = `a`.`operation_code`
        AND `c`.`supply_model` = `a`.`supply_model`
        inner join mps_capacity_supply_relationship d on d.id = a.id
        <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
            and d.vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
        </if>
        <if test="params.productCode != null and params.productCode != ''">
            and a.product_code = #{params.productCode,jdbcType=VARCHAR}
        </if>
        <if test="params.productName != null and params.productName != ''">
            and d.product_name = #{params.productName,jdbcType=VARCHAR}
        </if>
        <if test="params.operationCode != null and params.operationCode != ''">
            and a.operation_code = #{params.operationCode,jdbcType=VARCHAR}
        </if>
        <if test="params.operationName != null and params.operationName != ''">
            and d.operation_name = #{params.operationName,jdbcType=VARCHAR}
        </if>
        <if test="params.resourceCode != null and params.resourceCode != ''">
            and a.resource_code = #{params.resourceCode,jdbcType=VARCHAR}
        </if>
        <if test="params.resourceName != null and params.resourceName != ''">
            and d.resource_name = #{params.resourceName,jdbcType=VARCHAR}
        </if>
        <if test="params.forecastMonth != null and params.forecastMonth != ''">
            and a.forecast_month = #{params.forecastMonth,jdbcType=VARCHAR}
        </if>
        <if test="params.supplyMonth != null and params.supplyMonth != ''">
            and a.supply_month = #{params.supplyMonth,jdbcType=VARCHAR}
        </if>
        <if test="params.lockStatus != null and params.lockStatus != ''">
            and a.lock_status = #{params.lockStatus,jdbcType=VARCHAR}
        </if>
        <if test="params.rule != null and params.rule != ''">
            and a.rule = #{params.rule,jdbcType=VARCHAR}
        </if>
        <if test="params.supplyModel != null and params.supplyModel != ''">
            and a.supply_model = #{params.supplyModel,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="saveCapacitySupplyRelationshipOnVersion">
        {call SaveCapacitySupplyRelationship(#{versionId,jdbcType=VARCHAR},#{startId,jdbcType=VARCHAR},#{date,jdbcType=TIMESTAMP},#{userId,jdbcType=VARCHAR})}
    </select>
</mapper>
