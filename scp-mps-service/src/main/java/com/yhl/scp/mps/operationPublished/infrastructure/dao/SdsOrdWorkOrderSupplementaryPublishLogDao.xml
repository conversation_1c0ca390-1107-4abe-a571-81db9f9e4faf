<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.operationPublished.infrastructure.dao.SdsOrdWorkOrderSupplementaryPublishLogDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mps.operationPublished.infrastructure.po.SdsOrdWorkOrderSupplementaryPublishLogPO">
        <!--@Table sds_ord_work_order_supplementary_publish_log-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="test_order_number" jdbcType="VARCHAR" property="testOrderNumber"/>
        <result column="order_reason" jdbcType="VARCHAR" property="orderReason"/>
        <result column="generate_type" jdbcType="VARCHAR" property="generateType"/>
        <result column="order_time" jdbcType="TIMESTAMP" property="orderTime"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mps.operationPublished.vo.SdsOrdWorkOrderSupplementaryPublishLogVO">
        <!-- TODO -->
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="inventory_item_id" jdbcType="VARCHAR" property="inventoryItemId"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="risk_level" jdbcType="VARCHAR" property="riskLevel"/>
        <result column="plan_no" jdbcType="VARCHAR" property="planNo"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="order_planner" jdbcType="VARCHAR" property="orderPlanner"/>
        <result column="in_warehouse_quantity" jdbcType="VARCHAR" property="inWarehouseQuantity"/>
        <result column="un_warehouse_quantity" jdbcType="VARCHAR" property="unWarehouseQuantity"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="approval_status" jdbcType="VARCHAR" property="approvalStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,order_no,product_id,quantity,order_type,test_order_number,order_time,enabled,creator,create_time,modifier,modify_time,version_value,order_reason,generate_type
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,product_code,inventory_item_id,plan_no,plan_status,stock_point_code,product_type,risk_level,order_planner,in_warehouse_quantity,un_warehouse_quantity,vehicle_model_code,approval_status
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids != ''">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                and order_no = #{params.orderNo,jdbcType=VARCHAR}
            </if>
            <if test="params.orderNos != null and params.orderNos.size() > 0">
                and order_no in
                <foreach collection="params.orderNos" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.productIds != null and params.productIds.size() > 0">
                and product_id in
                <foreach collection="params.productIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.quantity != null">
                and quantity = #{params.quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.orderType != null and params.orderType != ''">
                and order_type = #{params.orderType,jdbcType=VARCHAR}
            </if>
            <if test="params.testOrderNumber != null and params.testOrderNumber != ''">
                and test_order_number = #{params.testOrderNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.orderTime != null">
                and order_time = #{params.orderTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryItemId != null and params.inventoryItemId != ''">
                and inventory_item_id  = #{params.inventoryItemId,jdbcType=VARCHAR}
            </if>
            <if test="params.planNo != null and params.planNo != ''">
                and plan_no = #{params.planNo,jdbcType=VARCHAR}
            </if>
            <if test="params.planNoIsNull != null and params.planNoIsNull != ''">
                and plan_no is null
            </if>
            <if test="params.planStatus != null and params.planStatus != ''">
                and plan_status = #{params.planStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.planStatusList != null and params.planStatusList.size > 0">
                and plan_status in
                <foreach collection="params.planStatusList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.planStatus != null and params.planStatus != ''">
                and order_reason = #{params.orderReason,jdbcType=VARCHAR}
            </if>
            <if test="params.generateType != null and params.generateType != ''">
                and generate_type = #{params.generateType,jdbcType=VARCHAR}
            </if>
            <if test="params.vehicleModelCode != null and params.vehicleModelCode != ''">
                and vehicle_model_code = #{params.vehicleModelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.approvalStatus != null and params.approvalStatus != ''">
                and approval_status = #{params.approvalStatus,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_ord_work_order_supplementary_publish_log
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_ord_work_order_supplementary_publish_log
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_sds_ord_work_order_supplementary_publish_log
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_ord_work_order_supplementary_publish_log
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_sds_ord_work_order_supplementary_publish_log
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.SdsOrdWorkOrderSupplementaryPublishLogPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sds_ord_work_order_supplementary_publish_log(
        id,
        order_no,
        product_id,
        quantity,
        order_type,
        test_order_number,
        order_reason,
        generate_type,
        order_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{orderNo,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{quantity,jdbcType=VARCHAR},
        #{orderType,jdbcType=VARCHAR},
        #{testOrderNumber,jdbcType=VARCHAR},
        #{orderReason,jdbcType=VARCHAR},
        #{generateType,jdbcType=VARCHAR},
        #{orderTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.SdsOrdWorkOrderSupplementaryPublishLogPO">
        insert into sds_ord_work_order_supplementary_publish_log(id,
                                                                 order_no,
                                                                 product_id,
                                                                 quantity,
                                                                 order_type,
                                                                 test_order_number,
                                                                 order_time,
                                                                 order_reason,
                                                                 generate_type,
                                                                 enabled,
                                                                 creator,
                                                                 create_time,
                                                                 modifier,
                                                                 modify_time,
                                                                 version_value)
        values (#{id,jdbcType=VARCHAR},
                #{orderNo,jdbcType=VARCHAR},
                #{productId,jdbcType=VARCHAR},
                #{quantity,jdbcType=VARCHAR},
                #{orderType,jdbcType=VARCHAR},
                #{testOrderNumber,jdbcType=VARCHAR},
                #{orderTime,jdbcType=TIMESTAMP},
                #{orderReason,jdbcType=VARCHAR},
                #{generateType,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_ord_work_order_supplementary_publish_log(
        id,
        order_no,
        product_id,
        quantity,
        order_type,
        test_order_number,
        order_time,
        order_reason,
        generate_type,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.orderNo,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.quantity,jdbcType=VARCHAR},
            #{entity.orderType,jdbcType=VARCHAR},
            #{entity.testOrderNumber,jdbcType=VARCHAR},
            #{entity.orderTime,jdbcType=TIMESTAMP},
            #{entity.orderReason,jdbcType=VARCHAR},
            #{entity.generateType,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sds_ord_work_order_supplementary_publish_log(
        id,
        order_no,
        product_id,
        quantity,
        order_type,
        test_order_number,
        order_time,
        order_reason,
        generate_type,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.orderNo,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.quantity,jdbcType=VARCHAR},
            #{entity.orderType,jdbcType=VARCHAR},
            #{entity.testOrderNumber,jdbcType=VARCHAR},
            #{entity.orderTime,jdbcType=TIMESTAMP},
            #{entity.orderReason,jdbcType=VARCHAR},
            #{entity.generateType,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.SdsOrdWorkOrderSupplementaryPublishLogPO">
        update sds_ord_work_order_supplementary_publish_log
        set order_no          = #{orderNo,jdbcType=VARCHAR},
            product_id        = #{productId,jdbcType=VARCHAR},
            quantity          = #{quantity,jdbcType=VARCHAR},
            order_type        = #{orderType,jdbcType=VARCHAR},
            test_order_number = #{testOrderNumber,jdbcType=VARCHAR},
            order_time        = #{orderTime,jdbcType=TIMESTAMP},
            order_reason      = #{orderReason,jdbcType=VARCHAR},
            generate_type     = #{generateType,jdbcType=VARCHAR},
            enabled           = #{enabled,jdbcType=VARCHAR},
            modifier          = #{modifier,jdbcType=VARCHAR},
            modify_time       = #{modifyTime,jdbcType=TIMESTAMP},
            version_value     = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.SdsOrdWorkOrderSupplementaryPublishLogPO">
        update sds_ord_work_order_supplementary_publish_log
        <set>
            <if test="item.orderNo != null and item.orderNo != ''">
                order_no = #{item.orderNo,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.orderType != null and item.orderType != ''">
                order_type = #{item.orderType,jdbcType=VARCHAR},
            </if>
            <if test="item.testOrderNumber != null and item.testOrderNumber != ''">
                test_order_number = #{item.testOrderNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.orderTime != null">
                order_time = #{item.orderTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.orderReason != null and item.orderReason != ''">
                order_reason = #{item.orderReason,jdbcType=VARCHAR},
            </if>
            <if test="item.generateType != null and item.generateType != ''">
                generate_type = #{item.generateType,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_ord_work_order_supplementary_publish_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="test_order_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.testOrderNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="order_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="generate_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                        when id = #{item.id,jdbcType=VARCHAR} then #{item.generateType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sds_ord_work_order_supplementary_publish_log
            <set>
                <if test="item.orderNo != null and item.orderNo != ''">
                    order_no = #{item.orderNo,jdbcType=VARCHAR},
                </if>
                <if test="item.productId != null and item.productId != ''">
                    product_id = #{item.productId,jdbcType=VARCHAR},
                </if>
                <if test="item.quantity != null">
                    quantity = #{item.quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.orderType != null and item.orderType != ''">
                    order_type = #{item.orderType,jdbcType=VARCHAR},
                </if>
                <if test="item.testOrderNumber != null and item.testOrderNumber != ''">
                    test_order_number = #{item.testOrderNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.orderReason != null and item.orderReason != ''">
                    order_reason = #{item.orderReason,jdbcType=VARCHAR},
                </if>
                <if test="item.generateType != null and item.generateType != ''">
                    generate_type = #{item.generateType,jdbcType=VARCHAR},
                </if>
                <if test="item.orderTime != null">
                    order_time = #{item.orderTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from sds_ord_work_order_supplementary_publish_log
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_ord_work_order_supplementary_publish_log where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
