package com.yhl.scp.mps.subInventoryCargoLocation.convertor;

import com.yhl.scp.mps.subInventoryCargoLocation.domain.entity.SubInventoryCargoLocationDO;
import com.yhl.scp.mps.subInventoryCargoLocation.dto.SubInventoryCargoLocationDTO;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.po.SubInventoryCargoLocationPO;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;


@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface SubInventoryCargoLocationConvertor {

    SubInventoryCargoLocationConvertor INSTANCE = Mappers.getMapper(SubInventoryCargoLocationConvertor.class);

    SubInventoryCargoLocationDO dto2Do(SubInventoryCargoLocationDTO obj);

    SubInventoryCargoLocationDTO do2Dto(SubInventoryCargoLocationDO obj);

    List<SubInventoryCargoLocationDO> dto2Dos(List<SubInventoryCargoLocationDTO> list);

    List<SubInventoryCargoLocationDTO> do2Dtos(List<SubInventoryCargoLocationDO> list);

    SubInventoryCargoLocationVO do2Vo(SubInventoryCargoLocationDO obj);

    SubInventoryCargoLocationVO po2Vo(SubInventoryCargoLocationPO obj);

    List<SubInventoryCargoLocationVO> po2Vos(List<SubInventoryCargoLocationPO> list);

    SubInventoryCargoLocationPO do2Po(SubInventoryCargoLocationDO obj);

    SubInventoryCargoLocationDO po2Do(SubInventoryCargoLocationPO obj);

    SubInventoryCargoLocationPO dto2Po(SubInventoryCargoLocationDTO obj);

    List<SubInventoryCargoLocationPO> dto2Pos(List<SubInventoryCargoLocationDTO> obj);

}
