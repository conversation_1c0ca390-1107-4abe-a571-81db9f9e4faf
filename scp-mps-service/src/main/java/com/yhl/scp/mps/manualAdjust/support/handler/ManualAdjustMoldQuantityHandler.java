package com.yhl.scp.mps.manualAdjust.support.handler;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ams.extension.schedule.dto.AdjustmentParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.algorithm.dto.RzzAdjustmentParam;
import com.yhl.scp.mps.enums.ManualAdjustPositionEnum;
import com.yhl.scp.mps.enums.ManualAdjustTypeEnum;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustParam;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustTarget;
import com.yhl.scp.mps.manualAdjust.support.ManualAdjustSupport;
import com.yhl.scp.sds.basic.order.infrastructure.po.OperationSubTaskBasicPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationSubTaskPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationTaskPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.yhl.platform.common.utils.DateUtils.COMMON_DATE_STR1;

/**
 * 修改模具数量处理器
 *
 * <AUTHOR>
 */
@Component
public class ManualAdjustMoldQuantityHandler extends ManualAdjustSupport {
  @Override
  protected String getCommand() {
    return ManualAdjustTypeEnum.ADJUST_MOLD_QUANTITY.getCode();
  }

  @Override
  protected BaseResponse<Void> verify(ManualAdjustParam param) {
    List<String> operationIds = param.getOperationIds();
    if (CollectionUtils.isEmpty(operationIds)) {
      return BaseResponse.error("工序ID集合参数错误");
    }
    if (operationIds.size() > 1) {
      return BaseResponse.error("仅支持单个操作的调整");
    }
    ManualAdjustTarget targetInfo = param.getTargetInfo();
    if (Objects.isNull(targetInfo)) {
      return BaseResponse.error("目标信息参数错误");
    }
    String targetResourceId = targetInfo.getResourceId();
    if (StringUtils.isBlank(targetResourceId)) {
      return BaseResponse.error("目标资源ID参数错误");
    }
    Integer moldQuantity = param.getMoldQuantity();
    if (Objects.isNull(moldQuantity)) {
      return BaseResponse.error("模具数量参数错误");
    }
    if (moldQuantity <= 0) {
      return BaseResponse.error("模具数量不允许小于等于0");
    }
    OperationPO operation = operationDao.selectByPrimaryKey(operationIds.get(0));
    String productId = operation.getProductId();
    List<NewProductStockPointVO> productStockPoints =
        newMdsFeign.selectProductStockPointByIds(
            SystemHolder.getScenario(), Collections.singletonList(productId));
    if (CollectionUtils.isEmpty(productStockPoints)) {
      return BaseResponse.error("对应产品不存在");
    }
    Integer moldQuantityLimit = productStockPoints.get(0).getMoldQuantityLimit();
    if (param.getMoldQuantity() > moldQuantityLimit) {
//      return BaseResponse.error("当前产品最模具数量不能超过" + moldQuantityLimit);
    }
    if (param.getMoldQuantity().equals(moldQuantityLimit)) {
      return BaseResponse.error("模具数量无变化");
    }
    return BaseResponse.success("校验成功");
  }

  @Override
  protected BaseResponse<Void> executeBackEnd(ManualAdjustParam param) {
    return BaseResponse.success("模具调整成功");
  }

  @Override
  protected List<AdjustmentParam> executeAlgorithm(
          PlanningHorizonVO planningHorizon, ManualAdjustParam param) {
      OperationPO adjustOperation = operationDao.selectByPrimaryKey(param.getOperationIds().get(0));
      String parentId = adjustOperation.getId();
      List<OperationPO> subOperations =
              operationDao.selectByParentIds(Collections.singletonList(parentId));
      if (CollectionUtils.isEmpty(subOperations)) {
          return Lists.newArrayList();
      }
      ManualAdjustTarget targetInfo = param.getTargetInfo();
      String appointStartTime = targetInfo.getAppointStartTime();
      if (StringUtils.isBlank(appointStartTime)) {
          String targetOperationId = targetInfo.getOperationId();
          String position = targetInfo.getPosition();
          if (StringUtils.isNotBlank(targetOperationId)) {
              OperationPO targetOperation = operationDao.selectByPrimaryKey(targetOperationId);
              if (ManualAdjustPositionEnum.AFTER.getCode().equals(position)) {
                  appointStartTime = DateUtils.dateToString(targetOperation.getEndTime(), COMMON_DATE_STR1);
              } else {
                  appointStartTime = DateUtils.dateToString(targetOperation.getStartTime(), COMMON_DATE_STR1);
              }
          } else {
              appointStartTime = DateUtils.dateToString(planningHorizon.getPlanStartTime(), COMMON_DATE_STR1);
          }
          targetInfo.setAppointStartTime(appointStartTime);
      }
      List<OperationPO> operationPOList =
              dataProcess(subOperations, adjustOperation, param.getMoldQuantity());
      if (CollectionUtils.isEmpty(operationPOList)) {
          return Lists.newArrayList();
      }
      List<RzzAdjustmentParam> adjustParams =
              manualAdjustAlgorithmService.getRzzAdjustmentParams(operationPOList, param);
      return manualAdjustAlgorithmService.getHandworkScheduleBatchParams(
              planningHorizon, adjustParams);
  }

  private List<OperationPO> dataProcess(
      List<OperationPO> subOperations, OperationPO adjustOperation, Integer newMoldQty) {
    int oldMoldQty = subOperations.size();
    int diff = newMoldQty - oldMoldQty;
    // 没有增加或减少模具
    if (diff == 0) {
      return Lists.newArrayList();
    }
    List<OperationPO> insertOperationList = Lists.newArrayList();
    List<OperationPO> updateOperationList = Lists.newArrayList();
    List<OperationTaskPO> insertOperationTaskList = Lists.newArrayList();
    List<OperationSubTaskPO> insertOperationSubTaskList = Lists.newArrayList();
    List<String> deleteOperationIdList = Lists.newArrayList();
    // 更新模具数量变化生成相关数据
    subOperations =
        updateMold(
            diff,
            subOperations,
            insertOperationSubTaskList,
            insertOperationTaskList,
            updateOperationList,
            insertOperationList,
            deleteOperationIdList,
            adjustOperation,
            newMoldQty);
    // 现在subOperations所有数量已经根据模具数量重新计算了
    saveDate(
        updateOperationList,
        insertOperationList,
        insertOperationTaskList,
        insertOperationSubTaskList,
        deleteOperationIdList);
    return subOperations;
  }

  private List<OperationPO> updateMold(
      int diff,
      List<OperationPO> subOperations,
      List<OperationSubTaskPO> insertOperationSubTaskList,
      List<OperationTaskPO> insertOperationTaskList,
      List<OperationPO> updateOperationList,
      List<OperationPO> insertOperationList,
      List<String> deleteOperationIdList,
      OperationPO adjustOperation,
      int newMoldQty) {
    // 新增模具，需要新建对应的子operation, operation_task, operation_sub_task
    if (diff > 0) {
      subOperations.sort(Comparator.comparing(OperationPO::getOperationIndex));
      int operationIndex = subOperations.get(subOperations.size() - 1).getOperationIndex() + 2;
      // 新增模具，对应模具全按开始时间最早的子工序进行赋值
      OperationPO subOperationPO = subOperations.get(0);
      List<OperationTaskPO> operationTaskList =
          operationTaskDao.selectByParams(
              ImmutableMap.of("operationIds", Collections.singletonList(subOperationPO.getId())));
      List<OperationSubTaskPO> operationSubTaskList =
          operationSubTaskDao.selectByParams(
              ImmutableMap.of("operationIds", Collections.singletonList(subOperationPO.getId())));
      Map<String, List<OperationSubTaskPO>> subTaskMap =
          operationSubTaskList.stream()
              .collect(Collectors.groupingBy(OperationSubTaskBasicPO::getTaskId));
      String[] split = subOperations.get(0).getOperationCode().split("-");
      for (int i = 0; i < diff; i++) {
        String operationCode = split[0] + "-" + split[1] + "-0" + operationIndex++;
        OperationPO operationPO = new OperationPO();
        String subOperationId = UUIDUtil.getUUID();
        BeanUtils.copyProperties(subOperationPO, operationPO);
        operationPO.setId(subOperationId);
        operationPO.setOperationCode(operationCode);
        for (OperationTaskPO operationTaskPO : operationTaskList) {
          OperationTaskPO insertTask = new OperationTaskPO();
          BeanUtils.copyProperties(operationTaskPO, insertTask);
          insertTask.setId(UUIDUtil.getUUID());
          List<OperationSubTaskPO> operationSubTaskPOS = subTaskMap.get(operationTaskPO.getId());
          for (OperationSubTaskPO operationSubTaskPO : operationSubTaskPOS) {
            OperationSubTaskPO insertSubTask = new OperationSubTaskPO();
            BeanUtils.copyProperties(operationSubTaskPO, insertSubTask);
            insertSubTask.setId(UUIDUtil.getUUID());
            insertSubTask.setTaskId(insertTask.getId());
            insertSubTask.setOperationId(subOperationId);
            insertOperationSubTaskList.add(insertSubTask);
          }
          insertTask.setOperationId(subOperationId);
          insertOperationTaskList.add(insertTask);
        }
        insertOperationList.add(operationPO);
        subOperations.add(operationPO);
      }
    }
    // 需要删掉模具，需要删除对应的子operation, operation_task, operation_sub_task
    else {
      subOperations.sort(Comparator.comparing(OperationPO::getOperationIndex).reversed());
      for (int i = 0; i < Math.abs(diff); i++) {
        OperationPO operationPO = subOperations.get(i);
        deleteOperationIdList.add(operationPO.getId());
      }
      subOperations =
          subOperations.stream()
              .filter(t -> !deleteOperationIdList.contains(t.getId()))
              .collect(Collectors.toList());
    }
    BigDecimal quantity = adjustOperation.getQuantity();

    BigDecimal size = new BigDecimal(newMoldQty);
    // 整除，减少循环次数
    BigDecimal quotient = quantity.divide(size, 0, RoundingMode.DOWN);
    // 取余
    BigDecimal remainder = quantity.remainder(size);
    for (OperationPO subOperation : subOperations) {
      subOperation.setQuantity(quotient);
      updateOperationList.add(subOperation);
    }
    while (remainder.compareTo(BigDecimal.ZERO) > 0) {
      for (OperationPO operationPO : subOperations) {
        operationPO.setQuantity(operationPO.getQuantity().add(BigDecimal.ONE));
        remainder = remainder.subtract(BigDecimal.ONE);
        if (remainder.compareTo(BigDecimal.ZERO) == 0) {
          break;
        }
      }
    }
    return subOperations;
  }

  private void saveDate(
      List<OperationPO> updateOperationList,
      List<OperationPO> insertOperationList,
      List<OperationTaskPO> insertOperationTaskList,
      List<OperationSubTaskPO> insertOperationSubTaskList,
      List<String> deleteOperationIdList) {
    if (CollectionUtils.isNotEmpty(insertOperationList)) {
      BasePOUtils.insertBatchFiller(insertOperationList);
      operationDao.insertBatch(insertOperationList);
    }
    if (CollectionUtils.isNotEmpty(updateOperationList)) {
      BasePOUtils.updateBatchFiller(updateOperationList);
      operationDao.updateBatch(updateOperationList);
    }
    if (CollectionUtils.isNotEmpty(insertOperationTaskList)) {
      BasePOUtils.insertBatchFiller(insertOperationTaskList);
      operationTaskDao.insertBatch(insertOperationTaskList);
    }
    if (CollectionUtils.isNotEmpty(insertOperationSubTaskList)) {
      BasePOUtils.insertBatchFiller(insertOperationSubTaskList);
      operationSubTaskDao.insertBatch(insertOperationSubTaskList);
    }
    if (CollectionUtils.isNotEmpty(deleteOperationIdList)) {
      operationDao.deleteBatch(deleteOperationIdList);
      operationTaskDao.deleteByOperationIds(deleteOperationIdList);
      operationSubTaskDao.deleteByOperationIds(deleteOperationIdList);
    }
  }
}
