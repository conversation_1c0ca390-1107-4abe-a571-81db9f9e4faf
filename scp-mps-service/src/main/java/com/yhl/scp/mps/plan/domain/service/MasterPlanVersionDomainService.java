package com.yhl.scp.mps.plan.domain.service;

import com.yhl.scp.mps.plan.domain.entity.MasterPlanVersionDO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanVersionDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>MasterPlanVersionDomainService</code>
 * <p>
 * 主计划发布版本表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 11:50:45
 */
@Service
public class MasterPlanVersionDomainService {

    @Resource
    private MasterPlanVersionDao masterPlanVersionDao;

    /**
     * 数据校验
     *
     * @param masterPlanVersionDO 领域对象
     */
    public void validation(MasterPlanVersionDO masterPlanVersionDO) {
        checkNotNull(masterPlanVersionDO);
        checkUniqueCode(masterPlanVersionDO);
        // Do nothing because of X and Y.
    }

    /**
     * 非空检验
     *
     * @param masterPlanVersionDO 领域对象
     */
    private void checkNotNull(MasterPlanVersionDO masterPlanVersionDO) {
        // Do nothing because of X and Y.
    }

    /**
     * 唯一性校验
     *
     * @param masterPlanVersionDO 领域对象
     */
    private void checkUniqueCode(MasterPlanVersionDO masterPlanVersionDO) {
        // Do nothing because of X and Y.
    }

}
