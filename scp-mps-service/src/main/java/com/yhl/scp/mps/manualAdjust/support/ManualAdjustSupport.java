package com.yhl.scp.mps.manualAdjust.support;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ams.extension.schedule.dto.AdjustmentParam;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.manualAdjust.dao.ManualAdjustHandleDao;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustParam;
import com.yhl.scp.mps.manualAdjust.service.impl.ManualAdjustAlgorithmService;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.service.MasterPlanInsertOrEditService;
import com.yhl.scp.mps.plan.support.MasterPlanSplitSupport;
import com.yhl.scp.sds.order.infrastructure.dao.*;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import com.yhl.scp.sds.pegging.infrastructure.dao.DemandDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.FulfillmentDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.SupplyDao;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.List;

/**
 * 手工调整抽象类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class ManualAdjustSupport {

    @Resource
    public ManualAdjustHandleDao manualAdjustHandleDao;

    @Resource
    public MdsFeign mdsFeign;

    @Resource
    public OperationDao operationDao;

    @Resource
    public OperationTaskDao operationTaskDao;

    @Resource
    public OperationSubTaskDao operationSubTaskDao;

    @Resource
    public WorkOrderDao workOrderDao;

    @Resource
    public NewMdsFeign newMdsFeign;

    @Resource
    public DemandDao demandDao;

    @Resource
    public SupplyDao supplyDao;

    @Resource
    public FulfillmentDao fulfillmentDao;

    @Resource
    public OperationExtendDao operationExtendDao;

    @Resource
    public OperationInputDao operationInputDao;

    @Resource
    public OperationOutputDao operationOutputDao;

    @Resource
    public MasterPlanSplitSupport masterPlanSplitSupport;

    @Resource
    public OperationTaskExtDao operationTaskExtDao;

    @Resource
    public OperationService operationService;

    @Resource
    public WorkOrderService workOrderService;

    @Resource
    public ManualAdjustAlgorithmService manualAdjustAlgorithmService;

  @Resource
  public MasterPlanInsertOrEditService masterPlanInsertOrEditService;

  protected abstract String getCommand();

    protected abstract BaseResponse<Void> verify(ManualAdjustParam param);

    protected abstract BaseResponse<Void> executeBackEnd(ManualAdjustParam param);

    protected abstract List<AdjustmentParam> executeAlgorithm(
            PlanningHorizonVO planningHorizon, ManualAdjustParam param);
}
