package com.yhl.scp.mps.algorithm.strategy.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.yhl.aps.api.obj.Operation;
import com.yhl.aps.api.obj.Order;
import com.yhl.aps.api.obj.WorkOrder;
import com.yhl.aps.api.runner.APSInput;
import com.yhl.aps.api.runner.APSOutput;
import com.yhl.aps.api.runner.APSRunner;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.basic.rule.enums.RuleEncodingsEnum;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mps.adjustmantSuggestion.dto.SdsAdjustmentSuggestionDTO;
import com.yhl.scp.mps.adjustmantSuggestion.service.SdsAdjustmentSuggestionService;
import com.yhl.scp.mps.algorithm.schedule.input.*;
import com.yhl.scp.mps.algorithm.schedule.output.RzzOperationDTO;
import com.yhl.scp.mps.domain.algorithm.RzzAlgorithmOutputService;
import com.yhl.scp.mps.domain.algorithm.RzzBaseAlgorithmDataConfigService;
import com.yhl.scp.mps.domain.algorithm.RzzBaseAlgorithmDataService;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.rule.enums.AlgorithmConstraintRuleEnum;
import com.yhl.scp.sds.extension.order.dto.OperationSubTaskDTO;
import com.yhl.scp.sds.extension.order.dto.OperationTaskDTO;
import com.yhl.scp.sds.extension.order.dto.WorkOrderDTO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.service.WorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum.SALE_ORGANIZATION;

/**
 * <code>DelayAdjustCommand</code>
 * <p>
 * 延期调整命令
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-14 16:33:12
 */
@Slf4j
@Component
public class DelayAdjustCommand extends RzzBaseAlgorithmDataConfigService {

    @Resource
    private RzzBaseAlgorithmDataService baseAlgorithmDataService;
    @Resource
    private RzzAlgorithmOutputService algorithmOutputService;
    @Resource
    private MasterPlanExtDao masterPlanExtDao;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private OperationDao operationDao;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private MdsFeign mdsFeign;
    @Resource
    private SdsAdjustmentSuggestionService sdsAdjustmentSuggestionService;


    public void executeCommand(String operationId) {
        AlgorithmLog amsAlgoLog = createAmsAlgoLog(null, ModuleCodeEnum.DELAY_ADJUST.getCode(), new ArrayList<>());
        String logId = amsAlgoLog.getId();
        log.info("开始运行延期调整建议算法");
        try {
            //组装算法参数
            RzzBaseAlgorithmData algorithmData = packageRzzAlgorithmData(operationId);

            APSInput apsInput = baseAlgorithmDataService.getApsInput(algorithmData, getOs(logId));
            //配置构建
            writeConf(apsInput, algorithmData);

            // 求解函数
            APSRunner apsRunner = new APSRunner();
            // 调用算法
            APSOutput apsOutput = apsRunner.run(apsInput);
            // 解析算法输出
            doAnalysisAlgorithmOutputData(apsOutput);

            amsAlgoLog.setStatus(AlgorithmLogStatusEnum.SUCCESS.getCode());
        } catch (Exception e) {
            log.error("延期调整建议算法运行失败：", e);
            amsAlgoLog.setStatus(AlgorithmLogStatusEnum.FAIL.getCode());
            amsAlgoLog.setFailMsg(e.getMessage());
            throw new BusinessException("延期调整建议运行失败：" + e.getMessage());
        } finally {
            amsAlgoLog.setEndTime(new Date());
            CompletableFuture.runAsync(() -> ipsFeign.updateAlgorithmLog(amsAlgoLog));
            log.info("延期调整建议算法日志更新完成");
        }

    }

    @Override
    protected void writeConf(APSInput apsInput, RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject conf = getConf();
        conf.put("runTimeParameter", new JSONObject());
        JSONArray modules = conf.getJSONArray("modules");

        ScheduleParamDetail scheduleParamDetail = baseAlgorithmData.getScheduleParamDetail();
        JSONObject confLoaderObject = getConfLoader(scheduleParamDetail.getBeginTime(), scheduleParamDetail.getEndTime());
        JSONObject confLoader = confLoaderObject.getJSONObject("confLoader");

        //排程方向
        confLoader.put("scheduleDirection", scheduleParamDetail.getScheduleDirection());
        //资源选择策略
        confLoader.put("resourceSelectionStrategy", scheduleParamDetail.getResourceSelectionStrategy());
        // 资源排序逻辑
        confLoader.put("operationAssignStrategy", scheduleParamDetail.getOperationAssignStrategy());
        // 工序排序规则
        confLoader.put("operatorSortStrategy", scheduleParamDetail.getOperatorSortStrategy());
        confLoader.put("noSwitchIfSameProduct", scheduleParamDetail.getNoSwitchIfSameProduct());
        confLoader.put("inWorkOrder", scheduleParamDetail.isInWorkOrder());

        modules.add(confLoaderObject);
//        modules.add(getDataLoader());
        modules.add(getRuleLoader());
        //连续生产

        //插单模式下并且是插入中间，则sovel4Rule改成insertOrderModule
        if (scheduleParamDetail.getWhetherInsertOrderMiddleModule()) {
            modules.add(getInsertOrderModule());
        } else {
            //生产排程特有的配置
            modules.add(getSolver4Rule(baseAlgorithmData));
        }
        if (CollectionUtils.isNotEmpty(scheduleParamDetail.getInvertResourceSelectionRule())) {
            modules.add(getAutoAdjustThatOperationsSatisfyDeliveryByLatestEndConf(scheduleParamDetail.getInvertResourceSelectionRule(), scheduleParamDetail.getCurrentResourceInverted()));
        }
        //调整建议模块
        modules.add(getDelayAdjust(baseAlgorithmData));
        //大小片连续生产
        // modules.add(getContinuousProductionModule());

        //拆批插单
    /*    if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_10.getCode())){
            modules.add(getSplitInsertOrderModule(baseAlgorithmData));
        }*/
        modules.add(getResultDumper());

        conf.put("runTimeParameter", getRunTimeParameter(baseAlgorithmData));
        Gson gson = new Gson();
        JsonElement config2 = gson.toJsonTree(conf);
        apsInput.setConfig(config2);
    }

    private JSONObject getRunTimeParameter(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject jsonObject = new JSONObject();
        //待排工序
        jsonObject.put("operationsNeedSchedule", baseAlgorithmData.getNeedScheduleOperationIds());
        //供需关系
        jsonObject.put("fulfillment", baseAlgorithmData.getFulfillmentList());
        jsonObject.put("capacity", getCapacityList(baseAlgorithmData));
        //镀膜工序规则 : 排产达到镀膜保养量后进入镀膜维保
        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_1.getCode())) {
            jsonObject.put("resourceMaintenance", getResourceMaintenanceList(baseAlgorithmData));
        }

        if (baseAlgorithmData.ruleEnable(AlgorithmConstraintRuleEnum.COMMON_RULE_8.getCode())) {
            //镀膜切换时间-产品切换时间表
            jsonObject.put("switchDurForProduct", getSwitchDurForProductList(baseAlgorithmData));
            //镀膜切换时间-规格切换时间表
            jsonObject.put("switchDurForSpec", getSwitchDurForSpecList(baseAlgorithmData));
            //工具资源切换时间-清洗时间换型
//            jsonObject.put("switchDurForTool", getSwitchDurForToolList(baseAlgorithmData));
        }

        return jsonObject;
    }

    private JSONObject getDelayAdjust(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject jsonObject = new JSONObject();
        JSONObject delayAdjust = new JSONObject();
        delayAdjust.put("adjustOperationIds", baseAlgorithmData.getDelayAdjustOperationIds());
        JSONArray canAdjustTags = new JSONArray();
        canAdjustTags.add("可延期");
        delayAdjust.put("canAdjustTags", canAdjustTags);
        delayAdjust.put("safeStocks", getsSafeStocks(baseAlgorithmData));
        delayAdjust.put("adjustQuantity", getAdjustQuantity(baseAlgorithmData));

        jsonObject.put("delayAdjust", delayAdjust);
        return jsonObject;
    }

    private JSONArray getsSafeStocks(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONArray jsonArray = new JSONArray();
        List<RzzWorkOrderData> workOrders = baseAlgorithmData.getWorkOrders();
        Map<String, WorkOrderVO> workOrderVOMap = baseAlgorithmData.getWorkOrderVOMap();
        Map<String, List<SafetyStockLevelVO>> stockLevelVOMap = baseAlgorithmData.getStockLevelVOMap();
        Map<String, String> productIdOfCodeMap = baseAlgorithmData.getProductIdOfCodeMap();
        for (RzzWorkOrderData rzzWorkOrderData : workOrders) {
            WorkOrderVO workOrderVO = workOrderVOMap.get(rzzWorkOrderData.getId());
            String productCode = productIdOfCodeMap.get(rzzWorkOrderData.getProductId());
            if (StringUtils.isNotEmpty(workOrderVO.getTopOrderId())) {
                WorkOrderVO topWorkOrderVO = workOrderVOMap.get(workOrderVO.getTopOrderId());
                productCode = productIdOfCodeMap.get(topWorkOrderVO.getProductId());
            }

            String key = String.join("-", rzzWorkOrderData.getStockPointId(), productCode);
            int standardStockDays = 0;
            int minStockDays = 0;
            int maxStockDays = 0;

            if (stockLevelVOMap.containsKey(key)) {
                List<SafetyStockLevelVO> safetyStockLevelVOS = stockLevelVOMap.get(key);
                SafetyStockLevelVO stockLevelVO = safetyStockLevelVOS.stream().filter(t -> t.getStandardStockDay() != null)
                        .min(Comparator.comparing(SafetyStockLevelVO::getStandardStockDay)).orElse(null);
                if (stockLevelVO != null) {
                    standardStockDays = stockLevelVO.getStandardStockDay().compareTo(BigDecimal.ZERO) > 0
                            ? stockLevelVO.getStandardStockDay().intValue() : 0;
                    minStockDays = stockLevelVO.getMinStockDay().compareTo(BigDecimal.ZERO) > 0 ? stockLevelVO.getMinStockDay().intValue() : 0;
                }
                maxStockDays = standardStockDays;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("workOrderId", rzzWorkOrderData.getId());
            jsonObject.put("standardStockDays", standardStockDays);
            jsonObject.put("minStockDays", minStockDays);
            jsonObject.put("maxStockDays", maxStockDays);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    private Map<String, Integer> getAdjustQuantity(RzzBaseAlgorithmData baseAlgorithmData) {
        List<RzzWorkOrderData> workOrders = baseAlgorithmData.getWorkOrders();
        return workOrders.stream()
                .collect(Collectors.toMap(
                        RzzWorkOrderData::getId,
                        workOrder -> Optional.ofNullable(workOrder.getEndingInventoryMinSafeDiff()).orElse(0)
                ));
    }

    private RzzBaseAlgorithmData packageRzzAlgorithmData(String operationId) {
        //可以延期的制造订单
        List<WorkOrderVO> workOrderVOS = masterPlanExtDao.selectDelayAdjustWorkOrder();
        if (CollectionUtils.isEmpty(workOrderVOS)) {
            throw new BusinessException("没有可延期的制造订单!");
        }
        BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MPS.getCode(),
                TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(mdsScenario.getData(), "OP_BUSINESS_AREA", "EXTERNAL_REQ", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        RzzBaseAlgorithmData algorithmData = baseAlgorithmDataService.getAlgorithmData(null, null);
        List<String> delayOrderIds = workOrderVOS.stream().map(WorkOrderVO::getId).collect(Collectors.toList());
        List<RzzOperationData> operations = algorithmData.getOperations().stream().filter(t -> delayOrderIds.contains(t.getWorkOrderId())).collect(Collectors.toList());
        for (RzzOperationData operation : operations) {
            Set<String> tags = operation.getTags();
            tags.add("可延期");
        }
        algorithmData.setDelayAdjustOperationIds(Collections.singletonList(operationId));
        List<String> productCodeList = algorithmData.getProductInSockingPoints().stream().map(RzzProductInSockingPointData::getProductCode).distinct().collect(Collectors.toList());
        //安全库存水位天数
        List<SafetyStockLevelVO> safetyStockLevelVOS = masterPlanExtDao.selectByProductCodeList(productCodeList, rangeData, SALE_ORGANIZATION.getCode());
        Map<String, List<SafetyStockLevelVO>> stockLevelVOMap = safetyStockLevelVOS.stream()
                .collect(Collectors.groupingBy(t -> String.join("-", t.getStockPointId(), t.getProductCode())));
        algorithmData.setStockLevelVOMap(stockLevelVOMap);
        return algorithmData;
    }

    private void doAnalysisAlgorithmOutputData(APSOutput apsOutput) {
        log.info("调整建议算法输出数据解析开始");

        List<Operation> operationVecsa = apsOutput.getOperations();
        List<WorkOrder> workOrderList = operationVecsa.stream().map(Operation::getWorkOrder).distinct().collect(Collectors.toList());
        Map<String, WorkOrder> workOrderIdMap = workOrderList.stream().collect(Collectors.toMap(Order::getId, Function.identity()));

        //制造订单
        List<WorkOrderVO> workOrderVOList = workOrderService.selectByParams(new HashMap<>());
        List<String> updateWorkOrderIds = new ArrayList<>();
        Map<String, LocalDateTime> dueMap = new HashMap<>();
        //找出制造订单的数量和交期发生变化的单子
        for (WorkOrderVO workOrderVO : workOrderVOList) {
            BigDecimal oldQuantity = workOrderVO.getQuantity();
            Date oldDueDate = workOrderVO.getDueDate();
            WorkOrder newWorkOrder = workOrderIdMap.get(workOrderVO.getId());
            double newQuantity = newWorkOrder.getQuantity();
            LocalDateTime newDueDate = newWorkOrder.getDueDate();
            LocalDateTime localDateTime = oldDueDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            if (oldQuantity.doubleValue() != newQuantity || !localDateTime.equals(newDueDate)) {
                updateWorkOrderIds.add(workOrderVO.getId());
                dueMap.put(workOrderVO.getId(), newDueDate);
            }
        }
        //原工序
        List<OperationVO> sourceOperations = operationDao.selectVOByParams(new HashMap<>());
        List<OperationVO> oldOperations = new ArrayList<>(sourceOperations);
        List<OperationVO> oldSubOperationVOS = oldOperations.stream()
                .filter(t -> StringUtils.isNotEmpty(t.getParentId()) && updateWorkOrderIds.contains(t.getOrderId()) && !"NORMEAL_PROCESS".equals(t.getStandardStepType())).collect(Collectors.toList());

        //资源
        CompletableFuture<List<PhysicalResourceVO>> physicalResourceVOListFuture = CompletableFuture.supplyAsync(() -> {
            //资源
            List<PhysicalResourceVO> physicalResourceVOList1 = mdsFeign.getPhysicalResourceParams(new HashMap<>());
            return physicalResourceVOList1;
        });
        //查询子工序编码规则
        CompletableFuture<RuleEncodingsVO> ruleEncodingsFuture = CompletableFuture.supplyAsync(() -> {
            //查询库存点物品
            RuleEncodingsVO ruleEncodings1 = mdsFeign.getRuleEncodingsByRuleName(RuleEncodingsEnum.SUB_OPERATION_CODE.getDesc());
            return ruleEncodings1;
        });
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                physicalResourceVOListFuture,
                ruleEncodingsFuture
        );

        allFutures.join();
        List<PhysicalResourceVO> physicalResourceVOList = physicalResourceVOListFuture.join();
        RuleEncodingsVO ruleEncodings = ruleEncodingsFuture.join();

        log.info("workOrderVOList = {}", workOrderVOList.size());
        log.info("sourceOperations = {}", sourceOperations.size());
        log.info("physicalResourceVOList = {}", physicalResourceVOList.size());

        Map<String, WorkOrderVO> workOrderIdMaps = workOrderVOList.stream().collect(Collectors.toMap(WorkOrderVO::getId, Function.identity()));

        Map<String, OperationVO> operationVoMapOfId = sourceOperations.stream().collect(Collectors.toMap(OperationVO::getId, item -> item));


        Map<String, PhysicalResourceVO> physicalResourceVOMap = physicalResourceVOList.stream().collect(Collectors.toMap(PhysicalResourceVO::getId, item -> item));

        //规则排程结果只需更新demand 和supply的时间
        List<RzzOperationDTO> allOperations = new ArrayList<>();
        List<OperationTaskDTO> operationTasks = new ArrayList<>();
        List<OperationSubTaskDTO> allSubTasks = new ArrayList<>();
        List<WorkOrderDTO> workOrderDTOS = new ArrayList<>();
        List<WorkOrderDTO> updateWorkOrders = new ArrayList<>();
        List<String> operationIds = new ArrayList<>();
        List<String> workOrderIds = new ArrayList<>();
        List<String> needDeleteOperationTaskOperationIds = new ArrayList<>();
        algorithmOutputService.handleOperation(null, operationVecsa, workOrderIdMaps, workOrderIds, workOrderDTOS, operationVoMapOfId,
                allOperations, needDeleteOperationTaskOperationIds, ruleEncodings, physicalResourceVOMap, operationTasks,
                allSubTasks, operationIds, null, new HashMap<>(), null);

        //系统已有工序（子工序）
        Map<String, RzzOperationDTO> updateOperationMap = allOperations.stream()
                .filter(item -> !item.getAlgo() && StringUtils.isNotBlank(item.getParentId()))
                .collect(Collectors.toMap(RzzOperationDTO::getId, Function.identity()));

        List<SdsAdjustmentSuggestionDTO> insertDTO = new ArrayList<>();
        for (OperationVO subOperationVO : oldSubOperationVOS) {
            if (updateOperationMap.containsKey(subOperationVO.getId())) {
                RzzOperationDTO operationDTO = updateOperationMap.get(subOperationVO.getId());
                Date oldStartTime = subOperationVO.getStartTime();
                BigDecimal oldQuantity = subOperationVO.getQuantity();
                String oldPlannedResourceId = subOperationVO.getPlannedResourceId();

                Date newStartTime = operationDTO.getStartTime();
                BigDecimal newQuantity = operationDTO.getQuantity();
                String newPlannedResourceId = operationDTO.getPlannedResourceId();
                LocalDateTime dateTime = dueMap.get(operationDTO.getOrderId());
                Date date = Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());

                if (!oldStartTime.equals(newStartTime) || oldQuantity.compareTo(newQuantity) != 0 || !oldPlannedResourceId.equals(newPlannedResourceId)) {
                    SdsAdjustmentSuggestionDTO suggestionDTO = new SdsAdjustmentSuggestionDTO();
                    suggestionDTO.setId(UUIDUtil.getUUID());
                    suggestionDTO.setSuggestion("压缩本厂安全库存");
                    suggestionDTO.setOperationId(subOperationVO.getId());
                    suggestionDTO.setAdjustmentQuantity(newQuantity);
                    suggestionDTO.setAdjustmentStartTime(newStartTime);
                    suggestionDTO.setAdjustmentResourceId(newPlannedResourceId);
                    suggestionDTO.setAdjustmentDueDate(date);
                    suggestionDTO.setRemark("调整建议算法结果");
                    insertDTO.add(suggestionDTO);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(insertDTO)) {
            sdsAdjustmentSuggestionService.doCreateBatch(insertDTO);
        }
        log.info("调整建议算法输出数据解析结束");
    }
}
