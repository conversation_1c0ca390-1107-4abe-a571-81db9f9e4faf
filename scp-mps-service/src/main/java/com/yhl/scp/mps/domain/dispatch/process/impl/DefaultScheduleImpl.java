package com.yhl.scp.mps.domain.dispatch.process.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.aps.api.runner.APSInput;
import com.yhl.aps.api.runner.APSOutput;
import com.yhl.aps.api.runner.APSRunner;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.*;
import com.yhl.scp.ams.basic.enums.ScheduleTypeEnum;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.das.core.AlgorithmVersion;
import com.yhl.scp.das.core.InputBase;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.ips.algorithm.dto.AlgorithmStepLogDTO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.rule.enums.RuleEncodingsEnum;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepInputDO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.rule.util.RuleEncodingsUtils;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.adjust.dao.AdjustDao;
import com.yhl.scp.mps.algorithm.fulfillment.RzzFulfillment;
import com.yhl.scp.mps.algorithm.schedule.input.RzzBaseAlgorithmData;
import com.yhl.scp.mps.algorithm.schedule.input.RzzOperationData;
import com.yhl.scp.mps.algorithm.schedule.input.RzzResourceData;
import com.yhl.scp.mps.algorithm.schedule.input.RzzWorkOrderData;
import com.yhl.scp.mps.algorithm.strategy.impl.FeedbackCommand;
import com.yhl.scp.mps.cache.service.CacheDelService;
import com.yhl.scp.mps.cache.service.CacheSetService;
import com.yhl.scp.mps.dispatch.ams.input.Schedule;
import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.domain.algorithm.RzzAlgorithmOutputService;
import com.yhl.scp.mps.domain.algorithm.RzzBaseAlgorithmDataService;
import com.yhl.scp.mps.domain.dispatch.IScheduleExtended;
import com.yhl.scp.mps.domain.dispatch.model.context.AmsAnalysisContext;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.domain.dispatch.process.AbstractSplitOrder;
import com.yhl.scp.mps.domain.sync.process.AbstractOperationInputSync;
import com.yhl.scp.mps.enums.PlanStatusEnum;
import com.yhl.scp.mps.manualAdjust.dao.ManualAdjustHandleDao;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.product.service.ProductAdvanceBatchRuleService;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.mps.productionLeadTime.enums.ProductionLeadTimeEnum;
import com.yhl.scp.mps.productionLeadTime.vo.ProductionLeadTimeVO;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.basic.pegging.enums.DemandTypeEnum;
import com.yhl.scp.sds.basic.pegging.enums.FulfillmentStatusEnum;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationInputVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.dto.DemandDTO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.DemandPO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.order.convertor.OperationConvertor;
import com.yhl.scp.sds.pegging.convertor.DemandConvertor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DefaultScheduleImpl extends AbstractSplitOrder implements IScheduleExtended {

    // 限制修改交期的制造订单状态
    private final static List<String> limitStatus = Arrays.asList(PlannedStatusEnum.STARTED.getCode(),
            PlannedStatusEnum.FINISHED.getCode());
    @Resource
    NewMdsFeign newMdsFeign;
    @Value("${schedule.url}")
    private String port;
    @Value("${schedule.create}")
    private String createUri;
    @Resource
    private RzzBaseAlgorithmDataService rzzBaseAlgorithmDataService;
    @Resource
    private RzzAlgorithmOutputService rzzAlgorithmOutputService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private ProductAdvanceBatchRuleService productAdvanceBatchRuleService;
    @Resource
    private CacheSetService cacheSetService;
    @Resource
    private MoldChangeTimeService moldChangeTimeService;
    @Resource
    private FeedbackCommand feedbackCommand;
    @Resource
    private AdjustDao adjustDao;
    @Autowired
    private ManualAdjustHandleDao manualAdjustHandleDao;
    @Resource
    private CacheDelService cacheDelService;


    @Override
    protected BaseResponse rpcDasServer(AlgorithmLog algorithmLog, InputBase inputBase, List<AlgorithmStepLogDTO> algorithmStepLogDTOList) {
        Date timeStep1 = new Date();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        // 调用远程算法服务执行算法
        String url = getBaseUrl(algorithmLog) + createUri;
        log.info("url is {}", url);
        log.info("inputBase is {}", inputBase.toString());
        BaseResponse rpcResult = restTemplate.postForObject(url, inputBase, BaseResponse.class);
        log.info("RPC-das服务算法结果：{}", JSON.toJSONString(rpcResult));
        assert rpcResult != null;
        if (!rpcResult.getSuccess()) {
            log.error("RPC-das服务算法调用失败");
            throw new BusinessException("RPC-das服务算法调用失败");
        }
        log.info("RPC-das服务算法调用成功");
        algorithmStepLogDTOList.add(getStepLog("RPC-DAS服务完成", "MPS", algorithmLog.getId(), timeStep1, new Date()));
        return rpcResult;
    }

    private String getBaseUrl(AlgorithmLog algorithmLog) {
        return "http://" + algorithmLog.getIpAddress() + ":" + port;
    }

    @Override
    protected AlgorithmLog createAmsAlgoLog(AlgorithmLog masterAlgorithmLog) {
        String parentId = null != masterAlgorithmLog ? masterAlgorithmLog.getId() : null;
        String scenario = null != masterAlgorithmLog ? masterAlgorithmLog.getScenario() : SystemHolder.getScenario();
        String userId = null != masterAlgorithmLog ? masterAlgorithmLog.getCreator() : SystemHolder.getUserId();
        String remark = null != masterAlgorithmLog ? masterAlgorithmLog.getRemark() : null;
        String lineGroup = null != masterAlgorithmLog ? masterAlgorithmLog.getLineGroup() : null;
        String productLine = null != masterAlgorithmLog ? masterAlgorithmLog.getProductLine() : null;
        log.info("创建AMS算法日志，parentId：{}，场景scenario：{}，创建者：{}", parentId, scenario, userId);
        String ipAddress = ipsNewFeign.getIpAddress(ModuleCodeEnum.AMS.getCode());
        log.info("AMS-ipAddress地址：{}", ipAddress);
        String logId = UUIDUtil.getUUID();
        Date date = new Date();
        AlgorithmLog algorithmLog = new AlgorithmLog();
        algorithmLog.setId(logId);
        algorithmLog.setExecutionNumber(logId);
        algorithmLog.setModuleCode(ModuleCodeEnum.AMS.getCode());
        algorithmLog.setParentId(parentId);
        algorithmLog.setIpAddress(ipAddress);
        algorithmLog.setStartTime(date);
        algorithmLog.setFilePath("/usr/local/aps/workspace/" + logId);
        algorithmLog.setModifyTime(date);
        algorithmLog.setCreateTime(date);
        algorithmLog.setScenario(scenario);
        algorithmLog.setCreator(userId);
        algorithmLog.setModifier(userId);
        algorithmLog.setRemark(remark);
        algorithmLog.setStatus(AlgorithmLogStatusEnum.RUNNING.getCode());
        algorithmLog.setLineGroup(lineGroup);
        algorithmLog.setProductLine(productLine);
        CompletableFuture.runAsync(() -> ipsFeign.createAlgorithmLog(algorithmLog));
        log.info("AMS算法日志创建完成，logId：{}", logId);
        return algorithmLog;
    }


    @Override
    protected void analysisAmsResult(AlgorithmLog amsAlgoLog, MpsAnalysisContext mpsAnalysisContext) {
        // AMS调用解析
        doRun(mpsAnalysisContext, amsAlgoLog.getId());
        // 标记任务成功
        amsAlgoLog.setStatus(AlgorithmLogStatusEnum.SUCCESS.getCode());
    }


    private List<OperationVO> getNeedOperation(MpsAnalysisContext mpsAnalysisContext) {
        List<String> workOrderIds = mpsAnalysisContext.getWorkOrderIds();
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("orderIds", workOrderIds);
        List<OperationVO> operationVOS = operationService.selectVOByParams(params)
                .stream().filter(p -> StrUtil.isEmpty(p.getParentId())).collect(Collectors.toList());
        log.info("订单计划员：{}，待排AMS订单数量：{}，待排父工序数量：{}", mpsAnalysisContext.getCreatorId(), workOrderIds.size(), operationVOS.size());
        mpsAnalysisContext.setUseOperationList(operationVOS);
        return operationVOS;
    }

    private void doRun(MpsAnalysisContext mpsAnalysisContext, String logId) {
        String creatorId = mpsAnalysisContext.getCreatorId();

        Date dateTime1 = new Date();
        // 后工序正排查询数据
        RzzBaseAlgorithmData algorithmData = rzzBaseAlgorithmDataService.getAlgorithmData(logId, getNeedOperation(mpsAnalysisContext));
        // 后处理关键工序正排的后工序
        afterJustOperation(algorithmData);
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("正排收集数据完成", AMS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime1, new Date()));
        // 正排
        doStart(algorithmData, logId + "/forward", mpsAnalysisContext);
        IOUtils.string2JSONFile(JSON.toJSONString(mpsAnalysisContext.getScheduleInfoMap()),
                "/usr/local/aps/workspace/" + logId + "/", "scheduleInfo.json");

        Date dateTime3 = new Date();
        // 前工序倒排查询数据
        RzzBaseAlgorithmData directionAlgorithmData = rzzBaseAlgorithmDataService.getAlgorithmData(logId, getNeedOperation(mpsAnalysisContext));
        // 后处理倒排输入数据
        afterDirectionOperation(directionAlgorithmData);
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("倒排收集数据完成", AMS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime3, new Date()));
        // 倒排
        doStart(directionAlgorithmData, logId + "/reverse", mpsAnalysisContext);

        Date dateTime4 = new Date();
        // 初始化上下文
        AmsAnalysisContext amsAnalysisContext = initAmsContext(creatorId, mpsAnalysisContext.getAlgorithmLog());
        // 更新工序最早开始时间信息
        afterSetOperationEarliestTime(amsAnalysisContext, mpsAnalysisContext);
        // 后处理包装工序信息计算最新时间
        afterProcessPackingOperation(amsAnalysisContext);
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("AMS结果后处理完成", AMS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime4, new Date()));
        // 刷新制造订单缓存数据
        refreshWorkOrderCache(mpsAnalysisContext);
    }

    private void refreshWorkOrderCache(MpsAnalysisContext mpsAnalysisContext) {
        Date dateTime1 = new Date();
        String scenario = mpsAnalysisContext.getAlgorithmLog().getScenario();
        String userId = mpsAnalysisContext.getAlgorithmLog().getCreator();
        log.info("排程完成刷新制造订单缓存 scenario：{}", scenario);
        // 刷新workOrder缓存
        cacheSetService.refreshWorkOrderCache(scenario);
        // 自动排程完清掉所有按需缓存
        cacheDelService.delMasterPlanCache(scenario, userId);
        log.info("排程结束刷新制造订单缓存结束");
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("刷新制造订单缓存完成", AMS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime1, new Date()));
    }

    private void afterDirectionOperation(RzzBaseAlgorithmData directionAlgorithmData) {
        for (RzzWorkOrderData workOrder : directionAlgorithmData.getWorkOrders()) {
            workOrder.setDueDate(DateUtils.stringToDate("2099-01-01 00:00:00", DateUtils.COMMON_DATE_STR1));
        }
    }

    private void doAdjustSchedule(List<OperationVO> waitingScheduleOperations,
                                  String logId,
                                  List<OperationVO> keyOperationResource
    ) {
        PlanningHorizonVO planningHorizon = otherFeign.getPlanningHorizon();
        List<PhysicalResourceVO> physicalResourceVOS = otherFeign.getPhysicalResourcesByParams(new HashMap<>());
        List<MoldChangeTimeVO> moldChangeTimeVOS = moldChangeTimeService.selectTime();
        // 待排工序信息及资源占位工序信息
        keyOperationResource.addAll(waitingScheduleOperations);
        RzzBaseAlgorithmData algorithmData = rzzBaseAlgorithmDataService.getAlgorithmData(physicalResourceVOS, keyOperationResource,
                waitingScheduleOperations, ScheduleTypeEnum.RULE_AUTO.getCode(), planningHorizon, null,
                null, null, null, moldChangeTimeVOS, null, null);
        // 标记排程方向
        algorithmData.setDirection(Boolean.FALSE);
        algorithmData.setWhetherAdjust(Boolean.TRUE);
        // 构建AMS输入数据
        APSInput apsInput = rzzBaseAlgorithmDataService.getApsInput(algorithmData, getOs(logId));
        // 写入配置文件conf.json
        rzzBaseAlgorithmDataService.writeConf(apsInput, algorithmData);
        // 求解函数
        APSRunner apsRunner = new APSRunner();
        // 调用算法
        APSOutput apsOutput = apsRunner.run(apsInput);
        // 解析算法输出
        rzzAlgorithmOutputService.doAnalysisAlgorithmOutputDataCommon(apsOutput, new ArrayList<>(algorithmData.getWorkOrderVOMap().values()),
                algorithmData.getSourceOperations(), logId, planningHorizon, false, new HashMap<>());
    }

    /**
     * 后处理包装工序需求时间
     */
    private void afterProcessPackingOperation(AmsAnalysisContext amsAnalysisContext) {
        log.info("后处理包装工序需求时间开始");
        // 2.重新计算发货计划的交期及库存：MAX（发货时间 ➖ 标准安全库存天数 ➖ 包装工序后处理时间 ➖ 该物料的发货数量 X 包装工序的生产节拍，计划期间开始时间）
        adjustDeliveryPlanTimes(amsAnalysisContext);
        // 3.使用计划批量覆盖剩余得发货计划并生成demand
        deliveryDemandPeg(amsAnalysisContext);
        // 4.数据库操作
        dataOperation(amsAnalysisContext);
        log.info("后处理包装工序需求时间结束");
    }

    private void dataOperation(AmsAnalysisContext amsAnalysisContext) {
        List<DemandVO> allDemandVOs = amsAnalysisContext.getDemandVOResult();
        log.info("后处理包装工序添加demand数量：{}", allDemandVOs.size());
        List<DemandDTO> demandDTOList = DemandConvertor.INSTANCE.vo2Dtos(allDemandVOs);
        List<DemandPO> demandPOS = DemandConvertor.INSTANCE.dto2Pos(demandDTOList);
        BasePOUtils.insertBatchFiller(demandPOS);
        List<List<DemandPO>> partition = Lists.partition(demandPOS, 2000);
        for (List<DemandPO> demandPOList : partition) {
            demandDao.insertBatch(demandPOList);
        }
        List<String> deleteDemandIds = amsAnalysisContext.getWaitingDeleteDemand().stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deleteDemandIds)) {
            log.info("包装需求计算待删除的demand数量：{}", deleteDemandIds.size());
            Lists.partition(deleteDemandIds, 2000).forEach(d -> demandDao.deleteBatch(d));
        }
    }

    private void deliveryDemandPeg(AmsAnalysisContext amsAnalysisContext) {
        String packing = "包装";
        RuleEncodingsVO ruleEncodingsVO = otherFeign.getRuleEncodingsByRuleName(RuleEncodingsEnum.WORK_ORDER_DEMAND.getDesc());
        for (Map.Entry<String, List<WorkOrderVO>> entry : amsAnalysisContext.getWorkOrderMapOnProductId().entrySet()) {
            String productId = entry.getKey();
            List<WorkOrderVO> workOrderVOS = entry.getValue();
            workOrderVOS.sort(Comparator.comparing(WorkOrderVO::getDueDate));
            NewProductStockPointVO newProductStockPointVO = amsAnalysisContext.getProductStockPointMapOnId().get(productId);
            if (newProductStockPointVO == null) continue;
            String productCode = newProductStockPointVO.getProductCode();
            if (!amsAnalysisContext.getDeliveryPlanPublishedMapOnProductCode().containsKey(productCode)) continue;
            // 当前物料得发货计划，用制造订单批量进行覆盖
            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = amsAnalysisContext.getDeliveryPlanPublishedMapOnProductCode().get(productCode)
                    .stream().filter(p -> p.getDemandQuantity() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(deliveryPlanPublishedVOS)) {
                continue;
            }
            deliveryPlanPublishedVOS.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));
            // 发货计划总览覆盖
            Map<String, Integer> qtyMap = deliveryPlanPublishedVOS.stream().collect(Collectors
                    .groupingBy(p -> DateUtils.dateToString(p.getDemandTime(), DateUtils.COMMON_DATE_STR3),
                            Collectors.summingInt(DeliveryPlanPublishedVO::getDemandQuantity)));

            // 按照制造订单批次交期依次覆盖
            for (int i = 0; i < workOrderVOS.size(); i++) {
                WorkOrderVO workOrderVO = workOrderVOS.get(i);
                String planStatus = workOrderVO.getPlanStatus();
                if (planStatus.equals(PlanStatusEnum.UNPLAN.getCode())) {
                    continue;
                }
                String id = workOrderVO.getId();
                List<OperationVO> operationVOS = amsAnalysisContext.getOperationMapOnOrderId().get(id);
                if (CollectionUtils.isEmpty(operationVOS)) {
                    log.warn("制造订单已计划，没有工序：{}", id);
                    continue;
                }
                operationVOS = operationVOS.stream().filter(p -> StrUtil.isEmpty(p.getParentId()))
                        .sorted(Comparator.comparing(OperationVO::getRoutingStepSequenceNo).reversed()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(operationVOS)) {
                    log.warn("制造订单已计划，没有父工序：{}", id);
                    continue;
                }
                // 获取最后一道工序判断是否是包装工序
                OperationVO lastOperationVO = operationVOS.get(0);
                if (!amsAnalysisContext.getOperationInputMapOnOperationId().containsKey(lastOperationVO.getId())) {
                    continue;
                }
                String operationPlanStatus = lastOperationVO.getPlanStatus();
                if (operationPlanStatus.equals(PlanStatusEnum.UNPLAN.getCode())) {
                    continue;
                }
                String standardStepId = lastOperationVO.getStandardStepId();
                StandardStepVO standardStepVO = amsAnalysisContext.getStandardStepMapOnId().get(standardStepId);
                String standardStepName = standardStepVO.getStandardStepName();
                if (!standardStepName.equals(packing)) {
                    continue;
                }
                int scheduleQuantity = lastOperationVO.getQuantity().intValue();
                Map<String, Integer> orderIdToCurrentMaxSerialNumMap = new HashMap<>();
                // 编码规则顺序号
                getSequence(id, orderIdToCurrentMaxSerialNumMap, amsAnalysisContext, ruleEncodingsVO);
                List<DemandVO> demandVOS = new ArrayList<>();
                for (DeliveryPlanPublishedVO deliveryPlanPublishedVO : deliveryPlanPublishedVOS) {
                    Date demandTime = deliveryPlanPublishedVO.getDemandTime();
                    String demandTimeStr = DateUtils.dateToString(demandTime, DateUtils.COMMON_DATE_STR3);
                    Integer demandQuantity = qtyMap.get(demandTimeStr);
                    if (demandQuantity == 0) {
                        continue;
                    }
                    if (demandQuantity <= scheduleQuantity) {
                        scheduleQuantity -= demandQuantity;
                        qtyMap.put(demandTimeStr, 0);
                        // 生成对应的demand信息
                        createDemand(workOrderVO, lastOperationVO, demandQuantity, amsAnalysisContext,
                                newProductStockPointVO, orderIdToCurrentMaxSerialNumMap, ruleEncodingsVO, demandTime, demandVOS, deliveryPlanPublishedVO);
                    } else {
                        int qty = demandQuantity - scheduleQuantity;
                        qtyMap.put(demandTimeStr, qty);
                        if (i == workOrderVOS.size() - 1) {
                            // 如果最后一个制造订单的数量小于需求数量，则将需求数量直接=当天的发货计划数量
                            scheduleQuantity = demandQuantity;
                        }
                        // 生成对应的demand信息
                        createDemand(workOrderVO, lastOperationVO, scheduleQuantity, amsAnalysisContext,
                                newProductStockPointVO, orderIdToCurrentMaxSerialNumMap, ruleEncodingsVO, demandTime, demandVOS, deliveryPlanPublishedVO);
                        break;
                    }
                }
                // 重新生成工序输入产生的demand，删除已有的包装工序的demand
                getWaitingDeleteDemand(lastOperationVO, amsAnalysisContext, demandVOS);
            }
        }
        newMdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsVO);
    }

    private void getWaitingDeleteDemand(OperationVO lastOperationVO, AmsAnalysisContext amsAnalysisContext, List<DemandVO> demandVOS) {
        String operationId = lastOperationVO.getId();
        Map<String, List<DemandVO>> newDemandMap = StreamUtils.mapListByColumn(demandVOS, DemandVO::getOperationInputId);
        List<OperationInputVO> operationInputs = amsAnalysisContext.getOperationInputMapOnOperationId().get(operationId);
        if (CollectionUtils.isEmpty(operationInputs)) {
            return;
        }
        String customerOrderDemandCode = DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode();
        for (OperationInputVO operationInputVO : operationInputs) {
            String inputId = operationInputVO.getId();
            // 如果没有生成过该 operationInput 的包装需求，则跳过
            if (!newDemandMap.containsKey(inputId)) {
                continue;
            }
            List<DemandVO> waitingDeleteDemands = amsAnalysisContext.getDemandMapOnOperationInputId().get(inputId);
            if (CollectionUtils.isEmpty(waitingDeleteDemands)) {
                continue;
            }
            List<String> demandIds = waitingDeleteDemands.stream()
                    .filter(p -> !p.getDemandType().equals(customerOrderDemandCode))
                    .map(DemandVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(demandIds)) {
                amsAnalysisContext.getWaitingDeleteDemand().addAll(demandIds);
            }
        }
    }


    private void getSequence(String id, Map<String, Integer> orderIdToCurrentMaxSerialNumMap, AmsAnalysisContext amsAnalysisContext, RuleEncodingsVO ruleEncodingsVO) {
        List<DemandVO> demands = amsAnalysisContext.getDemandMapOnOrderId().get(id);
        if (CollectionUtils.isNotEmpty(demands)) {
            Map<String, List<DemandVO>> orderIdToDemandMaps = demands.stream().collect(Collectors.groupingBy(DemandVO::getDemandOrderId));
            orderIdToDemandMaps.forEach((orderId, demandList) -> {
                if (org.apache.commons.collections4.CollectionUtils.isEmpty(demandList)) {
                    orderIdToCurrentMaxSerialNumMap.put(orderId, 0);
                } else {
                    String maxDemandCode = demandList.stream().max(Comparator.comparing(DemandVO::getDemandCode)).get().getDemandCode();
                    //获取流水号位数
                    String serialNumberLength = ruleEncodingsVO.getSerialNumberLength();
                    //当前最大序号
                    int maxIndex = Integer.parseInt(maxDemandCode.substring(maxDemandCode.length() - Integer.parseInt(serialNumberLength)));
                    orderIdToCurrentMaxSerialNumMap.put(orderId, maxIndex);
                }
            });
        }
    }

    private void createDemand(WorkOrderVO workOrderVO, OperationVO lastOperationVO, int qty,
                              AmsAnalysisContext amsAnalysisContext, NewProductStockPointVO newProductStockPointVO,
                              Map<String, Integer> orderIdToCurrentMaxSerialNumMap, RuleEncodingsVO ruleEncodingsVO,
                              Date demandTime, List<DemandVO> demandVOS, DeliveryPlanPublishedVO deliveryPlanPublishedVO) {
        String workOrderId = workOrderVO.getId();
        String operationId = lastOperationVO.getId();
        List<RoutingStepInputVO> routingStepInputVOS = amsAnalysisContext.getRoutingStepInputMapOnRoutingStepId().get(lastOperationVO.getRoutingStepId());
        if (CollectionUtils.isNotEmpty(routingStepInputVOS)) {
            routingStepInputVOS = routingStepInputVOS.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getEnabled())).collect(Collectors.toList());
        }
        List<OperationInputVO> operationInputVOS = amsAnalysisContext.getOperationInputMapOnOperationId().get(operationId);
        Map<String, OperationInputVO> operationInputVOMap = StreamUtils.mapByColumn(operationInputVOS, OperationInputVO::getProductId);
        for (RoutingStepInputVO routingStepInputVO : routingStepInputVOS) {
            String inputProductId = routingStepInputVO.getInputProductId();
            OperationInputVO operationInputVO = operationInputVOMap.get(inputProductId);
            if (null == operationInputVO) {
                log.warn("包装需求未找到工序输入物品：{}", routingStepInputVO.getId());
                continue;
            }
            RoutingStepInputDO routingStepInputDO = new RoutingStepInputDO();
            BeanUtils.copyProperties(routingStepInputVO, routingStepInputDO);
            BigDecimal quantity = AbstractOperationInputSync.calculateInputQuantity(BigDecimalUtils.toBigDecimal(qty), routingStepInputDO);
            String inputOperationId = operationInputVO.getId();
            Integer currentMaxSerialNum = orderIdToCurrentMaxSerialNumMap.get(workOrderId);
            //生成新的流水号
            int newSerialNum = 0;
            if (currentMaxSerialNum == null) {
                newSerialNum = newSerialNum + 1;
            } else {
                newSerialNum = currentMaxSerialNum + 1;
            }
            String serialNumber = String.valueOf(newSerialNum);
            String demandCode = RuleEncodingsUtils.getCode(ruleEncodingsVO, workOrderVO.getOrderNo(), serialNumber);
            ruleEncodingsVO.setSerialNumberMaxValue(serialNumber);
            orderIdToCurrentMaxSerialNumMap.put(workOrderId, newSerialNum);
            NewStockPointVO newStockPointVO = amsAnalysisContext.getStockPointMapOnCode().get(newProductStockPointVO.getStockPointCode());
            DemandVO demandVO = new DemandVO();
            demandVO.setDemandCode(demandCode);
            demandVO.setId(UUIDUtil.getUUID());
            demandVO.setQuantity(quantity);
            demandVO.setDemandTime(demandTime);
            //未分配数量 ，一开始默认为需求数量
            demandVO.setUnfulfilledQuantity(quantity);
            //分配状态
            demandVO.setFulfillmentStatus(FulfillmentStatusEnum.UNFULFILL.getCode());
            demandVO.setDemandType(DemandTypeEnum.WORK_ORDER_DEMAND.getCode());
            demandVO.setOperationInputId(inputOperationId);
            demandVO.setProductStockPointId(operationInputVO.getProductId());
            demandVO.setStockPointId(null != newStockPointVO ? newStockPointVO.getId() : null);
            demandVO.setProductId(operationInputVO.getProductId());
            demandVO.setDemandOrderId(workOrderId);
            demandVO.setFinishedProductCode(newProductStockPointVO.getProductCode());
            demandVO.setRemark("packing demand：" + deliveryPlanPublishedVO.getId());
            demandVO.setCustomerId(deliveryPlanPublishedVO.getId());
            demandVOS.add(demandVO);
            amsAnalysisContext.getDemandVOResult().add(demandVO);
        }
    }

    private List<Date> getDeliverTime(List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS) {
        Date startTime = deliveryPlanPublishedVOS.get(0).getDemandTime();
        Date demandTime = deliveryPlanPublishedVOS.get(deliveryPlanPublishedVOS.size() - 1).getDemandTime();
        return DateUtils.getIntervalDates(startTime, demandTime);
    }


    private void adjustDeliveryPlanTimes(AmsAnalysisContext amsAnalysisContext) {
        String packing = "包装";
        for (Map.Entry<String, List<WorkOrderVO>> entry : amsAnalysisContext.getWorkOrderMapOnProductId().entrySet()) {
            String productId = entry.getKey();
            NewProductStockPointVO newProductStockPointVO = amsAnalysisContext.getProductStockPointMapOnId().get(productId);
            if (newProductStockPointVO == null) continue;

            String productCode = newProductStockPointVO.getProductCode();
            String stockPointCode = newProductStockPointVO.getStockPointCode();

            if (!amsAnalysisContext.getDeliveryPlanPublishedMapOnProductCode().containsKey(productCode)) continue;

            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = amsAnalysisContext.getDeliveryPlanPublishedMapOnProductCode().get(productCode);
            deliveryPlanPublishedVOS.sort(Comparator.comparing(DeliveryPlanPublishedVO::getDemandTime));

            String standardStepCode = getStandardStepCode(amsAnalysisContext, stockPointCode, packing);
            String safetyKey = CharSequenceUtil.join("&", stockPointCode, productCode);
            String mainKey = CharSequenceUtil.join("&", stockPointCode, productCode, standardStepCode);

            adjustEachDeliveryPlan(deliveryPlanPublishedVOS, amsAnalysisContext, safetyKey, mainKey, stockPointCode, standardStepCode, productCode);
        }
    }

    private void adjustEachDeliveryPlan(List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS, AmsAnalysisContext amsAnalysisContext,
                                        String safetyKey, String mainKey, String stockPointCode, String standardStepCode, String productCode) {
        Integer stockQty = amsAnalysisContext.getStockMapOnProductCode().get(productCode);
        for (DeliveryPlanPublishedVO deliveryPlanPublishedVO : deliveryPlanPublishedVOS) {
            Date dueDate = deliveryPlanPublishedVO.getDemandTime();
            Integer demandQuantity = deliveryPlanPublishedVO.getDemandQuantity();

            // -标准安全库存天数
            dueDate = adjustDueDateBySafetyStock(dueDate, amsAnalysisContext.getSafetyStockLecelMap(), safetyKey);
            // -数量 * 生产节拍
            dueDate = adjustDueDateByProductionBeat(dueDate, amsAnalysisContext.getProductCandidateResourceMap(), mainKey, demandQuantity);
            // -包装工序后处理时间
            dueDate = adjustDueDateByPostProcessingTime(dueDate, amsAnalysisContext.getProductionLeadTimeMap(), stockPointCode, standardStepCode);

            if (dueDate.getTime() < amsAnalysisContext.getPlanningHorizonVO().getPlanStartTime().getTime()) {
                // 计算后的交期早于计划开始时间
                dueDate = amsAnalysisContext.getPlanningHorizonVO().getPlanStartTime();
            }
            deliveryPlanPublishedVO.setDemandTime(dueDate);

            if (null != stockQty && stockQty > 0) {
                if (demandQuantity <= stockQty) {
                    stockQty -= demandQuantity;
                    deliveryPlanPublishedVO.setDemandQuantity(0);
                } else {
                    deliveryPlanPublishedVO.setDemandQuantity(demandQuantity - stockQty);
                    stockQty = 0;
                }
            }
        }
    }

    private String getStandardStepCode(AmsAnalysisContext amsAnalysisContext, String stockPointCode, String packing) {
        String joinKey = CharSequenceUtil.join("&", stockPointCode, packing);
        return amsAnalysisContext.getStandardStepJoinMap().get(joinKey).getStandardStepCode();
    }

    private Date adjustDueDateBySafetyStock(Date dueDate, Map<String, List<SafetyStockLevelVO>> safetyStockLecelMap, String safetyKey) {
        if (safetyStockLecelMap.containsKey(safetyKey)) {
            SafetyStockLevelVO safetyStockLevelVO = safetyStockLecelMap.get(safetyKey).get(0);
            int minStockDay = safetyStockLevelVO.getStandardStockDay().intValue();
            return DateUtil.offsetDay(dueDate, -minStockDay);
        }
        return dueDate;
    }

    private Date adjustDueDateByProductionBeat(Date dueDate, Map<String, List<ProductCandidateResourceTimeVO>> productCandidateResourceMap, String mainKey, Integer demandQuantity) {
        if (productCandidateResourceMap.containsKey(mainKey)) {
            ProductCandidateResourceTimeVO productCandidateResourceVO = productCandidateResourceMap.get(mainKey).get(0);
            BigDecimal multiple = BigDecimalUtils.multiply(BigDecimalUtils.toBigDecimal(demandQuantity), BigDecimalUtils.toBigDecimal(productCandidateResourceVO.getBeat()));
            return DateUtil.offsetSecond(dueDate, -multiple.intValue());
        }
        return dueDate;
    }

    private Date adjustDueDateByPostProcessingTime(Date dueDate, Map<String, List<ProductionLeadTimeVO>> productionLeadTimeMap, String stockPointCode, String standardStepCode) {
        String key2 = CharSequenceUtil.join("&", stockPointCode, standardStepCode, ProductionLeadTimeEnum.POST_PRODUCTION_PROCESSING_TIME.getCode());
        return calculateTime(key2, productionLeadTimeMap, dueDate);
    }

    private void afterSetOperationEarliestTime(AmsAnalysisContext amsAnalysisContext, MpsAnalysisContext mpsAnalysisContext) {
        List<WorkOrderVO> workOrderVOS = amsAnalysisContext.getWorkOrderVOS();
        List<OperationVO> sourceOperations = amsAnalysisContext.getOperationVOS();

        List<String> workOrderProductIds = StreamUtils.columnToList(workOrderVOS, WorkOrderVO::getProductId);
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByIds(null, workOrderProductIds);
        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS = productAdvanceBatchRuleService.selectAll();
        List<String> productCodes = StreamUtils.columnToList(newProductStockPointVOS, NewProductStockPointVO::getProductCode);

        Map<String, WorkOrderVO> workOrderVOMap = amsAnalysisContext.getWorkOrderMapOnId();
        Map<String, StandardStepVO> standardStepVOMap = amsAnalysisContext.getStandardStepMapOnId();
        Map<String, ProductAdvanceBatchRuleVO> productAdvanceBatchRuleLevelMap = StreamUtils.mapByColumn(productAdvanceBatchRuleVOS, ProductAdvanceBatchRuleVO::getRiskLevel);
        Map<String, NewProductStockPointVO> productMap = StreamUtils.mapByColumn(newProductStockPointVOS, NewProductStockPointVO::getId);
        List<PartRiskLevelVO> partRiskLevelVOS = dfpFeign.selectMaterialRiskLeveByProductCodeList(null, productCodes);
        Map<String, PartRiskLevelVO> partRiskLevelVOMap = StreamUtils.mapByColumn(partRiskLevelVOS, PartRiskLevelVO::getProductCode);

        List<OperationVO> updateTimeOperations = new ArrayList<>();
        List<String> process = ListUtil.of("FORMING_PROCESS", "COATING_PROCESS");
        String low = "低";
        List<String> lockWorkOrderIds = new ArrayList<>();
        String creator = mpsAnalysisContext.getAlgorithmLog().getCreator();
        String key = RedisKeyManageEnum.MPS_ALGORITHM_LOCK_WORK_ORDER.getKey().replace("{userId}", creator);
        List<Object> cacheDeleteWorkOrderDelete = redisUtil.lGet(key);
        if(CollectionUtils.isNotEmpty(cacheDeleteWorkOrderDelete)){
            Object obj = cacheDeleteWorkOrderDelete.get(0);
            if (obj instanceof List) {
                List<?> list = (List<?>) obj;
                if (!list.isEmpty() && list.get(0) instanceof String) {
                    lockWorkOrderIds = (List<String>) obj;
                }
            }
        }
        List<String> finalLockWorkOrderIds = lockWorkOrderIds;
        log.info("afterSetOperationEarliestTime锁定期不更新最早开始时间订单数量：{}", finalLockWorkOrderIds.size());
        sourceOperations.forEach(operation -> {
            WorkOrderVO workOrderVO = workOrderVOMap.get(operation.getOrderId());
            if (workOrderVO == null) return;
            String workOrderVOId = workOrderVO.getId();
            NewProductStockPointVO productStockPointVO = productMap.get(workOrderVO.getProductId());
            if (productStockPointVO == null) return;

            StandardStepVO standardStepVO = standardStepVOMap.get(operation.getStandardStepId());
            if (standardStepVO == null || !process.contains(standardStepVO.getStandardStepType())) return;

            PartRiskLevelVO partRiskLevelVO = partRiskLevelVOMap.get(productStockPointVO.getProductCode());
            ProductAdvanceBatchRuleVO advanceBatchRuleVO;
            if (null == partRiskLevelVO) {
                advanceBatchRuleVO = productAdvanceBatchRuleLevelMap.get(low);
            } else {
                advanceBatchRuleVO = productAdvanceBatchRuleLevelMap.get(partRiskLevelVO.getMaterialRiskLevel());
            }
            if (advanceBatchRuleVO == null) return;
            Date earliestBeginTime = DateUtil.offsetDay(workOrderVO.getDueDate(), -advanceBatchRuleVO.getMaxProductionLeadDays().intValue());
            // 如果最早开始时间 <= 交期-最大提前天数，则不更新；
            Date earliestStartTime = operation.getEarliestStartTime();
            boolean flag = null != earliestStartTime && earliestStartTime.getTime() <= earliestBeginTime.getTime();
            if (finalLockWorkOrderIds.contains(workOrderVOId) && flag) {
                log.info("自动排程锁定期内工单不更新最早开始时间：{}", workOrderVOId);
                return;
            }
            if (limitStatus.contains(operation.getPlanStatus())) {
                return;
            }
            // 找到最早得开始时间
            Date minEarliestBeginTime = StreamUtils.getMinDate(earliestStartTime, earliestBeginTime, workOrderVO.getEarliestStartTime());
            operation.setEarliestStartTime(minEarliestBeginTime);
            operation.setEnabled(YesOrNoEnum.YES.getCode());
            updateTimeOperations.add(operation);
        });
        List<OperationPO> operationPOList = OperationConvertor.INSTANCE.vo2Pos(updateTimeOperations);
        BasePOUtils.updateBatchFiller(operationPOList);
        List<List<OperationPO>> lists = com.yhl.platform.common.utils.CollectionUtils.splitList(operationPOList, 2000);
        for (List<OperationPO> operationPOS : lists) {
            operationDao.updateBatch(operationPOS);
        }
        log.info("更新工序最早开始时间数量：{}", operationPOList.size());
    }

    private void doStart(RzzBaseAlgorithmData algorithmData, String filePathName, MpsAnalysisContext mpsAnalysisContext) {
        Date date1 = new Date();
        // 构建AMS输入数据
        APSInput apsInput = rzzBaseAlgorithmDataService.getApsInput(algorithmData, getOs(filePathName));
        // 写入配置文件conf.json
        rzzBaseAlgorithmDataService.writeConf(apsInput, algorithmData);
        // 求解函数
        APSRunner apsRunner = new APSRunner();
        // 调用算法
        APSOutput apsOutput = apsRunner.run(apsInput);
        if (Objects.nonNull(mpsAnalysisContext)) {
            mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("算法排程完成", AMS_MODULE,
                    mpsAnalysisContext.getAlgorithmLog().getId(), date1, new Date()));
        }
        // 解析算法输出
        Date date2 = new Date();
        Map<String, String> operationOnResourceIdMap = algorithmData.getOperationOnResourceIdMap();
        rzzAlgorithmOutputService.doAnalysisAlgorithmOutputData(apsOutput, filePathName, operationOnResourceIdMap);
        if (Objects.nonNull(mpsAnalysisContext)) {
            mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("算法结果解析完成", AMS_MODULE,
                    mpsAnalysisContext.getAlgorithmLog().getId(), date2, new Date()));
        }
    }

    private void afterJustOperation(RzzBaseAlgorithmData algorithmData) {
        List<StandardStepVO> standardStepVOS = mdsFeign.selectStandardStepAll(null);
        log.info("标准工艺数据：{}", JSON.toJSONString(standardStepVOS));
        Map<String, StandardStepVO> standardStepVOMap = standardStepVOS.stream()
                .collect(Collectors.toMap(StandardStepVO::getId, Function.identity()));
        List<RzzWorkOrderData> workOrders = algorithmData.getWorkOrders();
        Map<String, List<RzzOperationData>> workOrderMap = algorithmData.getOperations().stream()
                .collect(Collectors.groupingBy(RzzOperationData::getWorkOrderId));
        List<String> process = ListUtil.of(StandardStepEnum.FORMING_PROCESS.getCode());
        Set<String> waitingJustOperation = new TreeSet<>();
        List<RzzOperationData> notFoundTypeOperation = new ArrayList<>();
        for (RzzWorkOrderData workOrder : workOrders) {
            String id = workOrder.getId();
            List<RzzOperationData> rzzOperationData = workOrderMap.get(id);
            rzzOperationData.sort(Comparator.comparing(RzzOperationData::getRoutingStepSequenceNo));
            boolean whetherFirst = false;
            for (RzzOperationData operationData : rzzOperationData) {
                String standardStepId = operationData.getStandardStepId();
                StandardStepVO standardStepVO = standardStepVOMap.get(standardStepId);
                if (Objects.isNull(standardStepVO)) {
                    notFoundTypeOperation.add(operationData);
                    continue;
                }
                String standardStepType = standardStepVO.getStandardStepType();
                if (whetherFirst) {
                    waitingJustOperation.add(operationData.getOperationId());
                    continue;
                }
                if (process.contains(standardStepType)) {
                    // 包含关键工序，标记后续工序直接参与排程不区分关键工序
                    waitingJustOperation.add(operationData.getOperationId());
                    whetherFirst = true;
                }
            }
            // 如果完全没有匹配到关键工序则全排
            if (!whetherFirst) {
                waitingJustOperation.addAll(rzzOperationData.stream().map(RzzOperationData::getOperationId).collect(Collectors.toList()));
            }
        }
        log.info("未匹配到标准工艺工序工序：{}", JSON.toJSONString(notFoundTypeOperation));
        if (CollectionUtils.isNotEmpty(notFoundTypeOperation)) {
            throw new BusinessException("存在" + notFoundTypeOperation.size() + "个工序找不到标准工艺");
        }
        List<String> needScheduleOperationIds = algorithmData.getNeedScheduleOperationIds();
        log.info("原始待排工序数量：{}", needScheduleOperationIds.size());
        log.info("后处理关键工序待排工序数量：{}", waitingJustOperation.size());
        List<String> waitingScheduleList = needScheduleOperationIds.stream()
                .filter(waitingJustOperation::contains)
                .collect(Collectors.toList());
        // 被排除不参与首次排程的工序
        List<String> notScheduleOperationIds = needScheduleOperationIds.stream()
                .filter(p -> !waitingJustOperation.contains(p))
                .collect(Collectors.toList());
        log.info("后处理待排工序数量：{}", waitingScheduleList.size());
        log.info("被排除不参与首次排程的工序：{}", JSON.toJSONString(notScheduleOperationIds));
        algorithmData.setNeedScheduleOperationIds(waitingScheduleList);
        // 处理资源占位
        processResource(algorithmData, notScheduleOperationIds);
        // 处理供需关系
        processFulfillment(algorithmData, notScheduleOperationIds);
        // 标记排程方向
        algorithmData.setDirection(Boolean.FALSE);
    }

    private void processFulfillment(RzzBaseAlgorithmData algorithmData, List<String> notScheduleOperationIds) {
        log.info("正排前供需分配数量：{}", algorithmData.getFulfillmentList().size());
        Iterator<RzzFulfillment> iterator = algorithmData.getFulfillmentList().iterator();
        while (iterator.hasNext()) {
            RzzFulfillment rzzFulfillment = iterator.next();
            String operationId = rzzFulfillment.getOperationId();
            if (StrUtil.isEmpty(operationId)) continue;
            if (notScheduleOperationIds.contains(operationId)) {
                iterator.remove();
            }
        }
        List<RzzFulfillment> customerFulfillment = algorithmData.getFulfillmentList().stream()
                .filter(p -> StrUtil.isEmpty(p.getOperationId())).collect(Collectors.toList());
    }

    private void processResource(RzzBaseAlgorithmData algorithmData, List<String> notScheduleOperationIds) {
        for (List<RzzResourceData> resourceDataList : algorithmData.getResourceMap().values()) {
            for (RzzResourceData rzzResourceData : resourceDataList) {
                List<Map<String, Schedule>> fixedSchedules = rzzResourceData.getFixedSchedules();
                if (CollectionUtils.isEmpty(fixedSchedules)) {
                    continue;
                }
                fixedSchedules.forEach(fixedSchedule -> {
                    fixedSchedule.entrySet().removeIf(entry -> notScheduleOperationIds.contains(entry.getValue().getOperationId()));
                });
            }
        }
    }

    public String getOs(String fileName) {
        String osName = System.getProperty("os.name").toLowerCase();
        log.info("os：{}", osName);
        if (osName.contains("win")) {
            return "D:/ams/" + fileName;
        } else if (osName.contains("nix") || osName.contains("nux") || osName.contains("aix")) {
            return "/usr/local/aps/workspace/" + fileName;
        } else if (osName.contains("mac")) {
            return "";
        } else {
            return null;
        }
    }

    /**
     * 计划调整
     * 1.获取前端传来的需要计划调整的制造订单
     * 2.查询非关键工序外卖
     *
     * @param workOrderIds ids
     */
    @Override
    public void doAdjustPlan(List<String> workOrderIds) {
        AlgorithmLog algorithmLogMap = new AlgorithmLog();
        algorithmLogMap.setAlgorithmVersion(AlgorithmVersion.MPS2);
        algorithmLogMap.setCreator(SystemHolder.getUserId());
        algorithmLogMap.setProductionPlanner(SystemHolder.getUserName());
        algorithmLogMap.setScenario(SystemHolder.getScenario());
        algorithmLogMap.setRemark("计划调整");
        List<String> runningResourceCodeList = adjustDao.selectResourceCodeByWorkOrderIds(workOrderIds);
        if (CollectionUtils.isNotEmpty(runningResourceCodeList)) {
            algorithmLogMap.setProductLine(String.join(",", runningResourceCodeList));
        }
        AlgorithmLog amsAlgoLog = createAmsAlgoLog(algorithmLogMap);
        try {
            List<OperationVO> operationVOS = operationService.selectByWorkOrderIds(workOrderIds);
            List<OperationVO> keyOperationResourceIds = new ArrayList<>();
            List<String> waitingScheduleOperation = getPlanAdjustOperation(operationVOS, keyOperationResourceIds);
            log.info("计划调整待排工序数量：{}", waitingScheduleOperation);
            List<OperationVO> waitingScheduleOperations = operationService.selectByParams(ImmutableMap.of("ids", waitingScheduleOperation));
            // 计划调整
            doAdjustSchedule(waitingScheduleOperations, amsAlgoLog.getId(), keyOperationResourceIds);
            amsAlgoLog.setStatus(AlgorithmLogStatusEnum.SUCCESS.getCode());
            amsAlgoLog.setEndTime(new Date());
            log.info("计划调整重新排程完成");
        } catch (Exception e) {
            log.error("计划调整算法运行失败：", e);
            amsAlgoLog.setStatus(AlgorithmLogStatusEnum.FAIL.getCode());
            amsAlgoLog.setEndTime(new Date());
            amsAlgoLog.setFailMsg(e.getMessage());
            throw new BusinessException("计划调整算法运行失败：" + e.getMessage());
        } finally {
            // 更新ips算法日志调用状态
            CompletableFuture.runAsync(() -> ipsFeign.updateAlgorithmLog(amsAlgoLog));
            log.info("AMS算法日志更新完成");
        }
    }

    private List<OperationVO> cancelOperation(List<OperationVO> operationVOS, List<String> waitingScheduleIds, List<OperationVO> keyOperationResource) {
        List<StandardStepVO> standardStepVOS = mdsFeign.selectStandardStepAll(null);
        Map<String, StandardStepVO> stringStandardStepVOMap = StreamUtils.mapByColumn(standardStepVOS, StandardStepVO::getId);
        Map<String, List<OperationVO>> workOrderMap = StreamUtils.mapListByColumn(operationVOS, OperationVO::getOrderId);
        List<OperationVO> cancelOperation = new ArrayList<>();
        List<String> typeList = ListUtil.of(StandardStepEnum.FORMING_PROCESS.getCode());
        for (Map.Entry<String, List<OperationVO>> entry : workOrderMap.entrySet()) {
            List<OperationVO> value = entry.getValue();
            List<OperationVO> cancelChildOperation = new ArrayList<>();
            List<String> unPlanParentOperation = new ArrayList<>();
            // 遍历取消计划子工序
            for (OperationVO operationVO : value) {
                String standardStepId = operationVO.getStandardStepId();
                StandardStepVO standardStepVO = stringStandardStepVOMap.get(standardStepId);
                if (Objects.isNull(standardStepVO)) {
                    continue;
                }
                String standardStepType = standardStepVO.getStandardStepType();
                if (typeList.contains(standardStepType)) {
                    String planStatus = operationVO.getPlanStatus();
                    if (planStatus.equals(PlanStatusEnum.UNPLAN.getCode())) {
                        // 关键工序未计划，加入排程
                        unPlanParentOperation.add(operationVO.getId());
                    } else {
                        // 关键工序不取消计划
                        keyOperationResource.add(operationVO);
                    }
                    continue;
                }
                String parentId = operationVO.getParentId();
                String planStatus = operationVO.getPlanStatus();
                if (StrUtil.isNotEmpty(parentId)) {
                    // 非关键工序的子工序
                    cancelChildOperation.add(operationVO);
                }
                if (StrUtil.isEmpty(parentId) && planStatus.equals(PlanStatusEnum.UNPLAN.getCode())) {
                    // 非关键工序的父工序
                    unPlanParentOperation.add(operationVO.getId());
                }
            }
            // 仅取消包含成型的子工序
            cancelOperation.addAll(cancelChildOperation);
            // 取消计划子工序的父工序
            waitingScheduleIds.addAll(StreamUtils.columnToList(cancelOperation, OperationVO::getParentId));
            // 未计划的非关键父工序
            waitingScheduleIds.addAll(unPlanParentOperation);
        }
        return cancelOperation;
    }

    private List<String> getPlanAdjustOperation(List<OperationVO> operationVOS, List<OperationVO> keyOperationResourceIds) {
        List<String> waitingScheduleIds = new ArrayList<>();
        List<OperationVO> cancelOperation = cancelOperation(operationVOS, waitingScheduleIds, keyOperationResourceIds);
        cancelPlanCommand.doCancelAmsOperationPlan(cancelOperation);
        return waitingScheduleIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    protected void resetWorkOrderSequence(MpsAnalysisContext mpsAnalysisContext, RzzMpsAlgorithmOutput mpsAlgorithmOutput) {
        List<WorkOrderPO> workOrders = manualAdjustHandleDao.selectSortWorkOrders();
        if (CollectionUtils.isEmpty(workOrders)) {
            return;
        }
        AtomicInteger orderSequence = new AtomicInteger(0);
        workOrders.forEach(workOrder -> {
            workOrder.setOrderSequence(orderSequence.incrementAndGet());
        });
        List<List<WorkOrderPO>> partitions = Lists.partition(workOrders, 2000);
        for (List<WorkOrderPO> partition : partitions) {
            manualAdjustHandleDao.updateWorkOrderSequences(partition);
        }
    }

    /**
     * 取消计划
     * <p>
     * 锁定外计划单关联制造订单：全工序取消计划重排
     * 锁定期内制造订单：仅取消非关键工序，重排非关键工序
     *
     * @param mpsAnalysisContext context
     */
    @Override
    protected void cancelPlan(MpsAnalysisContext mpsAnalysisContext) {
        Date dateTime = new Date();
        String key = RedisKeyManageEnum.MPS_ALGORITHM_UN_PLAN_WORK_ORDER.getKey().replace("{userId}", mpsAnalysisContext.getCreatorId());
        String key2 = RedisKeyManageEnum.MPS_UN_PLAN_SCHEDULE_WORK_ORDER.getKey().replace("{userId}", mpsAnalysisContext.getCreatorId());

        List<Object> cacheDeleteWorkOrderDelete = redisUtil.lGet(key);
        List<Object> lockScheduleWorkOrder = redisUtil.lGet(key2);

        List<OperationVO> cancelOperationList = new ArrayList<>();
        List<String> waitingCancelPlanWorkOrderIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cacheDeleteWorkOrderDelete)) {
            List<String> waitingCancelPlanWorkOrder = (List<String>) cacheDeleteWorkOrderDelete.get(0);
            log.info("MPS解析结果取消计划锁定期外关联计划单数量：{}", waitingCancelPlanWorkOrder.size());
            List<OperationVO> cancelOperation = operationService.selectByWorkOrderIds(waitingCancelPlanWorkOrder)
                    .stream().filter(p -> StrUtil.isNotEmpty(p.getParentId())).collect(Collectors.toList());
            cancelOperationList.addAll(cancelOperation);
            waitingCancelPlanWorkOrderIds.addAll(waitingCancelPlanWorkOrder);
        }
//        if (CollectionUtils.isNotEmpty(lockScheduleWorkOrder)) {
//            List<String> waitingLockScheduleWorkOrder = (List<String>) lockScheduleWorkOrder.get(0);
//            log.info("MPS解析结果锁定期内制造订单数量：{}", waitingLockScheduleWorkOrder.size());
//            List<OperationVO> operationVOS = operationService.selectByWorkOrderIds(waitingLockScheduleWorkOrder);
//            List<String> cancelIds = new ArrayList<>();
//            // 得到非关键工序已排程的子工序做取消计划重排。
//            List<OperationVO> cancelOperation = cancelOperation(operationVOS, cancelIds);
//            cancelOperationList.addAll(cancelOperation);
//            waitingCancelPlanWorkOrderIds.addAll(waitingLockScheduleWorkOrder);
//        }
        // 取消计划子工序（锁定期外关联计划单订单+锁定期内非关键工序）
        cancelPlanCommand.doCancelAmsOperationPlan(cancelOperationList);
        // 设置待排制造订单
        log.info("重排制造订单数据：{}", JSON.toJSONString(waitingCancelPlanWorkOrderIds));
        mpsAnalysisContext.getWorkOrderIds().addAll(waitingCancelPlanWorkOrderIds);
        mpsAnalysisContext.getAlgorithmStepLogDTOList().add(getStepLog("取消计划完成", MPS_MODULE,
                mpsAnalysisContext.getAlgorithmLog().getId(), dateTime, new Date()));
    }

    @Override
    protected void deliveryPlanPublishedCompareDoEnable(MpsAnalysisContext analysisContext) {
        PlanningHorizonVO planningHorizonVO = analysisContext.getPlanningHorizonVO();
        if (null == planningHorizonVO) {
            planningHorizonVO = mdsFeign.selectPlanningHorizon(null);
        }
        Map<String, Object> params = new HashMap<>();
        params.put("startTime", DateUtils.dateToString(planningHorizonVO.getPlanStartTime(), DateUtils.COMMON_DATE_STR1));
        params.put("endTime", DateUtils.dateToString(planningHorizonVO.getPlanLockEndTime(), DateUtils.COMMON_DATE_STR1));
        String scenario = SystemHolder.getScenario();
        CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            dfpFeign.deliveryPlanPublishedCompareDoEnable(scenario, params);
            DynamicDataSourceContextHolder.clearDataSource();
        });

    }

    public void deletePlanByWorkOrder(List<String> workOrderIds) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            log.info("deletePlanByWorkOrder删除订单为空，不执行");
            return;
        }
        List<OperationVO> operationVOS = operationService.selectByWorkOrderIds(workOrderIds);
        if (CollectionUtils.isNotEmpty(operationVOS)) {
            List<String> operationIds = operationVOS.stream().map(OperationVO::getId).collect(Collectors.toList());
            operationExtendService.doDeleteByOperationIds(operationIds);
            operationTaskService.doDeleteByOperationIds(operationIds);

        } else {
            log.warn("制造订单未找到相关的工序---");
        }
        operationService.doDeleteByWorkOrderIds(workOrderIds);
        operationInputService.doDeleteByWorkOrderIds(workOrderIds);
        operationResourceService.doDeleteByWorkOrderIds(workOrderIds);
        operationOutputService.doDeleteByWorkOrderIds(workOrderIds);
        List<DemandVO> waitingDeleteDemand = demandService.selectByDemandOrderIds(workOrderIds);
        List<SupplyVO> waitingDeleteSupply = supplyService.selectByParams(ImmutableMap.of("supplyOrderIds", workOrderIds));
        if (CollectionUtils.isNotEmpty(waitingDeleteSupply)) {
            List<String> supplyIds = waitingDeleteSupply.stream().map(SupplyVO::getId).collect(Collectors.toList());
            fulfillmentDao.deleteBySupplyIds(supplyIds);
            log.info("MPS解析删除供应对应fulfillment");
        }
        if (CollectionUtils.isNotEmpty(waitingDeleteDemand)) {
            List<String> demandIds = waitingDeleteDemand.stream().map(DemandVO::getId).collect(Collectors.toList());
            fulfillmentDao.deleteByDemandIds(demandIds);
            log.info("MPS解析删除需求对应fulfillment");
        }
        demandService.doDeleteByWorkOrderIds(workOrderIds);
        supplyService.doDeleteByWorkOrderIds(workOrderIds);
        workOrderService.doDelete(workOrderIds);
        log.info("制造订单展开工序未计划工序删除结束，删除制造订单数量：{}", workOrderIds.size());
    }

    @Override
    @Transactional
    public void test(String id) {
//        AmsAnalysisContext amsAnalysisContext = initAmsContext(id);
//        afterProcessPackingOperation(amsAnalysisContext);
    }

    @Override
    public void doClosePlan(List<String> workOrderIds) {
        deletePlanByWorkOrder(workOrderIds);
    }

}
