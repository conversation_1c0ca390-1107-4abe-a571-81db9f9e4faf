package com.yhl.scp.mps.capacityBalance.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.MessageTypeEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.untils.UserMessageUtils;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.capacityBalance.dao.CapacityBalanceDao;
import com.yhl.scp.mps.capacityBalance.dto.CapacityLoadDTO;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceTypeEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacityLoadDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO;
import com.yhl.scp.mps.capacityBalance.service.CapacityWeekBalanceService;
import com.yhl.scp.mps.capacityBalance.tool.CustomColumnWidthHandler;
import com.yhl.scp.mps.capacityBalance.tool.EasyExcelUtil;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO2;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.plan.service.impl.MasterPlanServiceImpl;
import com.yhl.scp.mps.plan.support.MasterPlanInventorySupport;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CapacityWeekBalanceServiceImpl</code>
 * <p>
 * 周产能平衡页面查询相关接口实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-25 13:57:04
 */
@Slf4j
@Service
public class CapacityWeekBalanceServiceImpl implements CapacityWeekBalanceService {

    @Resource
    private CapacityLoadDao capacityLoadDao;
    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private CapacityBalanceDao capacityBalanceDao;
    @Resource
    private MasterPlanInventorySupport masterPlanInventorySupport;
    @Resource
    private UserMessageUtils userMessageUtils;

    @Override
    public List<CapacityLoadVO> selectResourceByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {

        if (StringUtils.isBlank(queryCriteriaParam)){
            queryCriteriaParam = "and version_id = 'WEEK'";
        }
        List<CapacityLoadVO> dataList = capacityLoadDao.selectByCondition(sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(dataList)){
            return new ArrayList<>();
        }
        String pattern = "yyyy-MM-dd";
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", CapacityBalanceTypeEnum.WEEK.getCode());
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectByParams(params);
        log.info("产能负荷数据条数{}，产能供应数据条数{}", dataList.size(), capacitySupplyRelationshipPOList.size());
        Date startDate = getMin(dataList, CapacityLoadVO::getForecastTime, Comparator.comparing(Date::getTime));
        Map<String, List<CapacitySupplyRelationshipPO>> suppluRelationMap = new HashMap<>();
        Set<String> weekList = new HashSet<>();
        for (CapacitySupplyRelationshipPO po : capacitySupplyRelationshipPOList) {

            String key = getKey(po.getSupplyTime(), startDate, pattern);
            if (!suppluRelationMap.containsKey(key)){
                List<CapacitySupplyRelationshipPO> list = new ArrayList<>();
                list.add(po);
                suppluRelationMap.put(key, list);
            }else {
                suppluRelationMap.get(key).add(po);
            }
            weekList.add(key);
        }

        Map<String, BigDecimal> averageBeatMap = new HashMap<>();
        for (Map.Entry<String, List<CapacitySupplyRelationshipPO>> entry : suppluRelationMap.entrySet()) {
            String key = entry.getKey();
            List<CapacitySupplyRelationshipPO> value = entry.getValue();
            //平均节拍=sum(需求量*节拍)/sum(需求量)
            BigDecimal sumBeat = value.stream().map(t -> t.getDemandQuantity().multiply(new BigDecimal(t.getBeat()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal sumDemandQuantity = value.stream().map(CapacitySupplyRelationshipPO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal averageBeat = sumBeat.divide(sumDemandQuantity, 2, RoundingMode.HALF_UP);
            averageBeatMap.put(key, averageBeat);
        }
        List<CapacityLoadVO> newDataList = new ArrayList<>();
        Map<String, List<CapacityLoadVO>> map = dataList.stream().collect(Collectors.groupingBy(t -> t.getPlantCode()+"-"+t.getResourceGroupCode()+"-"+ t.getResourceCode()));
        for (Map.Entry<String, List<CapacityLoadVO>> entry : map.entrySet()) {
            CapacityLoadVO vo = new CapacityLoadVO();
            vo.setPlantCode(entry.getKey().split("-")[0]);
            vo.setResourceGroupCode(entry.getKey().split("-")[1]);
            vo.setResourceCode(entry.getKey().split("-")[2]);
            vo.setOperationCode(entry.getValue().get(0).getOperationCode());
            vo.setOperationName(entry.getValue().get(0).getOperationName());
            List<CapacityLoadVO2> vo2List = new ArrayList<>();
            Map<String, List<CapacityLoadVO>> listMap = entry.getValue().stream().collect(Collectors.groupingBy(t -> getKey(t.getForecastTime(), startDate, pattern)));
            for (Map.Entry<String, List<CapacityLoadVO>> listEntry : listMap.entrySet()) {
                String key = listEntry.getKey();
                CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                capacityLoadVO2.setForecastWeekTime(key);
                //周总产能
                BigDecimal weekDemandQuantity = getSum(listEntry.getValue(), CapacityLoadVO::getDemandQuantity);
                // 周总产能
                BigDecimal weekAvailableCapacity = getSum(listEntry.getValue(), CapacityLoadVO::getAvailableCapacity);
                // 周总消耗产能
                BigDecimal weekProductionCapacity = getSum(listEntry.getValue(), CapacityLoadVO::getProductionCapacity);
                BigDecimal divide = weekProductionCapacity.divide(weekAvailableCapacity, 2, RoundingMode.HALF_UP);
                capacityLoadVO2.setDemandQuantity(weekDemandQuantity);
                capacityLoadVO2.setProductionCapacity(weekProductionCapacity);
                capacityLoadVO2.setAvailableCapacity(weekAvailableCapacity);
                capacityLoadVO2.setCapacityUtilization(divide);
                capacityLoadVO2.setAverageBeat(averageBeatMap.getOrDefault(key, BigDecimal.ZERO));
                capacityLoadVO2.setTotalCapacity(capacityLoadVO2.getAverageBeat().compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : capacityLoadVO2.getAvailableCapacity().divide(capacityLoadVO2.getAverageBeat(), 0, RoundingMode.CEILING));
                capacityLoadVO2.setColor(getColor(capacityLoadVO2.getCapacityUtilization()));
                vo2List.add(capacityLoadVO2);
            }
            Map<String, CapacityLoadVO2> vo2Map = vo2List.stream().collect(Collectors.toMap(CapacityLoadVO2::getForecastWeekTime, Function.identity(), (v1, v2) -> v1));
            for (String week : weekList) {
                if (!vo2Map.containsKey(week)){
                    CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2(null, week, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "0", 0);
                    vo2List.add(capacityLoadVO2);
                }
            }
            //按月份升序排序
            vo2List.sort(Comparator.comparing(CapacityLoadVO2::getForecastWeekTime));
            vo.setCapacityLoadVO2List(vo2List);
            newDataList.add(vo);
        }

        return newDataList.stream().sorted(Comparator.comparing(t->t.getOperationCode()+t.getOperationName()+t.getResourceCode())).collect(Collectors.toList());

    }
    private String getColor(BigDecimal capacityUtilization){
        if (capacityUtilization.compareTo(new BigDecimal("0.8")) >= 0 && capacityUtilization.compareTo(BigDecimal.ONE) < 0){
            //返回黄色
            return "1";
        }else if (capacityUtilization.compareTo(BigDecimal.ONE) >= 0){
            //返回红色
            return "2";
        }else {
            //返回白的
            return "0";
        }
    }

    public static String getKey(Date date, Date startDate, String pattern) {
        //该周第一天
        DateTime start = DateUtil.beginOfWeek(date);
        //该周最后一天
        DateTime end = DateUtil.endOfWeek(date);

        if (start.before(startDate)){
            start.setTime(startDate.getTime());
        }
        return DateUtils.dateToString(start, pattern) + "~" + DateUtils.dateToString(end, pattern);
    }

    @Override
    public List<CapacityLoadVO> selectOperationByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        List<CapacityLoadVO> result = new ArrayList<>();
        List<CapacityLoadVO> capacityLoadVOS = this.selectResourceByPage(pagination, sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(capacityLoadVOS)){
            return result;
        }
        List<String> weekList = capacityLoadVOS.get(0).getCapacityLoadVO2List().stream().map(CapacityLoadVO2::getForecastWeekTime).collect(Collectors.toList());
        Map<String, List<CapacityLoadVO>> operationMap = capacityLoadVOS.stream().collect(Collectors.groupingBy(t -> t.getOperationCode() + "-" + t.getOperationName()));

        for (Map.Entry<String, List<CapacityLoadVO>> entry : operationMap.entrySet()) {
            CapacityLoadVO capacityLoadVO = entry.getValue().get(0);
            List<CapacityLoadVO2> capacityLoadVO2List = new ArrayList<>();
            for (String week : weekList) {
                CapacityLoadVO2 vo2 = new CapacityLoadVO2();
                vo2.setForecastWeekTime(week);
                List<CapacityLoadVO2> vo2List = entry.getValue().stream().flatMap(t -> t.getCapacityLoadVO2List().stream()).filter(t->week.equals(t.getForecastWeekTime())).collect(Collectors.toList());
                //工序总需求量
                BigDecimal operationDemandQuantity = vo2List.stream().map(CapacityLoadVO2::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                //工序月度总产量
                BigDecimal operationTotalCapacity = vo2List.stream().map(CapacityLoadVO2::getTotalCapacity).reduce(BigDecimal.ZERO, BigDecimal::add);
                //工序负荷率
                BigDecimal operationCapacityUtilization = operationTotalCapacity.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : operationDemandQuantity.divide(operationTotalCapacity, 4, RoundingMode.HALF_UP);
                vo2.setDemandQuantity(operationDemandQuantity);
                vo2.setTotalCapacity(operationTotalCapacity);
                vo2.setCapacityUtilization(operationCapacityUtilization);
                vo2.setColor(getColor(vo2.getCapacityUtilization()));
                capacityLoadVO2List.add(vo2);
            }

            CapacityLoadVO vo = new CapacityLoadVO();
            BeanUtils.copyProperties(capacityLoadVO, vo);
            vo.setResourceCode(null);
            vo.setResourceName(null);
            vo.setCapacityLoadVO2List(capacityLoadVO2List);
            result.add(vo);
        }
        return result.stream().sorted(Comparator.comparing(t->t.getOperationCode()+t.getOperationName())).collect(Collectors.toList());
    }

    @Override
    public List<CapacitySupplyRelationshipVO> selectSupplyByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        if (StringUtils.isBlank(queryCriteriaParam)){
            queryCriteriaParam = "and version_id = 'WEEK'";
        }
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return capacitySupplyRelationshipDao.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Resource
    private IpsFeign ipsFeign;

    @Override
    public List<CapacityLoadVO> selectOverloadByPage(String resourceCode, Integer utilization) {
        //负荷率标准
        BigDecimal divide1 = new BigDecimal(utilization).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        //是否准确查询产线数据
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        standardStepVOS = standardStepVOS.stream()
                .filter(item->"FORMING_PROCESS".equals(item.getStandardStepType()))
                .collect(Collectors.toList());
        if (standardStepVOS.isEmpty()){
            throw new RuntimeException("未找到该租户下的关键工序数据，请进行维护");
        }
        String queryCriteriaParam = this.concatKeyStandardStep(standardStepVOS);
        if (StringUtils.isNotEmpty(resourceCode)){
            queryCriteriaParam += "and resource_code = '" + resourceCode + "'";
        }
        // 获取关键工序的产能负荷表数据
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadDao.selectByCondition(null, queryCriteriaParam);
        //没有数据就返回空集合
        if (CollectionUtils.isEmpty(capacityLoadVOS)){
            return new ArrayList<>();
        }
        //获取每个关键工序对应的资源编码
        Set<String> resourceCodes = capacityLoadVOS.stream().map(CapacityLoadVO::getResourceCode).collect(Collectors.toSet());
        //查询关键工序产能供应数据
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", CapacityBalanceTypeEnum.WEEK.getCode());
        params.put("resourceCodeList",resourceCodes);
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectByParams(params);
        log.info("产能负荷数据条数{}，产能供应数据条数{}", capacityLoadVOS.size(), capacitySupplyRelationshipPOList.size());
        // 获取所有对应的物料编码集合
        List<String> capacityProductCodes = capacitySupplyRelationshipPOList.stream().map(CapacitySupplyRelationshipPO::getProductCode).distinct().collect(Collectors.toList());
        //获取当前租户对应的销售组织 SystemHolder.getTenantCode()
        List<Scenario> scenarios = ipsNewFeign.selectDefaultByTenantId(TenantCodeEnum.FYQB.getCode());
        //获取销售组织的物料对象集合 SystemHolder.getScenario()
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodes", capacityProductCodes));
        List<NewProductStockPointVO> saleNewProductStockPointVOS = newProductStockPointVOS.stream().filter(t -> t.getOrganizationId().equals(scenarios.get(0).getAlternativeColumn())).collect(Collectors.toList());
        List<NewProductStockPointVO> productNewProductStockPointVOS = newProductStockPointVOS.stream().filter(t -> !t.getOrganizationId().equals(scenarios.get(0).getAlternativeColumn())).collect(Collectors.toList());
        // 获取产能负荷中最近的时间
        Date startDate = capacityLoadVOS.stream().min(Comparator.comparing(CapacityLoadVO::getForecastTime)).map(CapacityLoadVO::getForecastTime).orElse(new Date());
        // 将产能供应数据按照时间周分类
        Map<String, List<CapacitySupplyRelationshipPO>> suppluRelationMap = new HashMap<>();
        // 获取每个时间周的数据 比如2025-04-14~2025-04-20
        Set<String> weekSet = new HashSet<>();
        for (CapacitySupplyRelationshipPO po : capacitySupplyRelationshipPOList) {
            String key = getKey(po.getSupplyTime(), startDate, DateUtils.COMMON_DATE_STR3);
            if (!suppluRelationMap.containsKey(key)){
                List<CapacitySupplyRelationshipPO> list = new ArrayList<>();
                list.add(po);
                suppluRelationMap.put(key, list);
            }else {
                suppluRelationMap.get(key).add(po);
            }
            weekSet.add(key);
        }
        //获取时间周的最近和最远的时间
        String minDate = weekSet.stream().min(String::compareTo).get().split("~")[0];
        String maxDate =weekSet.stream().max(String::compareTo).get().split("~")[1] + " 23:59:59";
        //获取发货计划表数据 就这四五周的数据
        Map<String, Object> currentQueryMap = ImmutableMap.of("productCodes", capacityProductCodes,"startTime",minDate,"endTime",maxDate);
        List<DeliveryPlanVO2> deliveryPlanVO2s = dfpFeign.selectDeliveryPlanPublishedByParams(SystemHolder.getScenario(), currentQueryMap);
        //返回至前端的集合数据
        List<CapacityLoadVO> newDataList = new ArrayList<>();

        String specialStockPoint = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT"));
        String specialStockPoint2 = ipsFeign.getByCollectionCode("SPECIAL_STOCK_POINT_S2").stream()
                .map(CollectionValueVO::getCollectionValue)
                .findFirst()
                .orElseThrow(() -> new BusinessException("数据字典未配置SPECIAL_STOCK_POINT_S2"));
        //todo 获取该产线的详情数据
        if (StringUtils.isNotEmpty(resourceCode)){
            List<String> productCodeList = deliveryPlanVO2s.stream().map(DeliveryPlanVO2::getProductCode).distinct().collect(Collectors.toList());
            List<NewStockPointVO> newStockPoints = masterPlanInventorySupport.getStockPoint();
            List<String> bcStockPointList = newStockPoints.stream().map(NewStockPointVO::getStockPointCode)
                    .collect(Collectors.toList());

            Map<String, List<BomRoutingStepInputVO>> semiBomMap = masterPlanInventorySupport.getSemiBomMap(productCodeList);
            List<InventoryBatchDetailVO> inventoryBatchDetail = masterPlanInventorySupport.getInventoryBatchDetail(bcStockPointList, productCodeList);
            Map<String, List<InventoryBatchDetailVO>> finishInventoryMap = masterPlanInventorySupport.getFinishInventoryMap(inventoryBatchDetail, newStockPoints);
            Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = masterPlanInventorySupport.getOperationInventoryMap(inventoryBatchDetail, newStockPoints);
            Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap = masterPlanInventorySupport.getSemiFinishInventoryMap(inventoryBatchDetail, newStockPoints);
            Map<String, SubInventoryCargoLocationVO> cargoLocationMap = masterPlanInventorySupport.getSubInventoryCargoLocationMap(inventoryBatchDetail);
            Map<String, String> stepMap = masterPlanInventorySupport.getStepMap();
            List<String> productIds = semiBomMap.values().stream().flatMap(Collection::stream).map(BomRoutingStepInputVO::getInputProductId).distinct()
                    .collect(Collectors.toList());

            List<NewProductStockPointVO> inputProductStockPoints = newMdsFeign.selectProductStockPointByIds(SystemHolder.getScenario(),
                    productIds);
            productNewProductStockPointVOS.addAll(inputProductStockPoints);
            Map<String, NewProductStockPointVO> productionProductCodeMap = productNewProductStockPointVOS.stream()
                    .distinct().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(),
                            (u, v) -> u));
            List<InventoryBatchDetailVO> externalInventoryBatchDetailVOS = capacityBalanceDao.selectExternalInventoryByProductCodeList(productCodeList);
            Map<String, String> externalInventoryMap = externalInventoryBatchDetailVOS
                    .stream().collect(Collectors.toMap(InventoryBatchDetailVO::getProductCode,
                            InventoryBatchDetailVO::getCurrentQuantity, (u, v) -> u));

            //对时间进行排序，从小至大
            List<String> weekList = new ArrayList(weekSet);
            Collections.sort(weekList);
            //周 个数
            int size = weekList.size();
            //返回的对象数据

            //按照编码进行分类
            Map<String,List<DeliveryPlanVO2>> collect1 = deliveryPlanVO2s.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
            Map<String,List<String>> productMap = new HashMap();
            //遍历多少条编码，且发货量默认设置为0
            for (String itemProductCode : collect1.keySet()) {
                productMap.put(itemProductCode, Collections.nCopies(size, 0 + ""));
            }
            for (int i = 0; i < size; i++) {
                String weekDateStr = weekList.get(i);
                String[] parts = weekDateStr.split("~");
                Date beginDate = DateUtils.stringToDate(parts[0],DateUtils.COMMON_DATE_STR3);
                Date endDate = DateUtils.stringToDate(parts[1] + " 23:59:59",DateUtils.COMMON_DATE_STR1);
                //weekDateStr代表这一周两个时间
                List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = suppluRelationMap.get(weekDateStr);
                if (!capacitySupplyRelationshipPOS.isEmpty()){
                    List<String> productCodes = capacitySupplyRelationshipPOS.stream().map(CapacitySupplyRelationshipPO::getProductCode).distinct().collect(Collectors.toList());
                    //这一周的发货计划
                    List<DeliveryPlanVO2> weekData = deliveryPlanVO2s.stream().filter(item -> productCodes.contains(item.getProductCode()))
                            .filter(item -> item.getDemandTime().compareTo(beginDate) >= 0)
                            .filter(item -> item.getDemandTime().compareTo(endDate) <= 0).collect(Collectors.toList());
                    //按照编码分类
                    Map<String, List<DeliveryPlanVO2>> collect = weekData.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
                    //遍历数据
                    for (Map.Entry<String, List<DeliveryPlanVO2>> entry : collect.entrySet()) {
                        String key = entry.getKey();
                        List<String> originalList = productMap.get(key);
                        //创建原始列表的可变副本
                        List<String> newList = new ArrayList<>(originalList);
                        newList.set(i, entry.getValue().stream().mapToInt(DeliveryPlanVO2::getDemandQuantity).sum()+"");
                        productMap.put(key, newList);
                    }
                }
            }
            // 删除值全为0的键
            productMap.entrySet().removeIf(entry -> {
                List<String> list = entry.getValue();
                return list != null && !list.isEmpty() && list.stream().allMatch(i -> "0".equals(i));
            });
            List<Map<String,Object>> productDatalist = new ArrayList<>();
            for (Map.Entry<String,List<String>> entry :productMap.entrySet()) {
                String productCode = entry.getKey();
                List<String> originalList = entry.getValue();
                Map<String,Object> sonMap = new HashMap<>();
                //物料编码
                sonMap.put("productCode",productCode);
                //物料编码中每周的发货计划量
                for (int i = 0; i < size; i++) {
                    sonMap.put(weekList.get(i),originalList.get(i));
                }
                //设置计划员
                Optional<NewProductStockPointVO> first = saleNewProductStockPointVOS.stream().filter(item -> productCode.equals(item.getProductCode())).findFirst();
                sonMap.put("orderPlanner",first.isPresent() ? first.get().getOrderPlanner(): "");

                inventoryData(productionProductCodeMap, productCode, semiBomMap, semiFinishInventoryMap, cargoLocationMap,
                        stepMap, operationInventoryMap, finishInventoryMap, externalInventoryMap, sonMap,specialStockPoint,specialStockPoint2);
                productDatalist.add(sonMap);
            }
            CapacityLoadVO capacityLoadVO = new CapacityLoadVO();
            capacityLoadVO.setProductDatalist(productDatalist);
            newDataList.add(capacityLoadVO);
            return newDataList;
        }
        // todo 获取负荷的总和数据
        //产能负荷按照产线分类
        Map<String, List<CapacityLoadVO>> map = capacityLoadVOS.stream().collect(Collectors.groupingBy(CapacityLoadVO::getResourceCode));
        for (Map.Entry<String, List<CapacityLoadVO>> entry : map.entrySet()) {
            //判断负荷率是否合格
            boolean isOk = false;
            CapacityLoadVO vo = new CapacityLoadVO();
            vo.setResourceCode(entry.getKey());
            List<CapacityLoadVO2> vo2List = new ArrayList<>();
            //产能负荷中再按照时间分组
            Map<String, List<CapacityLoadVO>> listMap = entry.getValue().stream().collect(Collectors.groupingBy(t -> getKey(t.getForecastTime(), startDate, DateUtils.COMMON_DATE_STR3)));
            for (Map.Entry<String, List<CapacityLoadVO>> listEntry : listMap.entrySet()) {
                String key = listEntry.getKey();
                CapacityLoadVO2 capacityLoadVO2 = new CapacityLoadVO2();
                //标题 时间范围
                capacityLoadVO2.setForecastWeekTime(key);
                // 周总产能
                BigDecimal weekDemandQuantity = getSum(listEntry.getValue(), CapacityLoadVO::getDemandQuantity);
                // 周总产能
                BigDecimal weekAvailableCapacity = getSum(listEntry.getValue(), CapacityLoadVO::getAvailableCapacity);
                // 周总消耗产能
                BigDecimal weekProductionCapacity = getSum(listEntry.getValue(), CapacityLoadVO::getProductionCapacity);

                //周负荷率= 周占用产能 / 周可用产能
                BigDecimal divide = weekProductionCapacity.divide(weekAvailableCapacity, 2, RoundingMode.HALF_UP);
                if (divide.compareTo(divide1) >0 && !isOk){
                    isOk = true;
                }
                //赋值-周排产量
                capacityLoadVO2.setDemandQuantity(weekDemandQuantity);
                //赋值-周负荷率
                capacityLoadVO2.setCapacityUtilization(divide);
                //赋值-周均产能
                // 获取每周的平均节拍
                List<CapacitySupplyRelationshipPO> value = suppluRelationMap.get(key).stream().filter(item -> entry.getKey().equals(item.getResourceCode())).collect(Collectors.toList());
                //平均节拍=sum(需求量*节拍)/sum(需求量)
                BigDecimal sumBeat = value.stream().map(t -> t.getDemandQuantity().multiply(new BigDecimal(t.getBeat()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal sumDemandQuantity = value.stream().map(CapacitySupplyRelationshipPO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal averageBeat = sumBeat.divide(sumDemandQuantity, 2, RoundingMode.HALF_UP);
                capacityLoadVO2.setAverageBeat(averageBeat);
                //根据负荷率返回颜色
                capacityLoadVO2.setColor(getColor(capacityLoadVO2.getCapacityUtilization()));

                //key只是代表当前时间
                List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = suppluRelationMap.get(key);
                //获取物料以及对应的数据和发布人
                if (!capacitySupplyRelationshipPOS.isEmpty()){
//                  // 该产线中这一周的物料编码 通过编码和时间查询发货计划表数据
                    List<String> productCodes = capacitySupplyRelationshipPOS.stream()
                            .filter(item -> entry.getKey().equals(item.getResourceCode()))
                            .map(CapacitySupplyRelationshipPO::getProductCode).distinct().collect(Collectors.toList());
                    String[] split = key.split("~");
                    Date beforeDate = DateUtils.stringToDate(split[0], DateUtils.COMMON_DATE_STR3);
                    Date afterDate = DateUtils.stringToDate(split[1] + " 23:59:59", DateUtils.COMMON_DATE_STR1);
                    List<DeliveryPlanVO2> deliveryPlanVO2List = deliveryPlanVO2s.stream()
                            .filter(item -> productCodes.contains(item.getProductCode()))
                            .filter(item -> item.getDemandTime().compareTo(beforeDate) >= 0)
                            .filter(item -> item.getDemandTime().compareTo(afterDate) <= 0).collect(Collectors.toList());
                    int sum = deliveryPlanVO2List.stream().mapToInt(DeliveryPlanVO2::getDemandQuantity).sum();
                    capacityLoadVO2.setTotalProductCount(sum);
                }
                vo2List.add(capacityLoadVO2);
            }
            if (isOk){
                //周均产能
                BigDecimal divide = vo2List.stream().map(CapacityLoadVO2::getAverageBeat).reduce(BigDecimal.ZERO, BigDecimal::add).divide(new BigDecimal(vo2List.size()), 2, RoundingMode.HALF_UP);
                vo.setWeekAverageBeat(divide);
                //按月份升序排序
                vo2List.sort(Comparator.comparing(CapacityLoadVO2::getForecastWeekTime));
                vo.setCapacityLoadVO2List(vo2List);
                newDataList.add(vo);
            }
        }

        return newDataList.stream().sorted(Comparator.comparing(t->t.getOperationCode()+t.getOperationName()+t.getResourceCode())).collect(Collectors.toList());

    }

    /**
     * 拼接关键工序
     * @param standardStepVOS
     * @return
     */
    private String concatKeyStandardStep(List<StandardStepVO> standardStepVOS) {
        StringBuilder stringBuilder = new StringBuilder();
        //动态拼接字段
        for (int i = 0; i < standardStepVOS.size(); i++) {
            StandardStepVO standardStepVO = standardStepVOS.get(i);
            if (i > 0){
                stringBuilder.append(" OR ");
            }
            stringBuilder.append("(plant_code = '")
                    .append(standardStepVO.getStockPointCode())
                    .append("' AND operation_code = '")
                    .append(standardStepVO.getStandardStepCode())
                    .append("')");
        }
        // 拼接完整查询条件
        String queryCriteriaParam = "";
        if (!standardStepVOS.isEmpty()) {
            queryCriteriaParam = "AND (" + stringBuilder.toString() + ") AND version_id = 'WEEK'";
        }
        return queryCriteriaParam;
    }

    /**
     * 组装库存相关数据
     */
    private static void inventoryData(Map<String, NewProductStockPointVO> productionProductCodeMap, String productCode,
                                      Map<String, List<BomRoutingStepInputVO>> semiBomMap,
                                      Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap,
                                      Map<String, SubInventoryCargoLocationVO> cargoLocationMap,
                                      Map<String, String> stepMap,
                                      Map<String, List<InventoryBatchDetailVO>> operationInventoryMap,
                                      Map<String, List<InventoryBatchDetailVO>> finishInventoryMap,
                                      Map<String, String> externalInventoryMap,
                                      Map<String, Object> sonMap,
                                      String specialStockPoint, String specialStockPoint2) {
        NewProductStockPointVO productItem = productionProductCodeMap.get(productCode);

        String semiInventory = "";
        if(semiBomMap.containsKey(productCode)){
            List<BomRoutingStepInputVO> semiBomList = semiBomMap.get(productCode);
            List<Double> semiValue = new ArrayList<>();
            for (BomRoutingStepInputVO bomRoutingStepInputVO : semiBomList) {
                String semiProductCode = bomRoutingStepInputVO.getProductCode();
                // 半品物料
                NewProductStockPointVO semiProductCodeItem = productionProductCodeMap.get(semiProductCode);
                if(Objects.isNull(semiProductCodeItem)){
                    continue;
                }
                String semiProductCodeItemProductCode = semiProductCodeItem.getProductCode();
                String stockPointCode = semiProductCodeItem.getStockPointCode();
                String semiKey = stockPointCode + "-" + semiProductCodeItemProductCode;
                // 维护产品编码对应的半品库存
                List<InventoryBatchDetailVO> semiFinishList = MasterPlanServiceImpl.getFinishInventory(semiFinishInventoryMap
                        .get(semiKey), cargoLocationMap);
                BigDecimal semiFinishInventory = semiFinishList.stream().map(t ->
                        new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
                double semiBomInventory = MasterPlanServiceImpl.getSemiBomInventory(semiProductCodeItem, stepMap,
                        operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, productCode);
                semiBomInventory = semiBomInventory + semiFinishInventory.doubleValue();
                semiValue.add(semiBomInventory);
            }
            // 发货计划总览的成型后库存，应该是大小片各自的半品库存，然后取小
            if(CollectionUtils.isNotEmpty(semiValue)){
                semiValue.sort(Comparator.comparing(Double::doubleValue));
                semiInventory = new BigDecimal(String.valueOf(semiValue.get(0))).stripTrailingZeros().toPlainString();
            }
        }else{
            double semiBomInventory = MasterPlanServiceImpl.getSemiBomInventory(productItem, stepMap, operationInventoryMap, cargoLocationMap, specialStockPoint, specialStockPoint2, productCode);
            semiInventory = new BigDecimal(String.valueOf(semiBomInventory)).stripTrailingZeros().toPlainString();
        }
        BigDecimal finishedInventory = MasterPlanServiceImpl.getFinishInventory(finishInventoryMap.get(productCode), cargoLocationMap)
                .stream().map(t ->
                        new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
        String externalInventory = "0";
        if (externalInventoryMap.containsKey(productCode)) {
            externalInventory = new BigDecimal(String.valueOf(externalInventoryMap.get(productCode))).stripTrailingZeros().toPlainString();
        }
        sonMap.put("finishInventory", finishedInventory);
        sonMap.put("semiInventory", semiInventory);
        sonMap.put("externalInventory", externalInventory);
    }

    @SneakyThrows
    @Override
    public void exportData(HttpServletResponse response, Integer utilization) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "超负荷预警表");
        //数据
        List<List<String>> dataList = new ArrayList<>();
        //获取动态列
        List<String> forecastMonthList = getForecastMonthList(dataList,utilization);
        //初始化头
        List<List<String>> headers = initHeaders(forecastMonthList);
        // 设置单元格合并
        EasyExcel.write(out)
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .sheet("Sheet1")
                .doWrite(dataList);
    }

    /**
     * 初始化头
     * @param forecastMonthList
     * @return
     */
    private static List<List<String>> initHeaders(List<String> forecastMonthList) {
        List<List<String>> headers = new ArrayList<>();
        String mainTitle = "超负荷预警表";
        headers.add(Lists.newArrayList(mainTitle, "产线"));
        headers.add(Lists.newArrayList(mainTitle, "周平均节拍"));
        headers.add(Lists.newArrayList(mainTitle, "项"));
        for (String month : forecastMonthList) {
            headers.add(Lists.newArrayList(mainTitle, month));
        }
        headers.add(Lists.newArrayList(mainTitle, "计划员"));
        return headers;
    }

    /**
     * 获取数据
     *
     * @param dataList
     * @param utilization
     * @return
     */
    public List<String> getForecastMonthList(List<List<String>> dataList, Integer utilization) {
        //获取所有用户
        List<User> users = ipsNewFeign.userList();
        //是否准确查询产线数据
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        standardStepVOS = standardStepVOS.stream()
                .filter(item->"FORMING_PROCESS".equals(item.getStandardStepType()))
                .collect(Collectors.toList());
        if (standardStepVOS.isEmpty()){
            throw new RuntimeException("未找到该租户下的关键工序数据，请进行维护");
        }
        String queryCriteriaParam = this.concatKeyStandardStep(standardStepVOS);
        // 获取关键工序的产能负荷表数据
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadDao.selectByCondition(null, queryCriteriaParam);
        //没有数据就返回空集合
        if (CollectionUtils.isEmpty(capacityLoadVOS)){
            return new ArrayList<>();
        }
        //获取每个关键工序对应的资源编码
        Set<String> resourceCodes = capacityLoadVOS.stream().map(CapacityLoadVO::getResourceCode).collect(Collectors.toSet());
        //查询关键工序产能供应数据
        Map<String, Object> params = new HashMap<>();
        params.put("versionId", CapacityBalanceTypeEnum.WEEK.getCode());
        params.put("resourceCodeList",resourceCodes);
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectByParams(params);
        log.info("产能负荷数据条数{}，产能供应数据条数{}", capacityLoadVOS.size(), capacitySupplyRelationshipPOList.size());
        // 获取所有对应的物料编码集合
        List<String> capacityProductCodes = capacitySupplyRelationshipPOList.stream().map(CapacitySupplyRelationshipPO::getProductCode).distinct().collect(Collectors.toList());
        //获取当前租户对应的销售组织 SystemHolder.getTenantCode()
        List<Scenario> scenarios = ipsNewFeign.selectDefaultByTenantId(SystemHolder.getTenantCode());
        //获取销售组织的物料对象集合 SystemHolder.getScenario()
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of("productCodes", capacityProductCodes, "organizationId", scenarios.get(0).getAlternativeColumn()));
        // 获取产能负荷中最近的时间
        Date startDate = getMin(capacityLoadVOS, CapacityLoadVO::getForecastTime, Comparator.comparing(Date::getTime));
        // 将产能供应数据按照时间周分类
        Map<String, List<CapacitySupplyRelationshipPO>> suppluRelationMap = new HashMap<>();
        // 获取每个时间周的数据 比如2025-04-14~2025-04-20
        Set<String> weekSet = new HashSet<>();
        for (CapacitySupplyRelationshipPO po : capacitySupplyRelationshipPOList) {
            String key = getKey(po.getSupplyTime(), startDate, DateUtils.COMMON_DATE_STR3);
            if (!suppluRelationMap.containsKey(key)){
                List<CapacitySupplyRelationshipPO> list = new ArrayList<>();
                list.add(po);
                suppluRelationMap.put(key, list);
            }else {
                suppluRelationMap.get(key).add(po);
            }
            weekSet.add(key);
        }
        //给时间排序
        List<String> weekList = new ArrayList(weekSet);
        Collections.sort(weekList);
        //获取时间周的最近和最远的时间
        String minDate = weekSet.stream().min(String::compareTo).get().split("~")[0];
        String maxDate =weekSet.stream().max(String::compareTo).get().split("~")[1] + " 23:59:59";
        //获取发货计划表数据 就这四五周的数据
        Map<String, Object> currentQueryMap = ImmutableMap.of("productCodes", capacityProductCodes,"startTime",minDate,"endTime",maxDate);
        List<DeliveryPlanVO2> deliveryPlanVO2s = dfpFeign.selectDeliveryPlanPublishedByParams(SystemHolder.getScenario(), currentQueryMap);

        //负荷率标准
        BigDecimal divide1 = new BigDecimal(utilization).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        //产能负荷按照产线分类
        Map<String, List<CapacityLoadVO>> map = capacityLoadVOS.stream().collect(Collectors.groupingBy(CapacityLoadVO::getResourceCode));
        for (Map.Entry<String, List<CapacityLoadVO>> entry : map.entrySet()) {
            //判断负荷率是否合格
            boolean isOk = false;
            //当前产线
            String currentResourceCode = entry.getKey();
            //获取负荷率和排产量的集合
            List<String> utilizationList = new ArrayList<>();
            utilizationList.add(currentResourceCode);
            utilizationList.add("");
            utilizationList.add("负荷率");
            List<String> schedulingList = new ArrayList<>();
            schedulingList.add(currentResourceCode);
            schedulingList.add("");
            schedulingList.add("排产量");
            //设置该产线的均产能之和以及拥有数据的周数
            BigDecimal sumTotalBeat = new BigDecimal(0);
            int weekCount = 0;
            //将该产线下已有的数据按照时间分组
            Map<String, List<CapacityLoadVO>> listMap = entry.getValue().stream().collect(Collectors.groupingBy(t -> getKey(t.getForecastTime(), startDate, DateUtils.COMMON_DATE_STR3)));
            for (String week : weekList) {
                List<CapacityLoadVO> capacityLoadVOS1 = listMap.get(week);
                //表示在这一周没有数据
                if (capacityLoadVOS1 == null || capacityLoadVOS1.isEmpty()) {
                    utilizationList.add("0");//负荷率为空
                    schedulingList.add("0");//排产量为空
                } else {
                    //周可用产能
                    BigDecimal weekAvailableCapacity = getSum(capacityLoadVOS1, CapacityLoadVO::getAvailableCapacity);
                    //周占用产能
                    BigDecimal weekProductionCapacity = getSum(capacityLoadVOS1, CapacityLoadVO::getProductionCapacity);
                    //周负荷率= 周占用产能 / 周可用产能
                    BigDecimal divide = weekProductionCapacity.divide(weekAvailableCapacity, 2, RoundingMode.HALF_UP);
                    if (divide.compareTo(divide1) >0 && !isOk){
                        isOk = true;
                    }
                    //赋值-周负荷率
                    utilizationList.add(divide.toString());

                    //周排产量
                    BigDecimal weekDemandQuantity = capacityLoadVOS1.stream().map(CapacityLoadVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    schedulingList.add(weekDemandQuantity.toString());

                    // 获取每周的平均节拍 平均节拍=sum(需求量*节拍)/sum(需求量)
                    List<CapacitySupplyRelationshipPO> value = suppluRelationMap.get(week).stream().filter(item -> entry.getKey().equals(item.getResourceCode())).collect(Collectors.toList());
                    BigDecimal sumBeat = value.stream().map(t -> t.getDemandQuantity().multiply(new BigDecimal(t.getBeat()))).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal sumDemandQuantity = value.stream().map(CapacitySupplyRelationshipPO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 累加每周的平均节拍
                    sumTotalBeat = sumTotalBeat.add(sumBeat.divide(sumDemandQuantity, 2, RoundingMode.HALF_UP));
                    weekCount++;
                }
            }
            if (isOk && weekCount > 0) {
                //有数据的周均产能
                BigDecimal weekBeat = sumTotalBeat.divide(new BigDecimal(weekCount), 2, RoundingMode.HALF_UP);
                utilizationList.set(1, weekBeat.toString());
                schedulingList.set(1, weekBeat.toString());
                //将负荷率和排产量的数据加入到集合中
                dataList.add(utilizationList);
                dataList.add(schedulingList);

                //发货计划量集合
                List<String> sumDemandQuantityList = new ArrayList<>();
                sumDemandQuantityList.add(currentResourceCode);
                sumDemandQuantityList.add(weekBeat.toString());
                sumDemandQuantityList.add("发货计划量");
                for (String week : weekList) {
                    List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = suppluRelationMap.get(week);
                    //获取该周下该产线对应的物料编码
                    List<String> productList = capacitySupplyRelationshipPOS.stream()
                            .filter(item -> currentResourceCode.equals(item.getResourceCode()))
                            .map(CapacitySupplyRelationshipPO::getProductCode).distinct().collect(Collectors.toList());
                    String[] split = week.split("~");
                    Date beforeDate = DateUtils.stringToDate(split[0], DateUtils.COMMON_DATE_STR3);
                    Date afterDate = DateUtils.stringToDate(split[1] + " 23:59:59", DateUtils.COMMON_DATE_STR1);
                    //过滤后的计划发货单数据
                    List<DeliveryPlanVO2> collect = deliveryPlanVO2s.stream()
                            .filter(item -> productList.contains(item.getProductCode()))
                            .filter(item -> item.getDemandTime().compareTo(beforeDate) >= 0)
                            .filter(item -> item.getDemandTime().compareTo(afterDate) <= 0).collect(Collectors.toList());
                    sumDemandQuantityList.add(collect.stream().mapToInt(DeliveryPlanVO2::getDemandQuantity).sum()+"");
                }
                dataList.add(sumDemandQuantityList);
                //周 个数
                int size = weekList.size();
                //获取该产线下的供应数据
                List<CapacitySupplyRelationshipPO> currentCapacitySupplyRelationshipPO =
                        capacitySupplyRelationshipPOList.stream()
                                .filter(item -> currentResourceCode.equals(item.getResourceCode()))
                                .collect(Collectors.toList());
                //该产线下所有的物料编码
                List<String> currentResourceCodeToProductCodeList =
                        currentCapacitySupplyRelationshipPO.stream().map(CapacitySupplyRelationshipPO::getProductCode).distinct().collect(Collectors.toList());
                //按照编码进行分类
                Map<String,List<DeliveryPlanVO2>> collect1 = deliveryPlanVO2s.stream()
                        .filter(item -> currentResourceCodeToProductCodeList.contains(item.getProductCode()))
                        .collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
                Map<String,List<String>> productMap = new HashMap();
                //遍历多少条编码，且发货量默认设置为0
                for (String itemProductCode : collect1.keySet()) {
                    productMap.put(itemProductCode, Collections.nCopies(size, 0 + ""));
                }
                for (int i = 0; i < size; i++) {
                    String weekDateStr = weekList.get(i);
                    String[] parts = weekDateStr.split("~");
                    Date beginDate = DateUtils.stringToDate(parts[0],DateUtils.COMMON_DATE_STR3);
                    Date endDate = DateUtils.stringToDate(parts[1] + " 23:59:59",DateUtils.COMMON_DATE_STR1);
                    //weekDateStr代表这一周两个时间
                    List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOS = suppluRelationMap.get(weekDateStr);
                    if (!capacitySupplyRelationshipPOS.isEmpty()){
                        List<String> productCodes = capacitySupplyRelationshipPOS.stream()
                                .filter(item -> currentResourceCode.equals(item.getResourceCode()))
                                .map(CapacitySupplyRelationshipPO::getProductCode).distinct().collect(Collectors.toList());
                        //这一周的发货计划
                        List<DeliveryPlanVO2> weekData = deliveryPlanVO2s.stream().filter(item -> productCodes.contains(item.getProductCode()))
                                .filter(item -> item.getDemandTime().compareTo(beginDate) >= 0)
                                .filter(item -> item.getDemandTime().compareTo(endDate) <= 0).collect(Collectors.toList());
                        //按照编码分类
                        Map<String, List<DeliveryPlanVO2>> collect = weekData.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
                        //遍历数据
                        for (Map.Entry<String, List<DeliveryPlanVO2>> productCodeEntry : collect.entrySet()) {
                            String key = productCodeEntry.getKey();
                            List<String> originalList = productMap.get(key);
                            //创建原始列表的可变副本
                            List<String> newList = new ArrayList<>(originalList);
                            newList.set(i, productCodeEntry.getValue().stream().mapToInt(DeliveryPlanVO2::getDemandQuantity).sum()+"");
                            productMap.put(key, newList);
                        }
                    }
                }
                // 删除值全为0的键
                productMap.entrySet().removeIf(productCodeEntry -> {
                    List<String> list = productCodeEntry.getValue();
                    return list != null && !list.isEmpty() && list.stream().allMatch(i -> "0".equals(i));
                });
                List<Map<String,Object>> productDatalist = new ArrayList<>();
                for (Map.Entry<String,List<String>> productEntry :productMap.entrySet()) {
                    String key = productEntry.getKey();
                    List<String> originalList = productEntry.getValue();
                    //塞值
                    List<String> detailProductList = new ArrayList<>();
                    detailProductList.add(currentResourceCode);
                    detailProductList.add(weekBeat.toString());
                    detailProductList.add(key);
                    //物料编码中每周的发货计划量
                    for (String demandQuantity:originalList) {
                        detailProductList.add(demandQuantity);
                    }
                    //设置计划员
                    Optional<NewProductStockPointVO> first = newProductStockPointVOS.stream().filter(item -> key.equals(item.getProductCode())).findFirst();
                    if (first.isPresent()) {
                        Optional<User> first1 = users.stream().filter(item -> item.getId().equals(first.get().getOrderPlanner())).findFirst();
                        detailProductList.add(first1.isPresent() ? first1.get().getCnName() : "");
                    }
                    dataList.add(detailProductList);
                }
            }
        }
        return weekList;
    }

    @Override
    public void sendMessage(List<CapacityLoadDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        List<CollectionValueVO> roleTypeValues = ipsFeign.getByCollectionCode("ROLE_MANAGER");
        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(roleTypeValues)) {
            throw new BusinessException("ROLE_MANAGER值集未配置");
        }
        List<String> managerNames = StreamUtils.columnToList(roleTypeValues, CollectionValueVO::getValueMeaning);
        List<String> managerUserIds = ipsFeign.selectUserByRoleNames(managerNames)
                .stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(managerUserIds)) {
            log.warn("周产能平衡计算消息发送-根据配置的角色没有找到对应的用户!");
            return;
        }

        Date startDate = dtoList.stream().min(Comparator.comparing(CapacityLoadDTO::getForecastTime))
                .map(CapacityLoadDTO::getForecastTime).orElse(new Date());
        Map<String, List<CapacityLoadDTO>> map = dtoList.stream()
                .collect(Collectors.groupingBy(t ->
                        t.getPlantCode() + "-" + t.getResourceGroupCode() + "-" + t.getResourceCode()));

        List<UserMessageDTO> userMessageDTOList = new ArrayList<>();
        for (Map.Entry<String, List<CapacityLoadDTO>> entry : map.entrySet()) {
            Map<String, List<CapacityLoadDTO>> listMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(t -> getKey(t.getForecastTime(), startDate, DateUtils.COMMON_DATE_STR3)));
            for (Map.Entry<String, List<CapacityLoadDTO>> listEntry : listMap.entrySet()) {
                String key = listEntry.getKey();
                // 周总产能
                BigDecimal weekAvailableCapacity = getSum(listEntry.getValue(), CapacityLoadDTO::getAvailableCapacity);
                // 周总消耗产能
                BigDecimal weekProductionCapacity = getSum(listEntry.getValue(), CapacityLoadDTO::getProductionCapacity);
                // 周负荷率
                BigDecimal divide = weekProductionCapacity.divide(weekAvailableCapacity, 2, RoundingMode.HALF_UP);
                String resourceCode = listEntry.getValue().get(0).getResourceCode();
                if (divide.compareTo(BigDecimal.ZERO) >= 0) {
                    String message = "周产能平衡超负荷告警:" +
                            resourceCode +
                            "产线，" +
                            key +
                            "产线产能超100%";
                    UserMessageDTO messageDTO = new UserMessageDTO();
                    messageDTO.setMessageContent(message);
                    userMessageDTOList.add(messageDTO);
                }
            }
        }
        List<UserMessageDTO> allUserMessageDTOList = new ArrayList<>();
        for (String managerUserId : managerUserIds) {
            for (UserMessageDTO userMessageDTO : userMessageDTOList) {
                String messageContent = userMessageDTO.getMessageContent();
                UserMessageDTO result = UserMessageDTO.builder()
                        .id(UUIDUtil.getUUID())
                        .userId(managerUserId)
                        .messageSource("系统消息")
                        .messageType(MessageTypeEnum.MY_MESSAGE.getCode())
                        .messageTitle("产能平衡超负荷告警")
                        .messageContent(messageContent)
                        .messageEmergency("3")
                        .readStatus(YesOrNoEnum.NO.getCode())
                        .build();
                allUserMessageDTOList.add(result);
            }
        }
        if (CollectionUtils.isNotEmpty(allUserMessageDTOList)) {
            log.info("周产能平衡超负荷告警消息发送开始..., 总条数{}", allUserMessageDTOList.size());
            userMessageUtils.sendMessage(allUserMessageDTOList);
            log.info("周产能平衡超负荷告警消息发送结束!");
        }
    }


    private static <T> BigDecimal getSum(Collection<T> collection, Function<T, BigDecimal> function) {
        return collection.stream().map(function).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static <T, V> V getMin(Collection<T> collection, Function<T, V> getV, Comparator<V> comparator) {
        return collection.stream().filter(x -> Objects.nonNull(getV.apply(x))).map(getV).min(comparator).orElse(null);
    }

}
