package com.yhl.scp.mps.domain.adjust.context;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.adjust.strategy.IAdjustPlanStrategy;
import com.yhl.scp.mps.adjust.support.AdjustPlanSupport;
import com.yhl.scp.mps.algorithm.dto.RzzAdjustmentParam;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.vo.OperationAdjustResultVO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <code>AdjustPlanStrategyService</code>
 * <p>
 * 手工编辑-权限校验
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-05 16:32:29
 */
@Service
@Slf4j
public class AdjustPlanStrategyService {

    private static final long LOCK_EXPIRE_TIME = 60;
    @Resource
    AdjustPlanSupport adjustPlanSupport;
    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Resource
    private IAdjustPlanStrategy iAdjustPlanStrategy;
    @Resource
    private OperationDao operationDao;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 执行计划调整操作
     * 1.校验产线组是否在运行权限
     * 2.校验当前资源和目标资源是否有任务在调整
     * 3.执行计划调整
     *
     * @param adjustmentParam 调整参数
     * @return 返回操作调整结果VO
     * @throws BusinessException 如果资源被锁定或出现异常时抛出此异常
     */
    @Transactional(rollbackFor = Exception.class)
    public OperationAdjustResultVO execute(RzzAdjustmentParam adjustmentParam) {
        // 1.校验产线组运行权限
        checkStandardPermission(adjustmentParam.getSourceResourceId(), adjustmentParam.getTargetResourceId());
        log.info("分布式锁校验产线操作开始");
        // 2.校验产线运行权限
        String sourceResourceId = adjustmentParam.getSourceResourceId();
        if (StrUtil.isNotEmpty(sourceResourceId)) {
            sourceResourceId = "SOURCE_" + adjustmentParam.getSourceResourceId();
        }
        String targetResourceId = "TARGET_" + adjustmentParam.getTargetResourceId();
        // 检查资源是否已存在
        if (isResourceLocked(sourceResourceId)) {
            throw new BusinessException("源资源正在执行任务，请等待结束后再试");
        }
        if (isResourceLocked(targetResourceId)) {
            throw new BusinessException("目标资源正在执行任务，请等待结束后再试");
        }
        // 获取锁
        boolean sourceLockAcquired = acquireLock(sourceResourceId);
        boolean targetLockAcquired = acquireLock(targetResourceId);
        try {
            // 判断锁是否获取成功
            if (!sourceLockAcquired || !targetLockAcquired) {
                throw new BusinessException("源资源和目标资源正在执行任务，请等待结束后再试");
            }
            log.info("计划调整前置校验结束");
            long startTime = System.currentTimeMillis();
            log.info("手工调整前处理开始时间{}", startTime);
            // 执行计划调整
            doExecute(adjustmentParam);
            log.info("手工调整完成时间{}", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("计划调整异常：", e);
            throw new BusinessException(e.getMessage());
        } finally {
            // 释放锁
            if (sourceLockAcquired) {
                releaseLock(sourceResourceId);
            }
            if (targetLockAcquired) {
                releaseLock(targetResourceId);
            }
        }
        return null;
    }

    /**
     * 校验产线组运行权限
     *
     * @param sourceResourceId 源资源ID
     * @param targetResourceId 目标资源ID
     * @throws BusinessException 如果当前有排产任务正在运行或产线组正在运行排产任务，则抛出异常
     */
    public void checkStandardPermission(String sourceResourceId, String targetResourceId) {
        if (StrUtil.isEmpty(sourceResourceId) && StrUtil.isEmpty(targetResourceId)) {
            return;
        }
        List<AlgorithmLog> algorithmLogs = ipsFeign.selectTaskIsNotFail(Collections.singletonList(ModuleCodeEnum.MPS.getCode()));

        List<String> runningResources = ListUtil.of(sourceResourceId, targetResourceId);
        // 目标资源和源资源对应的产线
        List<String> productLines = operationTaskExtDao.selectRunResources(runningResources);

        if (CollectionUtils.isEmpty(algorithmLogs) || CollectionUtils.isEmpty(productLines)) {
            return;
        }

        List<String> runningProductLine = new ArrayList<>();
        for (AlgorithmLog algorithmLog : algorithmLogs) {
            String lineGroup = algorithmLog.getProductLine();
            if (StrUtil.isNotEmpty(lineGroup)) {
                runningProductLine.addAll(Arrays.asList(lineGroup.split(",")));
            }
        }

        List<String> intersection = CollectionUtils.getInterSection(productLines, runningProductLine);

        if (CollectionUtils.isNotEmpty(intersection)) {
            throw new BusinessException("产线正在运行排产任务，请稍后再试：" + intersection);
        }
    }

    /**
     * 关闭或取消计划时校验是否有ams算法任务在运行
     *
     * @param operationIds
     */
    public void checkRunTaskByOperationIds(List<String> operationIds) {
        if (CollectionUtils.isEmpty(operationIds)) {
            return;
        }
        List<String> list = Arrays.asList(ModuleCodeEnum.MPS.getCode(), ModuleCodeEnum.AMS.getCode());
        List<AlgorithmLog> algorithmLogs = ipsFeign.selectTaskIsNotFail(list);
        List<OperationPO> operationPOList = operationDao.selectByPrimaryKeys(operationIds);
        if (CollectionUtils.isEmpty(operationPOList)) {
            return;
        }
        List<String> plannedResourceIds = operationPOList.stream().map(OperationPO::getPlannedResourceId).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(plannedResourceIds)) {
            return;
        }
        // 校验是否存在拖拽任务
        BaseResponse<Void> checkResponse = checkAdjustIdRunning(plannedResourceIds);
        if (!checkResponse.getSuccess()) {
            throw new BusinessException("产线正在运行排产任务，请稍后再试");
        }
        if (CollectionUtils.isEmpty(algorithmLogs)) {
            return;
        }
        List<String> productLines = algorithmLogs.stream().map(AlgorithmLog::getProductLine).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productLines)) {
            return;
        }
        List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectByPhysicalIds(SystemHolder.getScenario(), plannedResourceIds);
        List<String> resourceCodeList = physicalResourceVOS.stream().map(PhysicalResourceVO::getPhysicalResourceCode).collect(Collectors.toList());
        List<String> runningProductLine = new ArrayList<>();
        for (String productLine : productLines) {
            runningProductLine.addAll(Arrays.asList(productLine.split(",")));
        }
        List<String> intersection = CollectionUtils.getInterSection(resourceCodeList, runningProductLine);

        if (CollectionUtils.isNotEmpty(intersection)) {
            throw new BusinessException("产线正在运行排产任务，请稍后再试：" + intersection);
        }
    }

    private BaseResponse<Void> checkAdjustIdRunning(List<String> plannedResourceIds) {
        if (CollectionUtils.isEmpty(plannedResourceIds)) {
            return BaseResponse.success();
        }
        boolean checkResult = true;
        for (String resourceId : plannedResourceIds) {
            String targetResourceKey = String.format(RedisKeyManageEnum.RESOURCE_LOCK_KEY_PREFIX.getKey(), resourceId);
            if (redisUtil.hasKey(targetResourceKey)) {
                checkResult = false;
                break;
            }
        }
        if (!checkResult) {
            return BaseResponse.error("产线正在运行排产任务，请稍后再试");
        }
        return BaseResponse.success();
    }


    private void doExecute(RzzAdjustmentParam adjustmentParam) {
        // 入口
        iAdjustPlanStrategy.doAdjust(adjustmentParam);
    }

    private boolean isResourceLocked(String resourceId) {
        if (StringUtils.isEmpty(resourceId)) {
            return false;
        }
        String lockKey = String.format(RedisKeyManageEnum.ADJUST_RESOURCE_LOCK_PREFIX.getKey(), resourceId);
        return redisTemplate.hasKey(lockKey);
    }

    private boolean acquireLock(String resourceId) {
        if (StringUtils.isEmpty(resourceId)) {
            return true;
        }
        String lockKey = String.format(RedisKeyManageEnum.ADJUST_RESOURCE_LOCK_PREFIX.getKey(), resourceId);
        return Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, "1", LOCK_EXPIRE_TIME, TimeUnit.SECONDS));
    }

    private void releaseLock(String resourceId) {
        if (StringUtils.isEmpty(resourceId)) {
            return;
        }
        String lockKey = String.format(RedisKeyManageEnum.ADJUST_RESOURCE_LOCK_PREFIX.getKey(), resourceId);
        redisTemplate.delete(lockKey);
    }

}
