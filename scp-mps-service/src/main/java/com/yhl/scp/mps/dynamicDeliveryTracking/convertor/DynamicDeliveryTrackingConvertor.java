package com.yhl.scp.mps.dynamicDeliveryTracking.convertor;

import com.yhl.scp.mps.dynamicDeliveryTracking.domain.entity.DynamicDeliveryTrackingDO;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingGHDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingJCDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>DynamicDeliveryTrackingConvertor</code>
 * <p>
 * 动态交付跟踪表转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:45:22
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface DynamicDeliveryTrackingConvertor {

    DynamicDeliveryTrackingConvertor INSTANCE = Mappers.getMapper(DynamicDeliveryTrackingConvertor.class);

    DynamicDeliveryTrackingDO dto2Do(DynamicDeliveryTrackingDTO obj);

    DynamicDeliveryTrackingDTO do2Dto(DynamicDeliveryTrackingDO obj);

    List<DynamicDeliveryTrackingDO> dto2Dos(List<DynamicDeliveryTrackingDTO> list);

    List<DynamicDeliveryTrackingDTO> do2Dtos(List<DynamicDeliveryTrackingDO> list);

    DynamicDeliveryTrackingVO do2Vo(DynamicDeliveryTrackingDO obj);

    DynamicDeliveryTrackingVO po2Vo(DynamicDeliveryTrackingPO obj);

    List<DynamicDeliveryTrackingVO> po2Vos(List<DynamicDeliveryTrackingPO> list);

    DynamicDeliveryTrackingPO do2Po(DynamicDeliveryTrackingDO obj);

    DynamicDeliveryTrackingDO po2Do(DynamicDeliveryTrackingPO obj);

    DynamicDeliveryTrackingPO dto2Po(DynamicDeliveryTrackingDTO obj);

    List<DynamicDeliveryTrackingPO> dto2Pos(List<DynamicDeliveryTrackingDTO> obj);

    DynamicDeliveryTrackingPO vo2Po(DynamicDeliveryTrackingVO obj);


    List<DynamicDeliveryTrackingPO> vo2Pos(List<DynamicDeliveryTrackingVO> obj);

    DynamicDeliveryTrackingPO jcDto2Po(DynamicDeliveryTrackingJCDTO obj);

    DynamicDeliveryTrackingPO ghDto2Po(DynamicDeliveryTrackingGHDTO obj);

}
